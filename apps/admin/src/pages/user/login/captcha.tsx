/*
 * @Description: 图片验证码组件
 * @param canCaptcha 是否需要验证码
 * @param phoneNumber 手机号
 * @param children 验证码按钮
 * @param onBizResultCallback 验证码成功回调
 */
'use client';
import { CAPTCHA_PREFIX, CAPTCHA_SCENE_ID } from '@/configs';
import { sendSmsWithCaptcha } from '@/services/user';
import { useCaptcha } from '@repo/core/hooks/use-captcha';
import to from 'await-to-js';
import React from 'react';

const Captcha = ({
  canCaptcha,
  phoneNumber,
  children,
  onBizResultCallback,
}: {
  canCaptcha: boolean;
  phoneNumber: string;
  children: React.ReactNode;
  onBizResultCallback: (bizResult?: unknown) => void;
}) => {
  const captchaVerifyCallback = async (captchaVerifyParam: string) => {
    const [err, res] = await to(
      sendSmsWithCaptcha({
        captchaVerifyParam,
        phone_number: phoneNumber,
        sceneId: CAPTCHA_SCENE_ID,
        platform: 1,
      }),
    );

    if (err || res.code) {
      return {
        captchaResult: false,
        bizResult: false,
      };
    }

    return {
      captchaResult: true,
    };
  };

  useCaptcha({
    captchaVerifyCallback,
    onBizResultCallback,
    SceneId: CAPTCHA_SCENE_ID,
    prefix: CAPTCHA_PREFIX,
  });

  if (canCaptcha) {
    return (
      <>
        <span id="captcha-button">{children}</span>
        <div id="captcha-element"></div>
      </>
    );
  }

  return (
    <>
      <span id="captcha-button"></span>
      <div id="captcha-element"></div>
      {children}
    </>
  );
};

export default Captcha;
