/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  return {
    canAdmin: currentUser && currentUser.access === 'admin',
    staticMenu: () => true,
    canUseMenu: (route: any) =>
      currentUser?.menus && currentUser?.menus.some((item) => item.menuPath === route.path),
  };
}
