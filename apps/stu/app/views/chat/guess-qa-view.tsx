"use client";

import { FormatMath } from "@repo/core/exercise/components";

// 定义类型
type GuessQAProps = {
  suggestedQuestions: { askText: string }[];
  onSendMessage: (message: string) => void;
};

export default function GuessQA({
  suggestedQuestions,
  onSendMessage,
}: GuessQAProps) {
  // 发送消息需要传递给父组件
  const handleSendMessage = (message: string) => {
    onSendMessage(message);
  };
  return (
    <>
      {/* 猜你想问 点击问题作为用户输入 发送消息 */}
      <ul className="px-8">
        {suggestedQuestions.map((item, index) => (
          <li
            key={index.toString()}
            className="mb-4"
            onClick={() => handleSendMessage(item.askText)}
          >
            <div className="inline-block min-h-[50px] rounded-2xl border border-[rgba(31,35,43,0.12)] px-4 py-3 text-base leading-6 text-[rgba(31,35,43,0.75)]">
              <FormatMath
                key={"guess-qa-" + index}
                htmlContent={item.askText}
                questionId={""}
              />
            </div>
          </li>
        ))}
      </ul>
    </>
  );
}
