import { useSignal } from "@preact-signals/safe-react";
import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { useCourseViewContext } from "../course/course-view-context";

type InteractiveViewContextType = {
  next: () => void;
};

const InteractiveViewContext = createContext<InteractiveViewContextType>(
  {} as InteractiveViewContextType
);

export const InteractiveViewProvider: FC<
  PropsWithChildren<{
    active: boolean;
    index: number;
  }>
> = ({ children, active, index }) => {
  const {
    localProgressRecorder,
    next: courseNext,
    reportCostTime,
  } = useCourseViewContext();

  const progress = useMemo(() => {
    return localProgressRecorder.load(index, {
      costSeconds: 1,
    });
  }, [index, localProgressRecorder]);

  const costSeconds = useSignal(progress.costSeconds); // 初始化为上一次的播放时长
  const lastTime = useSignal(Date.now());

  const next = useCallback(() => {
    reportCostTime(costSeconds.value);

    courseNext();
  }, [costSeconds, reportCostTime, courseNext]);

  useEffect(() => {
    if (active) {
      lastTime.value = Date.now();
    }
  }, [active, lastTime]);

  useEffect(() => {
    if (!active) return;
    const updateTime = () => {
      const now = Date.now();
      costSeconds.value += now - lastTime.value;
      lastTime.value = now;
      localProgressRecorder.save(index, {
        costSeconds: costSeconds.value,
      });
    };
    const interval = setInterval(updateTime, 250);
    return () => {
      clearInterval(interval);
      updateTime();
    };
  }, [lastTime, costSeconds, localProgressRecorder, index, active]);

  return (
    <InteractiveViewContext.Provider value={{ next }}>
      {children}
    </InteractiveViewContext.Provider>
  );
};

export const useInteractiveViewContext = () =>
  useContext(InteractiveViewContext);
