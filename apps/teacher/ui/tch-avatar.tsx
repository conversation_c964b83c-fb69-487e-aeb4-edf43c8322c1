import defaultAvatar from "@/public/images/default-avatar.webp";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  AvatarPrimitive,
} from "@/ui/avatar";
import { cn } from "@/utils/utils";
import Image from "next/image";
import { memo } from "react";

function TchAvatar({
  className,
  src,
  alt,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Root> & {
  src: string;
  alt?: string;
}) {
  return (
    <Avatar className={cn("cursor-pointer rounded-full", className)} {...props}>
      <AvatarImage src={src} alt={alt} />

      <AvatarFallback>
        <Image src={defaultAvatar} alt="avatar" width={300} height={300} />
      </AvatarFallback>
    </Avatar>
  );
}

export default memo(TchAvatar);
