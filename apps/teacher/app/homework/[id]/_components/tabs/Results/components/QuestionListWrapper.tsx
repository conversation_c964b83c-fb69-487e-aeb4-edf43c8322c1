"use client";

import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
// import { useComputed } from "@preact-signals/safe-react";
import {
  // QaContentType,
  QuestionList,
} from "@repo/core/views/tch-question-view";
// import { useUpdateEffect } from "ahooks";
// import { useState } from "react";
import { selectQuestion, useAnswerResults } from "../store/answers";
import CustomFooter from "./CustomFooter";
import ReportButton from "./ReportButton";

interface QuestionListWrapperProps {
  onClick?: () => void;
}
export default function QuestionListWrapper({
  onClick,
}: QuestionListWrapperProps) {
  const { viewMode } = useTaskContext();
  const { filteredAnswers, loading } = useAnswerResults();
  // const [filterAnswers, setFilterAnswers] = useState<QaContentType[]>([]);
  const handleClickView = (questionId: string) => {
    // DONE: 埋点28 => `homework_list_report_student_question_detail` 学生的作业报告页中查看题目详情 view=student
    if (viewMode.value === "student") {
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.REPORT_STUDENT_QUESTION_DETAIL,
        {
          eventName: "学生的作业报告页中查看题目详情",
        }
      );
    }
    selectQuestion(questionId);
    onClick?.();
  };
  // const filterAnswers = useComputed(() => {
  //   return filteredAnswers.value;
  // });
  // console.log("filterAnswers", filterAnswers.value);
  // useUpdateEffect(() => {
  //   console.log("filteredAnswers", filteredAnswers.value);
  //   const temp = filteredAnswers.value?.map((item) => item.questionId);
  //   const ids = [...new Set(temp)];
  //   const map = filteredAnswers.value?.reduce(
  //     (acc, item) => {
  //       acc[item.questionId] = item;
  //       return acc;
  //     },
  //     {} as Record<string, QaContentType>
  //   );
  //   console.log("map", map);
  //   const newFilterAnswers = ids.map((id) => map[id]);
  //   console.log("newFilterAnswers", newFilterAnswers);
  //   setFilterAnswers(newFilterAnswers);
  // }, [filteredAnswers.value]);
  return (
    <QuestionList
      questions={filteredAnswers.value}
      loading={loading.value}
      onClickView={handleClickView}
      customFooter={CustomFooter}
      hasFooterButton={true}
      footerButtonComponent={ReportButton}
    />
  );
}
