import "katex/dist/katex.min.css";
import { useEffect, useRef } from "react";
import { RichTextViewProps } from "./types";
import {
  addHostToImgSrc,
  handleImageError,
  renderFormulasByKatex,
} from "./utils";

export function RichTextMath({
  htmlContent,
  questionId,
  ossHost,
  resourceApiHost,
}: RichTextViewProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const errorHandler = (event: Event) =>
      handleImageError(event, questionId || "", resourceApiHost || "");

    let images: HTMLImageElement[] = [];

    // 使用 requestAnimationFrame 来优化渲染时机，减少抖动
    const updateContent = () => {
      if (!containerRef.current) return;

      // 清空容器，确保干净的 DOM 环境
      containerRef.current.innerHTML = "";

      let html = renderFormulasByKatex(htmlContent);
      html = addHostToImgSrc(html, ossHost || "");

      // 直接设置 innerHTML，让 MathContent 在纯 HTML 环境中工作
      containerRef.current.innerHTML = html;

      // 处理图片错误事件
      if (html && containerRef.current) {
        const imgs = containerRef.current.querySelectorAll("img");
        images = Array.from(imgs);
        images.forEach((image) => {
          image.addEventListener("error", errorHandler);
        });
      }
    };

    // 使用 requestAnimationFrame 确保在浏览器下一次重绘时更新内容
    const rafId = requestAnimationFrame(updateContent);

    // 清理函数
    return () => {
      cancelAnimationFrame(rafId);
      if (images.length > 0) {
        images.forEach((image) => {
          image.removeEventListener("error", errorHandler);
        });
      }
    };
  }, [htmlContent, ossHost, resourceApiHost, questionId]);

  // 4. 初始渲染（此时公式尚未处理）
  // 将 MathContent 包裹在 HTML 外面，避免 React 组件实例监控问题
  return <div className="rich-text-math w-full" ref={containerRef} />;
}
