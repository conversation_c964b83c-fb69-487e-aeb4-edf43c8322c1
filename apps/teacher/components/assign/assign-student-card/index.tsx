import { ClassStudentListItem, StudentItem } from "@/types/assign";
import { cn } from "@/utils/utils";
import Tabs from "antd-mobile/es/components/tabs";
import { StudentProfile } from "./student-profile";
interface AssignStudentCardProps {
  style?: React.CSSProperties;
  className?: string;
  classStudentList?: ClassStudentListItem[];
}

export function AssignStudentCard({
  style = {},
  className = "",
  classStudentList = [],
}: AssignStudentCardProps) {
  const tabsCls = `
    [&_.adm-tabs-header]:pb-0
    [&_.adm-tabs-header]:border-b!
    [&_.adm-tabs-header]:border-line-1!

    [&_.adm-tabs-header-mask-left]:bg-linear-to-r!
    [&_.adm-tabs-header-mask-left]:from-white!
    [&_.adm-tabs-header-mask-left]:to-transparent!

    [&_.adm-tabs-header-mask-right]:bg-linear-to-l!
    [&_.adm-tabs-header-mask-right]:from-white!
    [&_.adm-tabs-header-mask-right]:to-transparent!

    [&_.adm-tabs-tab]:text-sm!
    [&_.adm-tabs-tab]:pb-3.25!
    [&_.adm-tabs-tab]:leading-normal!
    [&_.adm-tabs-tab]:font-semibold!
    [&_.adm-tabs-tab]:text-gray-4!
    [&_.adm-tabs-tab]:p-0!
    [&_.adm-tabs-tab]:pb-0!
    [&_.adm-tabs-tab.adm-tabs-tab-active]:text-primary-1!

  `;

  const activeLineStyle = {
    "--active-line-color": "#6574FC",
    "--active-line-height": ".125rem",
  };

  return (
    <div className={cn("flex flex-col gap-2.5", className)} style={style}>
      <Tabs
        activeLineMode="auto"
        className={tabsCls}
        style={activeLineStyle}
        stretch={false}
      >
        {classStudentList.map((item) => (
          <Tabs.Tab title={`${item.className}(${item.students.length})`} key={item.classID}>
            <div className="grid gap-2.5 grid-cols-3">
              {item.students.map((student: StudentItem) => (
                <StudentProfile
                  key={student.id}
                  studentInfo={student}
                  className="col-span-1"
                />
              ))}
            </div>
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
}
