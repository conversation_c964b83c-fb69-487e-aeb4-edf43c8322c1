import { Handle, Node, NodeProps, Position } from "@xyflow/react";
import { FC, memo } from "react";

type KnowledgePointNode = Node<{
  label: string;
}>;

const KnowledgePointNode: FC<NodeProps<KnowledgePointNode>> = ({
  data,
  id,
}) => {
  return (
    <>
      <div className="bg-Assist-Purple-4 outline-text-gray-1 flex w-auto items-center justify-start gap-2.5 rounded-full px-4 py-2 outline outline-1 outline-offset-[-0.50px]">
        <div className="text-text-gray-1 text-normal justify-start font-['Alibaba_PuHuiTi_3.0'] font-medium leading-loose">
          {data.label}
        </div>
      </div>
      <Handle type="target" position={Position.Left} className="opacity-0" />
      <Handle
        type="source"
        position={Position.Right}
        className="h-4.5! w-min-5! w-auto! outline-text-gray-1 bg-white! px-1.5! rounded-[10px]! flex items-center justify-center gap-2.5 outline-1 outline-offset-[-1px]"
        onClick={() => {
          console.log("clicked", id);
        }}
      >
        <span className="text-text-gray-1 justify-start text-xs font-extrabold leading-none">
          123
        </span>
      </Handle>
    </>
  );
};

export default memo(KnowledgePointNode);
