"use client";

import { J<PERSON><PERSON>_TYPE, TASK_TYPE_OPTIONS } from "@/enums";
import { useApp } from "@/hooks";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { Button } from "@/ui/button";
import { DatePicker } from "@/ui/datePicker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { InputSearch } from "@/ui/searchInput";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { removeStorageAsync } from "@/utils/storage-utils";
import { cn } from "@/utils/utils";
import { format } from "date-fns";
import { ChevronDown, ChevronLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { useHomeworkListContext } from "../context";
// import { useOptions } from "../hooks/useOptions";
import { PageHeader } from "@/components/PageHeader";
import { GradeClass } from "@/types/subjectClass";
import { useMount } from "ahooks";
import { useSearchParams } from "next/navigation";
import { useOptions } from "../hooks/useOptionsV2";

export const TriggerButton = ({
  children,
  isOpen,
  className,
  onClick,
}: React.ComponentProps<"div"> & { isOpen: boolean }) => {
  return (
    <div
      className={cn(
        "border-line-1 flex h-9 items-center justify-between gap-2 whitespace-nowrap rounded-[1.125rem] border px-4 font-[500] shadow-none transition-all duration-300",
        isOpen
          ? "border-primary-1 text-primary-1 border bg-white"
          : "text-gray-2 bg-transparent hover:bg-gray-100",
        className
      )}
      onClick={(e) => onClick?.(e)}
    >
      <span className="text-sm">{children}</span>
      <ChevronDown
        className={cn(
          "h-4 min-w-4 transition-transform duration-300",
          isOpen
            ? "text-primary-1 rotate-[-180deg]"
            : "text-gray-2 rotate-[0deg]"
        )}
      />
    </div>
  );
};

interface NavigationProps {
  source?: string | null;
}

export function Navigation({ source }: NavigationProps) {
  const { query, setQuery } = useHomeworkListContext();
  const { primarySubject } = useApp();
  const { gotoTchAssignPage } = useTchNavigation();

  const { hasJobTypes } = useApp();
  const {
    subjectList,
    classTreeList,
    classFlatList,
    useFetchOptionData,
    subjectClassTreeList,
    subjectClassFlatList,
  } = useOptions();
  const { run } = useFetchOptionData();
  const searchParams = useSearchParams();

  // 获取实际的来源信息（优先使用 props，然后从 localStorage 获取）
  const actualSource =
    source ||
    (typeof window !== "undefined"
      ? localStorage.getItem("homework_source")
      : null);

  useEffect(() => {
    // console.log("classTreeList", classTreeList);
  }, [classTreeList]);

  useMount(() => {
    run();
  });

  // useMount(() => {
  //   const source = getStorageSync<string>("homework_source");
  //   if (source !== "courseId") {
  //     const q = initializeQuery(true);
  //     setQuery(q, false);
  //   }
  // });

  const isClassTeacher = hasJobTypes([
    JOB_TYPE.JOB_TYPE_CLASS_TEACHER,
    JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER,
  ]);

  // 管理下拉框的打开状态
  const [typeOpen, setTypeOpen] = useState(false);
  const [subjectOpen, setSubjectOpen] = useState(false);
  const [classOpen, setClassOpen] = useState(false);

  // 当前选中的班级名称
  const getSelectedClassName = () => {
    const selectedClassId = query.value.groupId;
    if (!selectedClassId) return "班级";
    const selectedClass = classFlatList.find(
      (cls) => cls.classID === selectedClassId
    );

    return (
      (selectedClass && selectedClass.gradeName
        ? `${selectedClass.gradeName}${selectedClass.className}`
        : selectedClass?.className) ||
      "" ||
      "班级"
    );
  };

  // 处理班级选择
  const handleClassSelect = (classId: number) => {
    setQuery({ groupId: classId, page: 1 });
    setClassOpen(false);
  };

  // 处理日期范围变化
  const handleDateChange = (
    dateArray: [Date | undefined, Date | undefined]
  ) => {
    if (!dateArray[0] || !dateArray[1]) return;
    setQuery({
      ...query.value,
      startDate: format(dateArray[0], "yyyy-MM-dd"),
      endDate: format(dateArray[1], "yyyy-MM-dd"),
      page: 1,
    });
  };

  // 初始化学科选择，优先使用持久化数据，其次使用主学科
  useEffect(() => {
    // 只有在没有持久化学科数据且当前学科为0时才设置默认学科
    if (!query.value.subject || query.value.subject === 0) {
      const defaultSubject =
        primarySubject?.subjectId || subjectList?.[0]?.subjectKey;
      if (defaultSubject) {
        setQuery({
          subject:
            (searchParams?.get("subjectId")
              ? Number(searchParams?.get("subjectId"))
              : 0) || defaultSubject,
        });
      }
    }
  }, [
    primarySubject,
    setQuery,
    subjectList,
    query.value.subject,
    searchParams,
  ]);
  // function handleSubject(value: string) {
  //   const groupId =
  //     (
  //       subjectClassTreeList.find(
  //         (subject) => subject.subjectKey === Number(value)
  //       ) as any
  //     )?.classList.find((cls: any) => cls.classID === query.value.groupId)
  //       ?.classID || 0;
  //   setQuery({
  //     taskType: Number(value) as TASK_TYPE,
  //     groupId: groupId || 0,
  //     page: 1,
  //   });
  // }
  // console.log(
  //   "classFlatList",
  //   subjectClassFlatList,
  //   subjectClassTreeList,
  //   subjectClassFlatList.filter(
  //     (subject) => subject.subjectId === query.value.subject
  //   )
  // );
  return (
    <PageHeader className="sticky top-0 z-10 flex items-center px-6">
      <div className="flex w-full items-center gap-x-4">
        {actualSource === "assign" && (
          <button
            className="flex-shrink-0 rounded-full bg-white p-2 shadow-sm transition-colors hover:bg-gray-100"
            onClick={() => {
              // 异步清除来源信息，避免下次直接访问作业列表时显示返回按钮
              removeStorageAsync("homework_source").catch(console.error);
              gotoTchAssignPage();
            }}
          >
            <ChevronLeft className="h-5 w-5 text-[#64698a]" />
          </button>
        )}

        <h1 className="text-gray-1 whitespace-nowrap text-xl font-medium leading-[150%] tracking-[1px]">
          作业报告
        </h1>

        <div className="flex flex-1 items-center gap-2 lg:justify-between">
          <InputSearch
            className="max-w-[20rem]"
            type="text"
            placeholder="搜索"
            value={query.value.keyword}
            onSearch={(value) => {
              // DONE: 埋点11 => `homework_list_reserach_click` 作业列表中触发搜索
              umeng.trackEvent(
                UmengCategory.HOMEWORK,
                UmengHomeworkAction.SEARCH_CLICK,
                {}
              );
              setQuery({ keyword: value, page: 1 });
            }}
          />

          <div className="flex flex-shrink-0 gap-x-2.5 pr-2.5">
            {/* 学科筛选 */}
            <Select
              value={String(query.value.subject)}
              onValueChange={(value) => {
                setQuery({ subject: Number(value), page: 1 });
              }}
              onOpenChange={setSubjectOpen}
              open={subjectOpen}
            >
              <SelectTrigger
                className="cursor-pointer border-none p-0 shadow-none"
                classNames={{
                  icon: `!hidden`,
                }}
              >
                <TriggerButton
                  isOpen={subjectOpen}
                  onClick={() => {
                    // DONE: 埋点8 => `homework_list_subject_click` 作业列表中点切换学科
                    umeng.trackEvent(
                      UmengCategory.HOMEWORK,
                      UmengHomeworkAction.SUBJECT_CLICK,
                      {}
                    );
                  }}
                >
                  <SelectValue placeholder={"选择学科"} />
                </TriggerButton>
              </SelectTrigger>
              <SelectContent>
                {/* {subjectList.map((subject) => (
                    <SelectItem
                      key={subject.subjectKey}
                      value={String(subject.subjectKey)}
                    >
                      {subject.subjectName}
                    </SelectItem>
                  ))} */}
                {(subjectClassTreeList || []).map((subject) => (
                  <SelectItem
                    key={subject.subjectKey}
                    value={String(subject.subjectKey)}
                  >
                    {subject.subjectName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={String(query.value.taskType)}
              onValueChange={(value) => {
                setQuery({ taskType: Number(value), page: 1 });
              }}
              onOpenChange={setTypeOpen}
              open={typeOpen}
            >
              <SelectTrigger
                className="cursor-pointer border-none p-0 shadow-none"
                classNames={{
                  icon: `!hidden`,
                }}
              >
                <TriggerButton isOpen={typeOpen}>
                  <SelectValue placeholder="选择类型">
                    {query.value.taskType === 0
                      ? "类型"
                      : TASK_TYPE_OPTIONS.find(
                          (type) => type.value === query.value.taskType
                        )?.label}
                  </SelectValue>
                </TriggerButton>
              </SelectTrigger>
              <SelectContent>
                <SelectItem key={0} value={"0"}>
                  全部
                </SelectItem>
                {TASK_TYPE_OPTIONS.map((type) => (
                  <SelectItem key={type.value} value={String(type.value)}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div>
              {/* 班级选择 - Dropdown Menu */}
              <DropdownMenu open={classOpen} onOpenChange={setClassOpen}>
                <DropdownMenuTrigger asChild>
                  {/* !不能使用 TriggerButton,会无法显示 content */}
                  <Button
                    className={cn(
                      "border-line-1 flex h-9 cursor-pointer items-center justify-between gap-2 whitespace-nowrap rounded-[1.125rem] border px-4 font-[500] shadow-none transition-all duration-300",
                      classOpen
                        ? "border-primary-1 text-primary-1 border bg-white"
                        : "text-gray-2 bg-transparent hover:bg-gray-100"
                    )}
                  >
                    <span className="text-sm">{getSelectedClassName()}</span>
                    <ChevronDown
                      className={cn(
                        "h-4 min-w-4 transition-transform duration-300",
                        classOpen
                          ? "text-primary-1 rotate-[-180deg]"
                          : "text-gray-2 rotate-[0deg]"
                      )}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="max-h-80 w-[6.75rem] overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]">
                  <DropdownMenuItem
                    key={0}
                    onClick={() => handleClassSelect(0)}
                    className={`flex cursor-pointer items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963] ${query.value.groupId === 0 ? "bg-primary-6 text-primary-1" : "text-gray-2 font-normal"}`}
                  >
                    全部班级
                  </DropdownMenuItem>
                  {isClassTeacher
                    ? //  班主任: 显示扁平列表
                      subjectClassFlatList
                        .filter(
                          (subject) => subject.subjectId === query.value.subject
                        )
                        ?.map((cls) => (
                          <DropdownMenuItem
                            key={cls.classID}
                            onClick={() => handleClassSelect(cls.classID)}
                            className={`hover:!bg-primary-6 hover:!text-gray-2 flex cursor-pointer items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963] ${query.value.groupId === cls.classID ? "bg-primary-6 text-primary-1" : "text-gray-2 font-normal"}`}
                          >
                            {cls.gradeName ? cls.gradeName : ""}
                            {cls.className}
                          </DropdownMenuItem>
                        ))
                    : // 非班主任: 显示树形结构按年级分组
                      subjectClassTreeList
                        .find(
                          (subject) =>
                            subject.subjectKey === query.value.subject
                        )
                        ?.gradeClasses?.map(
                          (grade: GradeClass, index: number) => (
                            <div key={grade.gradeID}>
                              <DropdownMenuGroup>
                                <DropdownMenuSub>
                                  <DropdownMenuSubTrigger
                                    className={`data-[state=open]:bg-primary-6 data-[state=open]:!text-gray-2 hover:bg-primary-6 hover:!text-gray-2 focus:bg-primary-6 focus:!text-gray-2 flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963]`}
                                  >
                                    {grade.gradeName}
                                  </DropdownMenuSubTrigger>
                                  <DropdownMenuPortal>
                                    <DropdownMenuSubContent
                                      className="rounded-xl border-none"
                                      // alignOffset={-(45 * (index + 1))}
                                      style={{
                                        position: "absolute",
                                        top: `${-47 * (index + 1)}px`,
                                      }}
                                    >
                                      {grade.class.map((cls) => (
                                        <DropdownMenuItem
                                          key={cls.classID}
                                          onClick={() =>
                                            handleClassSelect(cls.classID)
                                          }
                                          className={`hover:!bg-primary-6 hover:!text-gray-2 flex cursor-pointer items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963] ${query.value.groupId === cls.classID ? "bg-primary-6 text-accent-foreground" : ""}`}
                                        >
                                          {cls.className}
                                        </DropdownMenuItem>
                                      ))}
                                    </DropdownMenuSubContent>
                                  </DropdownMenuPortal>
                                </DropdownMenuSub>
                              </DropdownMenuGroup>
                            </div>
                          )
                        )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <DatePicker
              icon={(isOpen) => (
                <ChevronDown
                  className={cn(
                    "h-4 w-4 transition-transform duration-300",
                    isOpen.value
                      ? "text-primary-1 rotate-[-180deg]"
                      : "text-gray-2 rotate-[0deg]"
                  )}
                />
              )}
              value={[
                query.value.startDate
                  ? new Date(query.value.startDate)
                  : undefined,
                query.value.endDate ? new Date(query.value.endDate) : undefined,
              ]}
              mode="range"
              onClick={() => {
                // DONE: 埋点10 => `homework_list_time_click` 作业列表中点切换时间
                umeng.trackEvent(
                  UmengCategory.HOMEWORK,
                  UmengHomeworkAction.TIME_CLICK,
                  {}
                );
              }}
              onConfirm={handleDateChange}
              format="yyyy.MM.dd"
              className="border-line-1 text-gray-2 data-[slot=popover-trigger]:data-[state=open]:border-primary-1 data-[slot=popover-trigger]:data-[state=open]:text-primary-1 rounded-full border bg-transparent font-[500] shadow-none transition-colors hover:bg-[#f5f7fa] data-[slot=popover-trigger]:data-[state=open]:bg-white"
            />
          </div>
        </div>
      </div>
    </PageHeader>
  );
}
