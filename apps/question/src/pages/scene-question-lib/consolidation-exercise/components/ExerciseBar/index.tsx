import { Flex, Tag } from 'antd';
import { FC } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';

export interface ExerciseBarProps {
  node?: API.Common.BaseTreeNodeAntdType;
}
const ExerciseBar: FC<ExerciseBarProps> = (props) => {
  const { node } = props;
  return (
    <Flex align="center" justify="space-between" style={{ height: 38 }}>
      <Tag color="#f50">未上架</Tag>
      <Tag color="#87d068">已上架</Tag>
      <ExerciseBaseInfo node={node} />
      <Tag color="#757171">审核中</Tag>
      <Tag color="#f50">审核不通过</Tag>
    </Flex>
  );
};
export default ExerciseBar;
