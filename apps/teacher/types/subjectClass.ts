/**
 * 学科班级列表数据
 */
export interface SubjectClassListData {
  /**
   * 年级班级列表
   */
  gradeClasses: GradeClass[];
  /**
   * 学科列表
   */
  subjects: Subject[];
  subjectToGradeClasses: Array<{
    gradeClasses: GradeClass[];
    subjectKey: number;
    subjectName: string;
  }>;
  [property: string]: any;
}

/**
 * 年级信息
 */
export interface GradeClass {
  /**
   * 班级列表
   */
  class: Class[];
  /**
   * 年级ID
   */
  gradeID: number;
  /**
   * 年级名称
   */
  gradeName: string;
}

/**
 * 班级信息
 */
export interface Class {
  /**
   * 班级ID
   */
  classID: number;
  /**
   * 班级名称
   */
  className: string;
}

/**
 * 学科信息
 */
export interface Subject {
  /**
   * 学科ID
   */
  subjectKey: number;
  /**
   * 学科名称
   */
  subjectName: string;
}
