import { AssignCategory } from "@/configs/assign";
import { cn } from "@/utils/utils";
import Image from "next/image";

interface CategoryCardProps extends AssignCategory {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignCategoryCard({
  title,
  subtitle,
  icon: Icon,
  className,
  defaultClassName,
  hoverClassName,
  activeClassName,
  enable,
}: CategoryCardProps) {
  return (
    <div
      className={cn(
        "bg-linear-to-l pl-4.5 relative flex cursor-pointer items-center justify-between gap-2 overflow-hidden rounded-xl py-5 pr-4 lg:px-6 xl:px-8 2xl:px-10",
        defaultClassName,
        hoverClassName,
        activeClassName,
        className
      )}
      tabIndex={0}
    >
      <div>
        <div className="text-lg font-semibold leading-normal opacity-70">
          {title}
        </div>

        <div className="mt-0.5 text-xs font-medium leading-normal opacity-50">
          {subtitle}
        </div>
      </div>

      <div className="top-5.25 shrink-0 overflow-hidden lg:right-6 xl:right-8 2xl:right-10">
        <Image src={Icon} alt="voice" className="size-13.5" />
      </div>

      {!enable && (
        <div className="absolute inset-0 cursor-not-allowed bg-white/60"></div>
      )}
    </div>
  );
}
