/* eslint-disable */
import { RiFormula } from '@remixicon/react';
import { Button, Flex, Input, Popover, Tabs, TabsProps, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useEditorConfig } from '../../../config-ctx';
import emitter from '../../../utils/emitter';
import { Feature } from '../../../utils/enum';
import presets from './latex-presets';
import './style.scss';
import { FormulaRegex, formulaRegexs } from './util';
const { TextArea } = Input;
const { Text } = Typography;

interface FormulaContentProps {
  editor: any;
  onClose: () => void;
  pos: number | null;
  end: number | null;
  mfPreviewVal: string;
  setMfPreviewVal: (value: string) => void;
}

const FormulaContent = ({
  editor,
  onClose,
  pos,
  end,
  mfPreviewVal,
  setMfPreviewVal,
}: FormulaContentProps) => {
  const [isBlock, setIsBlock] = useState(false);
  const [formulaRegex, setFormulaRegex] = useState<FormulaRegex | null>(null);
  const [formulaContent, setFormulaContent] = useState('');

  const onChange = (key: string) => {
    // 标签页切换处理
  };

  const onClickItem = (value: any) => {
    setFormulaContent(formulaContent + value.latex);
  };

  const newPreviewVal = useMemo(() => {
    if (!formulaContent) {
      return '';
    }

    let start = '';
    let end = '';

    // 使用检测到的格式，或者根据块级/行内类型选择默认格式
    if (formulaRegex) {
      start = formulaRegex.start;
      end = formulaRegex.end;
    } else {
      start = isBlock ? '$$' : '\\(';
      end = isBlock ? '$$' : '\\)';
    }

    return start + formulaContent + end;
  }, [formulaRegex, formulaContent, isBlock]);

  const insertFormula = () => {
    try {
      // 验证公式内容
      if (!formulaContent || formulaContent.trim() === '') {
        onClose();
        return;
      }

      // 验证编辑器状态
      if (!editor || !editor.isEditable || !editor.view) {
        onClose();
        return;
      }

      // 确保生成有效的预览值
      if (!newPreviewVal) {
        onClose();
        return;
      }

      // 检查编辑器文档状态
      if (!editor.state || !editor.state.doc) {
        onClose();
        return;
      }

      // 执行插入或更新
      if (
        pos !== null &&
        pos !== undefined &&
        end !== null &&
        end !== undefined &&
        pos >= 0 &&
        end > pos
      ) {
        // 更新现有公式
        const success = editor.commands.updateFormula(pos, end, newPreviewVal);
        if (!success) {
          editor.commands.deleteFormula(pos, end);
          editor.commands.insertFormula(newPreviewVal);
        }
      } else {
        // 插入新公式
        // 确保编辑器有焦点
        editor.commands.focus();

        // 绕过命令系统，直接操作编辑器
        try {
          const { state, view } = editor;
          const { selection } = state;

          // 构建插入内容
          let content = newPreviewVal;
          const text = state.doc.textBetween(0, state.doc.content.size);
          const prevChar = selection.from > 0 ? text[selection.from - 1] : '';
          const nextChar = selection.from < text.length ? text[selection.from] : '';

          // 前置空格：只有当前一个字符是字母数字（不包括中文）时才添加
          if (prevChar && /[a-zA-Z0-9]/.test(prevChar)) {
            content = ' ' + content;
          }

          // 后置空格：只有当后一个字符是字母数字（不包括中文）时才添加
          if (nextChar && /[a-zA-Z0-9]/.test(nextChar)) {
            content = content + ' ';
          }

          // 使用底层事务操作
          const tr = state.tr.insertText(content, selection.from, selection.to);
          view.dispatch(tr);
        } catch (error) {
          // 如果直接插入失败，回退到命令系统
          const success = editor.commands.insertFormula(newPreviewVal);
        }
      }
    } catch (error) {
      // 公式操作失败，静默处理
    } finally {
      onClose();
    }
  };

  const items: TabsProps['items'] = presets.map((item) => ({
    key: item.key,
    label: item.label,
    children: (
      <div className="custom-tabpanel" key={item.key}>
        {item.content.map((it) => (
          <div className="formula-item" key={it.latex}>
            <div
              className="formula-item-mask"
              onClick={() => onClickItem(it)}
              onMouseDown={(e) => e.preventDefault()}
            ></div>
            <math-field contentEditable={false}>{it.latex}</math-field>
          </div>
        ))}
      </div>
    ),
  }));

  // 解析现有公式内容
  useEffect(() => {
    if (mfPreviewVal && mfPreviewVal.trim()) {
      // 查找匹配的正则规则
      const regex = formulaRegexs.find((item: FormulaRegex) => {
        const match = mfPreviewVal.match(item.regex);
        return match !== null;
      });

      if (regex) {
        setFormulaRegex(regex);
        const matches = mfPreviewVal.match(regex.regex);

        if (matches && matches[1] !== undefined) {
          const content = matches[1];
          setFormulaContent(content);
          setIsBlock(regex.tag === 'block');
        } else {
          // 正则匹配失败，可能是格式问题，使用原始内容
          setFormulaContent(mfPreviewVal);
          setIsBlock(false);
        }
      } else {
        // 没有找到匹配的规则，可能是纯文本或新格式
        setFormulaContent(mfPreviewVal);
        setIsBlock(false);
      }
    } else {
      // 重置状态
      setFormulaRegex(null);
      setFormulaContent('');
      setIsBlock(false);
    }
  }, [mfPreviewVal]);

  const onMathFieldInput = (evt: any) => {
    const value = (evt.target as HTMLInputElement)?.value || '';
    setFormulaContent(value);
  };

  return (
    <Flex vertical gap={10} className="formula-editor-container no-blur">
      <Tabs
        defaultActiveKey="1"
        items={items}
        onChange={onChange}
        onMouseDown={(e) => e.preventDefault()}
      />
      <Flex>
        <Text>预览：</Text>
        <div className="flex-1 mf overflow-hidden">
          <math-field onInput={onMathFieldInput}>{formulaContent}</math-field>
        </div>
      </Flex>
      <Flex className=" no-blur">
        <Text>源码：</Text>
        <div className="flex-1">
          <TextArea
            value={formulaContent}
            onChange={(evt) => setFormulaContent(evt.target.value)}
          />
        </div>
      </Flex>
      <Flex justify="flex-end" align="center">
        {/* <Checkbox checked={isBlock} onChange={(e) => setIsBlock(e.target.checked)}>
          块级显示
        </Checkbox> */}
        <Button
          type="primary"
          onClick={insertFormula}
          onMouseDown={(e) => e.preventDefault()}
          disabled={!formulaContent || formulaContent.trim() === ''}
        >
          {pos !== null &&
          pos !== undefined &&
          end !== null &&
          end !== undefined &&
          pos >= 0 &&
          end > pos
            ? '更新'
            : '插入'}
        </Button>
      </Flex>
    </Flex>
  );
};

const Formula = () => {
  const config = useEditorConfig();
  const editor = config.editor!;
  if (!config.features.includes(Feature.formula)) return null;

  const [open, setOpen] = useState(false);
  const [pos, setPos] = useState<number | null>(null);
  const [end, setEnd] = useState<number | null>(null);
  const [mfPreviewVal, setMfPreviewVal] = useState('');

  // 添加防抖状态，防止重复打开弹窗
  const [isProcessing, setIsProcessing] = useState(false);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      editor.commands.focus();
      // 重置处理状态
      setIsProcessing(false);
    }
  };

  const handleButtonClick = () => {
    // 确保编辑器处于编辑模式
    if (!editor.isEditable) {
      editor.setEditable(true);
      editor.commands.focus();
    }

    // 切换弹窗状态
    setOpen(!open);
  };

  const handleFormulaClick = useCallback(
    ({ content, pos: clickedPos, end: clickedEnd }: any) => {
      // 防抖：如果正在处理中，忽略新的点击
      if (isProcessing) {
        return;
      }

      // 设置处理状态
      setIsProcessing(true);

      // 选中公式的函数
      const selectFormula = () => {
        try {
          // 选中当前公式
          editor.commands.setTextSelection({ from: clickedPos, to: clickedEnd });
        } catch (error) {
          // 选中失败，静默处理
        }
      };

      // 启动编辑模式的函数
      const startEditMode = () => {
        if (!editor.isEditable) {
          editor.setEditable(true);
          editor.commands.focus();
          // 设置光标位置
          const docSize = editor.state.doc.content.size;
          editor.commands.setTextSelection(docSize);
        }
      };

      // 处理弹窗状态的函数
      const handlePopover = () => {
        // 先选中公式
        selectFormula();

        if (open) {
          // 检查是否点击的是同一个公式
          if (clickedPos === pos && clickedEnd === end && content === mfPreviewVal) {
            // 点击同一个公式，保持弹窗打开状态，不做任何操作
            setIsProcessing(false);
            return;
          } else {
            // 点击不同的公式，切换到新的公式内容
            setPos(clickedPos);
            setEnd(clickedEnd);
            setMfPreviewVal(content);
            setIsProcessing(false);
            return;
          }
        }

        // 弹窗未打开，打开公式弹窗
        setOpen(true);
        setPos(clickedPos);
        setEnd(clickedEnd);
        setMfPreviewVal(content);

        // 使用 requestAnimationFrame 优化性能，避免阻塞主线程
        requestAnimationFrame(() => {
          setIsProcessing(false);
        });
      };

      // 如果编辑器不可编辑，先启动编辑模式
      if (!editor.isEditable) {
        startEditMode();
        requestAnimationFrame(() => {
          handlePopover();
        });
      } else {
        // 编辑器已可编辑，直接处理弹窗
        handlePopover();
      }
    },
    [editor, open, pos, end, mfPreviewVal, isProcessing],
  );

  // 处理公式删除事件
  const handleFormulaDeleted = useCallback(() => {
    // 公式被删除时关闭弹窗
    setOpen(false);
  }, []);

  // 注册事件监听器
  useEffect(() => {
    emitter.on('handle-formula-click', handleFormulaClick);
    emitter.on('formula-deleted', handleFormulaDeleted);

    return () => {
      emitter.off('handle-formula-click', handleFormulaClick);
      emitter.off('formula-deleted', handleFormulaDeleted);
    };
  }, [handleFormulaClick, handleFormulaDeleted]);

  useEffect(() => {
    if (!open) {
      setMfPreviewVal('');
      setPos(null);
      setEnd(null);
    }
  }, [open]);

  // 添加全局点击监听，点击其他内容时关闭公式弹窗
  useEffect(() => {
    if (!open || !editor) return;

    const handleGlobalClick = (event: MouseEvent) => {
      const target = event.target as Element;

      // 检查点击的是否是公式相关元素
      const isFormulaClick =
        target.closest('.formula-widget') ||
        target.closest('.formula-editor-container') ||
        target.closest('[data-formula-start]') ||
        target.closest('.ant-popover') ||
        target.closest('math-field');

      // 如果不是公式相关的点击，关闭弹窗
      if (!isFormulaClick) {
        setOpen(false);
      }
    };

    // 添加事件监听
    document.addEventListener('click', handleGlobalClick, true);

    return () => {
      document.removeEventListener('click', handleGlobalClick, true);
    };
  }, [open, editor]);

  return (
    <Popover
      content={
        <FormulaContent
          pos={pos}
          end={end}
          mfPreviewVal={mfPreviewVal}
          setMfPreviewVal={setMfPreviewVal}
          editor={editor}
          onClose={() => handleOpenChange(false)}
        />
      }
      open={open}
      trigger={[]}
      destroyOnHidden={true}
      onOpenChange={handleOpenChange}
    >
      <Button onClick={handleButtonClick} color="default" variant="filled" autoInsertSpace>
        <RiFormula />
      </Button>
    </Popover>
  );
};
Formula.id = 'formula';

export default Formula;
