import { cn } from "@/utils/utils";
import { Signal, useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { ChevronLeft } from "lucide-react";
// Removed unused popover imports
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import QuestionPanelEmpty from "@/public/icons/homework/question_panel_empty.svg";
import { getAnswerPanel, PanelItem } from "@/services/homework";
import { Button } from "@/ui/tch-button";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { useRequest } from "ahooks";
// import { useState } from "react";
import { toast } from "@/ui/toast";
import { selectQuestion, useAnswerResults } from "../store/answers";
interface FloatingResultProps {
  resultPanlData: Signal<PanelItem[] | null>;
  isLoading: Signal<boolean>;
  // type?: string;
  openQuestionDrawer?: () => void;
}

const PanelEmpty = () => {
  return (
    <div className="flex h-full flex-col items-center justify-center gap-y-2">
      <QuestionPanelEmpty className="w-30 h-30" />
      <div className="text-gray-500">暂无题目</div>
    </div>
  );
};

const ColorLegend = () => {
  return (
    <div
      className="text-gray-2 overflow-hidden whitespace-nowrap p-1 px-3 text-xs font-normal transition-opacity duration-500 md:p-2 md:px-5"
      style={{ transitionDelay: "150ms" }}
    >
      <div className="flex items-center gap-2 md:gap-4">
        <span className="text-xs font-medium md:text-sm">正确率</span>
        <div className="ml-0 flex items-center gap-1">
          <span className="rounded-xs h-2 w-2 bg-red-400 md:h-3 md:w-3"></span>
          <span className="text-xs md:text-sm">{"<"}60%</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="rounded-xs h-2 w-2 bg-orange-400 md:h-3 md:w-3"></span>
          <span className="text-xs md:text-sm">60%～79%</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="rounded-xs h-2 w-2 bg-green-400 md:h-3 md:w-3"></span>
          <span className="text-xs md:text-sm">80%～100%</span>
        </div>
      </div>
    </div>
  );
};

const LoadingSkeleton = () => {
  return (
    <div
      className="grid h-[240px] grid-cols-4 gap-x-3 gap-y-3 px-3 transition-opacity duration-500 md:h-[300px] md:gap-x-6 md:gap-y-4 md:px-5"
      style={{ transitionDelay: "200ms" }}
    >
      {Array.from({ length: 16 }).map((_, index) => (
        <div key={index} className="flex flex-col items-center">
          <div className="mb-1 animate-pulse rounded-full bg-gray-200 px-3 py-1 md:px-5 md:py-1">
            <div className="h-4 w-4 md:h-6 md:w-6"></div>
          </div>
          <div className="h-3 w-6 animate-pulse rounded bg-gray-200 md:h-4 md:w-8"></div>
        </div>
      ))}
    </div>
  );
};

export function ResultFloatingButton({
  isLoading,
  resultPanlData,
  // type,
  openQuestionDrawer,
}: FloatingResultProps) {
  // 使用Signal管理面板显示状态
  const isOpen = useSignal(false);
  const loading = useSignal(true);
  const resultPanelData = useSignal<Array<PanelItem>>([]);
  const { viewMode } = useTaskContext();
  const { fetchParams, pagination, filter } = useAnswerResults();
  // const [panelData, setPanelData] = useState<PanelItem[]>([]);
  // useUpdateEffect(() => {
  //   setPanelData([...(resultPanlData.value || [])]);
  // }, [resultPanlData.value]);
  const { run } = useRequest(
    async () => {
      const commonParams = {
        ...fetchParams.value,
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        allQuestions: filter.value.allQuestions,
        questionType: filter.value.questionType,
        titleKey: filter.value.keyword,
        // taskId: 45,
        // assignId: 61,
      };
      return getAnswerPanel(commonParams);
    },
    {
      manual: true,
      debounceWait: 500,
      onError: (err) => {
        toast.error("获取题目面板数据失败");
        console.log("获取题目面板数据失败", err);
      },
      onSuccess: (data) => {
        resultPanelData.value = data.panel;
        loading.value = false;
      },
    }
  );
  useSignalEffect(() => {
    if (isOpen.value) {
      run();
    } else {
      setTimeout(() => {
        resultPanelData.value = [];
        loading.value = true;
      }, 300);
    }
  });
  // 点击题目面板的题目，打开对应的题目详情抽屉
  const handleClick = (item: PanelItem) => {
    // DONE: 埋点25 => `homework_list_report_question_bar_detail_click` 作业报告页题目面板中点击查看题目详情
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.REPORT_QUESTION_BAR_DETAIL_CLICK,
      {
        eventName: "作业报告页悬浮题目面板中点击查看题目详情",
      }
    );
    // DONE: 埋点29 => `homework_list_report_student_question_bar_detail` 学生的作业报告页的悬浮题目面板中查看题目详情
    // if (viewMode.value === 'student') {
    //   umeng.trackEvent(UmengCategory.HOMEWORK, UmengHomeworkAction.REPORT_STUDENT_QUESTION_BAR_DETAIL_CLICK, {
    //     eventName: "学生的作业报告页的悬浮题目面板中查看题目详情",
    //   });
    // }
    // 选中题目，触发抽屉打开
    selectQuestion(item.rootQuestionId || item.questionId);
    // 点击题目后关闭面板
    isOpen.value = false;
    openQuestionDrawer?.();
  };

  const getColorClass = (rate: number) => {
    if (rate >= 80) return { bg: "bg-green-100", text: "text-green-500" };
    if (rate >= 60) return { bg: "bg-orange-100", text: "text-orange-500" };
    return { bg: "bg-red-100", text: "text-red-500" };
  };
  // const resultPanelData = useComputed(() => {
  //   return resultPanlData.value?.map((item) => {
  //     return {
  //       ...item,
  //     };
  //   });
  // });
  if (viewMode.value === "student") {
    return null;
  }
  // console.log('panelData', panelData, resultPanlData.value, type);
  return (
    <div className="absolute bottom-20 right-0 z-50 flex h-[18rem] items-end md:h-[24rem]">
      <Button
        className={cn(
          "border-line-1 bottom-0px-0 z-20 flex h-fit w-8 flex-col items-center justify-center gap-1 rounded-[0.5rem] rounded-r-none border-r-0 bg-white py-2 transition-colors duration-200 md:w-12 md:py-3",
          isOpen.value ? "border-r-0" : ""
        )}
        onClick={() => (isOpen.value = !isOpen.value)}
        aria-label="关闭题目面板"
        style={{
          boxShadow: isOpen.value
            ? "rgba(171, 180, 209, 0.25) -20px 20px 60px 0px"
            : "none",
        }}
      >
        <div className="w-fit text-center text-xs font-medium tracking-[0.2rem] [text-orientation:upright] [writing-mode:vertical-rl] md:text-sm md:tracking-[0.3rem]">
          题目面板
        </div>
        <div
          className={cn(
            "rounded-full bg-[#F2F4F9] transition-transform duration-300",
            isOpen.value ? "rotate-180" : "rotate-0"
          )}
        >
          <ChevronLeft className="h-3 w-3 md:h-4 md:w-4" />
        </div>
      </Button>

      <div
        className={cn(
          "z-10 h-full overflow-hidden rounded-b-none rounded-l-2xl bg-white transition-all duration-300 ease-in-out",
          isOpen.value ? "border-line-1 w-[16rem] border md:w-[23.5rem]" : "w-0"
        )}
        style={{
          boxShadow: isOpen.value
            ? "0px 12px 60px 0px rgba(171, 180, 209, 0.30)"
            : "none",
          transform: isOpen.value ? "translateX(0)" : "translateX(20px)",
        }}
      >
        {!loading.value &&
        (!resultPanelData.value || resultPanelData.value.length === 0) ? (
          <PanelEmpty />
        ) : (
          <div className="flex h-full flex-col gap-y-1 py-2 md:gap-y-2 md:py-4">
            <ColorLegend />
            {loading.value ? (
              <LoadingSkeleton />
            ) : (
              <div className="flex-1 overflow-auto">
                <div
                  className="grid grid-cols-4 gap-x-3 gap-y-3 px-3 transition-opacity duration-500 md:gap-x-6 md:gap-y-4 md:px-5"
                  style={{ transitionDelay: "200ms" }}
                >
                  {resultPanelData.value.map((item, index) => {
                    const correctRatePercent = Math.floor(item.correctRate);
                    const { bg, text } = getColorClass(correctRatePercent);
                    const isSub = item.questionIndexStr?.includes("-");
                    return (
                      <div
                        onClick={() => handleClick(item)}
                        key={`${item.resourceId}_${item.resourceType}_${item.questionId}`}
                        className="flex transform flex-col items-center transition-all duration-300 hover:scale-105"
                      >
                        <div
                          className={cn(
                            "mb-1 w-full rounded-full px-3 py-1 text-center md:px-5 md:py-1",
                            bg
                          )}
                        >
                          <span
                            className={cn(
                              "text-base font-normal",
                              text,
                              isSub && "text-xs"
                            )}
                          >
                            {item.questionIndexStr || index + 1}
                          </span>
                        </div>
                        <span
                          className={cn(
                            "text-gray-2 text-xs font-normal",
                            correctRatePercent < 60 && text
                          )}
                        >
                          {correctRatePercent}%
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
