// @ts-ignore
/* eslint-disable */

declare namespace API {
  type UserDetailResponse = {
    code: number;
    data: Data;
    message: string;
    response_time: number;
    status: number;
    [property: string]: any;
  };

  export interface CurrentUser {
    createTime: string;
    menus: UserMenu[];
    /**
     * 注释信息
     */
    remark: string;
    /**
     * 用户角色列表，一个用户可能有多个角色，所以用列表
     */
    roles: UserRole[];
    schools: null;
    updateTime: string;
    /**
     * 雇佣状态
     */
    userEmploymentStatus: number;
    /**
     * 用户id
     */
    userId: number;
    /**
     * 用户名
     */
    userName: string;
    /**
     * 用户手机号
     */
    userPhoneNumber: string;
    userStatus: number;
    [property: string]: any;
  }

  type UserMenu = {
    createrId?: number;
    createTime?: string;
    deleted?: number;
    menuComponent?: string;
    menuIcon?: string;
    menuId?: number;
    menuIsCache?: number;
    menuName?: string;
    menuOrderNum?: number;
    menuParentId?: number;
    menuPath?: string;
    menuPerms?: string;
    menuPlatformId?: number;
    menuStatus?: number;
    menuType?: number;
    menuVisible?: number;
    remark?: string;
    updaterId?: number;
    updateTime?: string;
    [property: string]: any;
  };

  type UserRole = {
    createrId?: number;
    createTime?: string;
    deleted?: number;
    menuIds?: null;
    remark?: string;
    roleId?: number;
    roleKey?: string;
    roleName?: string;
    roleSort?: number;
    roleStatus?: number;
    updaterId?: number;
    updateTime?: string;
    users?: null;
    [property: string]: any;
  };

  // type CurrentUser = {
  //   name?: string;
  //   avatar?: string;
  //   userid?: string;
  //   email?: string;
  //   signature?: string;
  //   title?: string;
  //   group?: string;
  //   tags?: { key?: string; label?: string }[];
  //   notifyCount?: number;
  //   unreadCount?: number;
  //   country?: string;
  //   access?: string;
  //   geographic?: {
  //     province?: { label?: string; key?: string };
  //     city?: { label?: string; key?: string };
  //   };
  //   address?: string;
  //   phone?: string;
  // };

  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
}
