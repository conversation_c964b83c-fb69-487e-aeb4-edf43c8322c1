"use client";
import { ScrollArea } from "@/ui/scroll-area";
import TeacherInfo from "./_components/TeacherInfo";
// import SelectedForYou from "./_components/SelectedForYou";
import { PageHeader } from "@/components/PageHeader";
import { useRef } from "react";
import MyMessage from "./_components/MyMessage";

export default function Page() {
  const viewportRef = useRef<HTMLDivElement>(null);

  return (
    <ScrollArea className="h-full" viewportRef={viewportRef}>
      <div className="flex flex-col gap-2 pb-2.5 pl-6 pr-6">
        <PageHeader className="pl-0">
          <div className="text-gray-1 text-xl/normal font-medium">教师信息</div>
        </PageHeader>

        <TeacherInfo />

        <MyMessage viewportRef={viewportRef} />

        {/* <SelectedForYou /> */}
      </div>
    </ScrollArea>
  );
}
