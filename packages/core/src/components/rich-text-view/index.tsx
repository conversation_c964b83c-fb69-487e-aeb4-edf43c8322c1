// import { MathJax, MathJaxContext } from "better-react-mathjax";
import "katex/dist/katex.min.css";
import { useMemo } from "react";
import { RichTextMath } from "./rich-text-math";
import { RichTextViewProps } from "./types";

// 导入CSS文件
import "./index.css";

const RichTextView = ({
  htmlContent,
  questionId,
  ossHost = "",
  resourceApiHost = "",
  className = "",
}: RichTextViewProps) => {
  const formatMathFieldHtml = useMemo(() => {
    // TODO: 兼容老数据，待确认 将 <math-field> 内容转换为静态 HTML
    const html = htmlContent.replace(
      /<math-field[^>]*>([^<]*)<\/math-field>/g,
      (_, latex) => {
        return `\\(${latex.trim()}\\)`;
      }
    );
    return html;
  }, [htmlContent]);

  return (
    // <MathJaxContext>
    <div className={`rich-text-view ${className}`}>
      {/* <MathJax> */}
      {/* 🔥 修复：使用 RichTextMath 组件，它内部已经处理了 MathContent 的问题 */}
      <RichTextMath
        htmlContent={formatMathFieldHtml}
        questionId={questionId}
        resourceApiHost={resourceApiHost}
        ossHost={ossHost}
      />
      {/* </MathJax> */}
    </div>
    // </MathJaxContext>
  );
};

export default RichTextView;
