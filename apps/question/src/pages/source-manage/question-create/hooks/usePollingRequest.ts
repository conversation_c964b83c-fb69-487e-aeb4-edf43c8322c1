import { fetchParseResultForSwr } from '@/services/api';
import { message } from 'antd';
import { useState } from 'react';
import useSWR from 'swr';
interface UsePollingRequestProps {
  onSuccess?: (questionList: any[], isSingle: boolean) => void;
  onLoading?: (loadingText: string) => void;
  onLoadingComplete?: () => void;
  paperId: string;
  setPaperId: (paperId: string) => void;
  setBaseInfo: (baseInfo: any) => void;
}

interface UsePollingRequestReturn {
  startPolling: (paperId: string) => void;
  isValidating: boolean;
  shouldFetch: boolean;
  setShouldFetch: (value: boolean) => void;
}

/**
 * 轮询请求Hook
 * 用于处理试卷解析的轮询逻辑
 */
export const usePollingRequest = ({
  onSuccess,
  onLoading,
  onLoadingComplete,
  paperId,
  setPaperId,
  setBaseInfo,
}: UsePollingRequestProps): UsePollingRequestReturn => {
  const [shouldFetch, setShouldFetch] = useState(false);

  const { isValidating } = useSWR(
    shouldFetch ? ['fetchParse', paperId] : null,
    fetchParseResultForSwr,
    {
      revalidateOnFocus: false,
      refreshInterval: (data: any) => {
        if (isValidating) return 0;
        if (!shouldFetch) return 0;
        const status = data?.data?.paperStatus;
        if (status === 3) {
          return 1000 * 30; // 30秒轮询一次
        } else if (status === 4 || status === 8) {
          return 0; // 停止轮询
        } else {
          return 0;
        }
      },
      onSuccess: (data) => {
        const status = data?.data?.paperStatus;
        if (status === 3) {
          onLoading?.('正在识别,请等待...');
          return;
        }
        if (status === 8) {
          message.info('识别完成：成功');
          const questionList = data.data.questionList || [];
          setBaseInfo({
            phase: data.data.phase,
            subject: data.data.subject,
            generateChannel: data.data.generateChannel,
            paperName: data.data.paperName,
          });
          const isSingle = questionList.length === 1;
          onSuccess?.(questionList, isSingle);
        }
        if (status === 4) {
          message.info('识别完成：失败');
        }
        onLoadingComplete?.();
      },
      onError: (err) => {
        console.error('❌ 请求失败：', err);
        message.error('请求异常');
        onLoadingComplete?.();
      },
    },
  );

  const startPolling = (paperIdTemp: string) => {
    setPaperId(paperIdTemp);
    setShouldFetch(true);
  };

  return {
    startPolling,
    isValidating,
    shouldFetch,
    setShouldFetch,
  };
};
