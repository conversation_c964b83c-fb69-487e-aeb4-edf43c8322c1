import NormalEmpty from "@/public/images/normal-empty.svg";
import {
  getMessageList,
  getUnreadMessageCount,
  markMessageAsRead,
} from "@/services";
import { Message } from "@/types/user";
import Spin from "@/ui/spin";
import { cn } from "@/utils";
import { useInfiniteScroll, useRequest, useUnmount } from "ahooks";
import dayjs from "dayjs";

const MyMessage = ({
  viewportRef,
}: {
  viewportRef: React.RefObject<HTMLDivElement | null>;
}) => {
  const { data, noMore } = useInfiniteScroll<{
    list: Message[];
    page: number;
    page_size: number;
    total: number;
  }>(
    (d) =>
      getMessageList({
        page: (d?.page ?? 0) + 1,
        page_size: d?.page_size ?? 20,
      }),
    {
      target: viewportRef,
      isNoMore: (d) => {
        if (!d) return true;
        return d.list.length >= d.total;
      },
    }
  );

  const getUnreadMessageCountRequest = useRequest(getUnreadMessageCount);

  useUnmount(() => {
    const messageIds =
      data?.list.filter((item) => !item.isRead).map((item) => item.id) ?? [];

    if (messageIds.length) {
      markMessageAsRead({
        message_ids: messageIds,
      });
    }
  });

  return (
    <div className="message_container">
      <div className="text-gray-1 flex h-[3.8125rem] items-center text-[1.375rem] font-medium">
        我的消息
        {data && data?.total > 0 && getUnreadMessageCountRequest.data && (
          <span className="text-base font-normal text-black">
            （共{data?.total}条/{getUnreadMessageCountRequest.data.unread_count}
            条未读）
          </span>
        )}
      </div>

      <div>
        {data && data?.total > 0 && (
          <div className="pt-8.25 pb-4.25 px-5.5 border-line-1 space-y-5 rounded-xl border bg-white">
            {data?.list.map((message, index) => (
              <div
                key={message.id}
                className={cn(
                  "message_item_container border-line-2 pb-4",
                  index !== data?.list.length - 1 && "border-b"
                )}
              >
                {/* 消息来源 */}
                <div className="mb-3 flex h-6 items-center justify-between gap-5 overflow-hidden">
                  <div className="text-gray-4 ml-6.25 flex-1 truncate text-base font-normal leading-6">
                    消息回复源自：
                    {dayjs
                      .unix(message.createdAt)
                      .format("YYYY年MM月DD日 HH:mm")}
                    ，{message.title}
                  </div>

                  <div className="text-gray-4 text-base font-normal leading-6">
                    {dayjs
                      .unix(message.replyTime)
                      .format("YYYY年MM月DD日 HH:mm")}
                  </div>
                </div>

                {/* 消息内容 */}
                <div className="text-gray-1 flex text-base font-medium leading-6">
                  <span className="w-6.25 flex h-6 flex-none items-center">
                    {!message.isRead && (
                      <span className="bg-danger-2 h-2 w-2 rounded-full"></span>
                    )}
                  </span>
                  <span className="flex-1">{message.content}</span>
                </div>
              </div>
            ))}

            {!noMore && <Spin loading />}
          </div>
        )}

        {!data?.total && (
          <div className="flex flex-col items-center justify-center rounded-[1.25rem] p-6">
            <NormalEmpty className="h-30 w-30" />
            <div className="text-xxs text-gray-4 mt-2">这里还没有哦~</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyMessage;
