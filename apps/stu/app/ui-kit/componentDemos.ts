/**
 * 组件演示注册文件
 *
 * 这个文件用于集中注册所有组件的演示组件
 * 添加新组件时，只需在这里导入组件的演示文件并添加到 componentDemos 对象中
 */

import React from "react";
// import ButtonDemo from "../components/common/press-button/demo";

// 组件类型定义
export type ComponentCategory =
  | "basic"
  | "form"
  | "navigation"
  | "feedback"
  | "data"
  | "layout";

// 组件信息接口
export interface ComponentInfo {
  id: string;
  name: string;
  description: string;
  demo?: React.ComponentType;
  usage: string;
  category: ComponentCategory;
}

// 组件分类
export const componentCategories: { id: ComponentCategory; name: string }[] = [
  { id: "basic", name: "基础组件" },
  { id: "form", name: "表单组件" },
  { id: "navigation", name: "导航组件" },
  { id: "feedback", name: "反馈组件" },
  { id: "data", name: "数据展示" },
  { id: "layout", name: "布局组件" },
];

/**
 * 组件演示注册表
 *
 * 添加新组件时，只需在这里添加一个新的对象
 * 包含以下字段：
 * - id: 组件唯一标识符
 * - name: 组件名称
 * - description: 组件描述
 * - demo: 组件演示组件
 * - category: 组件分类
 * - usage: 组件使用示例代码
 */
export const componentDemos: ComponentInfo[] = [
  {
    id: "button",
    name: "按钮",
    description: "用于触发操作的按钮组件，支持多种颜色、尺寸和样式。",
    category: "basic",
    usage: `import Button from '@/app/components/common/button';

// 基本用法
<Button>默认按钮</Button>

// 设置颜色
<Button color="orange">橙色按钮</Button>
<Button color="red">红色按钮</Button>
<Button color="green">绿色按钮</Button>
<Button color="gray">灰色按钮</Button>
<Button color="white">白色按钮</Button>

// 设置次要模式
<Button secondary>次要按钮</Button>

// 设置尺寸
<Button size="sm">小按钮</Button>
<Button size="md">中按钮</Button>
<Button size="lg">大按钮</Button>

// 隐藏阴影
<Button showShadow={false}>无阴影按钮</Button>

// 禁用状态
<Button disabled>禁用按钮</Button>

// 加载状态（新功能）
<Button loading>加载中按钮</Button>
<Button loading color="red">红色加载按钮</Button>
<Button loading size="sm">小尺寸加载</Button>

// 组合使用
<Button
  color="green"
  secondary
  size="lg"
  className="w-full"
>
  自定义按钮
</Button>`,
  },
  // 在这里添加更多组件...
  // 例如：
  // {
  //   id: 'input',
  //   name: '输入框',
  //   description: '用于用户输入的文本框组件。',
  //   demo: InputDemo,
  //   category: 'form',
  //   usage: `import Input from '@/app/components/common/Input';...`,
  // },
];

// 组件注册表（以 ID 为键的对象形式，方便查询）
export const componentRegistry: Record<string, ComponentInfo> =
  componentDemos.reduce(
    (registry, component) => {
      registry[component.id] = component;
      return registry;
    },
    {} as Record<string, ComponentInfo>
  );

// 获取所有组件
export const getAllComponents = (): ComponentInfo[] => {
  return componentDemos;
};

// 按类别获取组件
export const getComponentsByCategory = (
  category: ComponentCategory
): ComponentInfo[] => {
  return componentDemos.filter((component) => component.category === category);
};

// 获取单个组件
export const getComponent = (id: string): ComponentInfo | undefined => {
  return componentRegistry[id];
};
