# 学生端练习资源聚合

本目录包含了学生端练习功能所需的所有静态资源文件，包括图片、图标和字体。

## 目录结构

```
stu-exercise/
├── images/
│   └── exercise/           # 练习反馈相关图片
│       ├── correct-ip-bg.svg          # 正确反馈背景
│       ├── correct-ip1.png            # 正确反馈角色
│       ├── incorrect-ip-bg.svg        # 错误反馈背景
│       ├── incorrect-ip1.png          # 错误反馈角色
│       ├── careless-ip-bg.svg         # 粗心反馈背景
│       ├── careless-ip1.png           # 粗心反馈角色
│       ├── continuous-correct-ip1.png # 连续正确反馈角色
│       ├── light-effect.png           # 光效图片
│       ├── rocket-base.svg            # 火箭底座
│       ├── difficulty-up-ip.png       # 难度提升火箭
│       ├── difficulty-up-text-bg.svg  # 难度提升文字背景
│       ├── difficulty-up-text-up.svg  # 向上箭头
│       ├── difficulty-down-ip.png     # 难度降低火箭
│       ├── difficulty-down-text-bg.svg # 难度降低文字背景
│       ├── difficulty-down-text-down.svg # 向下箭头
│       ├── difficulty-change-cloud1.svg # 云朵装饰1
│       ├── difficulty-change-cloud2.svg # 云朵装饰2
│       ├── difficulty-down-cloud2.svg   # 难度降低云朵
│       └── resume-exercise-ip.png       # 恢复练习角色
├── icons/
│   ├── choice/             # 选择题图标
│   │   ├── correct.svg     # 正确图标
│   │   └── error.svg       # 错误图标
│   └── fill-blank/         # 填空题图标
│       ├── camera-icon.svg      # 相机图标
│       ├── close.svg            # 关闭图标
│       ├── icon-loading.svg     # 加载图标
│       ├── right-green.svg      # 正确-绿色
│       ├── right-normal.svg     # 正确-普通
│       ├── wrong-normal.svg     # 错误-普通
│       ├── wrong-red.svg        # 错误-红色
│       ├── half-right-normal.svg # 半对-普通
│       └── half-right-red.svg    # 半对-红色
└── fonts/                  # 字体文件
    ├── ResourceHanRoundedSC-Bold.ttf    # 思源黑体圆体-粗体
    └── ResourceHanRoundedSC-Regular.ttf # 思源黑体圆体-常规
```

## 使用方式

### 在学生端应用中使用
```tsx
// 图片引用
<Image src="@repo/core/public/assets/stu-exercise/images/correct-ip-bg.svg" />

// 图标引用
import CorrectIcon from "@repo/core/public/assets/stu-exercise/icons/choice/correct.svg";
```

### 在 core 包预览组件中使用
```tsx
// 填空题图标
import RightIconGreen from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
```

## 迁移说明

所有资源文件已从学生端应用 (`apps/stu/public/`) 迁移到 core 包中，实现了资源的统一管理和跨应用复用。

### 原路径 → 新路径映射
- `/images/` → `@repo/core/public/assets/stu-exercise/images/`
- `/icons/choice/` → `@repo/core/public/assets/stu-exercise/icons/choice/`
- `/icons/fill-blank/` → `@repo/core/public/assets/stu-exercise/icons/fill-blank/`
- `/fonts/` → `@repo/core/public/assets/stu-exercise/fonts/`

## 注意事项

1. 所有引用路径已更新为新的统一路径
2. core 包的 package.json 已添加相应的导出配置
3. 学生端和教师端应用都可以通过 core 包访问这些资源
4. 资源文件保持原有的文件名和格式不变