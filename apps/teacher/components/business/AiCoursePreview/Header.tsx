import { cn } from "@/utils/utils";

interface HeadingProps {
  style?: React.CSSProperties;
  content: string;
  className?: string;
  showPrefix?: boolean;
  prefix?: string;
}

export default function Header({
  content,
  style = {},
  className = "",
  showPrefix = false,
  prefix = "",
}: HeadingProps) {
  return (
    <div
      className={cn("flex h-6 select-none items-center", className)}
      style={style}
    >
      {showPrefix && (
        <span className="h-4.5 leading-4.5 bg-orange-1 mr-1 inline-block rounded px-1 text-center text-xs font-medium text-white">
          {prefix}
        </span>
      )}
      <h1 className="text-gray-1 text-base font-medium leading-6">{content}</h1>
    </div>
  );
}
