{"compilerOptions": {"baseUrl": "./", "target": "esnext", "moduleResolution": "node", "jsx": "react-jsx", "esModuleInterop": true, "experimentalDecorators": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitAny": false, "declaration": true, "skipLibCheck": true, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"], "@@test/*": ["./src/.umi-test/*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@repo/core/*": ["../../packages/core/src/*"], "@repo/lib/*": ["../../packages/lib/src/*"]}}, "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"]}