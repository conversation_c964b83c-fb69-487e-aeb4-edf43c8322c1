import { Checkbox, Form } from 'antd';
import React, { memo, useEffect } from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';

interface QuestionMatchLineProps {
  /**
   * 表单字段名称，支持嵌套路径
   * 例如：'isMatchLine' 或 [name, 'isMatchLine']
   */
  name: string | (string | number)[];
  /**
   * 题目主题干填空点数量
   */
  count?: number;
  /**
   * 题目连线设置字段名称
   */
  isMatchLineName?: string | (string | number)[];
  /**
   * 题目作答方式
   */
  questionAnswerMode?: number;
}

/**
 * 题目连线设置组件
 * 统一管理所有题目的连线设置逻辑
 */
const QuestionMatchLine: React.FC<QuestionMatchLineProps> = ({
  name,
  count = 0,
  isMatchLineName,
  questionAnswerMode,
}) => {
  const { form } = useQuestionCreate();

  useEffect(() => {
    // 当填空点数量为0时，强制设置isMatchLine为false
    if (count === 0) {
      form.setFieldValue(isMatchLineName, false);
    }
  }, [count, isMatchLineName, form]);

  // 只有当count > 1时才显示组件
  if (count <= 1 || questionAnswerMode !== 1) {
    return null;
  }

  return (
    <Form.Item name={name} valuePropName="checked" label=" " initialValue={false}>
      <Checkbox>设为 m 选 n</Checkbox>
    </Form.Item>
  );
};

export default memo(QuestionMatchLine);
