declare namespace API {
  type BizTreeListRequestParams = {
    baseTreeId?: null;
    bizTreeId?: null;
    bizTreeNameKey?: string;
    page?: number;
    pageSize?: number;
    phaseList?: number[];
    subjectList?: number[];
    [property: string]: any;
  };

  interface BizTreeListResponse {
    code: number;
    message: string;
    data: {
      list: BizTreeItem[];
      formatList?: Common.TreeListItem[];
      page: number;
      pageSize: number;
      total: number;
    };
    [property: string]: any;
  }

  type BizTreeItem = {
    baseTreeId: number;
    bizTreeId: number;
    bizTreeName: string;
    bizTreeVersion: string;
    createTime: number;
    createrId: number;
    phase: number;
    subject: number;
    updateTime: number;
    updaterId: number;
    material: number;
    [property: string]: any;
  };

  type BizTreeShelfStatus = 0 | 1;

  interface BizTreeNode {
    baseTreeNodeIds: number[];
    bizTreeNodeChildren: BizTreeNode[];
    /**
     * 业务树节点id
     */
    bizTreeNodeId: number;
    /**
     * 业务树节点level，根节点level是1
     */
    bizTreeNodeLevel: number;
    /**
     * 业务树节点名称
     */
    bizTreeNodeName: string;
    /**
     * 业务树节点兄弟序号
     */
    bizTreeNodeSiblingOrder: number;
    /**
     * 业务树父节点id
     */
    bizTreeParentNodeId?: number;
    /**
     * 非叶子节点的统计：上架叶子节点数量/ 全部叶子节点数量 比如1/3
     */
    shelfOnLeafNodeStat: string;
    /**
     * 叶子节点的上架状态，如果showSelfStatus字段不传递，统一返回未上架。未上架值为0，已上架值为1
     */
    shelfStatus: number;
    [property: string]: any;
  }

  interface BizTreeDetailData {
    baseTreeId: string;
    bizTreeDetail: BizTreeNode;
    bizTreeId: number;
    bizTreeName: string;
    bizTreeVersion: string;
    phase: number;
    subject: number;
    treeData?: BaseTreeNodeAntdType[];
    [property: string]: any;
  }

  interface BizTreeDetailRequestParams {
    bizTreeId: number;
    sceneCategory?: number;
    showShelfStatus?: number;
  }

  interface BizTreeDetailResponse {
    code: number;
    message: string;
    data: BizTreeDetailData;
  }
}
