/**
 * Model 层测试工具
 * 专门用于 Model 层的数据相关测试
 */

// 导出 Model 层专用的测试工具
export {
  test,
  expect,
  getTestStats,
  resetTestStats,
  generateTestSummary,
  type TestResult
} from './simple-test-runner';

export {
  renderSWRHook,
  createMockResponse,
  createMockError,
  createNetworkError,
  createTimeoutError,
  createPaginatedMockResponse,
  waitForSWRState,
  swrStateCheckers,
  type FetcherMocks,
  type SWRHookRenderOptions
} from './swr-test-helpers';

// 🔥 Zod 测试生成器已删除，不再需要 schema 测试