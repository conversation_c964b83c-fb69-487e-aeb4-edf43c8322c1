"use client";
import {
  CAPTCHA_PREFIX,
  CAPTCHA_SCENE_ID_APP,
  CAPTCHA_SCENE_ID_WEB,
} from "@/configs";
import { PLATFORM } from "@/enums";
import { sendSmsWithCaptcha } from "@/services";
import { umeng, UmengCategory, UmengLoginAction } from "@/utils";
import { useCaptcha } from "@repo/core/hooks/use-captcha";
import { getAndroidBridgeAvailable } from "@repo/lib/utils/device";
import to from "await-to-js";
import React from "react";
import { match } from "ts-pattern";

const Captcha = ({
  canCaptcha,
  phoneNumber,
  children,
  onBizResultCallback,
}: {
  canCaptcha: boolean;
  phoneNumber: string;
  children: React.ReactNode;
  onBizResultCallback: (bizResult?: unknown) => void;
}) => {
  const captchaVerifyCallback = async (captchaVerifyParam: string) => {
    const [error] = await to(
      sendSmsWithCaptcha({
        captchaVerifyParam,
        phone_number: phoneNumber,
        platform: getAndroidBridgeAvailable() ? PLATFORM.APP : PLATFORM.WEB,
        sceneId: getAndroidBridgeAvailable()
          ? CAPTCHA_SCENE_ID_APP
          : CAPTCHA_SCENE_ID_WEB,
      })
    );

    umeng.trackEvent(
      UmengCategory.LOGIN,
      UmengLoginAction.LOGIN_PAGE_PIC_VERIFY_DONE,
      {
        captcha_status: error ? "失败" : "成功",
      }
    );

    if (error) {
      return {
        captchaResult: false,
        bizResult: false,
      };
    }

    return {
      captchaResult: true,
    };
  };

  useCaptcha({
    captchaVerifyCallback,
    onBizResultCallback,
    SceneId: getAndroidBridgeAvailable()
      ? CAPTCHA_SCENE_ID_APP
      : CAPTCHA_SCENE_ID_WEB,
    prefix: CAPTCHA_PREFIX,
  });

  return match(canCaptcha)
    .with(true, () => (
      <>
        <span id="captcha-button">{children}</span>
        <div id="captcha-element"></div>
      </>
    ))
    .otherwise(() => {
      return (
        <>
          <span id="captcha-button"></span>
          <div id="captcha-element"></div>
          {children}
        </>
      );
    });
};

export default Captcha;
