// import { NumberSelectorRef } from '@/pages/source-manage/components/NumberSelector';
import { LocalDraftService } from '@/utils/storage';
import { Form, FormInstance } from 'antd';
import React, { createContext, useContext, useRef, useState } from 'react';
import { QuestionSegmentedRef } from '../components/QuestionSegmented';
import { usePollingRequest } from '../hooks/usePollingRequest';
import { useQuestionFormActions } from '../hooks/useQuestionFormActions';

interface QuestionData {
  phase: number; // 学段
  subject: number; // 学科
  generateChannel?: number; // 生成渠道
  paperName?: string; // 文件名
}

interface QuestionCreateContextType {
  // Form 实例和操作
  form: FormInstance;

  // 基础数据
  baseInfo: QuestionData;
  setBaseInfo: React.Dispatch<React.SetStateAction<QuestionData>>;
  paperId: string;
  setPaperId: React.Dispatch<React.SetStateAction<string>>;
  questionList: API.Review.ImportQuestionAudit['list'];
  setQuestionList: React.Dispatch<React.SetStateAction<API.Review.ImportQuestionAudit['list']>>;
  // questionSelectorsNumber: string[] | number[];
  isSingleQuestion: boolean;
  setIsSingleQuestion: React.Dispatch<React.SetStateAction<boolean>>;
  activeIndex: number;
  setActiveIndex: React.Dispatch<React.SetStateAction<number>>;
  activeQuestion: API.Review.ImportQuestionAudit['list'][number];
  setActiveQuestion: React.Dispatch<
    React.SetStateAction<API.Review.ImportQuestionAudit['list'][number]>
  >;

  // 加载状态
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  loadingText: string;
  setLoadingText: React.Dispatch<React.SetStateAction<string>>;

  // 文件上传
  attachFileList: API.Review.AttachFileList[];
  setAttachFileList: React.Dispatch<React.SetStateAction<API.Review.AttachFileList[]>>;

  // 所属基础树默认值
  defaultBaseTreeNodeIds: number[];
  setDefaultBaseTreeNodeIds: React.Dispatch<React.SetStateAction<number[]>>;

  // 表单操作 hooks
  formActions: ReturnType<typeof useQuestionFormActions>;

  // 轮询请求
  pollingActions: ReturnType<typeof usePollingRequest>;

  // refs
  // numberSelectorRef: React.RefObject<NumberSelectorRef | null>;
  questionSelectorRef: React.RefObject<QuestionSegmentedRef | null>;
  saveQueue: React.RefObject<Promise<void>>;
  localDraft: LocalDraftService;
}

const QuestionCreateContext = createContext<QuestionCreateContextType | undefined>(undefined);

export const useQuestionCreate = () => {
  const context = useContext(QuestionCreateContext);
  if (!context) {
    throw new Error('useQuestionCreate must be used within a QuestionCreateProvider');
  }
  return context;
};

interface QuestionCreateProviderProps {
  children: React.ReactNode;
  initialBaseInfo: QuestionData;
}

export const QuestionCreateProvider: React.FC<QuestionCreateProviderProps> = ({
  children,
  initialBaseInfo,
}) => {
  const [form] = Form.useForm();
  const baseInfoParams = {
    phase: null,
    subject: null,
    generateChannel: 0,
    paperName: '',
  };
  // 基础数据状态
  const [baseInfo, setBaseInfo] = useState<QuestionData>({ ...baseInfoParams, ...initialBaseInfo });
  const [paperId, setPaperId] = useState<string>('');
  const [questionList, setQuestionList] = useState<API.Review.ImportQuestionAudit['list']>([]);
  const [isSingleQuestion, setIsSingleQuestion] = useState<boolean>(true);
  const [activeIndex, setActiveIndex] = useState(0);
  const [activeQuestion, setActiveQuestion] = useState<
    API.Review.ImportQuestionAudit['list'][number]
  >(questionList[activeIndex]);
  const [defaultBaseTreeNodeIds, setDefaultBaseTreeNodeIds] = useState<number[]>([]);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState('');

  // 图片收集状态
  const [attachFileList, setAttachFileList] = useState<API.Review.AttachFileList[]>([]);

  // // 题目标签
  // const questionSelectorsNumber = useMemo(() => {
  //   return questionList?.map((item) => item.questionId) || [];
  // }, [questionList]);

  // refs
  // const numberSelectorRef = useRef<NumberSelectorRef>(null);
  const questionSelectorRef = useRef<QuestionSegmentedRef>(null);
  const saveQueue = useRef<Promise<void>>(Promise.resolve());

  // 题目表单操作相关的hooks
  const formActions = useQuestionFormActions();

  // 轮询请求
  const pollingActions = usePollingRequest({
    onSuccess: (questionList, isSingle) => {
      setQuestionList(questionList);
      setActiveQuestion(questionList[0]);
      setIsSingleQuestion(isSingle);
    },
    onLoading: (loadingText) => {
      setLoadingText(loadingText);
    },
    onLoadingComplete: () => {
      setIsLoading(false);
      setLoadingText('');
    },
    paperId,
    setPaperId,
    setBaseInfo,
  });

  // 本地草稿
  const localDraft = new LocalDraftService();

  const contextValue: QuestionCreateContextType = {
    form,
    baseInfo,
    setBaseInfo,
    paperId,
    setPaperId,
    questionList,
    setQuestionList,
    // questionSelectorsNumber,
    isSingleQuestion,
    setIsSingleQuestion,
    activeIndex,
    setActiveIndex,
    activeQuestion,
    setActiveQuestion,
    isLoading,
    setIsLoading,
    loadingText,
    setLoadingText,
    attachFileList,
    setAttachFileList,
    defaultBaseTreeNodeIds,
    setDefaultBaseTreeNodeIds,
    formActions,
    pollingActions,
    // numberSelectorRef,
    questionSelectorRef,
    saveQueue,
    localDraft,
  };

  return (
    <QuestionCreateContext.Provider value={contextValue}>{children}</QuestionCreateContext.Provider>
  );
};
