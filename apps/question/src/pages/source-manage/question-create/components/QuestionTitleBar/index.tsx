import { PlusOutlined } from '@ant-design/icons';
import { Button, Space } from 'antd';
import cn from 'classnames';
import React from 'react';
import styles from './index.module.less';

export type QuestionLevel = 'main' | 'level1' | 'level2';

interface QuestionTitleBarProps {
  level: QuestionLevel;
  title: string;
  onDelete?: () => void;
  onAddSub?: () => void;
  required?: boolean;
}

/**
 * 题目标题栏
 * 用于显示题目标题和操作按钮
 */
const QuestionTitleBar: React.FC<QuestionTitleBarProps> = ({
  level,
  title,
  onDelete,
  onAddSub,
  required = false,
}) => {
  const getLevelClass = () => {
    switch (level) {
      case 'main':
        return styles.level0;
      case 'level1':
        return styles.level1;
      case 'level2':
        return styles.level2;
      default:
        return styles.level0;
    }
  };

  return (
    <div className={styles.questionTitleBar}>
      <span className={cn(getLevelClass(), required && styles.required)}>{title}</span>
      <Space>
        {onAddSub && (
          <Button
            type="link"
            icon={<PlusOutlined />}
            onClick={onAddSub}
            className={styles.addButton}
          >
            添加小题
          </Button>
        )}
        {onDelete && (
          <Button type="link" danger onClick={onDelete}>
            删除
          </Button>
        )}
      </Space>
    </div>
  );
};

export default QuestionTitleBar;
