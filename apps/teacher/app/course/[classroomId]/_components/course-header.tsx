"use client";
import NavBackIcon from "@/public/icons/ic_nav_back.svg";
import { useRouter } from "next/navigation";

export function CourseHeader({
  name,
  isInClass,
}: {
  name: string;
  isInClass: boolean;
}) {
  const router = useRouter();

  return (
    <div className="flex items-center py-3.5">
      <NavBackIcon
        onClick={() => router.back()}
        className="size-7.5 cursor-pointer active:opacity-80"
      />

      <span className="text-gray-1 ml-4 mr-2 text-xl/normal font-semibold">
        {name}
      </span>

      {isInClass && (
        <span className="h-4.5 bg-green-1 inline-flex items-center rounded px-1 text-xs/normal font-medium text-white">
          <span className="mr-0.5 h-1 w-1 rounded-full bg-white"></span>
          上课中
        </span>
      )}
    </div>
  );
}
