import QuestionList from '@/components/QuestionList';
import { FC } from 'react';

export interface ExerciseExceptionQuestionListProps {
  questions: API.QuestionItemType[];
}
/**
 * 审核不通过题目列表
 */
const ExerciseExceptionQuestionList: FC<ExerciseExceptionQuestionListProps> = ({
  questions = [],
}) => {
  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    console.log('onQuestionEventHandler: ', question, type);
  };

  return (
    <div>
      <QuestionList
        questions={questions}
        type="exercise-audit-fail"
        onQuestionEventHandler={onQuestionEventHandler}
      />
    </div>
  );
};
export default ExerciseExceptionQuestionList;
