export enum UmengLoginPageName {
  LOGIN_PAGE = "登录页 login_page",
}

export enum UmengLoginAction {
  // 登录页完成输入手机号：用户进入手机验证码登录页
  LOGIN_PAGE_ADD_DONE = "login_page_add_done 登录页完成输入手机号",

  // 登录页完成图形验证：用户完成图形验证
  LOGIN_PAGE_PIC_VERIFY_DONE = "login_page_pic_verify_done 登录页完成图形验证弹窗",

  // 登录页完成输入手机验证码：用户输入手机验证码
  LOGIN_PAGE_SMS_VERIFY_DONE = "login_page_sms_verify_done 登录页完成输入手机验证码",

  // 登录页完成选择学校：用户选择学校
  LOGIN_PAGE_SCHOOL_LIST_DONE = "login_page_school_list_done 登录页完成选择学校",

  // 登录页点击登录：用户点击登录
  LOGIN_PAGE_SUBMIT = "login_page_submit 登录页点登录",
}