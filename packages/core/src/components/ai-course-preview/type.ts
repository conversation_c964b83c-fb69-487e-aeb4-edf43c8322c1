export enum GuideMode {
  free = "free",
  follow = "follow",
}
// TODO: 待替换正确的类型
export type PreviewQuestionData = any;
export type InteractiveWidgetData = any;
export interface GuideWidgetData {
  // 数字人视频
  avatar: Video;
  // 字幕
  subtitles: Subtitle[];
  // 标题
  title: string;
  // 内容
  content: Line[];
};

// 标记
interface RoughNotation {
  // 类型: 圆圈, 括号, 方框, 下划线, 删除线
  // 默认: 下划线
  // 现在支持: underline, circle, highlight
  type:
  | "underline"
  | "box"
  | "circle"
  | "highlight"
  | "strike-through"
  | "crossed-off"
  | "bracket";
  // 颜色
  color?: string;
  // 宽度
  strokeWidth?: number;
  // 开始帧
  inFrame?: number;
  // 绘制时间(帧)
  duration?: number;
  // 结束帧
  outFrame?: number;
}

// 行文本
export interface LineTexture {
  // 类型: 默认, 加粗
  tag: "normal" | "bold";
  // 内容
  content: string;
  // 开始帧
  inFrame?: number;
  // 结束帧
  outFrame?: number;
  // 标记
  notation?: RoughNotation;
}

// 画布类型
export interface DrawElement {
  id?: string;
  drawMode: boolean;
  startTimestamp: number;
  endTimestamp: number;
  inFrame: number;
  outFrame: number;
  paths: {
    x: number;
    y: number;
  }[];
  strokeColor: string;
  strokeWidth: number;
  endFrame?: number;
}

// 行
export interface Line {
  // 唯一标识
  id?: string;
  // 行内容计算出来的hash
  lineId?: string;
  // 类型: 默认, 有序列表, 无序列表, 标题, 图片
  tag: "default" | "ol" | "ul" | "h1" | "h2" | "h3" | "h4" | "block";
  // 层级: 1, 2, 3, 4
  level: number;
  // 排序, 只对ol 有效
  order?: number;
  // 内容
  content: LineTexture[] | Line[];
  // 开始帧
  inFrame: number;
  // 结束帧
  outFrame: number;
  // 图片
  pic?: Picture;
  // 宽度
  width?: string;
  // 画布
  draw?: DrawElement[]; // 仅限h3级

  layout?: "vertical" | "horizontal"; // 布局方向
  imageRatio?: "16:9" | "1:1" | "9:16"; // 图片比例
  styleType?: "style1" | "style2" | "style3" | "style4" | "style5"; // 用户选择的样式类型
  styleText?: number; // 用户选择的样式类型
}
// 图片
export type Picture = {
  url: string;
  width: number;
  height: number;
  fileType?: "image" | "js" | "html" | "unknown"; // 新增文件类型字段
};

// 视频
export type Video = {
  url: string;
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
};

// 字幕
type Subtitle = {
  id: number;
  text: string;
  inFrame: number;
  outFrame: number;
};

export type WidgetStatus = any;

export type FetchWidgetParams = {
  courseId: number;
  widgetIndex: number;
};

// Widget类型：交互组件/视频课/练习/纯视频
export type WidgetType = "interactive" | "guide" | "exercise" | "video";

export interface GuideWidgetAvatar {
  durationInFrames: number;
  fps: number;
  url: string;
  height: number;
  width: number;
}

export interface GuideWidgetContentNotation {
  color: string;
  duration: number;
  inFrame: number;
  outFrame: number;
  strokeWidth: number;
  type: string;
}

export interface GuideWidgetContentItem {
  content: string;
  inFrame: number;
  outFrame: number;
  notation: GuideWidgetContentNotation | null;
  tag: string;
}

export interface GuideWidgetContent {
  id: string;
  content: GuideWidgetContentItem[];
  icon: string;
  inFrame: number;
  outFrame: number;
  level: number;
  pic: {
    url: string;
    width: number;
    height: number;
  } | null;
  tag: string;
}

export interface GuideWidgetSubtitles {
  id: number;
  inFrame: number;
  outFrame: number;
  text: string;
}

// 通用属性
export type CommonWidgetDetail = {
  index: number;
  name: string;
  duration: number;
};

// 交互组件Widget
export type InteractiveWidgetDetail = {
  type: "interactive";
  data: InteractiveWidgetData;
} & CommonWidgetDetail;

// 视频课Widget
export type GuideWidgetDetail = {
  type: "guide";
  data: GuideWidgetData;
} & CommonWidgetDetail;

// 练习Widget
export type ExerciseWidgetDetail = {
  type: "exercise";
  data: PreviewQuestionData[];
} & CommonWidgetDetail;

// 纯视频Widget
export type VideoWidgetDetail = {
  type: "video";
  // Q: 这个好像下掉了，产课中心暂时没有
  data: any;
} & CommonWidgetDetail;

export type CourseWidgetDetail =
  | InteractiveWidgetDetail
  | GuideWidgetDetail
  | ExerciseWidgetDetail
  | VideoWidgetDetail;

export type FetchWidgetInfo = (
  fetchWidgetParams: FetchWidgetParams
) => Promise<CourseWidgetDetail | undefined | null>;

// 进度数据
export type ProgressData = CourseWidgetSummary[];

// 组件摘要序列, 用于进度显示
export type CourseWidgetSummary = {
  type: WidgetType;

  index: number;
  name: string;

  hidden: number;
  data?: any;

  // 这个不确定有没有，组件支持，后端没返
  status?: WidgetStatus;
};
