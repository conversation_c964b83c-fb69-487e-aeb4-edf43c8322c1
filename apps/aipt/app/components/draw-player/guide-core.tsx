import { Layer, LayerItem } from "@repo/core/components/layer";
import { Subtitle } from "@repo/core/guide/components/subtitle";
import { CommonSketchProps } from "@repo/core/types/data/widget-guide";
import { FC } from "react";
import { AbsoluteFill, OffthreadVideo } from "remotion";

import { useGuideContext } from "@repo/core/guide/context/guide-context";

import { GuideSectionH2 } from "@repo/core/guide/components/guide-section-h2";

export const GuideCore: FC<CommonSketchProps & { hideSubtitles?: boolean }> = ({
  startFrame = 0,
  outFrame = 0,
  duration = 0,
  onDrawChange,
  eraserMode,
  highlighter,
  mode = 'draw',
  hideSubtitles = false,
}) => {
  const { data } = useGuideContext();

  const { avatar, subtitles } = data;

  return (
    <AbsoluteFill>
      <div className="guide-view relative flex h-full w-full bg-[#FFFDFA]">
        <Layer className="font-resource-han-rounded">
          <LayerItem index={2} className="pb-27 w-full py-8">
            <div className="relative flex h-full w-full flex-row overflow-y-scroll scroll-smooth">
              <div className="flex h-auto w-full flex-1 flex-col pt-1">
                <GuideSectionH2
                  sketchProps={{
                    onDrawChange,
                    eraserMode,
                    highlighter,
                    mode,
                    startFrame: startFrame,
                    outFrame: outFrame,
                    duration: duration,
                  }}
                />
              </div>
            </div>
          </LayerItem>
          <LayerItem index={1} className="right-0 w-1/5">
            <div className="relative flex h-full w-full flex-col items-center justify-end text-blue-800">
              {avatar.url && (
                <OffthreadVideo
                  src={avatar.url}
                  pauseWhenBuffering
                  crossOrigin="anonymous"
                />
              )}
            </div>
          </LayerItem>
        </Layer>
        {!hideSubtitles && subtitles && (
          <div className="right-30 fixed bottom-8 left-1/2 z-20 -translate-x-[50%]">
            <Subtitle subtitles={subtitles} />
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};
