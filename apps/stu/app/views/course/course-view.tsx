"use client";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { ComponentProps, FC } from "react";
import { CourseProgressBarView } from "./course-progress-bar-view";
import { CourseViewProvider } from "./course-view-context";
import { CourseWidgetsLoaderView } from "./course-widgets-loader-view";

const VolcengineLicense = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-license"),
  {
    ssr: false,
  }
);

export const CourseView: FC<
  {
    knowledgeId: number;
    subjectId: number;
    knowledgeName: string;
    lessonName: string;
    widgetIndex?: string;
    commentId?: string;
    commentRootId?: string;
    referenceId?: string;
    studySessionId: number;
    studyType: number;
  } & ComponentProps<"div">
> = ({
  className,
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  widgetIndex,
  commentId,
  commentRootId,
  referenceId,
  studySessionId,
  studyType,
}) => {
  return (
    <CourseViewProvider
      knowledgeId={knowledgeId}
      subjectId={subjectId}
      knowledgeName={knowledgeName}
      lessonName={lessonName}
      redirectWidgetIndex={widgetIndex}
      redirectCommentId={commentId}
      redirectCommentRootId={commentRootId}
      redirectReferenceId={referenceId}
      studySessionId={Number(studySessionId)}
      studyType={Number(studyType)}
    >
      <div className={cn("relative h-screen w-full", className)}>
        <VolcengineLicense />
        <CourseProgressBarView />
        <CourseWidgetsLoaderView />
      </div>
    </CourseViewProvider>
  );
};
