import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { ReactNode } from 'react';

// 上传结果接口
export interface UploadResult {
  businessType: string;
  fileName: string;
  fileSize: number;
  objectKey: string;
  remark: string;
  uploadTime: string;
  url: string;
  userId: string;
}

// 轮询状态查询响应接口
export interface QueryStatusResponse {
  status: 200;
  message: string;
  data: {
    taskID: number;
    taskType: number;
    taskStatus: 1 | 2 | 3 | 4; // 1: processing, 2: processing, 3: success, 4: failed
    uniqueKey: string;
    params: string;
    resulText: string;
    createrID: number;
  };
}

// 上传模态框属性接口
export interface UploadModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
  title?: string;
  // 区域1：下载模板区域
  customContent?: ReactNode;
  templateFileName?: string; // 下载的文件名
  /* 上传文件类型 */
  acceptFileTypes?: string[];
  /* 导入数据方法 */
  importData: (
    fileId: string,
    params?: any,
  ) => Promise<{ success: boolean; taskId?: string; error?: string }>;
  /* 上传参数 */
  uploadParams?: any;
  /* 下载模板 */
  downloadTemplate: () => void;
}

// 导入结果接口
export interface ImportResult {
  status: 'idle' | 'processing' | 'success' | 'failed';
  total?: number;
  success?: number;
  failed?: number;
  taskId?: string;
  downloadUrl?: string;
  failedItems?: any[];
}

// 上传状态钩子返回值接口
export interface UseUploadReturn {
  fileList: UploadFile[];
  uploading: boolean;
  uploadProgress: number;
  uploadError: boolean;
  fileId: string | null;
  fileName: string | null;
  handleUpload: (file: RcFile) => Promise<void>;
  handleRemoveFile: () => void;
  handleReupload: () => void;
  beforeUpload: (file: RcFile) => any;
  resetUploadState: () => void;
}

// 导入状态钩子返回值接口
export interface UseImportReturn {
  importing: boolean;
  importResult: ImportResult;
  handleImport: () => Promise<void>;
  handleDownloadFailReasons: () => Promise<void>;
  resetImportState: () => void;
}

// 上传状态钩子参数接口
export interface UseUploadParams {
  uploadParams?: any;
  acceptFileTypes?: string[];
}

// 导入状态钩子参数接口
export interface UseImportParams {
  fileId: string | null;
  importData: (
    fileId: string,
    params?: any,
  ) => Promise<{ success: boolean; taskId?: string; error?: string }>;
  onSuccess?: (data: any) => void;
  needPolling?: boolean; // 是否需要轮询
}
