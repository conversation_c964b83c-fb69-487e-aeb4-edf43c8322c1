import { CommonResponseType } from ".";

export type ListTempSelectionResponse = CommonResponseType<TempSelectionItem[]>;

/**
 * ListTempSelectionResponse
 */
export interface ListTempSelectionData {
  list?: TempSelectionItem[];
}

/**
 * TempSelectionItem
 */
export interface TempSelectionItem {
  /**
   * 记录创建时间
   */
  createTime: number;
  /**
   * 自增主键ID
   */
  id: number;
  /**
   * 资源ID
   */
  resourceId: string;
  /**
   * 资源类型
   */
  resourceType: number;
  /**
   * 学校ID
   */
  schoolId?: number;
  /**
   * 教师ID
   */
  teacherId?: number;
}


export interface TempSelectionCreateResponseData {
  id?: number;
}

export type TempSelectionCreateResponse = CommonResponseType<TempSelectionCreateResponseData>;

export interface TempSelectionDeleteParams {
  questionIds: string[];
  subject: number;
}

export type TempSelectionDeleteResponse = CommonResponseType<unknown>;

