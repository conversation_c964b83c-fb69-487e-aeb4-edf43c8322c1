import { defineConfig } from 'tsup';

export default defineConfig({
  entry: [
    'src/model/index.ts',
    'src/api/index.ts',
    'src/view/index.ts',
    'src/viewmodel/index.ts',
    'src/context/index.ts'
  ],
  format: ['cjs', 'esm'],
  dts: false,
  clean: true,
  target: 'es2022',
  esbuildOptions(options) {
    options.jsx = 'automatic';
    options.jsxImportSource = 'react';
  },
  external: [
    'react',
    'swr',
    'zod',
    '@testing-library/react',
    '@repo/lib/utils/fetcher',
    'next/server',
    'vitest'
  ],
  banner: {
    js: '// @repo/test-utils - 分层架构测试工具库'
  }
});