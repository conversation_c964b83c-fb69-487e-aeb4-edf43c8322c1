<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2916_9616)" filter="url(#filter0_d_2916_9616)">
<g clip-path="url(#paint0_angular_2916_9616_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.003 0.012 0.012 0.003 12 12)"><foreignObject x="-1195.62" y="-1195.62" width="2391.24" height="2391.24"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(255, 255, 255, 1) 0deg,rgba(255, 255, 255, 0) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M1.5 13.5H4.5C5.328 13.5 6 12.828 6 12C6 11.172 5.328 10.5 4.5 10.5H1.5C0.672001 10.5 0 11.172 0 12C0 12.828 0.672001 13.5 1.5 13.5ZM5.6355 7.758C6.222 8.343 7.1715 8.343 7.758 7.758C8.343 7.1715 8.343 6.222 7.758 5.6355L5.6355 3.5145C5.0505 2.9295 4.101 2.9295 3.5145 3.5145C2.9295 4.101 2.9295 5.0505 3.5145 5.6355L5.6355 7.758ZM12 24C12.828 24 13.5 23.3295 13.5 22.5V19.5C13.5 18.672 12.828 18 12 18C11.172 18 10.5 18.672 10.5 19.5V22.5C10.5 23.3295 11.172 24 12 24ZM12 6C12.828 6 13.5 5.3295 13.5 4.5V1.5C13.5 0.6705 12.828 0 12 0C11.172 0 10.5 0.6705 10.5 1.5V4.5C10.5 5.3295 11.172 6 12 6ZM18.3645 20.4855C18.9495 21.0705 19.899 21.0705 20.4855 20.4855C21.0705 19.9005 21.0705 18.951 20.4855 18.3645L18.3645 16.2435C17.778 15.657 16.8285 15.657 16.242 16.2435C15.657 16.8285 15.657 17.778 16.242 18.3645L18.3645 20.4855ZM18.3645 7.758L20.4855 5.6355C21.0705 5.0505 21.0705 4.101 20.4855 3.5145C19.899 2.9295 18.9495 2.9295 18.3645 3.5145L16.242 5.6355C15.657 6.222 15.657 7.1715 16.242 7.758C16.8285 8.343 17.778 8.343 18.3645 7.758ZM18 12C18 12.828 18.672 13.5 19.5 13.5H22.5C23.328 13.5 24 12.828 24 12C24 11.172 23.328 10.5 22.5 10.5H19.5C18.672 10.5 18 11.172 18 12ZM5.6355 16.2435L3.5145 18.3645C2.9295 18.951 2.9295 19.9005 3.5145 20.4855C4.101 21.0705 5.0505 21.0705 5.6355 20.4855L7.758 18.3645C8.343 17.778 8.343 16.8285 7.758 16.2435C7.1715 15.657 6.222 15.657 5.6355 16.2435Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-6.0,&#34;m01&#34;:24.0,&#34;m02&#34;:3.0000004768371582,&#34;m10&#34;:24.0,&#34;m11&#34;:6.0,&#34;m12&#34;:-2.9999997615814209},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<filter id="filter0_d_2916_9616" x="0" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2916_9616"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2916_9616" result="shape"/>
</filter>
<clipPath id="paint0_angular_2916_9616_clip_path"><path d="M1.5 13.5H4.5C5.328 13.5 6 12.828 6 12C6 11.172 5.328 10.5 4.5 10.5H1.5C0.672001 10.5 0 11.172 0 12C0 12.828 0.672001 13.5 1.5 13.5ZM5.6355 7.758C6.222 8.343 7.1715 8.343 7.758 7.758C8.343 7.1715 8.343 6.222 7.758 5.6355L5.6355 3.5145C5.0505 2.9295 4.101 2.9295 3.5145 3.5145C2.9295 4.101 2.9295 5.0505 3.5145 5.6355L5.6355 7.758ZM12 24C12.828 24 13.5 23.3295 13.5 22.5V19.5C13.5 18.672 12.828 18 12 18C11.172 18 10.5 18.672 10.5 19.5V22.5C10.5 23.3295 11.172 24 12 24ZM12 6C12.828 6 13.5 5.3295 13.5 4.5V1.5C13.5 0.6705 12.828 0 12 0C11.172 0 10.5 0.6705 10.5 1.5V4.5C10.5 5.3295 11.172 6 12 6ZM18.3645 20.4855C18.9495 21.0705 19.899 21.0705 20.4855 20.4855C21.0705 19.9005 21.0705 18.951 20.4855 18.3645L18.3645 16.2435C17.778 15.657 16.8285 15.657 16.242 16.2435C15.657 16.8285 15.657 17.778 16.242 18.3645L18.3645 20.4855ZM18.3645 7.758L20.4855 5.6355C21.0705 5.0505 21.0705 4.101 20.4855 3.5145C19.899 2.9295 18.9495 2.9295 18.3645 3.5145L16.242 5.6355C15.657 6.222 15.657 7.1715 16.242 7.758C16.8285 8.343 17.778 8.343 18.3645 7.758ZM18 12C18 12.828 18.672 13.5 19.5 13.5H22.5C23.328 13.5 24 12.828 24 12C24 11.172 23.328 10.5 22.5 10.5H19.5C18.672 10.5 18 11.172 18 12ZM5.6355 16.2435L3.5145 18.3645C2.9295 18.951 2.9295 19.9005 3.5145 20.4855C4.101 21.0705 5.0505 21.0705 5.6355 20.4855L7.758 18.3645C8.343 17.778 8.343 16.8285 7.758 16.2435C7.1715 15.657 6.222 15.657 5.6355 16.2435Z"/></clipPath><clipPath id="clip0_2916_9616">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 24 0)"/>
</clipPath>
</defs>
</svg>
