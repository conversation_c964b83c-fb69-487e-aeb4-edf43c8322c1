"use client";

import { FC } from "react";
import { DeviceFrameset } from 'react-device-frameset';
import 'react-device-frameset/styles/marvel-devices.min.css';
import { Main } from "./_cmp/main";
import { Sider } from "./_cmp/sider";
import { PreviewCtxProvider } from "./_helper";


interface LessonPreviewProps {
  simulation: boolean;
  widgets: Array<any>;
  widget: any;
  loading: boolean;
  active: number;
  setActive: (active: number) => void;
}

const LessonPreviewPropsDeafult: LessonPreviewProps = {
  simulation: false,
  widgets: [],
  widget: {},
  active: 0,
  loading: false,
  setActive: () => { },
}
const LessonPreview: FC<LessonPreviewProps> = (props = LessonPreviewPropsDeafult) => {

  const ctxProps = {
    ...props
  };
  return (
    <PreviewCtxProvider {...ctxProps}>
      <div className="lesson-preview flex h-full w-full">
        <Sider />
        {
          props.simulation ? <DeviceFrameset device="iPad Mini" color="black" landscape width={600} height={1000}>
            <Main />
          </DeviceFrameset> : <Main />
        }
      </div>
    </PreviewCtxProvider>
  );
};

export default LessonPreview;
