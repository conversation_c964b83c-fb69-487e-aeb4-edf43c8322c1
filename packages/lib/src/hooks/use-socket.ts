"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Socket, io } from "socket.io-client";

interface UseSocketProps {
  url: string;
  options?: {
    reconnectionAttempts?: number;
    reconnectionDelay?: number;
    autoConnect?: boolean;
  };
}

interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  error: Error | null;
  connect: () => void;
  disconnect: () => void;
  emit: (event: string, data?: object) => void;
  on: (event: string, callback: (data: object) => void) => void;
  off: (event: string) => void;
}

export const useSocket = ({
  url,
  options = {},
}: UseSocketProps): UseSocketReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const socketRef = useRef<Socket | null>(null);

  const defaultOptions = useMemo(() => {
    return {
      reconnectionAttempts: 5,
      reconnectionDelay: 3000,
      autoConnect: true,
      transports: ["websocket"],
      ...options,
    };
  }, [options]);

  // 初始化 socket 连接
  const initSocket = useCallback(() => {
    try {
      console.log("initSocket", url, defaultOptions);
      const socket = io(url, { ...defaultOptions, path: "/v1/demo/AI/chat" });
      socketRef.current = socket;

      // 连接成功
      socket.on("connect", () => {
        console.log("Socket connected");
        setIsConnected(true);
        setError(null);
      });

      // 连接错误
      socket.on("connect_error", (err) => {
        console.error("Socket 连接 error:", err);
        setError(err);
        setIsConnected(false);
      });

      // 断开连接
      socket.on("disconnect", (reason) => {
        console.log("Socket disconnected:", reason);
        setIsConnected(false);
      });

      // 重新连接尝试
      socket.on("reconnect_attempt", (attemptNumber) => {
        console.log("Socket reconnection attempt:", attemptNumber);
      });

      // 重新连接失败
      socket.on("reconnect_failed", () => {
        console.error("Socket reconnection failed");
        setError(new Error("重连失败，请检查网络连接"));
      });
    } catch (err) {
      console.error("Socket initialization error:", err);
      setError(
        err instanceof Error ? err : new Error("Socket initialization failed")
      );
    }
  }, [url, defaultOptions]);

  // 连接
  const connect = useCallback(() => {
    if (socketRef.current && !socketRef.current.connected) {
      socketRef.current.connect();
    } else if (!socketRef.current) {
      initSocket();
    }
  }, [initSocket]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  }, []);

  // 发送事件
  const emit = useCallback(
    (event: string, data?: object) => {
      if (socketRef.current && isConnected) {
        socketRef.current.emit(event, data);
      } else {
        console.warn("Socket is not connected, cannot emit event:", event);
      }
    },
    [isConnected]
  );

  // 监听事件
  const on = useCallback((event: string, callback: (data: object) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  }, []);

  // 取消监听事件
  const off = useCallback((event: string) => {
    if (socketRef.current) {
      socketRef.current.off(event);
    }
  }, []);

  // 组件挂载时初始化 socket
  useEffect(() => {
    if (defaultOptions.autoConnect) {
      initSocket();
    }

    // 组件卸载时清理
    return () => {
      if (socketRef.current) {
        // // socketRef.current.disconnect();
        // socketRef.current.removeAllListeners();
        // socketRef.current = null;
      }
    };
  }, [initSocket, defaultOptions.autoConnect]);

  return {
    socket: socketRef.current,
    isConnected,
    error,
    connect,
    disconnect,
    emit,
    on,
    off,
  };
};
