"use client";
import CircleArrowIcon from "@/public/assign/circle_arrow.svg";
import { UserSubjectItem } from "@/types/assign";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/ui/select";
import { cn } from "@/utils/utils";
import { useComputed, type Signal } from "@preact-signals/safe-react";

interface AssignSubjectSelectProps {
  onChange?: (subject: UserSubjectItem) => void;
  value: Signal<UserSubjectItem>;
  taskTypes: Signal<UserSubjectItem[]>;
}

export function AssignSubjectSelect({
  onChange = () => {},
  value,
  taskTypes,
}: AssignSubjectSelectProps) {
  const hasMultiSubjects = useComputed(() => {
    return taskTypes.value.length > 1;
  });

  const handleOnChange = (key: string) => {
    const subject = taskTypes.value.find(
      (subject) => subject.subjectKey === Number(key)
    );
    if (subject) {
      onChange(subject);
    }
  };

  // 学科选择器
  const SubjectTrigger = (
    <div className="flex w-fit items-center">
      <h1
        className={cn(
          "text-gray-1 inline-block select-none pr-2 text-xl font-medium leading-normal tracking-wider",
          hasMultiSubjects.value && "cursor-pointer"
        )}
      >
        {value.value.subjectName}
      </h1>
      {hasMultiSubjects.value && (
        <CircleArrowIcon className="size-5 cursor-pointer" />
      )}
    </div>
  );

  return hasMultiSubjects.value ? (
    <Select
      value={String(value.value.subjectKey)}
      onValueChange={handleOnChange}
    >
      <SelectTrigger asChild>{SubjectTrigger}</SelectTrigger>
      <SelectContent>
        {taskTypes.value.map((subject) => (
          <SelectItem
            key={subject.subjectKey}
            value={String(subject.subjectKey)}
          >
            {subject.subjectName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  ) : (
    SubjectTrigger
  );
}
