import { useApp } from "@/hooks/useApp";
import { cn } from "@/utils/utils";

import SidebarTriggerLeft from "@/public/icons/ic_sidebar_trigger_left.svg";
import SidebarTriggerRight from "@/public/icons/ic_sidebar_trigger_right.svg";

export default function Switch() {
  const { open, setOpen } = useApp();

  return (
    <>
      <div
        onClick={() => setOpen(false)}
        className={cn(
          `border-line-3 h-15.5 absolute right-0 top-1/2 flex w-4 -translate-y-1/2 cursor-pointer items-center justify-center rounded-l-xl border bg-[#D6DBEB] active:opacity-80`,
          !open.value && "cursor-default opacity-0 active:opacity-0"
        )}
      >
        <SidebarTriggerLeft className="w-0.75 h-2.5" />
      </div>

      <div
        onClick={() => setOpen(true)}
        className={cn(
          "border-line-3 z-999 h-15.5 absolute right-[-1rem] top-1/2 flex w-4 -translate-y-1/2 cursor-pointer items-center justify-center rounded-r-xl border bg-[#D6DBEB] active:opacity-80",
          open.value && "cursor-default opacity-0 active:opacity-0"
        )}
      >
        <SidebarTriggerRight className="w-0.75 h-2.5" />
      </div>
    </>
  );
}
