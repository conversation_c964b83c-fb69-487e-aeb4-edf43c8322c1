"use client";

import { Sheet, Sheet<PERSON>ontent, SheetTitle } from "@/ui/sheet";
import React, { useCallback, useMemo } from "react";
// import CustomFooter from "./CustomFooter";
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { PageHeader } from "@/components/PageHeader";
import { useUmeng } from "@/hooks/useUmeng";
import { ScrollArea } from "@/ui/scroll-area";
import { Button } from "@/ui/tch-button";
import { UmengCategory } from "@/utils";
import { cn } from "@/utils/utils";
import { QuestionItem } from "@repo/core/views/tch-question-view";
import { selectQuestion, useAnswerResults } from "../store/answers";
import QuestionStats from "./QuestionStats";
import ReportButton from "./ReportButton";

interface QuestionDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  questionId?: string;
  floatButton?: React.ReactNode;
}

export const QuestionDetailDrawer = React.memo(function QuestionDetailDrawer({
  open,
  onOpenChange,
  questionId,
  floatButton,
}: QuestionDetailDrawerProps) {
  const { viewMode, viewModeNew, studentListMap } = useTaskContext();
  // useUmeng(
  //   UmengCategory.HOMEWORK,
  //   viewMode.value === "student"
  //     ? "homework_list_report_student_question_detail"
  //     : "homework_list_report_question_detail"
  // );
  const umengEvent = useMemo(
    () =>
      viewMode.value === "student" || viewModeNew.value === "student"
        ? "homework_list_report_student_question_detail"
        : "homework_list_report_question_detail",
    [viewMode.value, viewModeNew.value]
  );
  useUmeng(UmengCategory.HOMEWORK, umengEvent);

  const { filteredAnswers } = useAnswerResults();
  // const [currentQuestion, setCurrentQuestion] = useState<QaContentType | null>(
  //   null
  // );
  // const [index, setIndex] = useState(0);
  const { currentQuestion, index } = useMemo(() => {
    if (!questionId || !open || !filteredAnswers.value) {
      return { currentQuestion: null, index: 0 };
    }
    const answers = filteredAnswers.value;
    const questionIndex = answers.findIndex((q) => q.questionId === questionId);
    return {
      currentQuestion: questionIndex !== -1 ? answers[questionIndex] : null,
      index: questionIndex !== -1 ? questionIndex : 0,
    };
  }, [questionId, open, filteredAnswers.value]);

  // 判断是否有上一题和下一题
  // const hasPrevious = index > 0;
  // const hasNext =
  //   filteredAnswers.value && index < filteredAnswers.value.length - 1;
  const { hasPrevious, hasNext } = useMemo(
    () => ({
      hasPrevious: index > 0,
      hasNext:
        filteredAnswers.value && index < filteredAnswers.value.length - 1,
    }),
    [index, filteredAnswers.value]
  );

  // 当 questionId 变化时，查找对应的题目
  // useEffect(() => {
  //   if (questionId && open) {
  //     const answers = filteredAnswers.value;
  //     const questionIndex = answers.findIndex(
  //       (q) => q.questionId === questionId
  //     );

  //     if (questionIndex !== -1) {
  //       setCurrentQuestion(answers[questionIndex]);
  //       setIndex(questionIndex);
  //     } else {
  //       setCurrentQuestion(null);
  //     }
  //   } else {
  //     setCurrentQuestion(null);
  //   }
  // }, [questionId, open, filteredAnswers.value]);

  // // 切换到上一题
  const goToPrevious = useCallback(() => {
    if (hasPrevious && filteredAnswers.value) {
      const prevIndex = index - 1;
      const prevQuestion = filteredAnswers.value[prevIndex];
      selectQuestion(prevQuestion.questionId);
    }
  }, [hasPrevious, index, filteredAnswers.value]);

  // // 切换到下一题
  const goToNext = useCallback(() => {
    if (hasNext && filteredAnswers.value) {
      const nextIndex = index + 1;
      const nextQuestion = filteredAnswers.value[nextIndex];
      selectQuestion(nextQuestion.questionId);
    }
  }, [hasNext, index, filteredAnswers.value]);

  const getStudentInfo = useCallback(
    (studentId: number) => {
      const student = studentListMap.value[studentId];
      return student
        ? { name: student.studentName, avatar: student.avatar }
        : null;
    },
    [studentListMap.value]
  );
  const totalStudents = useMemo(
    () => Object.keys(studentListMap.value).length,
    [studentListMap.value]
  );

  if (!currentQuestion) {
    return null;
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTitle></SheetTitle>
      <SheetContent
        side="right"
        className={cn(
          "h-full w-full gap-0 overflow-y-auto bg-[#F4F7FE] pb-6 sm:max-w-[100vw]",
          "flex flex-col"
        )}
        closeable={false}
      >
        {floatButton}
        {/* 抽屉头部 */}
        <PageHeader
          className="sticky top-0 z-10 flex items-center bg-[#F4F7FE] px-6 py-5"
          onBack={() => onOpenChange(false)}
          needBack={true}
        >
          {/* <div className="sticky top-0 z-10 flex items-center justify-between bg-[#F4F7FE] px-6 py-5"> */}
          <div className="ml-2 flex flex-1 justify-between">
            <div className="flex items-center gap-[0.625rem]">
              {/* <div
              className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-white shadow-sm"
              onClick={() => onOpenChange(false)}
            >
              <ChevronLeft className="h-5 w-5 text-[#64698a]" />
            </div> */}
              <span className="text-lg font-medium text-[#444963]">
                题目详情
              </span>
            </div>

            {/* 上一题下一题导航按钮 */}
            <div className="flex items-center gap-2">
              <Button
                type="default"
                size="sm"
                onClick={goToPrevious}
                disabled={!hasPrevious}
                className={cn(
                  "rounded-full text-sm font-medium",
                  !hasPrevious ? "cursor-not-allowed text-gray-400" : ""
                )}
              >
                上一题
              </Button>
              <Button
                type="default"
                size="sm"
                onClick={goToNext}
                disabled={!hasNext}
                className={cn(
                  "rounded-full text-sm font-medium",
                  !hasNext ? "cursor-not-allowed text-gray-400" : ""
                )}
              >
                下一题
              </Button>
            </div>
          </div>
        </PageHeader>

        {/* 题目内容 */}
        <ScrollArea className="mr-3 flex-1 overflow-y-auto pl-6 pr-3">
          <div className="rounded-xl bg-white">
            {currentQuestion && (
              <QuestionItem
                hasFooterButton={true}
                qaContent={currentQuestion}
                index={index}
                // customFooter={CustomFooter}
                // reportButton={
                //   <>
                //     <div className="bg-line-3 mx-2 h-4 w-px" />
                //     <ReportButton question={currentQuestion} />
                //   </>
                // }
                footerButton={<ReportButton question={currentQuestion} />}
                showViewButton={false}
              />
            )}
          </div>

          {/* 详细统计信息 */}
          <div className="mt-4 rounded-xl bg-white p-6">
            <QuestionStats
              qaContent={currentQuestion}
              totalStudents={totalStudents}
              getStudentInfo={getStudentInfo}
            />
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
});
