// import { EAuditStatus } from '@/enums';
// import { uploadQuestionAttachHelper } from '@/services/api/question';
import useEnumManagerMap from '@/hooks/useEnumManager';
import CreateQuestion from '@/pages/source-manage/question-create/CreateQuestionModal';
import { fetchAuditTaskList } from '@/services/api/review';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import React, { useMemo, useRef, useState } from 'react';
// import ChooseQuestionReview from '../common/ChooseQuestionReview';
import { removeNullFields } from '@/utils';
import ChooseQuestionGroupReview from '../common/ChooseQuestionGroupReview';
import ImportQuestionReview from '../common/ImportQuestionReview';

const Review: React.FC = () => {
  const {
    auditTypeList: auditTaskTypeEnumManager,
    auditStatusList: auditStatusEnumManager,
    phaseList: phaseEnumManager,
    subjectList: subjectEnumManager,
  } = useEnumManagerMap();

  const { initialState } = useModel('@@initialState');
  const phaseSubjectRelation =
    initialState?.globalDictValues?.enumConstants?.phaseSubjectRelation ?? [];

  const actionRef = useRef<ActionType>(null);
  const [reviewItem, setReviewItem] = useState<API.Review.AuditTaskItem | null>(null);

  // 构建级联选择器的选项数据
  const cascaderOptions = useMemo(() => {
    return phaseSubjectRelation.map((phase) => ({
      label: phase.nameZh,
      value: phase.value,
      children: phase.subjectList.map((subject) => ({
        label: subject.nameZh,
        value: subject.value,
      })),
    }));
  }, [phaseSubjectRelation]);

  console.log('cascaderOptions: ', auditTaskTypeEnumManager?.getProTableValueEnum());

  const columns: ProColumns<API.Review.AuditTaskItem>[] = [
    {
      title: '任务ID',
      order: 3,
      dataIndex: 'auditTaskId',
      width: 80,
      search: true,
      fieldProps: {
        placeholder: '请输入任务ID',
      },
    },
    {
      title: '任务类型',
      order: 6,
      dataIndex: 'auditTaskType',
      width: 130,
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择任务类型',
      },
      valueEnum: auditTaskTypeEnumManager?.getProTableValueEnum(),
      search: true,
    },
    {
      title: '任务名称',
      order: 2,
      dataIndex: 'auditTaskName',
      ellipsis: true,
      search: true,
    },
    {
      title: '学段',
      dataIndex: 'phase',
      width: 100,
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择',
      },
      valueEnum: phaseEnumManager?.getProTableValueEnum(),
      search: false,
    },
    {
      title: '学科',
      dataIndex: 'subject',
      width: 100,
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择',
      },
      valueEnum: subjectEnumManager?.getProTableValueEnum(),
      search: false,
    },
    {
      title: '提交人',
      order: 1,
      dataIndex: 'submiter',
      width: 100,
      search: true,
    },
    {
      title: '提交时间',
      dataIndex: 'createTimeStr',
      width: 200,
      search: false,
    },
    {
      title: '审核状态',
      order: 5,
      dataIndex: 'auditStatus',
      width: 100,
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择审核状态',
      },
      valueEnum: auditStatusEnumManager?.getProTableValueEnum(),
      search: true,
    },
    {
      title: '学段学科',
      order: 4,
      dataIndex: 'phaseSubject',
      width: 200,
      valueType: 'cascader',
      fieldProps: {
        options: cascaderOptions,
        placeholder: '请选择学段学科',
        fieldNames: { label: 'label', value: 'value', children: 'children' },
        changeOnSelect: true,
        allowClear: true,
      },
      search: true,
      hideInTable: true,
    },
    {
      title: '操作',
      width: 60,
      valueType: 'option',
      search: false,
      render: (_, record) => [
        <a key="review" onClick={() => setReviewItem(record)}>
          查看
        </a>,
      ],
    },
  ];

  // const uploadFileConfig = {
  //   // handler: uploadQuestionAttachHelper,
  //   transformBase64: true,
  //   onSuccess: (fileInfo: any) => {
  //     console.log(fileInfo);
  //   }
  // }

  return (
    <>
      <PageContainer>
        {/* <div style={{ width: '100%', height: '100%', backgroundColor: 'white',  padding: '20px', marginBottom: '20px' }}>
          <TiptapEditor editable={true} content={'呵呵'} placeholder="请输入内容"/>
        </div> */}
        <ProTable<API.Review.AuditTaskItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          request={async (params = {}) => {
            console.log('查询参数: ', params);
            const {
              current,
              pageSize,
              auditTaskId,
              auditStatus,
              auditTaskType,
              phaseSubject,
              auditTaskName,
              submiter,
            } = params;
            const [phase, subject] = phaseSubject || [];

            const status = auditStatus ? Number(auditStatus) : undefined;
            const type = auditTaskType ? Number(auditTaskType) : undefined;

            let payload: API.Review.AuditTaskListRequestParams = {
              page: current!,
              pageSize: pageSize!,
              auditTaskId: auditTaskId ? Number(auditTaskId) : undefined,
              auditStatus: status as API.EnumConstantData['auditStatusList'][number]['value'],
              auditTaskType: type as API.EnumConstantData['auditTypeList'][number]['value'],
              auditTaskNameKey: auditTaskName,
              submiterKey: submiter,
              phaseList: phase ? [phase] : undefined,
              subjectList: subject ? [subject] : undefined,
            };

            payload = removeNullFields(payload) as API.Review.AuditTaskListRequestParams;
            console.log('审核列表 payload: ', payload);

            try {
              const res = await fetchAuditTaskList(payload);
              // const res = await getAuditTaskList({
              //   auditTaskId,
              //   auditStatus,
              //   auditTaskType,
              //   auditTaskName,
              //   submiter,
              //   phase,
              //   subject,
              //   page: current!,
              //   pageSize: pageSize!,
              // });

              return {
                data: res.data.list || [],
                success: res.code === 0,
                total: res.data.total || 0,
              };
            } catch (error) {
              console.error('获取审核列表失败:', error);
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          }}
          rowKey="auditTaskId"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
            span: {
              xs: 24,
              sm: 12,
              md: 8,
              lg: 8,
              xl: 8,
              xxl: 6,
            },
          }}
          options={{
            reload: true,
            density: true,
            setting: true,
          }}
          form={{
            colon: false,
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          tableLayout="fixed"
          dateFormatter="string"
          headerTitle="审核列表"
        />
      </PageContainer>

      {
        <ChooseQuestionGroupReview
          onSubmit={() => {
            actionRef.current?.reload();
            setReviewItem(null);
          }}
          data={reviewItem!}
          canEdit={reviewItem?.auditStatus === 1}
          open={reviewItem?.auditTaskType === 2}
          onBack={() => setReviewItem(null)}
        />
      }

      {/* {
        <ChooseQuestionReview
          onSubmit={() => {
            actionRef.current?.reload();
            setReviewItem(null);
          }}
          data={reviewItem!}
          canEdit={reviewItem?.auditStatus === 1}
          open={reviewItem?.auditTaskType === 2}
          onBack={() => setReviewItem(null)}
        />
      } */}

      {reviewItem?.auditStatus === 1 ? (
        <CreateQuestion
          onSubmit={() => {
            actionRef.current?.reload();
            setReviewItem(null);
          }}
          data={reviewItem}
          open={reviewItem?.auditTaskType === 1}
          way={'audit'}
          onBack={() => setReviewItem(null)}
        />
      ) : (
        <ImportQuestionReview
          onSubmit={() => {
            actionRef.current?.reload();
            setReviewItem(null);
          }}
          data={reviewItem!}
          canEdit={false}
          open={reviewItem?.auditTaskType === 1}
          id={reviewItem?.auditTaskId || 0}
          onBack={() => setReviewItem(null)}
        />
      )}
    </>
  );
};

export default Review;
