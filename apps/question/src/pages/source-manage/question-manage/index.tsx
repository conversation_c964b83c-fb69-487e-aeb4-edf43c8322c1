import FilterPanel from '@/components/FilterPanel';
import QuestionSearch from '@/components/QuestionSearch';
import SubjectPhaseCascader from '@/components/SubjectPhaseCascader';
import TreeNodeFilter from '@/components/TreeNodeFilter';
import { ChangeEventPayload } from '@/types/common/common';
import { savePageState, scrollToTop } from '@/utils';
import { MANAGE_QUESTION_PAGE_TREE_HEIGHT, sortOptions } from '@/utils/constants';
import { getManageQuestionPagePhaseSubjectInitValue } from '@/utils/phaseSubject';
import { cardBodyStyle } from '@/utils/styles';
import { defaultQuestionListRequestParams } from '@/utils/tree';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Col, Flex, Grid, Radio, RadioChangeEvent, Row, Space } from 'antd';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import Sticky from 'react-stickynode';
import QuestionViewList from './components/QuestionViewList';
const QuestionManage: FC = () => {
  const [sort, setSort] = useState<string>('createTime');
  const screens = Grid.useBreakpoint();
  const { initialState } = useModel('@@initialState');
  const phaseSubjects = initialState?.globalDictValues?.enumConstants?.phaseSubjectRelation ?? [];
  const [affixEnabled, setAffixEnabled] = useState<boolean>(false);
  // 科目选择
  const [selectedSubject, setSelectedSubject] = useState<number[]>(
    getManageQuestionPagePhaseSubjectInitValue('questionManageSelectedSubject', phaseSubjects),
  );

  // 问题列表请求参数
  const [questionListRequestParams, setQuestionListRequestParams] =
    useState<API.QuestionListRequestParams>({ page: 1, pageSize: 10, sort: 'createTime' });

  useEffect(() => {
    // 如果列表请求参数有变化，将其存入 localStorage
    if (selectedSubject && selectedSubject.length > 0) {
      savePageState('questionManageSelectedSubject', selectedSubject);
    }
  }, [selectedSubject]);

  // useEffect(() => {
  //     // setTimeout(() => {
  //     //     setAffixTarget(document.querySelector('.question-manage-page-container') as HTMLDivElement);
  //     // }, 0);
  //     const affixTarget = document.querySelector('.question-manage-page-container') as HTMLDivElement;
  //     console.log('affixTarget ********: ', affixTarget);
  //     if (affixTarget) {
  //         const rect = affixTarget.getBoundingClientRect();
  //         // setAffixTarget(affixTarget);
  //         setTop(rect.top);
  //     }
  // }, []);

  useEffect(() => {
    setAffixEnabled(!!screens.xl);
  }, [screens.xl]);

  const onFilterChange = (params: ChangeEventPayload) => {
    const { type, field, value } = params;
    if (!questionListRequestParams) {
      return;
    }
    if (type === 'search') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        keyword: value,
        page: 1,
      });
      return;
    }
    questionListRequestParams.page = 1;
    if (type === 'filter') {
      const params: API.QuestionListRequestParams = { ...questionListRequestParams, page: 1 };
      params[field] = value;
      setQuestionListRequestParams(params);
      return;
    }

    if (type === 'sort') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        sort: value,
      });
      return;
    }

    if (type === 'reset') {
      const { phaseList, subjectList, baseTreeNodeIds, bizTreeNodeIds, sort, keyword } =
        questionListRequestParams;
      const requestParams = defaultQuestionListRequestParams();
      setQuestionListRequestParams({
        ...requestParams,
        phaseList,
        subjectList,
        baseTreeNodeIds,
        bizTreeNodeIds,
        sort,
        keyword,
      });
      return;
    }

    // 发送请求
  };

  const onPhaseSubjectValueChange = (value: number[]) => {
    setSelectedSubject(value);
    const [phaseId, subjectId] = value;
    const phaseList = phaseId ? [phaseId] : [];
    const subjectList = subjectId ? [subjectId] : [];
    setQuestionListRequestParams({
      ...questionListRequestParams,
      baseTreeNodeIds: [],
      bizTreeNodeIds: [],
      phaseList,
      subjectList,
    });
  };

  const onSortTypeChange = (e: RadioChangeEvent) => {
    const { value } = e.target;
    setSort(value);
    onFilterChange({
      type: 'sort',
      field: 'sort',
      value,
    });
  };

  const onKeywordSearch = (keyword: string) => {
    onFilterChange({
      type: 'search',
      field: '',
      value: keyword,
    });
  };

  const onPageChange = (page: number, pageSize: number) => {
    if (!questionListRequestParams) {
      return;
    }
    setQuestionListRequestParams({
      ...questionListRequestParams,
      page,
      pageSize,
    });
    // 滚动到页面顶部
    scrollToTop('.ant-pro-page-container');
  };

  const onTreeNodeChange = (
    value: number,
    node: API.Common.BaseTreeNodeAntdType,
    isBaseTree: boolean,
  ) => {
    if (!value) {
      return;
    }
    scrollToTop('.ant-pro-page-container');
    const params = isBaseTree
      ? {
          baseTreeNodeIds: [value],
          bizTreeNodeIds: [],
        }
      : {
          bizTreeNodeIds: [value],
          baseTreeNodeIds: [],
        };
    const [phaseId, subjectId] = selectedSubject;
    const phaseList = phaseId ? [phaseId] : [];
    const subjectList = subjectId ? [subjectId] : [];
    setQuestionListRequestParams({
      ...questionListRequestParams,
      ...params,
      phaseList,
      subjectList,
      page: 1,
    });
  };

  return (
    <PageContainer pageHeaderRender={false} className="question-manage-page-container">
      <Card styles={cardBodyStyle()}>
        <Row gutter={[16, 16]}>
          <Col xl={6} lg={24} md={24} sm={24} xs={24}>
            <Sticky enabled={affixEnabled} top={106}>
              <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <SubjectPhaseCascader
                  selectedSubject={selectedSubject}
                  onChange={onPhaseSubjectValueChange}
                  changeOnSelect={false}
                />
                <TreeNodeFilter
                  selectedSubject={selectedSubject}
                  onChange={onTreeNodeChange}
                  isOnlyFilterLeafNode={false}
                  treeHeight={MANAGE_QUESTION_PAGE_TREE_HEIGHT}
                />
              </Space>
            </Sticky>
          </Col>
          <Col xl={18} lg={24} md={24} sm={24} xs={24}>
            <Space direction="vertical" size={16} style={{ width: '100%' }}>
              <Card>
                <FilterPanel filters={questionListRequestParams} onChange={onFilterChange} />
              </Card>
              <Flex align="center" justify="space-between">
                <Radio.Group
                  value={sort}
                  options={sortOptions}
                  defaultValue="Apple"
                  optionType="button"
                  buttonStyle="solid"
                  onChange={onSortTypeChange}
                />
                <QuestionSearch
                  onSearch={onKeywordSearch}
                  value={questionListRequestParams?.search}
                />
              </Flex>
              <QuestionViewList
                requestParams={questionListRequestParams}
                onPageChange={onPageChange}
              />
            </Space>
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default QuestionManage;
