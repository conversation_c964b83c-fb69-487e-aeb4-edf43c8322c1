import img0 from "@/public/lottie/scan/img_0.png";
import img1 from "@/public/lottie/scan/img_1.png";
import img2 from "@/public/lottie/scan/img_2.png";
import img3 from "@/public/lottie/scan/img_3.png";
import img4 from "@/public/lottie/scan/img_4.png";
import Lottie from "lottie-react";

const animationData = {
  v: "5.12.2",
  fr: 24,
  ip: 0,
  op: 48,
  w: 426,
  h: 510,
  nm: "导出",
  ddd: 0,
  assets: [
    { id: "image_0", w: 7, h: 7, u: "", p: img0.src, e: 0 },
    { id: "image_1", w: 421, h: 35, u: "", p: img1.src, e: 0 },
    { id: "image_2", w: 265, h: 252, u: "", p: img2.src, e: 0 },
    { id: "image_3", w: 360, h: 361, u: "", p: img3.src, e: 0 },
    { id: "image_4", w: 327, h: 40, u: "", p: img4.src, e: 0 },
    {
      id: "comp_0",
      nm: "点点点",
      fr: 24.1200408935547,
      layers: [
        {
          ddd: 0,
          ind: 1,
          ty: 2,
          nm: "点.png",
          cl: "png",
          refId: "image_0",
          sr: 1,
          ks: {
            o: {
              a: 1,
              k: [
                {
                  i: { x: [0.667], y: [1] },
                  o: { x: [0.333], y: [0] },
                  t: 16.915,
                  s: [0],
                },
                { t: 24.8756218905473, s: [100] },
              ],
              ix: 11,
            },
            r: { a: 0, k: 0, ix: 10 },
            p: { a: 0, k: [1676, 1201.5, 0], ix: 2, l: 2 },
            a: { a: 0, k: [3.5, 3.5, 0], ix: 1, l: 2 },
            s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
          },
          ao: 0,
          ip: 0,
          op: 120.398009950249,
          st: 0,
          bm: 0,
        },
        {
          ddd: 0,
          ind: 2,
          ty: 2,
          nm: "点.png",
          cl: "png",
          refId: "image_0",
          sr: 1,
          ks: {
            o: {
              a: 1,
              k: [
                {
                  i: { x: [0.667], y: [1] },
                  o: { x: [0.333], y: [0] },
                  t: 8.955,
                  s: [0],
                },
                { t: 16.9154228855721, s: [100] },
              ],
              ix: 11,
            },
            r: { a: 0, k: 0, ix: 10 },
            p: { a: 0, k: [1665.75, 1201.5, 0], ix: 2, l: 2 },
            a: { a: 0, k: [3.5, 3.5, 0], ix: 1, l: 2 },
            s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
          },
          ao: 0,
          ip: 0,
          op: 120.398009950249,
          st: 0,
          bm: 0,
        },
        {
          ddd: 0,
          ind: 3,
          ty: 2,
          nm: "点.png",
          cl: "png",
          refId: "image_0",
          sr: 1,
          ks: {
            o: {
              a: 1,
              k: [
                {
                  i: { x: [0.667], y: [1] },
                  o: { x: [0.333], y: [0] },
                  t: 0,
                  s: [0],
                },
                { t: 8.95522388059702, s: [100] },
              ],
              ix: 11,
            },
            r: { a: 0, k: 0, ix: 10 },
            p: { a: 0, k: [1656.5, 1201.5, 0], ix: 2, l: 2 },
            a: { a: 0, k: [3.5, 3.5, 0], ix: 1, l: 2 },
            s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
          },
          ao: 0,
          ip: 0,
          op: 120.398009950249,
          st: 0,
          bm: 0,
        },
      ],
    },
    {
      id: "comp_1",
      nm: "搜索",
      fr: 24.1200408935547,
      layers: [
        {
          ddd: 0,
          ind: 1,
          ty: 2,
          nm: "手.png",
          cl: "png",
          refId: "image_2",
          sr: 1,
          ks: {
            o: { a: 0, k: 100, ix: 11 },
            r: { a: 0, k: 0, ix: 10 },
            p: {
              a: 1,
              k: [
                {
                  i: { x: 0.667, y: 1 },
                  o: { x: 0.333, y: 0 },
                  t: 0,
                  s: [1536, 1005, 0],
                  to: [0, 0, 0],
                  ti: [0, 0, 0],
                },
                {
                  i: { x: 0.667, y: 1 },
                  o: { x: 0.333, y: 0 },
                  t: 11.94,
                  s: [1551, 1005, 0],
                  to: [0, 0, 0],
                  ti: [0, 0, 0],
                },
                {
                  i: { x: 0.667, y: 1 },
                  o: { x: 0.333, y: 0 },
                  t: 23.881,
                  s: [1551, 990, 0],
                  to: [0, 0, 0],
                  ti: [0, 0, 0],
                },
                {
                  i: { x: 0.667, y: 1 },
                  o: { x: 0.333, y: 0 },
                  t: 35.821,
                  s: [1536, 990, 0],
                  to: [0, 0, 0],
                  ti: [0, 0, 0],
                },
                { t: 47.7611940298507, s: [1536, 1005, 0] },
              ],
              ix: 2,
              l: 2,
            },
            a: { a: 0, k: [132.5, 126, 0], ix: 1, l: 2 },
            s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
          },
          ao: 0,
          ip: 0,
          op: 120.398009950249,
          st: 0,
          bm: 0,
        },
        {
          ddd: 0,
          ind: 2,
          ty: 2,
          nm: "占位.png",
          cl: "png",
          refId: "image_3",
          sr: 1,
          ks: {
            o: { a: 0, k: 100, ix: 11 },
            r: { a: 0, k: 0, ix: 10 },
            p: { a: 0, k: [1500.5, 952, 0], ix: 2, l: 2 },
            a: { a: 0, k: [180, 180.5, 0], ix: 1, l: 2 },
            s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
          },
          ao: 0,
          ip: 0,
          op: 120.398009950249,
          st: 0,
          bm: 0,
        },
      ],
    },
  ],
  layers: [
    {
      ddd: 0,
      ind: 1,
      ty: 0,
      nm: "点点点",
      refId: "comp_0",
      sr: 1,
      ks: {
        o: { a: 0, k: 100, ix: 11 },
        r: { a: 0, k: 0, ix: 10 },
        p: { a: 0, k: [213, 123, 0], ix: 2, l: 2 },
        a: { a: 0, k: [1500, 900, 0], ix: 1, l: 2 },
        s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
      },
      ao: 0,
      w: 3000,
      h: 1800,
      ip: 24.8756218905473,
      op: 49.7512437810945,
      st: 24.8756218905473,
      bm: 0,
    },
    {
      ddd: 0,
      ind: 2,
      ty: 0,
      nm: "点点点",
      refId: "comp_0",
      sr: 1,
      ks: {
        o: { a: 0, k: 100, ix: 11 },
        r: { a: 0, k: 0, ix: 10 },
        p: { a: 0, k: [213, 123, 0], ix: 2, l: 2 },
        a: { a: 0, k: [1500, 900, 0], ix: 1, l: 2 },
        s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
      },
      ao: 0,
      w: 3000,
      h: 1800,
      ip: 0,
      op: 24.8756218905473,
      st: 0,
      bm: 0,
    },
    {
      ddd: 0,
      ind: 3,
      ty: 2,
      nm: "副标题.png",
      cl: "png",
      refId: "image_1",
      sr: 1,
      ks: {
        o: { a: 0, k: 100, ix: 11 },
        r: { a: 0, k: 0, ix: 10 },
        p: { a: 0, k: [213, 481.5, 0], ix: 2, l: 2 },
        a: { a: 0, k: [210.5, 17.5, 0], ix: 1, l: 2 },
        s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
      },
      ao: 0,
      ip: 0,
      op: 48,
      st: 0,
      bm: 0,
    },
    {
      ddd: 0,
      ind: 4,
      ty: 0,
      nm: "搜索",
      refId: "comp_1",
      sr: 1,
      ks: {
        o: { a: 0, k: 100, ix: 11 },
        r: { a: 0, k: 0, ix: 10 },
        p: { a: 0, k: [213, 123, 0], ix: 2, l: 2 },
        a: { a: 0, k: [1500, 900, 0], ix: 1, l: 2 },
        s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
      },
      ao: 0,
      w: 3000,
      h: 1800,
      ip: 0,
      op: 49.7512437810945,
      st: 0,
      bm: 0,
    },
    {
      ddd: 0,
      ind: 5,
      ty: 2,
      nm: "主标题.png",
      cl: "png",
      refId: "image_4",
      sr: 1,
      ks: {
        o: { a: 0, k: 100, ix: 11 },
        r: { a: 0, k: 0, ix: 10 },
        p: { a: 0, k: [189, 412.5, 0], ix: 2, l: 2 },
        a: { a: 0, k: [163.5, 20, 0], ix: 1, l: 2 },
        s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 },
      },
      ao: 0,
      ip: 0,
      op: 48,
      st: 0,
      bm: 0,
    },
  ],
  markers: [],
  props: {},
};

export default function CourseScan() {
  return (
    <div className="flex h-full items-center justify-center">
      <Lottie className="w-50" animationData={animationData} loop={true} />
    </div>
  );
}
