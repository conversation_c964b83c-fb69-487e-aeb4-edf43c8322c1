import ChatTeardropDotsIcon from "@/public/icons/ChatTeardropDots.svg";
import { Button } from "@/ui/tch-button";

export default function WidgetReportButton({
  onReport,
}: {
  onReport: () => void;
}) {
  return (
    <Button
      type="default"
      size="md"
      radius="full"
      className="right-13 top-5.5 gap-1.25 absolute z-10 flex h-7 items-center gap-1 rounded-[5.6px] px-2.5 text-xs font-normal active:bg-white active:opacity-60"
      style={{
        boxShadow: "0px 2.8px 11.2px 0px rgba(35, 42, 64, 0.05)",
      }}
      onClick={onReport}
    >
      <ChatTeardropDotsIcon className="relative top-[-1px] h-4 w-4" />
      问题反馈
    </Button>
  );
}
