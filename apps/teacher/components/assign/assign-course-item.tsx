import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { TargetJobClass } from "@/types";
import { CoursePracticeItem } from "@/types/assign/course";
import {
  getPracticeTimeFromBizTreeDetail,
  minutesToString,
  umeng,
  UmengAssignAction,
  UmengCategory,
} from "@/utils";
import { cn } from "@/utils/utils";
import { useComputed } from "@preact-signals/safe-react";
import { AssignCard } from "./assign-card";
import { AssignCourseItemOp } from "./assign-course-item-op";
import { AssignHeading } from "./assign-heading";

interface AssignCourseItemProps {
  style?: React.CSSProperties;
  className?: string;
  data: CoursePracticeItem;
  showPreview?: boolean;
}

const formatDeployedClassNames = (
  // 布置对象中选择的班级
  checkedClasses: TargetJobClass[],
  // 该资源下，已经布置过的班级Id列表
  sourceAssignedClassIds: Set<number>
) => {
  if (checkedClasses.length !== 1) {
    if (sourceAssignedClassIds.size === 0) {
      return {
        deployed: [],
        unDeployed: ["全部未布置"],
      };
    }

    if (
      checkedClasses.every((item) => sourceAssignedClassIds.has(item.jobClass))
    ) {
      return {
        deployed: ["全部已布置"],
        unDeployed: [],
      };
    }
  }

  const deployed: string[] = [];
  const unDeployed: string[] = [];

  for (let i = 0; i < checkedClasses.length; i++) {
    const item = checkedClasses[i];
    if (sourceAssignedClassIds.has(item.jobClass)) {
      deployed.push(`${item.jobGradeName}${item.name}`);
    } else {
      unDeployed.push(`${item.jobGradeName}${item.name}`);
    }
  }

  return { deployed, unDeployed };
};

export function AssignCourseItem({
  style = {},
  className = "",
  data,
  showPreview = false,
}: AssignCourseItemProps) {
  const {
    selectedAiCourses,
    selectedPractices,
    selectAiCourse,
    selectPractice,
    unselectAiCourse,
    unselectPractice,

    goToAiCoursePreview,
    goToPracticePreview,

    bizTreeNodeMap,

    // 布置对象中选中的班级
    checkedClasses,
  } = useAssignCourseContext();

  const aiCourseAssignedClassIds = useComputed(() => {
    return new Set(data.aiCourse.assignedClassIds || []);
  });

  const practiceAssignedClassIds = useComputed(() => {
    return new Set(data.practice.assignedClassIds || []);
  });

  // 获取巩固练习部署信息
  const deployedPracticeInfo = useComputed(() => {
    return formatDeployedClassNames(
      checkedClasses.value,
      practiceAssignedClassIds.value
    );
  });

  // 获取课程学习部署信息
  const deployedAiCourseInfo = useComputed(() => {
    return formatDeployedClassNames(
      checkedClasses.value,
      aiCourseAssignedClassIds.value
    );
  });

  const hasDeployedAiCourse = useComputed(() => {
    return selectedAiCourses.value.some(
      (item) => item.aiCourse.id === data.aiCourse.id
    );
  });

  const hasDeployedPractice = useComputed(() => {
    return selectedPractices.value.some(
      (item) => item.practice.questionSetId === data.practice.questionSetId
    );
  });

  const onAddAiCourse = () => {
    selectAiCourse(data);
  };

  const onRemoveAiCourse = () => {
    unselectAiCourse(data);
  };

  const onAddPractice = () => {
    selectPractice(data);
  };

  const onRemovePractice = () => {
    unselectPractice(data);
  };

  const onPreviewAiCourse = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_PREVIEW_CLICK,
      {}
    );
    if (data.aiCourse.id) {
      goToAiCoursePreview(data);
    }
  };

  const onPreviewPractice = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_PREVIEW_CLICK,
      {}
    );
    if (data.practice.questionSetId) {
      goToPracticePreview(data);
    }
  };

  // TODO：这俩数据有点脏，展示数据最好集中处理
  const aiCourseEstimatedTimeStr = useComputed(() => {
    const totalDuration = data.aiCourse.totalDuration || 0;
    return totalDuration ? minutesToString(totalDuration / 60) : "";
  });

  const practiceEstimatedTimeStr = useComputed(() => {
    const bizTreeNode = bizTreeNodeMap.value.get(data.bizTreeNodeId);
    return bizTreeNode ? getPracticeTimeFromBizTreeDetail(bizTreeNode) : "";
  });

  return (
    <AssignCard
      className={cn("flex gap-3 rounded-[1.25rem]", className)}
      style={style}
    >
      <AssignHeading
        content={
          `${data.bizTreeNodeSerialPath ? data.bizTreeNodeSerialPath + " " : ""}${data.bizTreeNodeName}` ||
          ""
        }
        showPrefix={data.isRecommend}
        prefix="推荐"
        className="h-6"
      />

      {data?.aiCourse?.id ? (
        <AssignCourseItemOp
          type="aiCourse"
          hasJoined={hasDeployedAiCourse.value}
          estimatedTimeStr={aiCourseEstimatedTimeStr.value}
          deployed={deployedAiCourseInfo.value.deployed}
          unDeployed={deployedAiCourseInfo.value.unDeployed}
          showPreview={showPreview}
          onPreview={() => onPreviewAiCourse()}
          onAdd={() => onAddAiCourse()}
          onRemove={() => onRemoveAiCourse()}
        />
      ) : null}

      {data?.practice?.questionSetId ? (
        <AssignCourseItemOp
          type="practice"
          hasJoined={hasDeployedPractice.value}
          estimatedTimeStr={practiceEstimatedTimeStr.value}
          deployed={deployedPracticeInfo.value.deployed}
          unDeployed={deployedPracticeInfo.value.unDeployed}
          showPreview={showPreview}
          onPreview={() => onPreviewPractice()}
          onAdd={() => onAddPractice()}
          onRemove={() => onRemovePractice()}
        />
      ) : null}
    </AssignCard>
  );
}
