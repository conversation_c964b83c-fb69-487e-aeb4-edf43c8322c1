import { convertMenusToTreeData } from '@/pages/system-permission-management/role/context';
import fetcher, { post } from '@/services/fetcher';
import type { Menu, PlatformMenus } from '@/types/menu';
import type { Role, RoleMultiPlatformMenu } from '@/types/role';
import { useModel } from '@umijs/max';
import { useCallback, useMemo, useState } from 'react';
import useSWRMutation from 'swr/mutation';

export interface PermissionData {
  menus: PlatformMenus[];
  users: API.UserInfo[];
  roles: RoleMultiPlatformMenu[];
  userMenus: Menu[];
  isLoading: boolean;
}

interface UsePermissionDataProps {
  schoolId?: string | number;
  userId?: number;
}

// 计算当前用户可分配的角色
export function getAvailableRoles(
  currentUserRoles: Role[] = [],
  allRoles: RoleMultiPlatformMenu[] = [],
): RoleMultiPlatformMenu[] {
  if (!currentUserRoles.length || !allRoles.length) return [];

  const currentUserRoleIds = currentUserRoles.map((role) => role.roleId);
  return allRoles.filter((role) => currentUserRoleIds.includes(role.roleId));
}

// 计算最大权限菜单列表
export function getMaxPermissionMenus(
  roles: RoleMultiPlatformMenu[],
  checkedRoleIds: number[],
): number[] {
  if (!roles.length) return [];

  const selectedRoles = roles.filter((role) => checkedRoleIds?.includes(role.roleId));

  // 合并所有角色的菜单，只获取platformId为1的平台下的菜单
  const menuIds = selectedRoles.reduce((acc: number[], role) => {
    // 找到platformId为1的平台菜单
    const operationPlatform = role.menuIds?.find((platform) => platform.platformId === 1);

    // 如果找到了platformId为1的平台，则合并其菜单ID
    if (operationPlatform && operationPlatform.menuIds) {
      return [...acc, ...operationPlatform.menuIds];
    }

    return acc;
  }, [] as number[]);

  return Array.from(new Set(menuIds));
}

// 根据菜单ID列表区分叶子节点和父节点
export function separateMenuNodes(
  menus: Menu[],
  menuIds: number[],
): { leafNodes: number[]; parentNodes: number[] } {
  // 构建菜单ID到子菜单的映射
  const menuChildrenMap = new Map<number, number[]>();
  menus.forEach((menu) => {
    if (menu.menuParentId !== 0) {
      const children = menuChildrenMap.get(menu.menuParentId) || [];
      children.push(menu.menuId);
      menuChildrenMap.set(menu.menuParentId, children);
    }
  });

  // 区分叶子节点和父节点
  const leafNodes: number[] = [];
  const parentNodes: number[] = [];

  menuIds.forEach((menuId) => {
    if (menuChildrenMap.has(menuId)) {
      parentNodes.push(menuId);
    } else {
      leafNodes.push(menuId);
    }
  });

  return { leafNodes, parentNodes };
}

export default function usePermissionData({ schoolId, userId }: UsePermissionDataProps = {}) {
  // console.log('usePermissionData: ', schoolId, userId);
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;

  const [data, setData] = useState<PermissionData>({
    menus: [],
    users: [],
    roles: [],
    userMenus: [],
    isLoading: false,
  });

  // 数据获取函数
  const fetchData = async (url: string) => {
    setData((prev) => ({ ...prev, isLoading: true }));
    try {
      return await fetcher(url);
    } finally {
      setData((prev) => ({ ...prev, isLoading: false }));
    }
  };

  // 数据加载 mutations
  const { trigger: fetchMenus } = useSWRMutation('/api/v1/menu/list', async (url) => {
    const data = (await fetchData(url)) as API.TableData<PlatformMenus>;
    setData((prev) => ({ ...prev, menus: data.list || [] }));
  });

  const { trigger: fetchUsers } = useSWRMutation(
    '/api/v1/user/list?page=1&pageSize=100&userEmploymentStatus=1&isOnlyMyAgent=1',
    async (url) => {
      const data = (await fetchData(url)) as API.TableData<API.UserInfo>;
      setData((prev) => ({ ...prev, users: data.list || [] }));
    },
  );

  const { trigger: fetchRoles } = useSWRMutation('/api/v1/role/list', async (url) => {
    const data = (await fetchData(url)) as API.TableData<RoleMultiPlatformMenu>;
    setData((prev) => ({ ...prev, roles: data.list || [] }));
  });

  const { trigger: fetchUserMenus } = useSWRMutation(
    '/api/v1/user_school_menu/list',
    async (key, { arg }: { arg: { userId: number; schoolId: string | number } }) => {
      const url = `/api/v1/user_school_menu/list?userId=${arg.userId}&schoolId=${arg.schoolId}`;
      const data = (await fetchData(url)) as { list: Menu[] };
      setData((prev) => ({ ...prev, userMenus: data.list || [] }));
    },
  );

  // 保存权限
  const { trigger: saveStaffPermission, isMutating } = useSWRMutation(
    '/api/v1/user_school_menu/update',
    post,
  );

  // 取消授权
  const { trigger: revokeStaffPermission } = useSWRMutation(
    '/api/v1/user_school_menu/deauthorize',
    post,
  );

  // 使用 useCallback 包装函数以保持引用稳定
  const loadBaseData = useCallback(() => {
    return Promise.all([fetchMenus(), fetchUsers(), fetchRoles()]);
  }, [fetchMenus, fetchUsers, fetchRoles]);

  // 使用 useCallback 包装 loadUserMenus
  const loadUserMenus = useCallback(
    (userId: number, schoolId: string | number) => {
      return fetchUserMenus({ userId, schoolId });
    },
    [fetchUserMenus],
  );

  // 计算当前用户可分配的角色
  const availableRoles = useMemo(
    () => getAvailableRoles(currentUser?.roles || [], data.roles),
    [currentUser?.roles, data.roles],
  );

  // 获取平台菜单
  const platformMenus = useMemo(() => {
    const platform = data.menus.find((menu) => menu.platformName === '运营平台');
    return platform?.menus || [];
  }, [data.menus]);

  // 使用 useCallback 包装 getMenuTree
  const getMenuTree = useCallback(
    (menuIds: number[]) => {
      if (!platformMenus.length || !menuIds.length) return [];

      const filteredMenus = platformMenus.filter((menu) => menuIds.includes(menu.menuId));

      return convertMenusToTreeData(filteredMenus);
    },
    [platformMenus],
  );

  return {
    ...data,
    availableRoles,
    platformMenus,
    loadBaseData,
    loadUserMenus,
    saveStaffPermission,
    revokeStaffPermission,
    getMenuTree,
    isMutating,
    // underSchoolMenus,
  };
}
