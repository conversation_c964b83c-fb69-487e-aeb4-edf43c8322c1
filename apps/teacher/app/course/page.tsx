"use client";

import { useApp, useUmeng } from "@/hooks";
import { getCourseTable } from "@/services/course";
import Spin from "@/ui/spin";
import { UmengCategory, UmengCoursePageName } from "@/utils";
import {
  useCreation,
  useMount,
  useRequest,
  useSessionStorageState,
} from "ahooks";
import dayjs from "dayjs";
import { CourseNav } from "./_components/course-nav";
import { CourseTable } from "./_components/course-table";
import { DatePicker } from "./_components/date-picker";

export default function CoursePage() {
  useUmeng(UmengCategory.COURSE, UmengCoursePageName.CLASSROOM);
  const { userInfo, setOpen } = useApp();

  useMount(() => {
    window.TecJsb?.firstPageReady?.();
    setOpen(true);
  });

  // 当前选中的日期
  const [currentDate, setCurrentDate] = useSessionStorageState(
    "course-current-date",
    {
      defaultValue: dayjs(),
      serializer: (value) => value.format("YYYY-MM-DD"),
      deserializer: (value) => dayjs(value),
    }
  );

  // 获取本周的日期数组
  const weekDays = useCreation(() => {
    const monday = currentDate!.startOf("week");
    return Array.from({ length: 7 }, (_, i) => monday.add(i, "day"));
  }, [currentDate]);

  // 生成时间轴时间点（从7点开始, 到24点结束, 每小时一个时间点）
  const timePoints = useCreation(() => {
    return Array.from({ length: 18 }, (_, i) => i + 7);
  }, [currentDate]);

  // 获取当前是星期几（0-6，0是周日）
  const currentDayIndex = useCreation(() => {
    const today = dayjs();
    // 找到当前日期在 weekDays 中的索引
    return weekDays.findIndex((date) => date.isSame(today, "day"));
  }, [weekDays]);

  // 加载课程表数据
  const getCourseTableRequest = useRequest(
    async () => {
      const params = {
        teacher_id: userInfo?.userID ?? 0,
        school_id: userInfo?.currentSchoolID ?? 0,
        school_year_id: 1,
        start_date: weekDays[0].format("YYYY-MM-DD"),
        end_date: weekDays[6].format("YYYY-MM-DD"),
      };

      return getCourseTable(params);
    },
    {
      debounceWait: 500,
      loadingDelay: 500,
      ready: !!userInfo,
    }
  );

  // 切换到今天
  const handleToday = () => {
    setCurrentDate(dayjs());
    getCourseTableRequest.run();
  };

  // 切换到上一周
  const handlePrevWeek = () => {
    setCurrentDate(currentDate!.subtract(1, "week"));
    getCourseTableRequest.run();
  };

  // 切换到下一周
  const handleNextWeek = () => {
    setCurrentDate(currentDate!.add(1, "week"));
    getCourseTableRequest.run();
  };

  return (
    <Spin loading={getCourseTableRequest.loading} className="h-full">
      <div className="flex h-full flex-col overflow-hidden px-6">
        <CourseNav refreshCourseTable={getCourseTableRequest.run} />

        {/* 主要内容区域 */}
        <div className="flex flex-1 flex-col gap-5 overflow-hidden pb-2.5 pt-4">
          <DatePicker
            currentDate={currentDate!}
            weekDays={weekDays}
            onToday={handleToday}
            onPrevWeek={handlePrevWeek}
            onNextWeek={handleNextWeek}
          />

          <CourseTable
            weekDays={weekDays}
            scheduleData={
              getCourseTableRequest.error
                ? {}
                : (getCourseTableRequest.data?.schedule ?? {})
            }
            timePoints={timePoints}
            currentDayIndex={currentDayIndex}
            onPrevWeek={handlePrevWeek}
            onNextWeek={handleNextWeek}
          />
        </div>
      </div>
    </Spin>
  );
}
