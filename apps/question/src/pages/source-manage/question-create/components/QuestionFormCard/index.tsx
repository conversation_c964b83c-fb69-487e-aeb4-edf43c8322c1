import AiToImg from '@/components/AiToImg';
import { Feature } from '@/components/TiptapEditor/src/utils/enum';
import { Button, Card, Form, Input, Space } from 'antd';
import React, { useCallback, useState } from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';
import { useQuestionCreateActions } from '../../hooks/useQuestionCreateActions';
import { useQuestionInitActions } from '../../hooks/useQuestionInitActions';
import { QuestionCreateProps } from '../../types';
import {
  EditableTiptap,
  MainAnswerSectionPanel,
  QuestionSegmented,
  QuestionStem,
  QuestionTitleBar,
  SubQuestionFormList,
} from '../index';
import BaseForm from './BaseTypeForm';
import styles from './index.module.less';

/**
 * 题目表单卡片组件
 * 封装题目创建/编辑的主要表单逻辑
 */
const QuestionFormCard: React.FC<QuestionCreateProps> = ({ way, data, onBack, onSubmit }) => {
  const {
    form,
    baseInfo,
    isSingleQuestion,
    questionList,
    questionSelectorRef,
    formActions,
    pollingActions,
  } = useQuestionCreate();

  const { handleDeleteQuestion } = useQuestionCreateActions({ way, data, onBack, onSubmit });
  const { identifyHandle } = useQuestionInitActions({});

  // 获取主题目的作答方式
  const questionAnswerMode = Form.useWatch('questionAnswerMode', form);

  const [activeFillIdx, setActiveFillIdx] = useState(0);

  const onQuestionFillAreaClick = useCallback((e: { index: number }) => {
    setActiveFillIdx(e.index);
  }, []);

  return (
    <>
      {!isSingleQuestion && (
        <Card className={styles.my8} hoverable={true}>
          <QuestionSegmented
            ref={questionSelectorRef}
            way={way}
            data={data}
            onBack={onBack}
            onSubmit={onSubmit}
          />
        </Card>
      )}
      {way === 'create' && (
        <div style={{ height: '100px', margin: '10px 0' }}>
          <AiToImg
            phase={baseInfo.phase}
            subject={baseInfo.subject}
            onLoadend={pollingActions.startPolling}
            onSubmit={identifyHandle}
          />
        </div>
      )}
      {questionList.length > 0 && (
        <Form.Provider>
          <Card className={styles.my8} hoverable={true}>
            <BaseForm name="baseForm" layout="vertical" />
          </Card>
          <Card hoverable={true}>
            <Form
              name="questionForm"
              form={form}
              layout="vertical"
              autoComplete="off"
              validateTrigger={['onSubmit']}
            >
              {/* 题目id，隐藏，供form提交时使用 */}
              <Form.Item name="questionId" label="questionId" hidden>
                <Input />
              </Form.Item>

              {/* 题目主题干 */}
              <QuestionStem
                key="main"
                name="questionStem"
                label={
                  <QuestionTitleBar
                    level="main"
                    title="题干"
                    onAddSub={formActions.handleAddSubQuestion}
                  />
                }
                questionAnswerMode={questionAnswerMode}
                onQuestionFillAreaClick={onQuestionFillAreaClick}
              />

              {/* 一级题目组件，监听二级题目列表，当没有二级题目时才显示主题干的作答方式和答案 */}
              <MainAnswerSectionPanel currentFillIdx={activeFillIdx} />

              {/* 二级题目列表组件 */}
              <SubQuestionFormList />

              <Form.Item
                name="questionExplanation"
                label="解析"
                rules={[{ required: true, message: '请输入解析' }]}
              >
                <EditableTiptap textarea excludeFeatures={[Feature.insertQs]} />
              </Form.Item>
            </Form>
            {!isSingleQuestion && way === 'upload' && questionList.length > 0 && (
              <Space align="center" className={styles.auditBtnContainer}>
                <Button type="primary" danger onClick={handleDeleteQuestion}>
                  删除
                </Button>
              </Space>
            )}
          </Card>
        </Form.Provider>
      )}
    </>
  );
};

export default QuestionFormCard;
