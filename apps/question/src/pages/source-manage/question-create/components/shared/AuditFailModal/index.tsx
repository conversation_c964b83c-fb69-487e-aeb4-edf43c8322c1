import { Form, Input, Modal } from 'antd';
import React from 'react';

interface AuditFailModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (reason: string) => void;
}

const AuditFailModal: React.FC<AuditFailModalProps> = ({ visible, onClose, onSubmit }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values.reason);
      form.resetFields();
      onClose();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal title="审核不通过" open={visible} onOk={handleSubmit} onCancel={onClose} destroyOnClose>
      <Form form={form}>
        <Form.Item
          name="reason"
          label="不通过原因"
          rules={[{ required: true, message: '请输入不通过原因' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AuditFailModal;
