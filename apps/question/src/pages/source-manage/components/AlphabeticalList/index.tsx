import { Feature } from '@/components/TiptapEditor/src/utils/enum';
import { DownOutlined, MinusOutlined, PlusOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Radio, Row, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import EditableTiptap from '../EditableTiptap';
import styles from './index.less';

const { Text } = Typography;

export interface ListItem {
  id: string;
  key: string;
  value: string;
  selected: boolean;
}

export type AlphabeticalListValue = {
  selectedKey: string | string[]; // 单选时为string，多选时为string[]
  items: { key: string; value: string }[];
};

export interface AlphabeticalListProps {
  value?: AlphabeticalListValue;
  onChange?: (value: AlphabeticalListValue) => void;
  disabled?: boolean;
  status?: 'error' | 'warning';
  help?: React.ReactNode;
  mode?: 'single' | 'multiple'; // 新增mode属性
}

const AlphabeticalList: React.FC<AlphabeticalListProps> = ({
  value,
  onChange,
  disabled,
  status,
  help,
  mode = 'single', // 默认为单选模式
}) => {
  const [items, setItems] = useState<ListItem[]>([]);

  // 初始化或同步外部值
  useEffect(() => {
    if (!value) {
      // 默认值
      const initialItems = ['A', 'B', 'C', 'D'].map((key, index) => ({
        id: key,
        key,
        value: '',
        selected: index === 0,
      }));
      setItems(initialItems);
      onChange?.({
        selectedKey: mode === 'single' ? 'A' : ['A'],
        items: initialItems.map((item) => ({ key: item.key, value: item.value })),
      });
    } else {
      // 同步外部值
      const newItems = value?.items?.map((item) => ({
        id: item.key,
        key: item.key,
        value: item.value,
        selected:
          mode === 'single'
            ? item.key === value.selectedKey
            : (value.selectedKey as string[]).includes(item.key),
      }));
      setItems(newItems);
    }
  }, [value, mode]);

  // 获取下一个字母
  const getNextLetter = (currentLetter: string) => {
    const code = currentLetter.charCodeAt(0);
    return String.fromCharCode(code + 1);
  };

  // 触发onChange
  const triggerChange = (newItems: ListItem[]) => {
    const selectedItems = newItems.filter((item) => item.selected);
    const value = {
      selectedKey:
        mode === 'single'
          ? selectedItems[0]?.key || newItems[0].key
          : selectedItems.map((item) => item.key),
      items: newItems.map((item) => ({ key: item.key, value: item.value })),
    };
    onChange?.(value);
  };

  // 添加新项
  const handleAdd = (index: number) => {
    if (items.length >= 26 || disabled) return;

    const newItems = [...items];

    // 保存当前位置及之后所有项的内容和选中状态
    const itemsToShift = newItems.slice(index + 1).map((item) => ({
      value: item.value,
      selected: item.selected,
    }));

    // 在当前位置后插入新的空项
    itemsToShift.unshift({
      value: '',
      selected: false,
    });

    // 重新分配内容和选中状态
    for (let i = index + 1; i < newItems.length; i++) {
      newItems[i].value = itemsToShift[i - (index + 1)].value;
      newItems[i].selected = itemsToShift[i - (index + 1)].selected;
    }

    // 如果需要添加新项
    if (itemsToShift.length > newItems.length - (index + 1)) {
      const lastKey = newItems[newItems.length - 1].key;
      const newKey = getNextLetter(lastKey);
      newItems.push({
        id: newKey,
        key: newKey,
        value: itemsToShift[itemsToShift.length - 1].value,
        selected: itemsToShift[itemsToShift.length - 1].selected,
      });
    }

    setItems(newItems);
    triggerChange(newItems);
  };

  // 删除项
  const handleRemove = (index: number) => {
    if (items.length <= 2 || disabled) return;

    const newItems = [...items];

    // 从被删除项的位置开始，将后面所有项的内容和选中状态向上移动
    for (let i = index; i < newItems.length - 1; i++) {
      newItems[i].value = newItems[i + 1].value;
      newItems[i].selected = newItems[i + 1].selected;
    }

    // 删除最后一项
    newItems.pop();
    setItems(newItems);
    triggerChange(newItems);
  };

  // 向上移动项 - 只移动值和选中状态
  const handleMoveUp = (index: number) => {
    if (index === 0 || disabled) return;
    const newItems = [...items];
    const currentValue = newItems[index].value;
    const currentSelected = newItems[index].selected;

    newItems[index].value = newItems[index - 1].value;
    newItems[index].selected = newItems[index - 1].selected;

    newItems[index - 1].value = currentValue;
    newItems[index - 1].selected = currentSelected;

    setItems(newItems);
    triggerChange(newItems);
  };

  // 向下移动项 - 只移动值和选中状态
  const handleMoveDown = (index: number) => {
    if (index === items.length - 1 || disabled) return;
    const newItems = [...items];
    const currentValue = newItems[index].value;
    const currentSelected = newItems[index].selected;

    newItems[index].value = newItems[index + 1].value;
    newItems[index].selected = newItems[index + 1].selected;

    newItems[index + 1].value = currentValue;
    newItems[index + 1].selected = currentSelected;

    setItems(newItems);
    triggerChange(newItems);
  };

  // 处理选中状态变化
  const handleSelectionChange = (index: number, checked: boolean) => {
    if (disabled) return;
    const newItems = items.map((item, i) => ({
      ...item,
      selected: mode === 'single' ? i === index : i === index ? checked : item.selected,
    }));
    setItems(newItems);
    triggerChange(newItems);
  };

  // 处理输入变化
  const handleInputChange = (index: number, value: string) => {
    if (disabled) return;
    const newItems = [...items];
    newItems[index].value = value;
    setItems(newItems);
    triggerChange(newItems);
  };

  return (
    <div className={styles.alphabeticalList}>
      {items.map((item, index) => (
        <div key={item.id} className={styles.listItem}>
          <Row align="middle" gutter={8} style={{ width: '100%' }} wrap={false}>
            <Col>
              {mode === 'single' ? (
                <Radio
                  checked={item.selected}
                  onChange={() => handleSelectionChange(index, true)}
                  disabled={disabled}
                />
              ) : (
                <Checkbox
                  checked={item.selected}
                  onChange={(e) => handleSelectionChange(index, e.target.checked)}
                  disabled={disabled}
                />
              )}
            </Col>
            <Col>
              <Text strong>{item.key}.</Text>
            </Col>
            <Col flex={1}>
              <EditableTiptap
                value={item.value}
                onChange={(v) => handleInputChange(index, v)}
                placeholder="请输入选项内容"
                excludeFeatures={[Feature.insertQs]}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={() => handleAdd(index)}
                  disabled={disabled || items.length >= 26}
                />
                <Button
                  type="text"
                  icon={<MinusOutlined />}
                  onClick={() => handleRemove(index)}
                  disabled={disabled || items.length <= 2}
                />
                <Button
                  type="text"
                  icon={<UpOutlined />}
                  onClick={() => handleMoveUp(index)}
                  disabled={disabled || index === 0}
                />
                <Button
                  type="text"
                  icon={<DownOutlined />}
                  onClick={() => handleMoveDown(index)}
                  disabled={disabled || index === items.length - 1}
                />
              </Space>
            </Col>
          </Row>
        </div>
      ))}
      {help && (
        <div
          className={styles.errorMessage}
          style={{ color: status === 'error' ? '#ff4d4f' : undefined }}
        >
          {help}
        </div>
      )}
    </div>
  );
};

export default AlphabeticalList;
