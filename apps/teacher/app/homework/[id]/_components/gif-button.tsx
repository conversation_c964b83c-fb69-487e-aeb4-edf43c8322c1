"use client";

import { But<PERSON> } from "@/ui/button";
import { cn } from "@/utils/utils";
import Image from "next/image";
import { useState } from "react";

interface GifButtonProps extends React.ComponentProps<typeof Button> {
  src?: string;
  activeSrc: string;
  duration: number;
  alt?: string;
  imgWidth?: number;
  imgHeight?: number;
  imgClassName?: string;
  activeImgClassName?: string;
  iconComponent?: React.ReactNode;
  praiseCount?: number;
  activedSrc?: string;
  SrcCompo: React.ElementType;
  ActiveSrcCompo: React.ElementType;
}

export function GifButton({
  onClick,
  children,
  alt,
  className,
  src,
  activeSrc,
  duration,
  imgWidth = 16,
  imgHeight = 16,
  imgClassName,
  activeImgClassName,
  iconComponent,
  praiseCount,
  activedSrc,
  SrcCompo,
  ActiveSrcCompo,
  ...props
}: GifButtonProps) {
  const [isPlaying, setIsPlaying] = useState(false);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isPlaying) return;

    if (onClick instanceof Function) {
      await onClick(event);
    }

    setIsPlaying(true);

    setTimeout(() => {
      setIsPlaying(false);
    }, duration);
  };

  return (
    <Button
      onClick={handleClick}
      className={cn(
        "gap-0.375rem h-2.25rem padding-0rem-1rem border-line-1 text-gray-2 text-0.875rem flex items-center justify-center rounded-[1.125rem] border text-sm font-[500] leading-[150%]",
        className,
        isPlaying && "pointer-events-none"
      )}
      {...props}
    >
      <div className="relative select-none">
        {iconComponent ? (
          iconComponent
        ) : praiseCount && praiseCount >= 1 ? (
          // <Image
          //   src={activedSrc}
          //   alt={
          //     alt || typeof children === "string" ? (children as string) : ""
          //   }
          //   className={cn(
          //     "h-2 w-2 transform",
          //     imgClassName,
          //     isPlaying && "opacity-0"
          //   )}
          //   width={imgWidth}
          //   height={imgHeight}
          // />
          <ActiveSrcCompo
            viewBox="0 0 20 20"
            className={cn(
              "!h-2.5 !w-2.5 transform",
              imgClassName,
              isPlaying && "opacity-0"
            )}
          />
        ) : (
          // <Image
          //   src={src}
          //   alt={
          //     alt || typeof children === "string" ? (children as string) : ""
          //   }
          //   className={cn(
          //     "h-4 w-4 transform",
          //     imgClassName,
          //     isPlaying && "opacity-0"
          //   )}
          //   width={imgWidth}
          //   height={imgHeight}
          // />
          <SrcCompo
            className={cn(
              "h-4 w-4 transform",
              imgClassName,
              isPlaying && "opacity-0"
            )}
          />
        )}
        {/* <Image
          src={isPlaying ? activeSrc : src}
          alt={alt || typeof children === "string" ? (children as string) : ""}
          className={cn(
            "h-4.5 w-4.5 absolute left-0 top-0 transform",
            activeImgClassName,
            !isPlaying && "hidden"
          )}
          width={imgWidth}
          height={imgHeight}
        /> */}
        {isPlaying ? (
          <Image
            src={activeSrc}
            alt={
              alt || typeof children === "string" ? (children as string) : ""
            }
            className={cn(
              "h-4.5 w-4.5 absolute left-0 top-0 transform",
              activeImgClassName
            )}
            style={{ top: "-3px" }}
            width={imgWidth}
            height={imgHeight}
          />
        ) : (
          <SrcCompo
            className={cn(
              "h-4.5 w-4.5 absolute left-0 top-0 transform",
              activeImgClassName,
              !isPlaying && "hidden"
            )}
          />
        )}
      </div>
      {children}
    </Button>
  );
}
