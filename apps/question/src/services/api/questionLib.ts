import { request, useRequest } from '@umijs/max';
import { message } from 'antd';

/**
 * 搜索题目列表
 */
export async function fetchExerciseQuestionList(
  params: API.ExerciseQuestionListRequestParams,
): Promise<API.ExerciseQuestionListResponse> {
  return request('/api/v1/scene/get/question/set/info', {
    method: 'GET',
    params,
  }).then((res) => {
    if (!res || !res.data || !res.data.list) {
      return res;
    }
    // const list = res.data.list.map((item: API.QuestionItemType) => {
    //     return {
    //         ...(item?.questionInfo || {}),
    //         ...item,
    //     }
    // });
    res.data.list = Array.isArray(res.data.list) ? res.data.list : [];
    return res;
  });
}

/**
 * 操作题目
 */
export async function exerciseQuestionAdd(
  params: API.ExerciseQuestionOperateRequestParams,
): Promise<API.ExerciseQuestionOperateResponse> {
  return request('/api/v1/scene/add/question/to/question/set', {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除题目
 */
export async function exerciseQuestionRemove(
  params: API.ExerciseQuestionOperateRequestParams,
): Promise<API.ExerciseQuestionOperateResponse> {
  return request('/api/v1/scene/del/question/from/question/set', {
    method: 'POST',
    data: params,
  });
}

/**
 * 推荐/取消推荐题目
 */
export async function exerciseQuestionRecommend(
  params: API.ExerciseQuestionRecommendRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/mark/question/recommend', {
    method: 'POST',
    data: params,
  });
}

/**
 * 批量推荐/取消推荐题目
 */
export async function exerciseQuestionBatchRecommend(
  params: API.ExerciseQuestionBatchRecommendRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/mark/question/group/recommend', {
    method: 'POST',
    data: params,
  });
}
export async function exerciseOnShelf(
  params: API.ExerciseOnShelfRequestParams,
): Promise<API.ExerciseOnShelfResponse> {
  return request('/api/v1/scene/on/shelf/question/biz/tree/node', {
    method: 'POST',
    data: params,
  });
}

export async function exerciseAuditProcess(
  params: API.ExerciseAuditResultHandleRequestParams,
): Promise<API.ExerciseAuditResultHandleResponse> {
  return request('/api/v1/scene/proc/audit/fail/info', {
    method: 'POST',
    data: params,
  });
}

export async function exerciseRevocationAudit(
  auditTaskId: number,
): Promise<API.ExerciseRevocationAuditResponse> {
  return request('/api/v1/audit/task/revoke', {
    method: 'POST',
    data: {
      auditTaskId,
    },
  });
}

export async function exerciseCopyFromOtherExercise(
  params: API.ExerciseCopyFromOtherExerciseRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/copy/question/set', {
    method: 'POST',
    data: params,
  });
}

export async function exerciseSearchBySetId(
  questionSetId: number,
): Promise<API.ExerciseSearchQuestionSetByIdResponse> {
  return request('/api/v1/scene/get/question/set/for/copy', {
    method: 'GET',
    params: {
      questionSetId,
    },
  });
}

/**
 * 创建题组
 */
export async function exerciseQuestionGroupCreate(
  params: API.ExerciseQuestionGroupCreateRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/create/question/group', {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除题组
 */
export async function exerciseQuestionGroupRemove(
  params: API.ExerciseQuestionGroupRemoveRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/del/question/group', {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑题组
 */
export async function exerciseQuestionGroupEdit(
  params: API.ExerciseQuestionGroupEditRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/scene/modify/question/group/info', {
    method: 'POST',
    data: params,
  });
}

/**
 * 操作题目, 加入或移出题目
 */
export function useExerciseQuestionOperate(op: 'add' | 'remove', callback: () => void) {
  const func = op === 'add' ? exerciseQuestionAdd : exerciseQuestionRemove;
  const messageText = op === 'add' ? '加入' : '移出';
  const { run, loading } = useRequest(
    (payload: API.ExerciseQuestionOperateRequestParams) => func(payload),
    {
      manual: true,
      formatResult: (result) => result,
      onSuccess: (result) => {
        console.log('exerciseQuestionOperate success: ', result);
        if (result.code === 0) {
          callback();
          message.success(messageText + '题目成功');
        } else {
          message.error('操作失败');
        }
      },
      onError: () => {
        message.error(messageText + '题目失败');
      },
    },
  );
  return { run, loading };
}

export function useExerciseQuestionRecommend(callback: () => void) {
  const { run, loading } = useRequest(
    (payload: API.ExerciseQuestionRecommendRequestParams) => exerciseQuestionRecommend(payload),
    {
      manual: true,
      formatResult: (result) => result,
      onSuccess: (result) => {
        console.log('useExerciseQuestionRecommend success: ', result);
        if (result.code === 0) {
          callback();
          message.success('操作成功');
        } else {
          message.error('操作失败');
        }
      },
      onError: () => {
        message.error('操作失败');
      },
    },
  );
  return { run, loading };
}

export function useExerciseQuestionBatchRecommend(callback: () => void) {
  const { run, loading } = useRequest(
    (payload: API.ExerciseQuestionBatchRecommendRequestParams) =>
      exerciseQuestionBatchRecommend(payload),
    {
      manual: true,
      formatResult: (result) => result,
      onSuccess: (result) => {
        if (result.code === 0) {
          callback();
          message.success('操作成功');
        } else {
          message.error('操作失败');
        }
      },
      onError: () => {
        message.error('操作失败');
      },
    },
  );
  return { run, loading };
}
