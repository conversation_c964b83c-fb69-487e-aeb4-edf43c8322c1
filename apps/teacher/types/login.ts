export interface School {
  remark: string;
  schoolAddress: string;
  schoolEduLevel: number;
  schoolEduSystem: number;
  schoolFeature: number;
  schoolID: number;
  schoolIsTest: number;
  schoolName: string;
  schoolNature: number;
  schoolNumber: string;
  schoolRegionID: number;
  schoolStatus: number;
  schoolTag: string;
}

interface RegionDict {
  regionDictId: number;
  regionDictName: string;
  regionDictCode: number;
  regionDictParentCode: number;
  regionDictLevel: number;
}

export interface Organization {
  organizationId: number;
  organizationName: string;
  organizationTypeId: number;
  organizationRegionId: number;
  organizationAddress: string;
  organizationIsTest: number;
  organizationStatus: number;
  remark: string;
  createTime: number;
  updateTime: number;
  regionMap: {
    city: RegionDict;
    district: RegionDict;
    province: RegionDict;
  };
}

interface UserType {
  userTypeId: number;
  userTypeName: string;
  createTime: number;
  updateTime: number;
  organizationList: Organization[];
}

interface User {
  userId: number;
  userName: string;
  userAccount: string;
  userPhone: string;
  userIsTest: number;
  userStartTime: number;
  userEndTime: number;
  userPhoto: string;
  userSource: number;
  createTime: number;
  updateTime: number;
  userTypes: UserType[];
  mdmInfo: {
    mdmUserId: string;
    mdmUserName: string;
  };
}

export interface LoginResponse {
  token: string;
  user: User;
}
