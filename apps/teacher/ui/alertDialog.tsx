/**
 * AlertDialog 组件
 *
 * 这是一个基于 Radix UI 的 AlertDialog 组件，增强了显示和功能。
 *
 * @特性
 * - 支持4种状态变体：default/warning/error/success，每种有对应图标
 * - 支持"不再提醒"功能，数据存储在本地localStorage (key: app_dialog_reminders_store)
 * - 默认宽度为25rem (w-100)，可通过className="w-[32.5rem]"等方式覆盖
 *
 * @参数
 * - variant: "default" | "warning" | "error" | "success" - 控制对话框风格和图标
 * - showReminder: true | number | undefined - 控制"不再提醒"功能
 *   - true: 显示"不再提醒"选项（永久不再提醒）
 *   - number(如30): 显示"N天内不再提醒"选项
 *   - undefined/false: 不显示"不再提醒"选项
 * - id: string - 当启用showReminder时必须提供，作为唯一标识符存储提醒设置
 * - onOk: () => any - 确认按钮回调，返回true值时才会保存"不再提醒"设置
 *
 * @行为
 * - 如果用户已设置"不再提醒"且open为true，组件会自动触发onOk而不显示对话框
 * - 设置了相同id的不同对话框会共享"不再提醒"设置
 * - 开发环境会检测并警告重复使用的reminderKey(id)
 */

import IcCorrectIcon from "@/public/icons/ic_correct.svg";
import IcIncorrectIcon from "@/public/icons/ic_incorrect.svg";
import IcRemindIcon from "@/public/icons/ic_remind.svg";
import IcWarningIcon from "@/public/icons/ic_warning.svg";
import { cn } from "@/utils/utils";
import { FC, useEffect } from "react";
import { NolongerTipCheckBox, useReminderHook } from "./nolongerTipCheckBox";
import { Button } from "./tch-button";

import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog";
import { type AlertDialogProps } from "@radix-ui/react-alert-dialog";
import { buttonVariants } from "@repo/ui/components/aipt-button";
import * as React from "react";

function AlertDialogComponent({
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {
  return <AlertDialogPrimitive.Root data-slot="alert-dialog" {...props} />;
}

function AlertDialogTrigger({
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {
  return (
    <AlertDialogPrimitive.Trigger data-slot="alert-dialog-trigger" {...props} />
  );
}

function AlertDialogPortal({
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {
  return (
    <AlertDialogPrimitive.Portal data-slot="alert-dialog-portal" {...props} />
  );
}

function AlertDialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {
  return (
    <AlertDialogPrimitive.Overlay
      data-slot="alert-dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  );
}

function AlertDialogContent({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {
  return (
    <AlertDialogPortal>
      <AlertDialogOverlay />
      <AlertDialogPrimitive.Content
        data-slot="alert-dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed left-[50%] top-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          className
        )}
        {...props}
      />
    </AlertDialogPortal>
  );
}

function AlertDialogHeader({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-dialog-header"
      className={cn("flex flex-col gap-2 text-center sm:text-left", className)}
      {...props}
    />
  );
}

function AlertDialogFooter({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-dialog-footer"
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        className
      )}
      {...props}
    />
  );
}

function AlertDialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {
  return (
    <AlertDialogPrimitive.Title
      data-slot="alert-dialog-title"
      className={cn("text-lg font-semibold", className)}
      {...props}
    />
  );
}

function AlertDialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {
  return (
    <AlertDialogPrimitive.Description
      data-slot="alert-dialog-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  );
}

function AlertDialogAction({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {
  return (
    <AlertDialogPrimitive.Action
      className={cn(buttonVariants({ type: "primary" }), className)}
      {...props}
    />
  );
}

function AlertDialogCancel({
  className,
  ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {
  return (
    <AlertDialogPrimitive.Cancel
      className={cn(buttonVariants({ type: "outline" }), className)}
      {...props}
    />
  );
}

const TitleIcon: FC<{
  variant: "default" | "warning" | "error" | "success";
  className?: string;
}> = ({ variant, className }) => {
  const iconMap = {
    default: IcRemindIcon,
    warning: IcWarningIcon,
    error: IcIncorrectIcon,
    success: IcCorrectIcon,
  };

  const Icon = iconMap[variant];

  return <Icon width={24} height={24} className={className} />;
};

interface BaseAlertDialogProps extends AlertDialogProps {
  style?: React.CSSProperties;
  className?: string;
  title?: string;
  description?: string;
  content?: React.ReactNode;
  trigger?: React.ReactNode;
  cancelText?: string;
  okText?: string;
  showCancel?: boolean;
  showOk?: boolean;
  onCancel?: () => void;
  onOk?: () => void;
  variant?: "default" | "warning" | "error" | "success";
  showTitleIcon?: boolean;
  onOpenChange?: (open: boolean) => void;
  id?: string; // 作为 reminderKey 使用的唯一标识符
}

type AlertDialogPropsWithReminder = BaseAlertDialogProps & {
  showReminder: true | number;
  id: string;
};

type AlertDialogPropsWithoutReminder = BaseAlertDialogProps & {
  showReminder?: false | undefined;
  id?: string;
};

type alertDialogProps =
  | AlertDialogPropsWithReminder
  | AlertDialogPropsWithoutReminder;

export function AlertDialog({
  open,
  style = {},
  className = "",
  title = "",
  description = "",
  content,
  trigger = null,
  cancelText = "取 消",
  showCancel = true,
  showOk = true,
  okText = "确 认",
  onCancel = () => {},
  onOk = () => {},
  variant = "default",
  showReminder,
  showTitleIcon = true,
  onOpenChange = () => {},
  id,
  ...alertDialogProps
}: alertDialogProps) {
  const reminderKey = showReminder ? id : undefined;
  const { shouldShow, saveReminderSetting, setReminderChecked } =
    useReminderHook({
      reminderKey: reminderKey as string,
      showReminder,
    });

  useEffect(() => {
    if (!shouldShow && open === true) {
      onOk();
    }
  }, [shouldShow, open, onOk]);

  const handleOk = async () => {
    const result = await onOk();
    if (showReminder && id && result) {
      saveReminderSetting({ title, description }, showReminder, true);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen && !shouldShow) {
      onOk();
      return;
    }
    onOpenChange(newOpen);
  };

  const handleCancel = () => {
    onCancel?.();
    setReminderChecked(false);
  };

  if (!shouldShow && open === true) return null;

  return (
    <AlertDialogComponent
      open={open}
      onOpenChange={handleOpenChange}
      {...(alertDialogProps as AlertDialogProps)}
    >
      {open === undefined && (
        <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>
      )}
      <AlertDialogContent
        className={cn(
          "border-line-1 flex w-[25rem] flex-col justify-center gap-6 rounded-2xl border bg-white p-6 pb-5 shadow-[0px_8px_32px_0px_rgba(16,18,25,0.10)]",
          className
        )}
        style={style}
      >
        <AlertDialogHeader className="flex flex-col items-start gap-0">
          <AlertDialogTitle className="text-gray-1 flex w-full flex-row items-center gap-2 text-xl font-medium leading-normal">
            {showTitleIcon && <TitleIcon variant={variant} />}
            {title}
          </AlertDialogTitle>
          {description && (
            <AlertDialogDescription
              className={cn(
                "text-gray-2 mt-2 text-sm font-normal leading-normal",
                {
                  "pl-7": showTitleIcon,
                }
              )}
            >
              {description}
            </AlertDialogDescription>
          )}
          {content && (
            <div
              className={cn("mt-6 w-full", {
                "pl-7": showTitleIcon,
              })}
            >
              {content}
            </div>
          )}
        </AlertDialogHeader>
        <AlertDialogFooter className="justify-between! flex items-center">
          {showReminder ? (
            <NolongerTipCheckBox
              reminderKey={id!}
              showReminder={showReminder}
              enabled={true}
            />
          ) : (
            <span />
          )}
          <div className="flex items-center gap-2">
            {showCancel && (
              <AlertDialogCancel onClick={handleCancel} asChild>
                <Button className="text-gray-2 h-9 rounded-full border-[#CFD5E8] px-5">
                  {cancelText}
                </Button>
              </AlertDialogCancel>
            )}
            {showOk && (
              <AlertDialogAction onClick={handleOk} asChild>
                <Button className="bg-primary-2 hover:bg-primary-2/90 h-9 rounded-full px-5">
                  {okText}
                </Button>
              </AlertDialogAction>
            )}
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialogComponent>
  );
}
