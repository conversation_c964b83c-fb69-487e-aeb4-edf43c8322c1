# 快速 Docker 构建文件 - 使用根目录构建上下文
# 使用方法：先本地构建，再用此 Dockerfile 打包

FROM node:20-alpine AS runtime

# 安装运行时依赖
RUN apk add --no-cache libc6-compat curl

# 设置工作目录
WORKDIR /app

# 接受构建参数
ARG APP_NAME=teacher
ARG NODE_ENV=production

# 设置环境变量
ENV NODE_ENV=${NODE_ENV}
ENV NEXT_TELEMETRY_DISABLED=1
ENV APP_NAME=${APP_NAME}

# 创建用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建产物（从根目录构建上下文）
COPY apps/${APP_NAME}/dist/standalone ./
COPY apps/${APP_NAME}/dist/static ./apps/${APP_NAME}/dist/static
COPY apps/${APP_NAME}/public ./apps/${APP_NAME}/public

# 设置正确的权限
RUN chown -R nextjs:nodejs /app

USER nextjs

# 端口配置
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动命令
ENV HOSTNAME="0.0.0.0"
CMD ["sh", "-c", "node apps/${APP_NAME}/server.js"] 