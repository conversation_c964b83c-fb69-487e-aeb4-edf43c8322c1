# 逐步讲解二级导航组件

## 📋 组件概述

逐步讲解二级导航组件 (`StepByStepNavigation`) 是专门为逐步讲解阶段设计的水平导航条，支持动态步骤数量和基于实际数据的导航功能。

## 🎯 设计特性

### 核心功能
- ✅ **动态步骤导航**：基于实际mock数据生成步骤，支持任意数量
- ✅ **数据驱动**：从`stepByStepGuide.steps`中获取真实步骤标题
- ✅ **精确布局还原**：完全按照Figma设计的尺寸、颜色、字体实现
- ✅ **分段进度线**：每段进度线长度与步骤文字长度成正比
- ✅ **四角星指示**：每个步骤正中有四角星，两侧是横线
- ✅ **横向滚动支持**：当步骤过多时支持横向滚动浏览
- ✅ **自动居中定位**：激活步骤自动滚动到视窗中心
- ✅ **智能边界处理**：首尾步骤无法居中时自动调整到最佳位置
- ✅ **隐藏滚动条**：提供无滚动条的流畅滚动体验
- ✅ **进度状态管理**：支持已完成、当前、未开始三种状态
- ✅ **响应式适配**：支持不同屏幕尺寸的比例缩放
- ✅ **语义化样式**：所有元素都有语义化class便于调试

### 视觉设计
```
StepByStepNavigation
├── 分段进度线 (.step-navigation-progress-line)
│   ├── 左侧线段
│   │   ├── 当前步骤: 渐变色 (5.62% #FFA453 → 99.21% #FF8D29)
│   │   ├── 已完成: 纯色 #FFA453
│   │   └── 未开始: 灰色 #EAEAEA
│   ├── 四角星 (已完成: #FFA453, 当前: #FF964A, 未开始: #EAEAEA)
│   └── 右侧线段 (已完成: #FFA453, 未开始: #EAEAEA)
├── 步骤容器 (.step-navigation-container)
│   └── 5个步骤项 (.step-navigation-item) - 宽度根据文字长度计算
│       ├── 编号 (.step-number) - DIN Alternate字体
│       └── 标题 (.step-title) - Resource Han Rounded SC字体
└── 横向滚动系统
    ├── 滚动容器 (.step-navigation-scroll-container) - 隐藏滚动条
    ├── 自动居中算法 (激活项滚动到视窗中心)
    ├── 边界智能处理 (首尾项最佳位置)
    └── 平滑滚动动画 (behavior: smooth)
```

## 🔧 组件接口

### Props
```typescript
interface StepByStepNavigationProps {
  currentStep: number;        // 当前激活的步骤 (1-n)
  onStepChange: (step: number) => void;  // 步骤切换回调
  className?: string;         // 可选的额外样式类
  steps?: Array<{            // 从外部传入的步骤数据
    stepTitle: string;
    [key: string]: unknown;  // 允许其他属性
  }>;
}
```

### 动态步骤数据
```typescript
// 从实际mock数据中获取
const steps: StepItem[] = stepsData ? stepsData.map((step, index) => ({
  id: index + 1,
  title: step.stepTitle
})) : [
  // 默认fallback数据
  { id: 1, title: '解密平抛，求 vQ' },
  { id: 2, title: '穿越圆弧，求 vP' },
  { id: 3, title: '分析受力，求 P 点压力' },
  { id: 4, title: '摩擦做功，求 vM' },
  { id: 5, title: '能量转化，求 Ep' },
];
```

### 数据流
```
aiExplanation.stepByStepGuide.steps
  → StepByStepGuideStage.data.steps
  → StepByStepNavigation.steps
```

## 🎨 样式状态

### 激活状态 (currentStep)
- **背景**：`#FFEDE0` 圆角18px
- **文字颜色**：`#E6710A`
- **字重**：编号bold，标题medium
- **进度线**：渐变色 `linear-gradient(90deg, #FFA453 5.62%, #FF8D29 99.21%)`
- **星标**：`#FF964A` 填充，3px白色描边，12px尺寸

### 非激活状态
- **背景**：`#F7F6F5` 圆角6px
- **文字颜色**：`rgba(51,48,45,0.7)`
- **字重**：编号bold，标题normal
- **星标**：`#EAEAEA` 填充，4px白色描边，11.31px尺寸

## 📐 布局规格

### 容器尺寸
- **总宽度**：最大936px，居中对齐
- **总高度**：50px
- **进度线位置**：距顶部6px

### 星标位置 (基于936px宽度)
- 步骤1：86px (9.19%)
- 步骤2：263.34px (28.13%)
- 步骤3：461.34px (49.29%)
- 步骤4：660.34px (70.55%)
- 步骤5：839.34px (89.68%)

## 🔗 集成使用

### 在逐步讲解阶段中使用
```typescript
import StepByStepNavigation from '../components/step-by-step-navigation';

const StepByStepGuideStage = ({ data }) => {
  const [currentStep, setCurrentStep] = useState(1);

  return (
    <div className="step-by-step-guide-stage">
      <div className="step-navigation-wrapper px-6 pt-4">
        <StepByStepNavigation
          currentStep={currentStep}
          onStepChange={setCurrentStep}
          steps={data?.steps}  // 传递实际步骤数据
        />
      </div>
      {/* 步骤内容 */}
    </div>
  );
};
```

## 🎯 设计原则

### 第一性原理
- **单一职责**：专注于步骤导航，不混合其他功能
- **数据驱动**：通过currentStep控制视觉状态
- **可预测性**：固定5步，行为一致

### KISS原则
- **数据驱动**：基于实际数据生成步骤，避免硬编码
- **精确定位**：使用绝对定位确保布局精确性
- **最小依赖**：仅依赖基础React和样式工具

### SOLID原则
- **开闭原则**：对样式扩展开放，对核心逻辑修改封闭
- **单一职责**：仅负责导航展示和交互
- **依赖倒置**：通过props接收状态和回调

## ⚠️ 使用注意事项

1. **步骤范围**：currentStep必须在1-n范围内（n为实际步骤数量）
2. **数据依赖**：需要传入有效的steps数据，否则使用默认fallback
3. **字体依赖**：需要确保DIN Alternate和Resource Han Rounded SC字体可用
4. **响应式**：在小屏幕上可能需要调整字体大小或布局
5. **语义化**：所有class都有明确语义，便于样式调试和测试

## 🔄 更新记录

- **v2.0.0** - 重大更新：支持动态数据驱动
  - 移除硬编码步骤，改为从props获取
  - 支持任意数量的步骤
  - 保持向后兼容的fallback机制
- **v1.0.0** - 初始实现，完全按照Figma设计规格
  - 支持5步固定导航
  - 实现精确的视觉还原
  - 添加响应式支持
