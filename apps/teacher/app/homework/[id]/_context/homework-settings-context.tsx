"use client";

import { createContext, useContext } from "react";
import { effect, useSignal, Signal } from "@preact-signals/safe-react";
import { useDebounceFn } from "ahooks";
import { toast } from "@/ui/toast";

// 本地存储键名
const STORAGE_KEY = "homework_settings";

// 默认设置
export const DEFAULT_SETTINGS = {
  oneClickPraise: false,
  oneClickReminder: true,
  peopleCount: "",
  accuracyRate: "全部",
  learningStatusValues: {
    learningPoints: "",
    completionProgress: "",
    accuracyRate: "80%",
    questionDifficulty: "",
    wrongQuestions: ""
  }
};

export type HomeworkSettings = typeof DEFAULT_SETTINGS;


// 创建上下文类型
interface HomeworkSettingsContextType {
  settings: Signal<HomeworkSettings>;
  tempPeopleCount: Signal<string>;
  updateSettings: (newSettings: Partial<HomeworkSettings>) => void;
  updateSetting: <K extends keyof HomeworkSettings>(key: K, value: HomeworkSettings[K]) => void;
  updatePeopleCount: (value: string) => void;
}

// 创建上下文
const HomeworkSettingsContext = createContext<HomeworkSettingsContextType | undefined>(undefined);

// 上下文提供者组件
export function HomeworkSettingsProvider({ children }: { children: React.ReactNode }) {

  // 创建信号
  const settingsSignal = useSignal<HomeworkSettings>(DEFAULT_SETTINGS);
  // 创建输入框临时状态信号
  const tempPeopleCountSignal = useSignal<string>(DEFAULT_SETTINGS.peopleCount);

  // 从本地存储加载设置
  if (typeof window !== 'undefined') {
    try {
      const savedSettings = localStorage.getItem(STORAGE_KEY);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        const settings = {
          oneClickPraise: parsedSettings.oneClickPraise ?? DEFAULT_SETTINGS.oneClickPraise,
          oneClickReminder: parsedSettings.oneClickReminder ?? DEFAULT_SETTINGS.oneClickReminder,
          peopleCount: parsedSettings.peopleCount ?? DEFAULT_SETTINGS.peopleCount,
          accuracyRate: parsedSettings.accuracyRate ?? DEFAULT_SETTINGS.accuracyRate,
          learningStatusValues: parsedSettings.learningStatusValues ?? DEFAULT_SETTINGS.learningStatusValues
        };
        settingsSignal.value = settings;
        tempPeopleCountSignal.value = settings.peopleCount;
      }
    } catch (error) {
      console.error("Failed to load settings from localStorage:", error);
    }
  }
  // 使用 effect 监听设置变化并保存到本地存储
  effect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settingsSignal.value));
    } catch (error) {
      console.error("Failed to save settings to localStorage:", error);
    }
  });

  // 更新设置
  const updateSettings = (newSettings: Partial<HomeworkSettings>) => {
    settingsSignal.value = { ...settingsSignal.value, ...newSettings };
  };

  // 更新单个设置项
  const updateSetting = <K extends keyof HomeworkSettings>(key: K, value: HomeworkSettings[K]) => {
    updateSettings({ [key]: value } as Partial<HomeworkSettings>);
  };

  // 使用 useDebounceFn 处理人数输入防抖
  const { run: debouncedSave } = useDebounceFn(
    (value: string) => {
      updateSetting('peopleCount', value);
      toast.success("设置已更新");
    },
    { wait: 300 }
  );

  // 更新人数输入
  const updatePeopleCount = (value: string) => {
    // 立即更新临时状态
    tempPeopleCountSignal.value = value;
    // 延迟保存到设置
    debouncedSave(value);
  };

  return (
    <HomeworkSettingsContext.Provider value={{
      settings: settingsSignal,
      tempPeopleCount: tempPeopleCountSignal,
      updateSettings,
      updateSetting,
      updatePeopleCount
    }}>
      {children}
    </HomeworkSettingsContext.Provider>
  );
}

// 使用上下文的钩子
export function useHomeworkSettings() {
  const context = useContext(HomeworkSettingsContext);
  if (context === undefined) {
    throw new Error("useHomeworkSettings must be used within a HomeworkSettingsProvider");
  }
  return context;
}
