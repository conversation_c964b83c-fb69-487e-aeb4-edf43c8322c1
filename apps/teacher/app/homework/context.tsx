"use client";
import { GROUP_TYPE } from "@/enums";
import { getHomeworkList, GetHomeworkListParams } from "@/services/homework";
import { Homework } from "@/types/homeWork";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { transformHomeworkToTaskData } from "./_components/task-card-v2";
// import { format, subDays } from "date-fns";
import { useCurrentSubjectLocalStorage } from "@/hooks/useCurrentSubjectLocalStorage";
import { createContext, useContext } from "@preact-signals/safe-react/react";
import to from "await-to-js";
import { type ReactNode } from "react";
import { OriginalClassData, TaskData } from "./_components/task-card";
import {
  convertFilterToQuery,
  getDefaultFilterData,
  useHomeworkFilterPersistence,
} from "./hooks/useHomeworkFilterPersistence";
/**
 * 作业列表相关钩子函数
 * 提供查询、获取作业列表等功能
 */
const useHomeworkList = () => {
  // 持久化管理
  const filterPersistence = useHomeworkFilterPersistence();

  // 添加加载状态和更多数据信号
  const loadingSignal = useSignal(false);
  const hasMoreSignal = useSignal(true);
  const initializedSignal = useSignal(false);

  const { getCurrentSubjectFromLocalStorage } = useCurrentSubjectLocalStorage();

  // 初始化查询参数，优先使用持久化数据
  const initializeQuery = (isInit = false): GetHomeworkListParams => {
    const persistedFilter = filterPersistence.getFilterFromLocalStorage();
    const defaultFilter = getDefaultFilterData();

    const currentSubject = getCurrentSubjectFromLocalStorage();

    if (persistedFilter && !isInit) {
      if (currentSubject) {
        persistedFilter.subject = currentSubject.subjectKey;
      }
      return {
        ...convertFilterToQuery(persistedFilter),
        groupType: GROUP_TYPE.GROUP_TYPE_CLASS,
        keyword: "",
        page: 1,
        pageSize: 30,
      };
    }

    if (currentSubject) {
      defaultFilter.subject = currentSubject.subjectKey;
    }

    return {
      ...convertFilterToQuery(defaultFilter),
      groupType: GROUP_TYPE.GROUP_TYPE_CLASS,
      keyword: "",
      page: 1,
      pageSize: 30,
    };
  };

  const querySignal = useSignal<GetHomeworkListParams>(initializeQuery());

  // 初始化数据加载
  const initializeData = async () => {
    if (initializedSignal.value) return;
    initializedSignal.value = true;
    await fetchTaskList();
  };

  const homeworkListSignal = useSignal<Homework[]>([]);

  const taskListSignal = useComputed(() => {
    const list = homeworkListSignal.value.map(transformHomeworkToTaskData);

    const newList: (TaskData & { classData: OriginalClassData })[] = [];
    list.forEach((item) => {
      if (!item.classes.length) return;
      item.classes.forEach((c) => {
        newList.push({ ...item, classes: [c], classData: c.classData });
      });
    });

    return newList;
  });
  /**
   * 获取作业列表数据
   * @param isLoadMore 是否加载更多数据
   */
  const fetchTaskList = async (isLoadMore = false) => {
    if (loadingSignal.value && querySignal.value.subject !== 0) return null;

    loadingSignal.value = true;

    try {
      const [error, res] = await to(getHomeworkList(querySignal.value));
      if (error) {
        console.error(error);
        return null;
      }

      // 更新数据：加载更多时追加，否则替换
      if (isLoadMore) {
        homeworkListSignal.value = [
          ...homeworkListSignal.value,
          ...(res.tasks || []),
        ];
      } else {
        homeworkListSignal.value = res.tasks || [];
      }

      // 判断是否还有更多数据
      const { page, pageSize, total } = res.pageInfo;
      hasMoreSignal.value = page * pageSize < total;

      return res;
    } finally {
      loadingSignal.value = false;
    }
  };

  /**
   * 重置查询参数并清空列表
   */
  const resetList = () => {
    querySignal.value = {
      ...querySignal.value,
      page: 1,
    };
    homeworkListSignal.value = [];
    hasMoreSignal.value = true;
  };

  /**
   * 加载下一页数据
   */
  const loadMore = async () => {
    // console.log("loadMore");

    if (!hasMoreSignal.value || loadingSignal.value) return;

    // 更新页码
    querySignal.value = {
      ...querySignal.value,
      page: (querySignal.value.page || 1) + 1,
    };

    return fetchTaskList(true);
  };

  const setQuery = (params: Partial<GetHomeworkListParams>, isFetch = true) => {
    querySignal.value = {
      ...querySignal.value,
      ...params,
    };

    // 持久化筛选条件（排除非筛选相关的参数）
    const filterParams = {
      taskType: params.taskType,
      subject: params.subject,
      groupId: params.groupId,
      startDate: params.startDate,
      endDate: params.endDate,
    };

    // 只保存有效的筛选参数
    const validFilterParams = Object.fromEntries(
      Object.entries(filterParams).filter(([, value]) => value !== undefined)
    );

    if (Object.keys(validFilterParams).length > 0) {
      filterPersistence.setFilterToLocalStorage(validFilterParams);
    }

    if (isFetch) {
      fetchTaskList();
    }
  };

  return {
    query: querySignal,
    homeworkList: homeworkListSignal,
    taskList: taskListSignal,
    loading: loadingSignal,
    hasMore: hasMoreSignal,
    initialized: initializedSignal,
    fetchTaskList,
    resetList,
    loadMore,
    setQuery,
    initializeData,
    initializeQuery,
  };
};

// 创建 Context
const HomeworkListContext = createContext<
  ReturnType<typeof useHomeworkList> | undefined
>(undefined);

// 创建 Provider 组件
export const HomeworkListProvider = ({ children }: { children: ReactNode }) => {
  const homeworkList = useHomeworkList();
  return (
    <HomeworkListContext.Provider value={homeworkList}>
      {children}
    </HomeworkListContext.Provider>
  );
};

// 创建一个 hook 来使用 Context
export const useHomeworkListContext = () => {
  const context = useContext(HomeworkListContext);
  if (context === undefined) {
    throw new Error(
      "useHomeworkListContext must be used within a HomeworkListProvider"
    );
  }
  return context;
};
