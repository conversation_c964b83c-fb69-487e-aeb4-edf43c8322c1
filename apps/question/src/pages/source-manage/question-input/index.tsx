/**
 * 题目录入列表
 */
import WordReview from '@/pages/common/ImportQuestionReview';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Space } from 'antd';
import React, { useState } from 'react';
import UploadHistoryList from './components/UploadHistoryList';
import UploadWord from './components/UploadWord';

const QuestionInput: React.FC = () => {
  const [showWordReview, setShowWordReview] = useState(false);
  const [reviewItem, setReviewItem] = useState<API.UploadWordListItem | null>(null);
  const [reloadTime, setReloadTime] = useState(0);
  return (
    <PageContainer pageHeaderRender={false}>
      <div>
        <Space direction="vertical" size={10} style={{ width: '100%' }}>
          <Card>
            <UploadWord onUploadSucess={() => setReloadTime(Date.now())} />
          </Card>

          <UploadHistoryList
            reloadTime={reloadTime}
            onView={(record) => {
              setShowWordReview(true);
              setReviewItem(record);
            }}
          />
        </Space>

        {
          <WordReview
            id={reviewItem?.auditTaskId ?? 0}
            onBack={() => {
              setShowWordReview(false);
              setReviewItem(null);
            }}
            open={showWordReview}
          />
        }
      </div>
    </PageContainer>
  );
};

export default QuestionInput;
