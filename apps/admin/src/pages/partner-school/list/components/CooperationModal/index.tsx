import HistoryList from '@/components/HistoryList';
import { ApprovalDetail } from '@/pages/approval/detail/context';
import fetcher, { post } from '@/services/fetcher';
import { CooperationType } from '@/types/cooperation';
import { HistoryStatus } from '@/types/history';
import { SchoolItem } from '@/types/school';
import { InfoCircleOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import { message, Modal, Select, Space, Tabs, Tag, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import CooperationForm from './CooperationForm';

const { TabPane } = Tabs;
const { Text } = Typography;

// 用户信息类型

// 模拟历史记录数据
export const mockHistoryData = [
  {
    id: '1',
    type: CooperationType.PAYMENT,
    status: HistoryStatus.APPROVED,
    title: '付费申报审批',
    operator: '张三',
    operateTime: '2024-03-20 14:30:00',
    content: {
      studentCount: 100,
      startTime: '2024-03-01',
      duration: 180,
      endTime: '2024-09-01',
      remark: '上半年付费申报',
    },
  },
  {
    id: '2',
    type: CooperationType.TRIAL,
    status: HistoryStatus.PENDING,
    title: '试用申报审批',
    operator: '李四',
    operateTime: '2024-03-19 10:20:00',
    content: {
      subjects: ['语文', '数学', '英语'],
      startTime: '2024-03-15',
      duration: 180,
      endTime: '2024-04-15',
      remark: '新学期试用申请',
    },
  },
  {
    id: '3',
    type: CooperationType.TRIAL_EXTENSION,
    status: HistoryStatus.APPROVED,
    title: '启用合作',
    operator: '王五',
    operateTime: '2024-03-18 15:40:00',
    content: {
      startTime: '2024-03-01',
      duration: 180,
      endTime: '2024-09-01',
      remark: '上半年启用合作',
    },
  },
  {
    id: '4',
    type: CooperationType.DISABLE,
    status: HistoryStatus.APPROVED,
    title: '停用合作',
    operator: '赵六',
    operateTime: '2024-03-17 12:10:00',
    content: {
      reason: '学校管理调整',
      startTime: '2024-03-01',
      duration: 180,
      endTime: '2024-09-01',
      remark: '上半年停用合作',
    },
  },
];

interface CooperationModalProps {
  visible: boolean;
  school: SchoolItem;
  onVisibleChange: (visible: boolean) => void;
  isHiddenStaff?: boolean;
  defaultActiveKey?: string;
}

const CooperationModal: React.FC<CooperationModalProps> = ({
  isHiddenStaff = false,
  defaultActiveKey = 'staff',
  visible,
  school,
  onVisibleChange,
}) => {
  const { trigger: updateStaff, error } = useSWRMutation(
    '/api/v1/userSchool/createStaff',
    post<
      {
        opUserIDs: string;
        schoolID: number;
      },
      {
        opUserIDs: string;
        schoolID: number;
      }
    >,
  );
  // 获取所有用户列表
  const { data: { list: allUsers } = { list: [] } } = useSWR(
    visible ? '/api/v1/user/list?page=1&pageSize=100&userEmploymentStatus=1&isOnlyAgent=1' : null,
    fetcher<API.TableData<API.UserInfo>>,
    {
      revalidateOnFocus: false,
    },
  );

  // 获取所有用户列表
  const { data: approvalList, isLoading } = useSWR(
    visible ? '/api/v1/commonApproval/listApprovalBySchool?schoolID=' + school?.schoolId : null,
    fetcher<ApprovalDetail[]>,
    {
      revalidateOnFocus: false,
    },
  );

  // 获取已绑定的运营人员列表
  const { data: boundStaff, mutate: mutateStaff } = useSWR(
    visible ? '/api/v1/userSchool/listStaff?schoolID=' + school?.schoolId : '',
    fetcher<{ userIDs: number[] }>,
    {
      revalidateOnFocus: false,
    },
  );

  const [selectedOperators, setSelectedOperators] = useState<{ label: string; value: string }[]>(
    [],
  );
  const [isNeedSave, setIsNeedSave] = useState(false);

  useEffect(() => {
    if (error) {
      message.error(error.message);
    }
  }, [error, message]);
  // 转换用户列表为Select选项格式
  const userOptions = useMemo(
    () =>
      allUsers?.map((user) => ({
        ...user,
        label: `${user.userName}`,
        value: String(user.userId),
      })) || [],
    [allUsers],
  );

  useEffect(() => {
    if (boundStaff?.userIDs && userOptions.length > 0) {
      const initialSelected = userOptions
        .filter((option) => boundStaff?.userIDs.includes(Number(option.value)))
        .map((option) => ({ label: option.label, value: option.value }));

      setSelectedOperators(initialSelected);
    }
  }, [boundStaff, userOptions]); // 依赖 userOptions 确保数据就绪
  // 过滤掉已绑定的用户
  const availableOptions = userOptions.filter(
    (option) => !boundStaff?.userIDs.includes(Number(option.value)),
  );

  // 处理人员选择
  const handleOperatorSelect = (value: string, option: any) => {
    const newOperator = { label: option.label, value: option.value };
    if (!selectedOperators.find((op) => op.value === value)) {
      setIsNeedSave(true);
      setSelectedOperators([...selectedOperators, newOperator]);
    }
  };

  // 处理人员移除
  const handleOperatorRemove = (value: string) => {
    setIsNeedSave(true);
    setSelectedOperators(selectedOperators.filter((op) => op.value !== value));
  };

  // 处理弹窗关闭
  const handleCancel = () => {
    if (isNeedSave) {
      // 二次弹窗确认
      Modal.confirm({
        title: '确认关闭',
        content: '当前未保存数据，是否确认关闭',
        onOk: () => {
          setSelectedOperators([]);
          onVisibleChange(false);
          setIsNeedSave(false);
        },
      });
    } else {
      setSelectedOperators([]);
      onVisibleChange(false);
      setIsNeedSave(false);
    }
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!school?.schoolId) {
      return;
    }

    try {
      await updateStaff({
        opUserIDs: selectedOperators.map((op) => op.value).join(','),
        schoolID: school.schoolId,
      });
      message.success('绑定成功');

      // 刷新已绑定人员列表
      await mutateStaff();
      setIsNeedSave(false);
      setSelectedOperators([]);
      onVisibleChange(false);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Modal
      title="变更合作信息"
      open={visible}
      onCancel={handleCancel}
      width={800}
      destroyOnHidden
      footer={null}
    >
      <Tabs defaultActiveKey={defaultActiveKey}>
        {!isHiddenStaff && (
          <TabPane tab="人员设置" key="staff">
            <ProForm
              className="mt-3"
              submitter={{
                searchConfig: {
                  submitText: '确认',
                  resetText: '取消',
                },
                onReset: handleCancel,
                render: (props, dom) => {
                  return <Space style={{ float: 'right' }}>{dom}</Space>;
                },
              }}
              onFinish={handleSubmit}
            >
              <ProForm.Item
                label="运营人员"
                name="operators"
                style={{ marginBottom: 32 }}
                extra={
                  <Text
                    type="secondary"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginTop: 18,
                      marginBottom: 18,
                    }}
                  >
                    <InfoCircleOutlined style={{ marginRight: 4 }} />
                    运营人员可查看和管理该学校的所有数据
                  </Text>
                }
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Select
                    showSearch
                    placeholder="请输入关键词搜索"
                    style={{ width: '100%' }}
                    options={availableOptions}
                    onSelect={handleOperatorSelect}
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                  <Space wrap style={{ marginTop: 8 }}>
                    {selectedOperators.map((op) => (
                      <Tag key={op.value} closable onClose={() => handleOperatorRemove(op.value)}>
                        {op.label}
                      </Tag>
                    ))}
                  </Space>
                </Space>
              </ProForm.Item>
            </ProForm>
          </TabPane>
        )}
        <TabPane tab="发起合作审批" key="approval">
          {school?.schoolId && (
            <CooperationForm
              onCancel={handleCancel}
              schoolId={school?.schoolId}
              schoolName={school?.schoolName}
            />
          )}
        </TabPane>
        <TabPane tab="历史记录" key="history">
          <HistoryList dataSource={approvalList || []} loading={isLoading} />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default CooperationModal;
