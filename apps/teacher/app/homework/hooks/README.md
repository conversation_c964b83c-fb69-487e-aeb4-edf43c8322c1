# 作业报告 Hooks

## useHomeworkFilterPersistence

### 功能描述
作业报告筛选条件持久化管理 Hook，提供安全的本地存储功能，结合用户ID确保数据隔离。

### 主要特性
- **用户隔离**: 基于用户ID创建独立的存储键，确保不同用户数据不会互相影响
- **数据持久化**: 自动保存和恢复筛选条件，包括类型、学科、班级、时间范围
- **错误处理**: 完善的异常捕获和错误日志记录
- **类型安全**: 完整的 TypeScript 类型定义

### 持久化数据结构
```typescript
interface HomeworkFilterData {
  taskType: TASK_TYPE;    // 作业类型
  subject: number;        // 学科ID
  groupId: number;        // 班级ID
  startDate: string;      // 开始日期 (yyyy-MM-dd)
  endDate: string;        // 结束日期 (yyyy-MM-dd)
}
```

### API 接口
```typescript
interface HomeworkFilterPersistence {
  getFilterLocalStorageKey: () => string;
  getFilterFromLocalStorage: () => HomeworkFilterData | null;
  setFilterToLocalStorage: (filter: Partial<HomeworkFilterData>) => void;
  clearFilterFromLocalStorage: () => void;
}
```

### 使用示例
```typescript
const filterPersistence = useHomeworkFilterPersistence();

// 获取持久化数据
const savedFilter = filterPersistence.getFilterFromLocalStorage();

// 保存筛选条件
filterPersistence.setFilterToLocalStorage({
  taskType: TASK_TYPE.HOMEWORK,
  subject: 1,
  groupId: 123
});

// 清除持久化数据
filterPersistence.clearFilterFromLocalStorage();
```

### 存储键格式
- 格式: `homework-filter-{userID}`
- 示例: `homework-filter-12345`

### 默认值策略
当没有持久化数据时，使用以下默认值：
- `taskType`: 0 (全部类型)
- `subject`: 0 (将由组件逻辑设置为用户主学科)
- `groupId`: 0 (全部班级)
- `startDate`: 7天前
- `endDate`: 今天

### 集成说明
该 Hook 已集成到作业报告的 Context 中：
1. **初始化**: 页面加载时自动读取持久化数据
2. **自动保存**: 用户操作筛选条件时自动保存
3. **数据恢复**: 下次访问时自动恢复上次的筛选状态

### 安全性
- 基于用户ID的数据隔离
- 异常处理防止数据损坏
- 只持久化筛选相关的核心数据