import { useApp } from "@/hooks";
import { cn } from "@/utils/utils";

export default function FixedLayout({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const { statusBarHeight } = useApp();
  // 保持zindex > 50，高于drawer就可以
  return (
    <div
      className={cn("fixed left-0 top-0 z-50 h-full w-full", className)}
      style={{
        height: `calc(100vh - ${statusBarHeight}px)`,
        top: `${statusBarHeight}px`,
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {children}
    </div>
  );
}
