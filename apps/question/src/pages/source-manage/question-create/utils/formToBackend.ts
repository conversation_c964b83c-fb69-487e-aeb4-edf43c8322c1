/**
 * 表单数据转换为后端接口格式的工具函数
 */

/**
 * 从表单选项结构中提取真实的选项数据
 * @param questionOptionMatrix 表单中的选项矩阵
 * @param questionAnswerMode 题目类型
 * @returns 后端需要的选项矩阵格式
 */
const extractQuestionOptions = (questionOptionMatrix: any[], questionAnswerMode: number): any[] => {
  if (!questionOptionMatrix?.length) return [];

  // 填空题、简答题等非选择题类型应该返回空数组
  if (questionAnswerMode === 3 || questionAnswerMode === 5) {
    return [];
  }

  return questionOptionMatrix
    .map((optionRow) => {
      // 处理判断题
      if (questionAnswerMode === 4) {
        // 判断题固定返回 true 和 false 两个选项
        return [
          { optionKey: 'true', optionVal: '' },
          { optionKey: 'false', optionVal: '' },
        ];
      }

      // 如果是空对象（综合题），直接返回空数组
      if (!optionRow.singleChoice && !optionRow.multipleChoice) {
        return [];
      }

      let items = [];
      if (questionAnswerMode === 1) {
        items = optionRow.singleChoice?.items || [];
      }

      if (questionAnswerMode === 2) {
        items = optionRow.multipleChoice?.items || [];
      }

      // 从 singleChoice 或 multipleChoice 中提取 items
      // const items = optionRow.singleChoice?.items || optionRow.multipleChoice?.items || [];

      // 转换格式：{ key, value } -> { optionKey, optionVal }
      return items.map((item: any) => ({
        optionKey: item.key,
        optionVal: item.value,
      }));
    })
    .filter((row) => row.length > 0); // 过滤掉空数组
};

/**
 * 从表单选项结构中提取答案数据
 * @param questionOptionMatrix 表单中的选项矩阵
 * @param questionAnswerMode 题目类型
 * @returns 后端需要的答案矩阵格式
 */
const extractAnswerOptions = (questionOptionMatrix: any[], questionAnswerMode: number): any[] => {
  if (!questionOptionMatrix?.length) return [];

  return questionOptionMatrix
    .map((optionRow) => {
      // 处理填空题
      if (questionAnswerMode === 3) {
        // 填空题只需要返回 fillBlank 的值
        return [{ optionKey: '', optionVal: optionRow.fillBlank || '' }];
      }

      // 处理判断题
      if (questionAnswerMode === 4) {
        // 判断题从 judge 字段获取答案
        const judgeValue = optionRow.judge;
        if (judgeValue !== undefined && judgeValue !== null && judgeValue !== '') {
          return [{ optionKey: judgeValue.toString(), optionVal: '' }];
        }
        // 如果没有答案，返回空答案
        return [{ optionKey: '', optionVal: '' }];
      }

      // 处理简答题
      if (questionAnswerMode === 5 && optionRow.shortAnswer !== undefined) {
        return [{ optionKey: '', optionVal: optionRow.shortAnswer || '' }];
      }

      // 处理选择题（单选/多选）
      if (optionRow.singleChoice || optionRow.multipleChoice) {
        let selectedKeys: string[] = [];

        if (questionAnswerMode === 2) {
          // 多选题
          selectedKeys = optionRow.multipleChoice?.selectedKey || [];
        } else {
          // 单选题
          const singleKey = optionRow.singleChoice?.selectedKey;
          selectedKeys = singleKey ? [singleKey] : [];
        }

        // 转换为答案格式
        return selectedKeys.map((key) => ({
          optionKey: key,
          optionVal: '',
        }));
      }

      return [];
    })
    .filter((row) => row.length > 0); // 过滤掉空数组
};

/**
 * 转换单个题目的表单数据为后端格式
 * @param formData 单个题目的表单数据
 * @returns 后端接口格式的题目数据
 */
const transformSingleQuestionToBackend = (
  formData: any,
  type?: 'mainQuestion' | 'subQuestion',
): any => {
  if (!formData) return {};

  const {
    questionStem,
    questionAnswerMode,
    isMatchLine,
    questionId,
    questionDifficult,
    questionExplanation,
    baseTreeNodeIds,
    questionOptionMatrix = [],
    subQuestionList = [],
    questionStatus,
    questionYear,
    region,
    labelQuestionType,
  } = formData;

  // 基础字段
  let backendData: any = {
    questionId: questionId,
    questionAnswerMode: questionAnswerMode || 1,
    questionStatus: questionStatus,
    subQuestionList: subQuestionList,
  };
  if (type === 'mainQuestion') {
    const [provinceCode, cityCode, areaCode] = region || [];
    backendData = {
      ...backendData,
      labelQuestionType: labelQuestionType,
      questionDifficult: questionDifficult,
      questionExplanation: questionExplanation,
      baseTreeNodeIds: baseTreeNodeIds,
      questionYear: questionYear ?? null,
      provinceCode: provinceCode || 0,
      cityCode: cityCode || 0,
      areaCode: areaCode || 0,
    };
  }

  // 处理主题目
  if (questionAnswerMode === 0) {
    // 综合题：题干在根级别，选项矩阵为空对象
    backendData.questionContent = {
      questionStem: questionStem || '',
      isMatchLine: isMatchLine ? 1 : 0,
      questionOptionMatrix: [],
    };
  } else {
    // 普通题目：题干和选项都在 questionContent 中
    backendData.questionContent = {
      questionStem: questionStem || '',
      isMatchLine: isMatchLine ? 1 : 0,
      questionOptionMatrix: extractQuestionOptions(questionOptionMatrix, questionAnswerMode),
    };

    // 添加答案信息
    const answerMatrix = extractAnswerOptions(questionOptionMatrix, questionAnswerMode);
    if (answerMatrix.length > 0) {
      backendData.questionAnswer = {
        answerOptionMatrix: answerMatrix,
      };
    }
  }

  // 递归处理子题目
  if (subQuestionList?.length > 0) {
    backendData.subQuestionList = subQuestionList.map((subQuestion: any) =>
      transformSingleQuestionToBackend({ ...subQuestion, questionStatus: questionStatus }),
    );
  }

  return backendData;
};

/**
 * 将表单数据转换为后端接口格式
 * @param formData 表单数据
 * @returns 后端接口格式的数据
 */
export const transformFormDataToBackend = (formData: any) => {
  return transformSingleQuestionToBackend(formData, 'mainQuestion');
};

/**
 * 批量转换表单数据为后端格式
 * @param formDataList 表单数据列表
 * @returns 后端接口格式的数据列表
 */
export const transformFormDataListToBackend = (formDataList: any[]) => {
  return formDataList.map((formData) => transformFormDataToBackend(formData));
};
