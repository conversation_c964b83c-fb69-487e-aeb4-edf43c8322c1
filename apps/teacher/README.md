# Schoolroom 教师端应用 

这是 Schoolroom 课堂管理系统的教师端应用，基于 Next.js 构建。

## 注意事项

- 本应用是 Schoolroom monorepo 的一部分，使用了共享的配置和依赖
- 开发时请遵循项目统一的代码规范和 Git 提交规范
- 如需修改共享组件，请在 `packages/ui` 中进行
- **注意**: 如非必要，请勿使用 `any` 类型，请勿直接使用 `useEffect` `useUpdateEffect`等 effect 相关 hook，生命周期相关请用 `ahooks` 提供的hook

## 项目结构

```
teacher/
├── app/          # Next.js 应用核心目录 (路由、页面、布局)
├── components/   # 通用的业务组件 (页面自用的组件放到页面下_component目录)
├── configs/      # 应用配置文件 (如 API 地址、业务配置)
├── enums/        # 全局枚举
├── hooks/        # 自定义 React Hooks
├── libs/         # 第三方库封装或适配层 (axios, mitt 等)
├── services/     # API 服务层 (基于 libs/axios 的具体业务请求)
├── stores/       # 状态管理 (当前主要用于本地存储, 目前还没启用)
├── types/        # TypeScript 类型定义
├── ui/           # 基于 shadcn/ui 的原子 UI 组件
├── utils/        # 工具函数
└── public/       # 静态资源文件
```

## 开发环境要求

- Node.js 18.0.0 或更高版本
- pnpm 8.0.0 或更高版本

## 本地开发

1. 安装依赖：

```bash
pnpm install
```

2. 启动开发服务器：

```bash
pnpm dev -F=teacher
```

应用将在 [http://localhost:3030](http://localhost:3030) 启动。

## 构建部署

构建生产版本：

```bash
pnpm build
```

## 技术栈

- Next.js - React 框架
- TypeScript - 静态类型检查
- Tailwind CSS - 样式解决方案
- shadcn/ui & 自定义组件库 - UI 解决方案

## 核心库封装

为了保证应用架构的统一性和可维护性，我们对一些核心的第三方库进行了封装。**原则上，业务开发中应优先使用我们封装后的模块，而不是直接使用原始库。**

### API 请求与数据获取(`libs/axios.ts`)

- **底层封装 (`libs/axios.ts`)**: 我们基于 `axios` 封装了统一的请求实例 `r`，集中处理了请求/响应拦截、Token注入、全局错误提示和认证过期等通用逻辑。所有的业务请求都应定义在 `services/` 目录下，并基于此实例创建。

- **组件内调用 (`useRequest`)**:
  - **原则**: 为了规范组件内的数据请求、管理加载状态（loading）、处理错误（error）以及优化性能，所有在 React 组件中对 `services` 层的接口调用都 **必须** 使用 `ahooks` 提供的 `useRequest` Hook 进行包裹。**禁止**在组件中直接调用 `services` 里的异步函数（例如：`getCourseTable(params).then(...)`）。
  - **解决竞态条件**: 在处理例如搜索框快速输入这类场景时，短时间内会触发多次请求，手动处理（如在 `useEffect` 中）容易引发"竞态条件"——即先发出的请求可能晚于后发出的请求返回，导致展示了错误的数据。`useRequest` 内部自动处理了这种情况，会取消掉前一次未完成的请求，确保组件只会接收到最后一次请求的数据，无需开发者手动编写复杂的清理函数。更多关于此问题的背景，可以参考 [React 官方文档关于数据获取的说明](https://zh-hans.react.dev/learn/you-might-not-need-an-effect#fetching-data)。
  - **使用示例**:
    ```tsx
    import { useRequest } from 'ahooks';
    import { getCourseTable } from '@/services/course'; // 导入 service 方法

    // 在组件中:
    const getCourseTableRequest = useRequest(
      async () => {
        const params = {
          teacher_id: '...',
          school_id: '...',
          start_date: '...',
          end_date: '...',
        };

        return getCourseTable(params);
      },
      {
        debounceWait: 500,
        loadingDelay: 500,
      }
    );
    ```
  - **推荐**: 充分利用 `useRequest` 提供的缓存、轮询、防抖、节流等高级功能来优化用户体验和性能。

### 全局事件总线 (`libs/mitt.ts`)

- **功能**: 基于 `mitt` 库实现了一个全局的、类型安全的事件总线。
- **目的**: **仅用于 React 组件与外部系统（如 `axios` 拦截器）之间的通信。** 例如，当 `axios` 拦截器捕获到401错误时，会发出一个全局事件，由布局组件中的 Hook 监听并执行页面跳转。
- **使用限制**: **严禁使用此事件总线进行 React 组件之间的通信。** 组件间通信应遵循 React 的标准模式（Props、Context、或状态管理库）。
- **注意**: **如非必要，请勿直接使用 `mitt` 库，React组件优先使用封装好的 hook`useEmitter`，非 React组件优先使用封装好的实例`emitter`。**

## UI 组件 (`ui/`)

项目中的所有原子 UI 组件均收录于此目录，基于 `shadcn/ui` 的风格和实现，并结合项目实际需求进行了定制。当需要通用、无业务逻辑的 UI 元素时，应首先考虑在此目录中寻找或创建。

### `ScrollArea`

- **目的**: 提供一个统一的、跨浏览器兼容的滚动容器组件，以替代原生浏览器滚动条，确保在不同环境下视觉体验的一致性。
- **背景**: 基于 `@radix-ui/react-scroll-area` 封装，提供了样式化和一致性的滚动体验。
- **使用**:
  ```tsx
  import { ScrollArea } from "@/ui/scroll-area";

  const MyComponent = () => (
    <ScrollArea className="h-50 rounded-md border p-4">
      Jokester began sneaking into the castle in the middle of the night and leaving
      jokes all over the place: under the king's pillow, in his soup, even in the
      royal toilet. The king was furious, but he couldn't seem to stop Jokester.
    </ScrollArea>
  );
  ```
- **注意**: 当需要为内容区域（例如长列表、大段文本）提供滚动功能时，应优先使用此组件，以保证视觉和体验的统一性。

## 相关文档

- [项目文档](../../README.md)
- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [shadcn/ui 文档](https://ui.shadcn.com)
