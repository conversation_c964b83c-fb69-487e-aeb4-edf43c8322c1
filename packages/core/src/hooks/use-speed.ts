import { PlayerRef } from "@remotion/player";
import { useCallback, useEffect, useState } from "react";

interface UseSpeedOptions {
  playerRef: React.RefObject<PlayerRef | null>;
  defaultRate?: number;
  steps?: number[];
  onChange?: (rate: number) => void;
}

interface RemotionPlayerInstance extends PlayerRef {
  playbackRate: number;
}

// 格式化速率的辅助函数
const formatRate = (rate: number): string => {
  // 如果是整数，强制显示一位小数
  if (Number.isInteger(rate)) {
    return rate.toFixed(1);
  }
  // 其他情况保持原始值
  return rate.toString();
};

export const useSpeed = ({
  playerRef,
  defaultRate = 1.0,
  steps = [0.75, 1.0, 1.25, 1.5, 2.0],
  onChange,
}: UseSpeedOptions) => {
  // 存储格式化后的字符串值
  const [currentRate, setCurrentRate] = useState(formatRate(defaultRate));

  const setRate = useCallback(
    (rate: number) => {
      if (!playerRef.current) return;

      try {
        const closestRate = steps.reduce((prev, curr) =>
          Math.abs(curr - rate) < Math.abs(prev - rate) ? curr : prev
        );

        (playerRef.current as RemotionPlayerInstance).playbackRate =
          closestRate;
        // 格式化后存储
        setCurrentRate(formatRate(closestRate));
        onChange?.(closestRate);
      } catch (error) {
        console.error("更新播放速率失败:", error);
      }
    },
    [playerRef, steps, onChange]
  );

  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handleRateChange = () => {
      const newRate = (player as RemotionPlayerInstance).playbackRate;
      // 格式化后存储
      setCurrentRate(formatRate(newRate));
      onChange?.(newRate);
    };

    setRate(defaultRate);

    player.addEventListener("ratechange", handleRateChange);
    return () => {
      player.removeEventListener("ratechange", handleRateChange);
    };
  }, [playerRef, defaultRate, setRate, onChange]);

  return {
    currentRate, // 现在返回的是格式化后的字符串
    availableRates: steps.map((rate) => formatRate(rate)), // 格式化所有可用速率
    setRate,
    isDefaultRate: currentRate === formatRate(defaultRate),
  };
};
