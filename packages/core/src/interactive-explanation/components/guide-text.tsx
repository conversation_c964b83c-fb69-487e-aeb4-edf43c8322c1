"use client";

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';
import interactiveIp from '@repo/core/public/assets/interactive-explanation/interactive-ip.png';

interface GuideTextProps {
  text: string;
  className?: string;
}

export const GuideText: React.FC<GuideTextProps> = ({
  text,
  className
}) => {
  return (
    <div>

      <div className={cn(
        "guide-text relative inline-flex items-center justify-center gap-2.5 bg-[#F7F6F5] rounded-[3.75rem_3.75rem_3.75rem_0.25rem] pl-[4.125rem] pr-6 py-3 w-max-content mb-6",
        className
      )}>
        {/* 虚拟导师头像 */}
        <div className="absolute left-0 ">
          <div className="relative w-full h-full">
            {/* 背景遮罩 */}
            <div className="absolute inset-0  rounded-full" />

            {/* 导师头像 */}
            <div className="absolute  w-16 h-18 bottom-0 translate-y-[37%]">
              <Image
                src={interactiveIp}
                alt="虚拟导师"
                width={65}
                height={65}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* 引导文本 */}
        <div className="guide-text-content w-max-content">
          <span className="text-[17px] leading-[1.75em] text-[rgba(51,48,45,0.85)] font-normal whitespace-nowrap">
            {text}
          </span>
        </div>
      </div>
    </div>

  );
};

export default GuideText;
