import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { isTeaching } from "@/services/course";
import { useRequest } from "ahooks";

export function CourseNav({
  refreshCourseTable,
}: {
  refreshCourseTable: () => void;
}) {
  const { userInfo } = useApp();

  const { data: teachingStatus } = useRequest(
    async () => {
      const params = {
        teacher_id: userInfo?.userID.toString() ?? "",
        school_id: userInfo?.currentSchoolID.toString() ?? "",
      };

      return isTeaching(params);
    },
    {
      pollingInterval: 60000,
      ready: !!userInfo,
      onBefore() {
        refreshCourseTable();
      },
    }
  );

  return (
    <PageHeader className="pl-0">
      <div className="border-line-1 flex h-full flex-1 items-center gap-2 border-b">
        <div className="text-gray-1 text-xl font-medium tracking-[1px]">
          Hi {userInfo?.userName.substring(0, 1)}老师
        </div>

        <div className="flex items-center gap-2 text-xs">
          {teachingStatus?.is_teaching && (
            <>
              <div className="bg-green-1 h-4.5 flex items-center gap-1 rounded-sm px-1.5">
                <div className="bg-fill-white h-1 w-1 rounded-full" />
                <span className="text-xs font-medium text-white">上课中</span>
              </div>

              {teachingStatus.schedule && (
                <div className="flex items-center gap-1">
                  <span className="text-gray-2">
                    {teachingStatus.schedule.grade_name +
                      teachingStatus.schedule.class_name}
                  </span>
                  <div className="bg-primary-4 h-1.5 w-px opacity-50" />
                  <span className="text-gray-2">
                    {teachingStatus.schedule.class_schedule_course}
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </PageHeader>
  );
}
