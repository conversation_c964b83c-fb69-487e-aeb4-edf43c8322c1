import Link from "next/link";
import { FC, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface BorderButtonProps {
  bgColor?: string;
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
}

const BorderButton: FC<BorderButtonProps> = ({
  bgColor,
  children,
  className = "",
  onClick,
  href,
}) => {
  const baseStyles =
    "flex flex-row items-center justify-center gap-2 rounded-full border-4 border-white px-6 py-4 shadow-[0px_8px_32px_0px_rgba(35,42,64,0.08)]";

  const buttonStyle = {
    backgroundColor: bgColor,
  };

  // 如果提供了href，则渲染为链接
  if (href) {
    return (
      <Link
        href={href}
        className={twMerge(baseStyles, className)}
        style={buttonStyle}
      >
        {children}
      </Link>
    );
  }

  // 否则渲染为普通的div
  return (
    <div
      className={twMerge(className, baseStyles)}
      style={buttonStyle}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default BorderButton;
