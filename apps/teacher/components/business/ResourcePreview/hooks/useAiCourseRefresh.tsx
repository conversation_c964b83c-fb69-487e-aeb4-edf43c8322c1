import { useApp } from "@/hooks";
import { Button } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
import { AiCoursePreviewHandler } from "@repo/core/components/ai-course-preview";
import { useCallback, useMemo, useRef, useState } from "react";

export type UseAiCourseRefreshConfig = {
  className?: string;
  fullscreenClassName?: string;
};

export default function useAiCourseRefresh(
  disableFixed?: boolean,
  { className, fullscreenClassName }: UseAiCourseRefreshConfig = {}
) {
  const aiCoursePreviewHandlerRef = useRef<AiCoursePreviewHandler | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { statusBarHeight } = useApp();

  const handleEnterFullscreen = () => {
    setIsFullscreen(true);
  };
  const handleExitFullscreen = () => {
    setIsFullscreen(false);
  };

  const refreshWidget = useCallback(() => {
    aiCoursePreviewHandlerRef.current?.refresh();
  }, []);

  const headerSuffixNode = useMemo(() => {
    return (
      <div
        className={cn(
          disableFixed ? "duration-800 pointer-events-none opacity-0" : "",
          "right-38 z-999999 fixed top-[1.375rem] flex items-center overflow-visible transition-opacity duration-300 ease-in-out",
          isFullscreen ? fullscreenClassName : className
        )}
      >
        <Button
          type="outline"
          size="lg"
          radius="full"
          className={cn("bg-fill-light relative px-4 ease-in-out")}
          style={{
            top: `calc(${statusBarHeight}px - 0.3125rem)`,
            display: isFullscreen ? "none" : "block",
          }}
          onClick={refreshWidget}
        >
          刷新
        </Button>
        <Button
          type="default"
          size="md"
          radius="full"
          className={cn(
            "relative h-7 items-center rounded-[5.6px] px-2.5 text-xs font-normal active:bg-white active:opacity-60"
          )}
          style={{
            boxShadow: "0px 2.8px 11.2px 0px rgba(35, 42, 64, 0.05)",
            top: `0.0625rem`,
            display: isFullscreen ? "block" : "none",
          }}
          onClick={refreshWidget}
        >
          刷新
        </Button>
      </div>
    );
  }, [
    disableFixed,
    className,
    fullscreenClassName,
    statusBarHeight,
    isFullscreen,
    refreshWidget,
  ]);

  return {
    handlerRef: aiCoursePreviewHandlerRef,
    onEnterFullscreen: handleEnterFullscreen,
    onExitFullscreen: handleExitFullscreen,
    headerSuffixNode,
  };
}
