import { request } from '@umijs/max';

export async function fetchBaseTreeList(
  params: API.BaseTreeListRequestParams,
): Promise<API.BaseTreeListResponse> {
  return request('/api/v1/base/data/base/tree/list', {
    method: 'POST',
    data: params,
  });
}

export async function fetchBaseTreeDetail(baseTreeId: number): Promise<API.BaseTreeDetailResponse> {
  return request('/api/v1/base/data/base/tree/detail', {
    method: 'GET',
    params: { baseTreeId },
  });
}
