import { Button } from "@/app/components/common/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/common/select";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { formatTime } from "@repo/lib/utils/time";
import { Progress } from "@repo/ui/components/progress";
import { Pause, Play } from "lucide-react";
import { FC, useCallback, useEffect, useRef } from "react";

interface PlayerControlsProps {
  ref: React.RefObject<HTMLMediaElement | null>;
  defaultPlaybackRate?: number;
  onPlay?: () => void;
  onPause?: () => void;
  onProgressChange?: (value: number) => void;
  onPlaybackRateChange?: (value: number) => void;
}

const playbackRateOptions = [0.5, 0.7, 1, 1.2, 1.5, 1.7, 2];

const PlayerControls: FC<PlayerControlsProps> = ({
  ref,
  onPlay,
  onPause,
  onProgressChange,
  onPlaybackRateChange,
  defaultPlaybackRate = 1,
}: PlayerControlsProps) => {
  const playbackRate = useSignal(defaultPlaybackRate);
  const currentTime = useSignal(0);
  const totalTime = useSignal(0);
  const isPlaying = useSignal(false);

  const progressRef = useRef<HTMLDivElement>(null);
  const progress = useComputed(() => {
    return totalTime.value > 0
      ? (currentTime.value / totalTime.value) * 100
      : 0;
  });

  useEffect(() => {
    const mediaElement = ref.current;
    if (!mediaElement) return;

    // 获取总时长
    const updateTotalTime = () => {
      totalTime.value = mediaElement.duration || 0;
    };

    // 获取当前播放时间
    const updateCurrentTime = () => {
      currentTime.value = mediaElement.currentTime || 0;
    };

    // 监听元数据加载完成事件，获取总时长
    mediaElement.addEventListener("loadedmetadata", updateTotalTime);

    // 监听时间更新
    const timer = setInterval(() => {
      updateCurrentTime();
    }, 50);

    // 监听播放状态变化
    const handlePlay = () => {
      isPlaying.value = true;
    };
    const handlePause = () => {
      isPlaying.value = false;
    };
    mediaElement.addEventListener("play", handlePlay);
    mediaElement.addEventListener("pause", handlePause);

    // 初始化
    updateTotalTime();
    updateCurrentTime();
    isPlaying.value = !mediaElement.paused;

    return () => {
      mediaElement.removeEventListener("loadedmetadata", updateTotalTime);
      mediaElement.removeEventListener("play", handlePlay);
      mediaElement.removeEventListener("pause", handlePause);
      clearInterval(timer);
    };
  }, [ref, totalTime, currentTime, isPlaying]);

  // 处理播放/暂停按钮点击
  const handlePlayPause = useCallback(() => {
    if (!ref.current) return;

    const mediaElement = ref.current;

    if (isPlaying.value) {
      mediaElement.pause();
      onPause?.();
    } else {
      mediaElement.play();
      onPlay?.();
    }
  }, [ref, isPlaying, onPause, onPlay]);

  // 处理进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current || !progressRef.current) return;
    const mediaElement = ref.current;
    const rect = progressRef.current.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    const newTime = pos * totalTime.value;

    mediaElement.currentTime = newTime;
    onProgressChange?.(pos);
  };

  // 处理播放速度变化
  const handleSpeedChange = useCallback(
    (speed: string) => {
      const rate = parseFloat(speed);
      playbackRate.value = rate;
      onPlaybackRateChange?.(rate);
    },
    [playbackRate, onPlaybackRateChange]
  );

  useSignalEffect(() => {
    const mediaElement = ref.current;
    if (!mediaElement) return;

    mediaElement.playbackRate = playbackRate.value;
  });

  return (
    <div className="flex w-full flex-col items-end px-2">
      <div className="text-xs">
        {formatTime(currentTime.value)} / {formatTime(totalTime.value)}
      </div>

      <div className="-my-2 flex w-full flex-row items-center">
        <Button
          icon={
            isPlaying.value ? (
              <Pause fill="currentColor" size={16} />
            ) : (
              <Play fill="currentColor" size={16} />
            )
          }
          className="bg-primary size-8 rounded-full"
          type="primary"
          onClick={handlePlayPause}
        />

        <div
          ref={progressRef}
          className="flex h-8 flex-1 cursor-pointer items-center justify-center"
          onClick={handleProgressClick}
        >
          <Progress
            value={progress.value}
            max={100}
            className="*:bg-primary/80 -ml-0.5 h-1 rounded-none"
          />
        </div>
      </div>

      <Select
        defaultValue={playbackRate.toString()}
        onValueChange={handleSpeedChange}
      >
        <SelectTrigger className="text-xs">
          <SelectValue>{playbackRate.value}X</SelectValue>
        </SelectTrigger>
        <SelectContent>
          {playbackRateOptions.map((speed) => (
            <SelectItem
              className="text-xs"
              key={speed}
              value={speed.toString()}
            >
              {speed}X
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export { playbackRateOptions, PlayerControls };
