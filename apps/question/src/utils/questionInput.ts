// 获取审核不通过的题目
export const getFailQuestions = (exerciseInfo: API.ExerciseQuestionListData | undefined) => {
  // if (!exerciseInfo || !exerciseInfo.list || exerciseInfo.curAuditTaskStatus !== 3) {
  //     return [];
  // }
  // return exerciseInfo.list.filter((item: API.ExerciseQuestionListItem) => {
  //     const { auditStatus, isProcAuditFailed } = item;
  //     return auditStatus === 3 && isProcAuditFailed === 0;
  // });
  console.log('exercise info: ', exerciseInfo);
  return [];
};

// 获取内容有变更的题目
export const getChangedQuestions = (exerciseInfo: API.ExerciseQuestionListData | undefined) => {
  if (
    !exerciseInfo ||
    exerciseInfo.shelfStatus !== 1 ||
    exerciseInfo.curAuditTaskStatus === 1 ||
    exerciseInfo.curAuditTaskStatus === 2
  ) {
    return [];
  }
  const { list = [] } = exerciseInfo;
  return list.filter((item) => {
    const { auditStatus, addDelStatus } = item;
    return (addDelStatus === 2 || addDelStatus === 1) && auditStatus !== 2;
  });
};
