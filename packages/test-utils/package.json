{"name": "@repo/test-utils", "version": "0.0.0", "private": true, "description": "Layered architecture testing utilities", "bin": {"repo-test-models": "./scripts/test-models.js", "repo-test-api": "./scripts/test-api.js"}, "exports": {"./model": {"types": "./dist/model/index.d.ts", "import": "./dist/model/index.mjs", "require": "./dist/model/index.js"}, "./api": {"types": "./dist/api/index.d.ts", "import": "./dist/api/index.mjs", "require": "./dist/api/index.js"}, "./view": {"types": "./dist/view/index.d.ts", "import": "./dist/view/index.mjs", "require": "./dist/view/index.js"}, "./viewmodel": {"types": "./dist/viewmodel/index.d.ts", "import": "./dist/viewmodel/index.mjs", "require": "./dist/viewmodel/index.js"}, "./context": {"types": "./dist/context/index.d.ts", "import": "./dist/context/index.mjs", "require": "./dist/context/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@testing-library/react": "^14.1.2", "@types/react": "^18.2.45", "next": "15.3.2", "react": "^18.2.0", "swr": "^2.2.4", "tsup": "^8.0.1", "typescript": "^5.3.3", "vitest": "^1.1.0"}, "peerDependencies": {"@testing-library/react": ">=14.0.0", "react": ">=18.0.0", "swr": ">=2.0.0", "vitest": ">=1.0.0", "zod": ">=3.20.0"}}