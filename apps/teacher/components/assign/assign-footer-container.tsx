import { cn } from "@/utils/utils";

interface AssignFooterContainerProps {
  style?: React.CSSProperties;
  className?: string;
  children: React.ReactNode;
}

export function AssignFooterContainer({
  style = {},
  className = "",
  children,
}: AssignFooterContainerProps) {
  return (
    <div
      className={cn(
        "h-17 flex select-none items-center justify-end gap-3 border-t border-slate-200 bg-white px-8 shadow-[0px_8px_32px_0px_rgba(16,18,25,0.10)]",
        className
      )}
      style={style}
    >
      {children}
    </div>
  );
}
