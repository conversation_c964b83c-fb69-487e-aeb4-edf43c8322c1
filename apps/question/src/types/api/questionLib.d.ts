declare namespace API {
  export interface ExerciseQuestionListRequestParams {
    bizTreeNodeId: number;
    sceneCategory: number;
  }
  interface ExerciseQuestionListResponse {
    code: number;
    message: string;
    data: ExerciseQuestionListData;
  }
  interface ExerciseQuestionListData {
    sceneCategory?: number;
    curAuditFailReason?: string;
    curAuditTaskId?: number;
    curAuditTaskStatus?: number;
    curAuditTaskStatusText?: string;
    difficultStat?: ExerciseDifficultStat[];
    list?: ExerciseQuestionGroupListItem[];
    questionSetId?: number;
    isQuestionSetModified?: number;
    /**
     * 每个题集所属
     */
    questionVersionId?: number;
    [property: string]: any;
  }

  export interface ExerciseQuestionGroupListItem {
    difficultStat: ExerciseDifficultStat[];
    questionGroupId: number;
    questionGroupName: string;
    questionGroupOrder: number;
    questionStatusInfoList: ExerciseQuestionGroupQuestionItem[];
    [property: string]: any;
  }

  interface ExerciseQuestionGroupQuestionItem {
    questionId: string;
    addDelStatus: number;
    isRecommend: number;
    questionInfo: QuestionItemType;
  }
  interface ExerciseQuestionListItem extends QuestionItemType {
    /**
     * 新增移除状态
     */
    addDelStatus?: number;
    /**
     * 审核状态，通过还是不通过
     */
    auditStatus?: number;
    question_id?: string;
    isProcAuditFailed?: number;
    /**
     * 题目状态都需要加上
     */
    questionStatus?: number;
    questionInfo?: QuestionItemType;
    /**
     * 题组标签
     */
    questionGroupName?: string;
    /**
     * 题组id
     */
    questionGroupId?: number;
    [property: string]: any;
  }
  interface ExerciseDifficultStat {
    difficultName: string;
    difficultNeedQuestionCount: number;
    difficultQuestionCount: number;
    [property: string]: any;
  }

  interface ExerciseQuestionOperateRequestParams {
    questionId: string;
    /**
     * 业务节点题集id，第一次加入题目是非必须的，后续加入题目是必须的
     */
    questionSetId?: number;
    sceneCategory: number;
    bizTreeNodeId?: number;
    questionGroupId?: number;
    [property: string]: any;
  }
  interface ExerciseQuestionRecommendRequestParams {
    questionSetId?: number;
    questionGroupId?: number;
    questionId: string;
    recommend: number;
  }
  interface ExerciseQuestionBatchRecommendRequestParams {
    questionSetId?: number;
    questionGroupId?: number;
    recommend: number;
  }
  interface ExerciseQuestionOperateResponse {
    code: number;
    data: ExerciseQuestionOperateData;
    message: string;
    [property: string]: any;
  }
  interface ExerciseQuestionOperateData {
    /**
     * 课程场景题集id，第一次加入题目生成一个id，后续加入题目会直接返回入参courseSceneQuestionSetId
     */
    questionSetId: number;
    [property: string]: any;
  }

  interface ExerciseOnShelfRequestParams {
    /**
     * 业务树id
     */
    bizTreeNodeId: number;
    /**
     * 题集id
     */
    questionSetId?: number;
    [property: string]: any;
  }

  interface ExerciseOnShelfResponse {
    code: number;
    data: {
      auditTaskId: number;
      questionSetId?: number;
    };
    message: string;
    [property: string]: any;
  }

  interface ExerciseAuditResultHandleRequestParams {
    /**
     * 1 采纳 2 是忽略
     */
    accept?: number;
    auditTaskId?: number;
    questionId: string;
    questionSetId?: number;
    [property: string]: any;
  }

  interface ExerciseAuditResultHandleResponse {
    code: number;
    data: {
      auditTaskId?: number;
      questionSetId?: number;
    };
    message: string;
    [property: string]: any;
  }

  interface ExerciseRevocationAuditResponse {
    code: number;
    message: string;
    [property: string]: any;
  }

  interface ExerciseCopyFromOtherExerciseRequestParams {
    /**
     * 复制的是哪个题集
     */
    fromQuestionSetId: number;
    /**
     * 当前的题集
     */
    questionSetId: number;
  }

  interface ExerciseSearchQuestionSetByIdResponse {
    code: number;
    data: {
      questionSetId: number;
      questionSetBrief: string;
    };
    message: string;
  }

  interface ExerciseQuestionGroupEditRequestParams {
    /**
     * 0代指最前面进行插入，如果有值，则在之后插入。
     */
    afterInsertQuestionGroupId: number;
    questionGroupId: number;
    questionGroupName: string;
    questionSetId: number;
  }

  interface ExerciseQuestionGroupRemoveRequestParams {
    questionGroupId: number;
    questionSetId: number;
  }

  interface ExerciseQuestionGroupCreateRequestParams {
    /**
     * -1代指最前面进行插入，如果有值，则在之后插入。
     */
    afterInsertQuestionGroupId: number;
    questionGroupName: string;
    questionSetId: number;
  }

  interface ExerciseGroupTabItem {
    label: string;
    children: null;
    key: string;
    editable: boolean;
    closable: boolean;
    status?: 0 | 1 | 2;
  }
}
