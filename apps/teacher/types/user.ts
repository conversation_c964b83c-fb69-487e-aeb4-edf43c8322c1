import { JOB_TYPE } from "@/enums";

export interface UserInfo {
  /**
   * 当前登录的学校ID
   */
  currentSchoolID: number;
  schoolInfos: SchoolInfo[];
  /**
   * 教师当前登录的学校的任职信息、学科、班级
   */
  teacherJobInfos: TeacherJobInfo[];
  /**
   * 教师ID
   */
  userID: number;
  /**
   * 教师姓名
   */
  userName: string;
  /**
   * 教师手机号
   */
  userPhone: string;
  /**
   * 教师头像
   */
  avatarUrl: string;
}

export interface SchoolInfo {
  /**
   * 学校备注
   */
  remark: string;
  /**
   * 学校地址
   */
  schoolAddress: string;
  /**
   * 学段：1 小学，2 初中，3 高中
   */
  schoolEduLevel: number;
  /**
   * 学制
   */
  schoolEduSystem: number;
  /**
   * 办学特色：1 初中+高中，2 小学+初中，3 小学+初中+高中
   */
  schoolFeature: number;
  /**
   * 学校ID
   */
  schoolID: number;
  /**
   * 是否测试学校
   */
  schoolIsTest: number;
  /**
   * 学校名称
   */
  schoolName: string;
  /**
   * 办学性质：1 私立，2 公立，3 其它
   */
  schoolNature: number;
  /**
   * 学校编号，例如 SCH44
   */
  schoolNumber: string;
  /**
   * 学校地区ID，省市区
   */
  schoolRegionID: number;
  /**
   * 学校状态
   */
  schoolStatus: number;
  /**
   * 学校标签
   */
  schoolTag: string;
  /**
   * 教师在职状态
   */
  teacherEmploymentStatus: number;
  /**
   * 是否测试用户
   */
  userIsTest: number;
}

export interface TeacherJobInfo {
  schoolID: number;
  jobInfos?: JobInfo[];
  jobSubject?: JobSubject;
  /**
   * 职务信息
   */
  jobType?: JobType;
}

export interface JobInfo {
  /**
   * 职务下的年级和班级
   */
  jobClass?: JobClass[];
  /**
   * 年级枚举值：1 ~ 14
   */
  jobGrade?: number;
  /**
   * 年级名称：一年级 ~ 六年级，初一 ~ 初四，高一 ~ 高三复读
   */
  name?: string;
}

export interface JobClass {
  /**
   * 班级ID
   */
  jobClass: number;
  /**
   * 班级名称
   */
  name: string;
}

export interface TargetJobClass {
  jobClass: number;
  jobGrade?: number;
  jobGradeName?: string;
  name: string;
  studentList?: unknown[];
}

export interface JobSubject {
  /**
   * 学科枚举值：1 ~ 9
   */
  jobSubject: number;
  /**
   * 学科名称：语文，数学，英语，物理，化学，生物，历史，地理，道德与法治
   */
  name: string;
}

/**
 * 职务信息
 */
export interface JobType {
  /**
   * 职务枚举值：1 ~ 5
   */
  jobType: JOB_TYPE;
  /**
   * 职务名称：1 校长，2 年级主任，3 学科组长，4 学科教师，5 班主任
   */
  name: string;
}

/**
 * 获取未读消息数量接口返回
 */
export interface UnreadMessageCountResult {
  /** 未读消息数量 */
  unread_count: number;
}

export type Message = {
  /**
   * 消息内容
   */
  content?: string;
  /**
   * 创建时间，秒值
   */
  createdAt: number;
  /**
   * 额外数据JSON字符串
   */
  extraData?: string;
  /**
   * 消息ID
   */
  id: number;
  /**
   * 是否已读
   */
  isRead: boolean;
  /**
   * 消息类型：3000001-反馈消息，3000002-通知消息，3000003-任务消息，3000004-系统消息
   */
  messageType?: number;
  /**
   * 回复时间
   */
  replyTime: number;
  /**
   * 发送时间
   */
  sendTime: number;
  /**
   * 消息标题
   */
  title?: string;
  /**
   * 更新时间
   */
  updatedAt: number;
  /**
   * 用户ID
   */
  userId?: number;
};

export interface GetMessageListResponse {
  page: number;
  page_size: number;
  total: number;
  messages: Message[];
}

/**
 * 反馈类型配置项
 */
export interface FeedbackTypeConfig {
  /**
   * 错误描述
   */
  description: string;
  /**
   * 错误类型
   */
  errorType: string;
  /**
   * 错误名称
   */
  name: string;
  /**
   * 错误排序
   */
  sortOrder: number;
}

/**
 * 获取反馈类型配置接口返回
 */
export type FeedbackTypeConfigList = FeedbackTypeConfig[];

export interface SubmitFeedbackRequest {
  /**
   * 教师当前所在班级
   */
  classId?: number;
  /**
   * 课程id
   */
  courseId?: number;
  /**
   * 描述
   */
  description: string;
  /**
   * 设备信息
   */
  deviceInfo: DeviceInfo;
  /**
   * 反馈来源
   */
  feedbackSource?: string;
  /**
   * 反馈来源客户端ID
   */
  feedbackSourceClientId?: number;
  /**
   * 反馈子类型
   */
  feedbackSubType?: string[];
  /**
   * 反馈类型
   */
  feedbackType: string;
  /**
   * 教师当前所在年级
   */
  gradeId?: number;
  /**
   * 问题id
   */
  questionId?: string;
  /**
   * 屏幕截图
   */
  screenshotUrls?: string[];
  /**
   * 学科id
   */
  subjectId?: number;
  /**
   * Ai课预览的组件索引
   */
  widgetIndex?: number;
  /**
   * Ai课预览的组件名称
   */
  widgetName?: string;
}

/**
 * 设备信息
 */
export interface DeviceInfo {
  /**
   * 设备型号
   */
  model: string;
  /**
   * 系统平台
   */
  platform: string;
  /**
   * 系统版本
   */
  version: string;
}
