const runEnv = process.env.NEXT_PUBLIC_ENV;
export const isDev = runEnv === "local" || runEnv === "dev";
export const isTest = runEnv === "test";
export const isPro = runEnv === "production";
console.log("当前环境变量：", runEnv);

const localUrl = "https://web-admin.local.xiaoluxue.cn";
const devUrl = "https://admin.dev.xiaoluxue.cn";
const proUrl = "https://admin.xiaoluxue.cn";
const testUrl = "https://admin.test.xiaoluxue.cn";
export let siteUrl = "";
export let templFileUrl =
  "https://gil-prod.oss-cn-beijing.aliyuncs.com/assets/guide-theme/lesson-template";
export let courseCenterApiBase = "";
export let adminApiBase = "";

if (isDev) {
  siteUrl = devUrl;
  templFileUrl = templFileUrl + "/上传模版（开发）.xlsx";
  // courseCenterApiBase = "https://gw-inner.dev.xiaoluxue.cn";
  courseCenterApiBase = "https://course-center-api.dev.xiaoluxue.cn";
  adminApiBase = "https://admin-api.dev.xiaoluxue.cn";
} else if (isTest) {
  siteUrl = testUrl;
  templFileUrl = templFileUrl + "/上传模版（测试）.xlsx";
  // courseCenterApiBase = "https://gw-inner.test.xiaoluxue.cn";
  courseCenterApiBase = "https://course-center-api.test.xiaoluxue.cn";
  adminApiBase = "https://admin-api.test.xiaoluxue.cn";
} else if (isPro) {
  siteUrl = proUrl;
  templFileUrl = templFileUrl + "/上传模版（生产）.xlsx";
  // courseCenterApiBase = "https://gw-inner.xiaoluxue.cn";
  courseCenterApiBase = "https://course-center-api.xiaoluxue.cn";
  adminApiBase = "https://admin-api.xiaoluxue.cn";
}
