/**
 * 题目创建
 */
import React from 'react';
import CreateQuestionProvider from './CreateQuestionProvider';
import styles from './index.module.less';
import { QuestionCreateProps } from './types';

const CreateQuestion: React.FC<QuestionCreateProps> = ({
  data,
  onBack,
  onSubmit,
  way = 'create',
}) => {
  return (
    <div className={styles.createQuestion}>
      <div className={styles.createQuestionContent}>
        <CreateQuestionProvider data={data} onBack={onBack} onSubmit={onSubmit} way={way} />
      </div>
    </div>
  );
};

export default CreateQuestion;
