"use client";
import { get } from "../others/fetcher";
import {
  CourseSummary,
  CourseWidgetSummary,
} from "@repo/core/types/data/course";
import { useRequest } from "ahooks";
import { useState } from "react";

type CourseSequenceViewmodel = {
  /**
   * 知识点ID
   */
  knowledgeId: number;
  /**
   * 课程ID
   */
  lessonId: number;
  /**
   * 当前index
   */
  activeIndex: number;
  /**
   * 组件摘要序列, 用于进度显示
   */
  widgetSummarySequence: CourseWidgetSummary[];
  /**
   * 总页数
   */
  total: number;
  /**
   * 按index跳转组件
   */
  goto: (index: number) => void;
  /**
   * 下一页
   */
  next: () => void;
};

// 课程的侧边栏数据
const useCourseSequenceViewmodel = (knowledgeId: number) => {
  const {
    data = {
      lessonId: String(knowledgeId),
      total: 0,
      widgets: [],
    },
  }: any = useRequest(async() =>{
    const res:any = await get(`/api/v1/lesson_widget/preview/progress`, {
      query: { lessonId: String(knowledgeId) },
    })
    res.lessonWidgets = res.lessonWidgets.map((item:any)=>{
      return {
        index: item.widgetIndex,
        name: item.widgetName,
        type: item.widgetType,
        status: item.widgetType 
        // status: item.widgetType === 'exercise' ? 'locked' : 'unlocked',// "locked" | "completed" | "unlocked";
      }
    })
    return res;
  }
   
  );

  //TODO: 从接口获取课程数据
  // const { data, error } = useCourseModel(knowledgeId);
  // const data = {} as CourseSummary;
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const { lessonId, total, lessonWidgets: widgetSummaries } = data;

  // const activeWidgetSummary = useMemo(() => {
  //   return widgetSummaries[activeIndex.value];
  // }, [activeIndex, widgetSummaries]);

  const next = () => {
    // if (activeIndex.value >= total) {
    if (activeIndex >= total - 1) {
      //END
      console.log("----THE END----");
    } else {
      setActiveIndex(activeIndex + 1);
    }
  };

  const goto = (index: number) => {
    console.log("goto", index);
    if (index < 0 || index >= total) {
      return;
    }
    setActiveIndex(index);
  };

  return {
    knowledgeId,
    lessonId,
    total,
    activeIndex,
    widgetSummarySequence: widgetSummaries,
    next,
    goto,
  };
};

export { useCourseSequenceViewmodel, type CourseSequenceViewmodel };
