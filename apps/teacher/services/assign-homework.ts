import { QuestionDetailListParams, QuestionFilterEnumData, QuestionListParams, QuestionListResponseData, QuestionType } from "@/types/assign";
import { r } from "../libs/axios";

export const getQuestionFilterEnum = () => {
  return r.get<QuestionFilterEnumData>("/task/question/enums");
};

export const getQuestionList = (params: QuestionListParams) => {
  return r.post<QuestionListResponseData>("/task/question/list/search", params);
};

export const getQuestionListByIds = (params: QuestionDetailListParams) => {
  return r.post<QuestionType[]>("/task/question/detail", params);
};
