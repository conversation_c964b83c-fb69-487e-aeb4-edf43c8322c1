# @repo/test-utils

分层架构测试工具库，按照应用架构分层提供统一的测试工具和辅助函数。

## 🏗️ 分层架构

本工具库按照应用架构分为以下几个层级：

### ✅ Model 层 (已实现)
- **简单测试运行器** - 统一的 `test()` 和 `expect()` 断言函数
- **SWR Hook 测试辅助** - 标准化的 SWR Hook 测试环境和 Mock 工具
- **Zod Schema 测试生成器** - 根据 Zod Schema 自动生成测试用例

### ✅ API 层 (已实现)
- **HTTP 请求创建** - 创建各种类型的测试请求 (GET/POST/PUT/DELETE)
- **API 响应验证** - 验证 API 返回数据格式和状态码
- **Mock Store 工厂** - 快速创建 Mock 对象
- **错误处理测试** - 测试各种错误场景和边界条件

### 🚧 View 层 (待实现)
- **React 组件测试** - 组件渲染和交互测试
- **用户交互模拟** - 点击、输入等用户行为模拟
- **DOM 查询断言** - DOM 元素查找和状态断言

### 🚧 ViewModel 层 (待实现)
- **业务逻辑测试** - 复杂业务逻辑单元测试
- **状态管理测试** - 状态变更和副作用测试
- **异步操作测试** - 异步业务流程测试

### 🚧 Context 层 (待实现)
- **Context Provider 测试** - React Context 提供者测试
- **全局状态测试** - 全局状态管理测试
- **状态持久化测试** - 状态持久化机制测试

## 🚀 特性

- **分层设计** - 按架构层级组织，职责清晰
- **TypeScript 支持** - 完整的类型定义和类型安全
- **零配置使用** - 开箱即用，无需复杂配置
- **向后兼容** - 支持现有代码平滑迁移
- **可扩展性** - 为未来各层级测试工具预留空间

## 📦 安装

```bash
# 作为 workspace 包，通常已经安装
npm install @repo/test-utils
```

## 🛠️ 使用方法

**重要：必须使用分层路径导入，不支持根级别导入**

按照架构层级导入对应的测试工具：

```javascript
// Model 层测试 - 数据相关操作
import { test, expect, renderSWRHook, generateSchemaTests } from '@repo/test-utils/model';

// API 层测试 - 接口和网络请求
import { createGetRequest, createPostRequest, validateApiResponse, createMockStore } from '@repo/test-utils/api';

// View 层测试 - React 组件和 UI (未来)
import { renderComponent, simulateClick } from '@repo/test-utils/view';

// ViewModel 层测试 - 业务逻辑和状态管理 (未来)
import { testBusinessLogic, mockState } from '@repo/test-utils/viewmodel';

// Context 层测试 - React Context 和全局状态 (未来)
import { testContext, mockProvider } from '@repo/test-utils/context';
```

### ❌ 不支持的导入方式

```javascript
// ❌ 错误：不支持根级别导入
import { test, expect } from '@repo/test-utils';

// ✅ 正确：必须指定层级路径
import { test, expect } from '@repo/test-utils/model';
```

## 📚 详细文档

- [Model 层测试工具文档](./src/model/README.md) - 完整的 Model 层测试指南和 API 参考
- [API 层测试指南](../../.cursor/rules/test/test-api-guide.mdc) - API 层测试标准规范和最佳实践

## 🧪 API 层测试示例

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createGetRequest, createPostRequest, validateApiResponse, createMockStore } from '@repo/test-utils/api';

// Mock 外部依赖
vi.mock('../../../store', () => ({
  exerciseSessionStore: createMockStore(['createSession', 'getSession', 'submitAnswer'])
}));

// 导入被测试的 API
import { GET, POST } from '../route';

describe('API Tests', () => {
  let mockStore: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    const store = await import('../../../store');
    mockStore = store.exerciseSessionStore;
  });

  it('应该正确处理GET请求', async () => {
    // 设置Mock返回值
    mockStore.createSession.mockReturnValue({ id: 'session_123', data: 'test' });

    // 创建测试请求
    const request = createGetRequest('http://localhost:3000/api/test', { param: 'value' });

    // 执行API并验证响应
    const response = await GET(request);
    const data = await validateApiResponse(response, 200);

    expect(data.code).toBe(0);
    expect(mockStore.createSession).toHaveBeenCalled();
  });

  it('应该正确处理POST请求', async () => {
    const requestBody = { key: 'value' };
    mockStore.submitAnswer.mockReturnValue({ success: true });

    const request = createPostRequest('http://localhost:3000/api/submit', requestBody);
    const response = await POST(request);
    const data = await validateApiResponse(response, 200);

    expect(data.code).toBe(0);
    expect(mockStore.submitAnswer).toHaveBeenCalledWith(requestBody);
  });
});
```

## 📋 Model 层详细使用

### 1. 简单测试运行器

用于基础功能测试，替代每个模块重复定义的测试函数。

```javascript
import { test, expect, getTestStats } from '@repo/test-utils/model';

// 基础断言测试
test('计算准确率', () => {
  expect(calculateAccuracyRate(8, 10)).toBe(80);
});

test('格式化时间', () => {
  expect(formatTime(125)).toBe('02:05');
});

// 获取测试统计
const stats = getTestStats();
console.log(`通过率: ${stats.passRate}%`);
```

**支持的断言方法**：
- `toBe(expected)` - 严格相等
- `toEqual(expected)` - 深度相等
- `toThrow()` - 函数抛出异常
- `toBeTruthy()` - 真值断言
- `toBeFalsy()` - 假值断言
- `toBeDefined()` - 定义断言
- `toBeUndefined()` - 未定义断言

### 2. SWR Hook 测试辅助

标准化的 SWR Hook 测试环境，提供 Mock 工具和状态检查。

```typescript
import {
  renderSWRHook,
  createMockResponse,
  createMockError,
  waitForSWRState,
  swrStateCheckers
} from '@repo/test-utils/model';

// 测试成功获取数据
test('useUsers 成功获取数据', async () => {
  const mockUsers = [{ id: 1, name: 'John' }];
  
  const { result } = renderSWRHook(
    () => useUsers(),
    { mocks: { get: createMockResponse(mockUsers) } }
  );

  // 等待数据加载完成
  await waitForSWRState(result, swrStateCheckers.hasData);
  
  expect(result.current.users).toEqual(mockUsers);
});

// 测试错误处理
test('useUsers 错误处理', async () => {
  const { result } = renderSWRHook(
    () => useUsers(),
    { mocks: { get: createMockError('Network Error') } }
  );

  await waitForSWRState(result, swrStateCheckers.hasError);
  
  expect(result.current.error).toBeDefined();
});
```

**Mock 工具函数**：
- `createMockResponse(data, delay)` - 成功响应
- `createMockError(message, delay)` - 错误响应
- `createNetworkError(delay)` - 网络错误
- `createTimeoutError(delay)` - 超时错误
- `createPaginatedMockResponse(items, page, pageSize, delay)` - 分页数据

### 3. Zod Schema 测试生成器

根据 Zod Schema 自动生成完整的测试用例。

```typescript
import {
  generateSchemaTests,
  generateCommonInvalidSamples
} from '@repo/test-utils/model';
import { ApiUserSchema } from './schemas';

// 自动生成完整的 Schema 测试
generateSchemaTests(
  ApiUserSchema,
  'ApiUser',
  // 有效数据样本
  [
    { user_id: 1, user_name: 'John', status: 'active' },
    { user_id: 2, user_name: 'Jane', status: 'inactive' }
  ],
  // 无效数据样本
  [
    { data: { user_id: 'invalid' }, expectedError: 'Expected number' },
    { data: { user_name: 123 }, expectedError: 'Expected string' },
    { data: { status: 'invalid' }, expectedError: 'Invalid enum value' }
  ],
  // 配置选项
  {
    testBoundaryConditions: true,
    testTypeCoercion: false,
    customTests: [
      {
        name: '应该接受最小有效用户数据',
        data: { user_id: 1, user_name: 'A', status: 'active' },
        shouldPass: true
      }
    ]
  }
);

// 或者使用预定义的无效样本
const invalidSamples = generateCommonInvalidSamples('object');
generateSchemaTests(ApiUserSchema, 'ApiUser', validSamples, invalidSamples);
```

## 📊 测试报告

使用简单测试运行器时，可以生成标准化的测试报告：

```javascript
import { generateTestSummary } from '@repo/test-utils/model';

// 运行所有测试后
console.log(generateTestSummary());
```

输出示例：
```
📊 测试统计:
   总测试数: 15
   通过: 15 (100.0%)
   失败: 0 (0.0%)

🎉 所有测试通过！
```

## 🔧 高级配置

### 自定义 SWR 配置

```typescript
const { result } = renderSWRHook(
  () => useUsers(),
  {
    mocks: { get: createMockResponse(data) },
    swrConfig: {
      refreshInterval: 0,
      revalidateOnFocus: false,
      // 其他 SWR 配置
    }
  }
);
```

### 复杂的 Schema 测试

```typescript
generateSchemaTests(
  ComplexSchema,
  'Complex',
  validSamples,
  invalidSamples,
  {
    testBoundaryConditions: true,
    testTypeCoercion: true,
    customTests: [
      {
        name: '应该处理嵌套对象',
        data: { nested: { field: 'value' } },
        shouldPass: true
      },
      {
        name: '应该拒绝缺少必需字段',
        data: { incomplete: true },
        shouldPass: false,
        expectedError: 'required'
      }
    ]
  }
);
```

## 🎯 最佳实践

1. **统一使用工具库** - 避免在每个模块重复定义测试函数
2. **标准化 Mock 策略** - 使用提供的 Mock 工具函数
3. **自动生成测试用例** - 利用 Zod Schema 生成器减少手动编写
4. **完整的边界测试** - 启用边界条件测试确保健壮性
5. **类型安全** - 充分利用 TypeScript 类型检查

## 📝 开发指南

### 添加新的断言方法

在 `simple-test-runner.ts` 中扩展 `expect` 函数：

```typescript
export function expect(actual: any) {
  return {
    // 现有方法...
    
    // 新增断言方法
    toContain: (expected: any) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected ${actual} to contain ${expected}`);
      }
    }
  };
}
```

### 添加新的 Mock 工具

在 `swr-test-helpers.ts` 中添加新的 Mock 函数：

```typescript
export function createCustomMock(config: CustomConfig) {
  return () => new Promise((resolve, reject) => {
    // 自定义 Mock 逻辑
  });
}
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具库。

## 📄 许可证

MIT License