"use client";

import { Skeleton } from "@/ui/skeleton";

// 表格行骨架屏
export function TableRowSkeleton({ rows = 3 }: { rows?: number }) {
  return (
    <>
      {Array(rows)
        .fill(0)
        .map((_, index) => (
          <tr key={index} className="animate-pulse">
            <td className="p-3">
              <Skeleton className="h-5 w-32" />
            </td>
            <td className="p-3 text-right">
              <Skeleton className="h-5 w-10 ml-auto" />
            </td>
          </tr>
        ))}
    </>
  );
}

// 抽屉内容骨架屏
export function DrawerSkeleton() {
  return (
    <div className="mt-6 ">
      {/* 课程列表骨架屏 */}
      <div>
        <h3 className="mb-4 text-sm font-medium">课程列表</h3>
        <div className="rounded-md">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/50">
                <th className="p-3 text-left text-sm font-medium">课程名称</th>
                <th className="w-24 p-3 text-right text-sm font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              <TableRowSkeleton rows={2} />
            </tbody>
          </table>
        </div>
      </div>

      <div className="h-px bg-gray-200 w-full my-6" />

      {/* 班级列表骨架屏 */}
      <div>
        <h3 className="mb-4 text-sm font-medium">班级列表</h3>
        <div className="rounded-md">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/50">
                <th className="p-3 text-left text-sm font-medium">班级名称</th>
                <th className="w-24 p-3 text-right text-sm font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              <TableRowSkeleton rows={3} />
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// 学生报告页骨架屏
export function ReportPageSkeleton() {
  return (
    <div className="pt-4 mx-auto rounded-lg h-full pr-3 pl-6">
      <div className="z-10">
        {/* 顶部区域骨架屏 */}
        <div className="flex gap-3 my-5">
          {/* 值得表扬区域 */}
          <div className="flex-1 bg-white rounded-[0.75rem] p-3 border border-px border-line-1">
            <div className="flex justify-between items-center mb-3">
              <Skeleton className="h-6 w-24 mb-4" />
              <Skeleton className="h-6 w-24 mb-4" />
            </div>
            <div className="flex flex-wrap gap-2">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <div className="flex items-center gap-2 justify-between w-full" key={i}>
                    <Skeleton key={i + "praise_left"} className="h-6 w-1/2 rounded-full" />
                    <Skeleton key={i + "praise_right"} className="h-6 w-1/2 rounded-full" />
                  </div>
                ))}
            </div>
          </div>

          {/* 需要关注区域 */}
          <div className="flex-1 bg-white rounded-[0.75rem] p-3 border border-px border-line-1">
            <div className="flex justify-between items-center mb-3">
              <Skeleton className="h-6 w-24 mb-4" />
              <Skeleton className="h-6 w-24 mb-4" />
            </div>
            <div className="flex flex-wrap gap-2">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <div className="flex items-center gap-2 justify-between w-full" key={i}>
                    <Skeleton key={i + "attention_left"} className="h-6 w-1/2 rounded-full" />
                    <Skeleton key={i + "attention_right"} className="h-6 w-1/2 rounded-full" />
                  </div>
                ))}
            </div>
          </div>

          {/* 班级统计区域 */}
          <div className="flex-1 grid grid-cols-1 gap-3">
            <div className="bg-white rounded-[0.75rem] p-3 border border-px border-line-1">
              <Skeleton className="h-5 w-32 mb-2" />
              <div className="flex items-center justify-between h-full gap-2 w-full">
                <Skeleton className="h-24 w-1/2" />
                <Skeleton className="h-24 w-1/2" />
              </div>
            </div>

          </div>
        </div>

        {/* 表格区域骨架屏 */}
        <div className="bg-white rounded-[0.75rem] p-3 border border-px border-line-1">
          {/* 工具栏 */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-30" />
              <Skeleton className="h-8 w-64" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>

          {/* 表格头部 */}
          <div className="rounded-md">
            <div className="p-3 grid grid-cols-7 gap-4">
              {Array(7)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={i} className="h-6 w-full" />
                ))}
            </div>

            {/* 表格内容 */}
            <div className="p-2 h-[calc(100vh-17rem)]">
              {Array(7)
                .fill(0)
                .map((_, i) => (
                  <div key={i + "table_row"} className="grid grid-cols-7 gap-4 p-2 py-2">
                    {Array(7)
                      .fill(0)
                      .map((_, j) => (
                        <Skeleton key={j + "table_cell"} className="h-6 w-full" />
                      ))}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}
