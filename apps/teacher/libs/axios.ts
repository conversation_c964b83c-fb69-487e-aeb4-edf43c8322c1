import { LOCALSTORAGE_SCHOOL_ID_KEY, LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { AppErrorCode, AppEvents, emitter } from "@/libs";
import type { GilAxiosInstance } from "@/types/axios";
import { toast } from "@/ui/toast";
import * as Sentry from "@sentry/nextjs";
import axios from "axios";
import store from "store2";

console.log("NEXT_PUBLIC_API_HOST ==>", process.env.NEXT_PUBLIC_API_HOST);

const NEXT_PUBLIC_API_HOST = process.env.NEXT_PUBLIC_API_HOST || "";

const r: GilAxiosInstance = axios.create({
  baseURL: `${NEXT_PUBLIC_API_HOST}/teacher-api/api/v1`,
  timeout: 10000,
});

r.interceptors.request.use((config) => {
  const token = store(LOCALSTORAGE_TOKEN_KEY);
  const schoolId = store(LOCALSTORAGE_SCHOOL_ID_KEY);

  if (token && !config.url?.startsWith(`${NEXT_PUBLIC_API_HOST}/ucenter-api`)) {
    config.headers.Authorization = `Bearer ${token}`;
    config.headers.organizationId = schoolId;
  }

  return config;
});

r.interceptors.response.use(
  (response) => {
    if (response.config.responseType === "blob") {
      return response.data;
    }
    const {
      data: { code, message, data },
    } = response;

    if (code === 0) {
      return data;
    }

    if ((response.config as { showToast?: boolean }).showToast !== false) {
      toast.warning(message + `[${code}]`);
    }

    if (code === AppErrorCode.Unauthorized || code === AppErrorCode.Forbidden) {
      emitter.emit(AppEvents.APP_AUTH, { status: code });
    } else {
      Sentry.captureException(response, {
        level: "warning",
        extra: {
          response,
        },
      });
    }

    throw response;
  },
  (err) => {
    Sentry.captureException(err, {
      level: "error",
      extra: {
        err: err,
      },
    });
    const status = err?.response?.status;

    if (!status) {
      toast.warning("您的网络不太稳定，请稍后重试");
      throw err;
    }

    if (status === 401 || status === 403) {
      // 使用新的 emitter 实例和静态常量
      emitter.emit(AppEvents.APP_AUTH, { status });

      throw err;
    }

    // 命中网关频次控制
    if (status === 405) {
      toast.warning(`操作太频繁啦，请休息一下`);
      throw err;
    }

    throw err;
  }
);

export { r };

