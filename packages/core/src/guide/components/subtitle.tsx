import { MathContent } from "@repo/core/components/math-content/math-content";
import { Subtitle as SubtitleData } from "@repo/core/types/data/base";
import React from "react";
import { Sequence } from "remotion";
interface SubtitleProps {
  subtitles?: SubtitleData[];
}

export const Subtitle: React.FC<
  React.HTMLAttributes<HTMLDivElement> & SubtitleProps
> = ({ subtitles }) => {
  if (!subtitles) {
    return null;
  }

  return (
    <div className="pointer-events-none bottom-5 flex min-h-[32px] justify-center whitespace-nowrap text-center text-base font-medium leading-8 tracking-[0.52px] text-[#4d3c32]">
      {subtitles.map((item, index) => {
        const { inFrame, outFrame, text } = item;
        const durationInFrames = outFrame - inFrame;
        if (durationInFrames <= 0) {
          throw new Error(`<字幕>  ${JSON.stringify(item)}`);
        }
        if (text === "") {
          return null;
        }
        return (
          <Sequence
            layout="none"
            key={index.toString()}
            from={inFrame}
            durationInFrames={durationInFrames}
          >
            <div className="rounded-lg bg-[#f7f2ed] px-2.5 text-center">
              <MathContent>{text}</MathContent>
            </div>
          </Sequence>
        );
      })}
    </div>
  );
};
