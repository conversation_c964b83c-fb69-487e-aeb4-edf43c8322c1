import {
  TaskDataWithClassData,
  useTaskContext,
} from "@/app/homework/[id]/_context/task-context";
import { transformHomeworkToTaskData } from "@/app/homework/_components/task-card";
import { useApp } from "@/hooks";
import { Homework, HomeworkReport, StatData } from "@/types/homeWork";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { format, isSameWeek, isToday, isYesterday } from "date-fns";
import { zhCN } from "date-fns/locale";
import { TaskCard } from "./TaskCard";

const formatPublishTime = (statData?: StatData) => {
  const publishTime = statData?.startTime ? statData.startTime * 1000 : null;
  if (!publishTime) return "";

  if (isToday(publishTime)) {
    return `今天${format(publishTime, "HH:mm", { locale: zhCN })}发布`;
  }
  if (isYesterday(publishTime)) {
    return `昨天发布`;
  }

  if (isSameWeek(publishTime, new Date(), { weekStartsOn: 1 })) {
    return format(publishTime, "本eee", { locale: zhCN }) + `发布`;
  }

  const lastWeek = new Date();
  lastWeek.setDate(lastWeek.getDate() - 7);
  if (isSameWeek(publishTime, lastWeek, { weekStartsOn: 1 })) {
    return format(publishTime, "上eee", { locale: zhCN }) + `发布`;
  }

  return format(publishTime, "M月d日", { locale: zhCN }) + `发布`;
};

export default function AssignTaskCard({
  taskData,
  onTaskClick,
  onTaskReportClick,
}: {
  taskData: Homework;
  onTaskClick?: (taskData: Homework) => void;
  onTaskReportClick?: (report: HomeworkReport, taskData: Homework) => void;
}) {
  const { setOpen } = useApp();
  const { assignId } = useTaskContext();
  const handleTaskClick = () => {
    onTaskClick?.(taskData);

    if (taskData.taskId) {
      // 收起侧边栏
      setOpen(false);

      // TODO：改不动了，这之前居然是在传组件自己的数据orz，还是基于taskData来回组装的
      const data: TaskDataWithClassData = {
        ...transformHomeworkToTaskData(taskData),
        classData: {
          ...taskData.reports[0],
          taskId: taskData.taskId,
          taskName: taskData.taskName,
          taskType: taskData.taskType,
        },
      };

      // 保存完整任务数据到本地存储
      localStorage.setItem("homework_currentTask", JSON.stringify(data));

      // 保存来源信息
      localStorage.setItem("homework_source", "assign");
      assignId.value = taskData.reports[0].assignId.toString();
    }
  };

  const handleTaskReportClick = (report: HomeworkReport) => {
    onTaskReportClick?.(report, taskData);

    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_CARD_CLICK,
      {}
    );
    if (report.assignObject.id) {
      // 收起侧边栏
      setOpen(false);

      // 保存任务数据
      // TODO：改不动了，这之前居然是在传组件自己的数据orz，还是基于taskData来回组装的
      const data: TaskDataWithClassData = {
        ...transformHomeworkToTaskData(taskData),
        classData: {
          ...report,
          taskId: taskData.taskId,
          taskName: taskData.taskName,
          taskType: taskData.taskType,
        },
      };

      // 保存完整任务数据到本地存储
      localStorage.setItem("homework_currentTask", JSON.stringify(data));

      // 保存来源信息
      localStorage.setItem("homework_source", "assign");
    }
  };

  return (
    <TaskCard
      source="assign"
      taskData={taskData}
      onlyShow={true}
      className="col-span-1 cursor-pointer border-none bg-transparent p-0 hover:bg-transparent"
      onClick={handleTaskClick}
      onClickReportCard={handleTaskReportClick}
      timeFormatter={formatPublishTime}
    />
  );
}
