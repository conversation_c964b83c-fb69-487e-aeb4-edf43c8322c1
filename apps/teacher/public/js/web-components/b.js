if (!customElements.get("web-component-physics-gravity")) {
    class WebComponentPhysicsGravity extends HTMLElement {
        constructor() {
            super();
            this.katexLoaded = false;
        }

        async connectedCallback() {
            this.style.opacity = '0';
            this.style.transition = 'opacity 0.3s ease-in-out';

            const shadow = this.attachShadow({ mode: 'open' });
            await this.loadKaTeX();
            this.render(shadow);
            await this.renderMath(shadow);
            this.setupInteractions(shadow);

            this.style.opacity = '1';
        }

        async loadKaTeX() {
            if (this.katexLoaded) return;

            try {
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = 'https://cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.css';
                document.head.appendChild(cssLink);

                if (!window.katex) {
                    await new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = 'https://cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.js';
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                }

                this.katexLoaded = true;
            } catch (error) {
                console.warn('KaTeX failed to load:', error);
            }
        }

        render(shadow) {
            shadow.innerHTML = `
          <style>
            .container {
              padding: 20px;
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 15px;
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
              max-width: 900px;
              margin: 0 auto;
              color: white;
            }
            
            .title {
              text-align: center;
              font-size: 28px;
              font-weight: bold;
              margin-bottom: 30px;
              text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 10px;
            }
            
            .title::before {
              content: '🌍';
              font-size: 32px;
            }
            
            .formula-section {
              margin-bottom: 25px;
              background: rgba(255, 255, 255, 0.95);
              padding: 20px;
              border-radius: 12px;
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
              color: #2c3e50;
              backdrop-filter: blur(10px);
            }
            
            .section-title {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 15px;
              color: #34495e;
              display: flex;
              align-items: center;
              gap: 8px;
              border-bottom: 2px solid #3498db;
              padding-bottom: 8px;
            }
            
            .formula-item {
              margin: 15px 0;
              padding: 15px;
              background: #f8f9fa;
              border-radius: 8px;
              border-left: 4px solid #3498db;
              transition: all 0.3s ease;
              cursor: pointer;
            }
            
            .formula-item:hover {
              background: #e3f2fd;
              transform: translateX(5px);
              box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
            }
            
            .formula-item.active {
              background: #e8f5e8;
              border-left-color: #27ae60;
            }
            
            .formula-label {
              color: #555;
              font-size: 16px;
              margin-bottom: 8px;
              font-weight: 600;
            }
            
            .formula-description {
              color: #666;
              font-size: 14px;
              margin-top: 8px;
              line-height: 1.4;
            }
            
            .math-formula {
              font-size: 18px;
              color: #2c3e50;
              min-height: 1.5em;
              display: inline-block;
            }
            
            .math-formula[data-display="true"] {
              display: block;
              text-align: center;
              min-height: 2.5em;
              margin: 15px 0;
              padding: 10px;
              background: rgba(52, 152, 219, 0.1);
              border-radius: 6px;
            }
            
            .calculator-section {
              background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
              color: white;
              padding: 20px;
              border-radius: 12px;
              margin: 20px 0;
            }
            
            .calculator-title {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 15px;
              text-align: center;
            }
            
            .input-group {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin-bottom: 15px;
            }
            
            .input-field {
              display: flex;
              flex-direction: column;
              gap: 5px;
            }
            
            .input-field label {
              font-size: 14px;
              font-weight: 500;
            }
            
            .input-field input {
              padding: 8px 12px;
              border: none;
              border-radius: 6px;
              font-size: 16px;
              background: rgba(255, 255, 255, 0.9);
              color: #2c3e50;
            }
            
            .calculate-btn {
              background: #27ae60;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              width: 100%;
              margin-top: 10px;
            }
            
            .calculate-btn:hover {
              background: #219a52;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
            }
            
            .result-display {
              background: rgba(255, 255, 255, 0.2);
              padding: 15px;
              border-radius: 8px;
              margin-top: 15px;
              text-align: center;
              font-size: 18px;
              font-weight: 600;
              min-height: 50px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            
            .constants-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
              gap: 15px;
            }
            
            .constant-item {
              background: #f8f9fa;
              padding: 12px;
              border-radius: 8px;
              border-left: 4px solid #e74c3c;
            }
            
            .constant-name {
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 5px;
            }
            
            .constant-value {
              font-family: 'Courier New', monospace;
              color: #e74c3c;
              font-size: 14px;
            }
            
            @media (max-width: 768px) {
              .container {
                padding: 15px;
                margin: 10px;
              }
              
              .title {
                font-size: 24px;
              }
              
              .input-group {
                grid-template-columns: 1fr;
              }
              
              .constants-grid {
                grid-template-columns: 1fr;
              }
            }
          </style>
          
          <div class="container">
            <div class="title">重力物理学</div>
            
            <div class="formula-section">
              <div class="section-title">🍎 牛顿万有引力定律</div>
              
              <div class="formula-item" data-formula="newton-gravity">
                <div class="formula-label">万有引力定律</div>
                <div class="math-formula" data-math="F = G \\frac{m_1 m_2}{r^2}" data-display="true"></div>
                <div class="formula-description">
                  任意两个质点之间的引力大小与它们质量的乘积成正比，与它们距离的平方成反比
                </div>
              </div>
              
              <div class="formula-item" data-formula="gravitational-field">
                <div class="formula-label">重力场强度</div>
                <div class="math-formula" data-math="g = \\frac{GM}{r^2}" data-display="true"></div>
                <div class="formula-description">
                  重力场强度等于万有引力常数乘以天体质量除以距离的平方
                </div>
              </div>
              
              <div class="formula-item" data-formula="weight">
                <div class="formula-label">重力</div>
                <div class="math-formula" data-math="W = mg" data-display="true"></div>
                <div class="formula-description">
                  物体的重力等于质量乘以重力加速度
                </div>
              </div>
            </div>
            
            <div class="formula-section">
              <div class="section-title">🚀 运动学公式</div>
              
              <div class="formula-item" data-formula="free-fall">
                <div class="formula-label">自由落体运动</div>
                <div class="math-formula" data-math="h = \\frac{1}{2}gt^2" data-display="true"></div>
                <div class="formula-description">
                  自由落体的位移等于重力加速度乘以时间平方的一半
                </div>
              </div>
              
              <div class="formula-item" data-formula="velocity">
                <div class="formula-label">自由落体速度</div>
                <div class="math-formula" data-math="v = gt" data-display="true"></div>
                <div class="formula-description">
                  自由落体的速度等于重力加速度乘以时间
                </div>
              </div>
              
              <div class="formula-item" data-formula="kinematic">
                <div class="formula-label">运动学方程</div>
                <div class="math-formula" data-math="v^2 = v_0^2 + 2gh" data-display="true"></div>
                <div class="formula-description">
                  末速度的平方等于初速度的平方加上2倍重力加速度乘以位移
                </div>
              </div>
            </div>
            
            <div class="formula-section">
              <div class="section-title">🌌 天体力学</div>
              
              <div class="formula-item" data-formula="orbital-velocity">
                <div class="formula-label">第一宇宙速度</div>
                <div class="math-formula" data-math="v_1 = \\sqrt{\\frac{GM}{R}}" data-display="true"></div>
                <div class="formula-description">
                  环绕天体表面运行所需的最小速度
                </div>
              </div>
              
              <div class="formula-item" data-formula="escape-velocity">
                <div class="formula-label">第二宇宙速度（脱离速度）</div>
                <div class="math-formula" data-math="v_2 = \\sqrt{\\frac{2GM}{R}}" data-display="true"></div>
                <div class="formula-description">
                  脱离天体引力束缚所需的最小速度
                </div>
              </div>
              
              <div class="formula-item" data-formula="kepler-third">
                <div class="formula-label">开普勒第三定律</div>
                <div class="math-formula" data-math="T^2 = \\frac{4\\pi^2}{GM}r^3" data-display="true"></div>
                <div class="formula-description">
                  行星公转周期的平方与轨道半径的三次方成正比
                </div>
              </div>
            </div>
            
            <div class="formula-section">
              <div class="section-title">⚡ 重力势能</div>
              
              <div class="formula-item" data-formula="potential-energy">
                <div class="formula-label">重力势能</div>
                <div class="math-formula" data-math="E_p = mgh" data-display="true"></div>
                <div class="formula-description">
                  近地面重力势能等于质量乘以重力加速度乘以高度
                </div>
              </div>
              
              <div class="formula-item" data-formula="gravitational-potential">
                <div class="formula-label">万有引力势能</div>
                <div class="math-formula" data-math="E_p = -\\frac{GMm}{r}" data-display="true"></div>
                <div class="formula-description">
                  万有引力势能，负号表示引力为吸引力
                </div>
              </div>
              
              <div class="formula-item" data-formula="mechanical-energy">
                <div class="formula-label">机械能守恒</div>
                <div class="math-formula" data-math="E = \\frac{1}{2}mv^2 + mgh = \\text{常数}" data-display="true"></div>
                <div class="formula-description">
                  在只有重力做功的情况下，机械能守恒
                </div>
              </div>
            </div>
            
            <div class="calculator-section">
              <div class="calculator-title">🧮 重力计算器</div>
              
              <div class="input-group">
                <div class="input-field">
                  <label>质量 (kg)</label>
                  <input type="number" id="mass" placeholder="输入质量" value="70">
                </div>
                <div class="input-field">
                  <label>高度 (m)</label>
                  <input type="number" id="height" placeholder="输入高度" value="10">
                </div>
                <div class="input-field">
                  <label>重力加速度 (m/s²)</label>
                  <input type="number" id="gravity" placeholder="重力加速度" value="9.8">
                </div>
              </div>
              
              <button class="calculate-btn" id="calculateBtn">计算重力相关量</button>
              
              <div class="result-display" id="resultDisplay">
                请输入参数并点击计算
              </div>
            </div>
            
            <div class="formula-section">
              <div class="section-title">📊 重要常数</div>
              
              <div class="constants-grid">
                <div class="constant-item">
                  <div class="constant-name">万有引力常数</div>
                  <div class="constant-value">G = 6.674 × 10⁻¹¹ m³/(kg·s²)</div>
                </div>
                
                <div class="constant-item">
                  <div class="constant-name">地球表面重力加速度</div>
                  <div class="constant-value">g = 9.8 m/s²</div>
                </div>
                
                <div class="constant-item">
                  <div class="constant-name">地球质量</div>
                  <div class="constant-value">M⊕ = 5.972 × 10²⁴ kg</div>
                </div>
                
                <div class="constant-item">
                  <div class="constant-name">地球半径</div>
                  <div class="constant-value">R⊕ = 6.371 × 10⁶ m</div>
                </div>
                
                <div class="constant-item">
                  <div class="constant-name">第一宇宙速度</div>
                  <div class="constant-value">v₁ = 7.9 km/s</div>
                </div>
                
                <div class="constant-item">
                  <div class="constant-name">第二宇宙速度</div>
                  <div class="constant-value">v₂ = 11.2 km/s</div>
                </div>
              </div>
            </div>
          </div>
        `;
        }

        setupInteractions(shadow) {
            const formulaItems = shadow.querySelectorAll('.formula-item[data-formula]');
            const calculateBtn = shadow.getElementById('calculateBtn');
            const resultDisplay = shadow.getElementById('resultDisplay');

            formulaItems.forEach(item => {
                item.addEventListener('click', () => {
                    formulaItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                });
            });

            calculateBtn.addEventListener('click', () => {
                const mass = parseFloat(shadow.getElementById('mass').value) || 0;
                const height = parseFloat(shadow.getElementById('height').value) || 0;
                const gravity = parseFloat(shadow.getElementById('gravity').value) || 9.8;

                if (mass <= 0) {
                    resultDisplay.textContent = '请输入有效的质量值';
                    return;
                }

                const weight = mass * gravity;
                const potentialEnergy = mass * gravity * height;
                const freeFailTime = height > 0 ? Math.sqrt(2 * height / gravity) : 0;
                const finalVelocity = height > 0 ? Math.sqrt(2 * gravity * height) : 0;

                resultDisplay.innerHTML = `
            <div style="text-align: left; line-height: 1.6;">
              <div><strong>重力:</strong> ${weight.toFixed(2)} N</div>
              <div><strong>重力势能:</strong> ${potentialEnergy.toFixed(2)} J</div>
              ${height > 0 ? `
                <div><strong>自由落体时间:</strong> ${freeFailTime.toFixed(2)} s</div>
                <div><strong>落地速度:</strong> ${finalVelocity.toFixed(2)} m/s</div>
              ` : ''}
            </div>
          `;
            });
        }

        async renderMath(shadow) {
            const mathElements = shadow.querySelectorAll('[data-math]');

            mathElements.forEach(element => {
                const formula = element.dataset.math;
                const displayMode = element.dataset.display === 'true';

                try {
                    if (window.katex && this.katexLoaded) {
                        window.katex.render(formula, element, {
                            displayMode,
                            throwOnError: false,
                            strict: false,
                            trust: true
                        });
                    } else {
                        element.textContent = formula;
                    }
                } catch (error) {
                    element.textContent = formula;
                }
            });
        }

        disconnectedCallback() {
            // 清理事件监听器
        }
    }

    customElements.define("web-component-physics-gravity", WebComponentPhysicsGravity);
}
