/**
 * 问题难易程度枚举
 */
export enum QuestionDifficulty {
  /** 简单 */
  EASY = 1,
  /** 较易 */
  RELATIVELY_EASY = 2,
  /** 中等 */
  MEDIUM = 3,
  /** 较难 */
  RELATIVELY_HARD = 4,
  /** 困难 */
  HARD = 5,
}

/**
 * 问题难易程度标签映射
 */
export const QuestionDifficultyLabel: Record<QuestionDifficulty, string> = {
  [QuestionDifficulty.EASY]: '简单',
  [QuestionDifficulty.RELATIVELY_EASY]: '较易',
  [QuestionDifficulty.MEDIUM]: '中等',
  [QuestionDifficulty.RELATIVELY_HARD]: '较难',
  [QuestionDifficulty.HARD]: '困难',
};

/**
 * 问题难易程度颜色映射
 */
export const QuestionDifficultyColor: Record<QuestionDifficulty, string> = {
  [QuestionDifficulty.EASY]: 'green',
  [QuestionDifficulty.RELATIVELY_EASY]: 'cyan',
  [QuestionDifficulty.MEDIUM]: 'blue',
  [QuestionDifficulty.RELATIVELY_HARD]: 'orange',
  [QuestionDifficulty.HARD]: 'red',
};
