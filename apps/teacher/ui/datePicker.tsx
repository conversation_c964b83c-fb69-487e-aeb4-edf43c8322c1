"use client";

import { But<PERSON> } from "@/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/popover";
import { cn } from "@/utils/utils";
import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { format as formatFn } from "date-fns";
import { zhCN } from "date-fns/locale";
import * as React from "react";
import { Calendar } from "./calendar";
// import ReactDOM from "react-dom";
import { CalendarIcon } from "lucide-react";
// 基础类型定义
export type DateValue = Date | undefined;
export type DateRangeValue = [DateValue, DateValue];
export type ValueType<M> = M extends "single" ? DateValue : DateRangeValue;

// 时间选择器配置
export interface TimePickerConfig {
  showSecond?: boolean;
}

// 位置偏移配置
export interface OffsetConfig {
  x?: number;
  y?: number;
}

// 内部状态类型
interface DateRangeState {
  from: DateValue;
  to: DateValue;
}

// 快捷选项类型
export interface QuickOption {
  label: string;
  value: string;
  getDateRange: () => DateRangeState;
}

export interface DatePickerProps<T extends "single" | "range"> {
  icon?: (isOpen: Signal<boolean>) => React.ReactNode;
  onClick?: () => void;
  value?: T extends "single" ? DateValue : DateRangeValue;
  onChange?: (value: T extends "single" ? DateValue : DateRangeValue) => void;
  onConfirm?: (value: T extends "single" ? DateValue : DateRangeValue) => void;
  mode?: T;
  className?: string;
  placeholder?: string;
  quickOptions?: QuickOption[] | false;
  formatDate?: (value: DateValue | DateRangeValue, _format?: string) => string;
  format?: string;
  defaultQuickOption?: string;
  onMonthChange?: (month: Date) => void;
  showTime?: boolean;
  timePickerConfig?: TimePickerConfig;
  offset?: {
    x?: number;
    y?: number;
  };
  min?: number;
  max?: number;
  textBtnClassName?: string;
}

// 默认快捷选项
const DEFAULT_QUICK_OPTIONS: QuickOption[] = [
  {
    label: "最近一周",
    value: "last7days",
    getDateRange: () => {
      const to = new Date();
      const from = new Date();
      from.setDate(to.getDate() - 6);
      return { from, to };
    },
  },
  {
    label: "最近两周",
    value: "last2weeks",
    getDateRange: () => {
      const to = new Date();
      const from = new Date();
      from.setDate(to.getDate() - 13);
      return { from, to };
    },
  },
  {
    label: "1个月内",
    value: "last1month",
    getDateRange: () => {
      const to = new Date();
      const from = new Date();
      from.setMonth(to.getMonth() - 1);
      return { from, to };
    },
  },
];

// 辅助函数：比较两个日期是否是同一天
const isSameDay = (date1?: Date, date2?: Date): boolean => {
  if (!date1 || !date2) return false;
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

// 时间选择器组件
interface TimePickerProps {
  value?: Date;
  onChange?: (date: Date) => void;
  showSecond?: boolean;
  className?: string;
}

function TimePicker({
  value,
  onChange,
  showSecond = false,
  className,
}: TimePickerProps) {
  const currentDate = useSignal<Date>(value ? new Date(value) : new Date());
  React.useEffect(() => {
    if (value) {
      currentDate.value = new Date(value);
    }
  }, [value]);

  const hours = Array.from({ length: 24 }, (_, i) => i);
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  // 滚动到选中的小时和分钟
  const hourRef = React.useRef<HTMLDivElement>(null);
  const minuteRef = React.useRef<HTMLDivElement>(null);

  // 使用简单、可靠的滚动方法
  const scrollToItem = React.useCallback(
    (container: HTMLDivElement | null, itemIndex: number) => {
      if (!container) return;

      // 基础单项高度
      const itemHeight = 40;

      // 滚动到该位置，考虑顶部填充
      // 目标: 将选中项放在容器中央附近
      const targetScrollTop =
        Math.max(
          0,
          itemIndex * itemHeight - container.clientHeight / 2 + itemHeight / 2
        ) + 40;

      // 使用简单的滚动方式，避免复杂的计算
      try {
        container.scrollTo({
          top: targetScrollTop,
          behavior: "smooth",
        });
      } catch (e) {
        // 降级处理：如果smooth不支持，直接设置scrollTop
        container.scrollTop = targetScrollTop;
      }
    },
    []
  );

  // 首次渲染和值变化时滚动到对应位置
  React.useEffect(() => {
    const scrollHandler = () => {
      if (hourRef.current && currentDate.value) {
        const hourIndex = currentDate.value.getHours();
        scrollToItem(hourRef.current, hourIndex);
      }

      if (minuteRef.current && currentDate.value) {
        const minuteIndex = currentDate.value.getMinutes();
        scrollToItem(minuteRef.current, minuteIndex);
      }
    };

    // 延迟执行以确保渲染完成
    const timer = setTimeout(scrollHandler, 100);
    return () => clearTimeout(timer);
  }, [scrollToItem, currentDate.value]);

  // 处理小时变化
  const handleHourChange = React.useCallback(
    (hour: number) => {
      if (hour === currentDate.value.getHours()) return; // 避免重复设置相同的值

      const newDate = new Date(currentDate.value);
      newDate.setHours(hour);
      currentDate.value = newDate;
      onChange?.(newDate);

      // 确保视图更新后滚动到正确位置
      requestAnimationFrame(() => {
        if (hourRef.current) {
          scrollToItem(hourRef.current, hour);
        }
      });
    },
    [currentDate, onChange, scrollToItem]
  );

  // 处理分钟变化
  const handleMinuteChange = React.useCallback(
    (minute: number) => {
      if (minute === currentDate.value.getMinutes()) return; // 避免重复设置相同的值

      const newDate = new Date(currentDate.value);
      newDate.setMinutes(minute);
      currentDate.value = newDate;
      onChange?.(newDate);

      // 确保视图更新后滚动到正确位置
      requestAnimationFrame(() => {
        if (minuteRef.current) {
          scrollToItem(minuteRef.current, minute);
        }
      });
    },
    [currentDate, onChange, scrollToItem]
  );

  return (
    <div className={cn("flex h-60", className)}>
      {/* 小时选择器 */}
      <div
        ref={hourRef}
        className="scrollbar-thin h-full w-16 overflow-y-auto border-r"
        style={{ scrollbarWidth: "thin" }}
      >
        {/* 顶部填充，确保内容可以滚动到顶部 */}
        <div className="h-[60px]" />

        {hours.map((hour) => (
          <div
            key={hour}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleHourChange(hour);
            }}
            className={cn(
              "flex h-10 w-full cursor-pointer items-center justify-center text-base transition-all hover:bg-blue-50 hover:text-blue-700",
              currentDate.value.getHours() === hour
                ? "bg-blue-100 font-medium text-blue-700"
                : "text-gray-600"
            )}
          >
            {String(hour).padStart(2, "0")}
          </div>
        ))}

        {/* 底部填充，确保内容可以滚动到底部 */}
        <div className="h-[60px]" />
      </div>

      {/* 分钟选择器 */}
      <div
        ref={minuteRef}
        className="scrollbar-thin h-full w-16 overflow-y-auto"
        style={{ scrollbarWidth: "thin" }}
      >
        {/* 顶部填充，确保内容可以滚动到顶部 */}
        <div className="h-[60px]" />

        {minutes.map((minute) => (
          <div
            key={minute}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleMinuteChange(minute);
            }}
            className={cn(
              "flex h-10 w-full cursor-pointer items-center justify-center text-base transition-all hover:bg-blue-50 hover:text-blue-700",
              currentDate.value.getMinutes() === minute
                ? "bg-blue-100 font-medium text-blue-700"
                : "text-gray-600"
            )}
          >
            {String(minute).padStart(2, "0")}
          </div>
        ))}

        {/* 底部填充，确保内容可以滚动到底部 */}
        <div className="h-[60px]" />
      </div>
    </div>
  );
}

export function DatePicker<T extends "single" | "range">({
  icon,
  onClick,
  format,
  value,
  onChange,
  onConfirm,
  mode = "single" as T,
  className,
  placeholder = "选择日期",
  quickOptions = DEFAULT_QUICK_OPTIONS,
  defaultQuickOption = "last7days",
  formatDate,
  onMonthChange,
  showTime = false,
  timePickerConfig = { showSecond: false },
  offset = { x: 0, y: 0 },
  min,
  max,
  textBtnClassName,
}: DatePickerProps<T>) {
  // 根据showTime属性更新默认format
  const defaultFormat = React.useMemo(() => {
    if (!showTime) return "yyyy年MM月dd日";
    return timePickerConfig.showSecond
      ? "yyyy年MM月dd日 HH:mm:ss"
      : "yyyy年MM月dd日 HH:mm";
  }, [showTime, timePickerConfig.showSecond]);

  // 确保format使用正确的默认值
  const effectiveFormat = format || defaultFormat;

  // 默认格式化函数
  const defaultFormatDate = React.useCallback(
    (val: DateValue | DateRangeValue, _format?: string): string => {
      const dateFormat = _format || effectiveFormat;
      if (!val) return "";

      if (Array.isArray(val)) {
        const [from, to] = val;
        // 如果 from 和 to 是同一天，只返回 from
        if (from && to && isSameDay(from, to)) {
          return formatFn(from, dateFormat, { locale: zhCN });
        }

        const startStr = from
          ? formatFn(from, dateFormat, { locale: zhCN })
          : "";
        const endStr = to
          ? ` - ${formatFn(to, dateFormat, { locale: zhCN })}`
          : "";
        return `${startStr}${endStr}`;
      }
      return val ? formatFn(val, dateFormat, { locale: zhCN }) : "";
    },
    [effectiveFormat]
  );

  const formatter = formatDate || defaultFormatDate;

  // 获取初始状态
  const getInitialDateState = (): DateRangeState => {
    if (value) {
      if (Array.isArray(value)) {
        return { from: value[0], to: value[1] };
      }
      return { from: value, to: undefined };
    }

    if (
      mode === "range" &&
      Array.isArray(quickOptions) &&
      quickOptions.length > 0
    ) {
      const defaultOption =
        quickOptions.find((opt) => opt.value === defaultQuickOption) ||
        quickOptions[0];
      return defaultOption.getDateRange();
    }

    return { from: undefined, to: undefined };
  };

  // 状态管理
  const initialState = React.useMemo(
    () => getInitialDateState(),
    [value, mode, quickOptions, defaultQuickOption]
  );
  const dateState = useSignal<DateRangeState>(initialState);
  const confirmedDateState = useSignal<DateRangeState>(initialState);
  const isOpen = useSignal(false);
  React.useEffect(() => {
    confirmedDateState.value = initialState;
  }, [initialState]);

  // 当前显示的月份
  const currentMonth = useSignal<Date>(dateState.value.from || new Date());

  // 处理点击外部
  const datePickerRef = React.useRef<HTMLDivElement>(null);
  const popoverContentRef = React.useRef<HTMLDivElement>(null);

  // 处理打开弹窗时，设置当前显示月份
  React.useEffect(() => {
    if (isOpen.value) {
      // 打开时重置为已确认的状态
      dateState.value = { ...confirmedDateState.value };
      selectedQuickOption.value = confirmedQuickOption.value;
      isManualSelect.value = confirmedIsManualSelect.value;

      // 如果有日期值，则显示该日期所在月份，否则显示当前月份
      currentMonth.value = confirmedDateState.value.from || new Date();

      // 触发onMonthChange回调
      if (onMonthChange) {
        onMonthChange(currentMonth.value);
      }
    }
  }, [isOpen.value, onMonthChange]);

  // 处理月份变化
  const handleMonthChange = (month: Date) => {
    currentMonth.value = month;
    if (onMonthChange) {
      onMonthChange(month);
    }
  };

  // 类型保护的值转换函数
  const emitValue = (state: DateRangeState): ValueType<typeof mode> => {
    if (mode === "single") {
      return state.from as ValueType<typeof mode>;
    }
    return [state.from, state.to] as unknown as ValueType<typeof mode>;
  };

  // 获取初始快捷选项
  const getInitialQuickOption = (): string | null => {
    if (mode !== "range" || !Array.isArray(quickOptions)) return null;

    if (Array.isArray(value) && value[0] && value[1]) {
      const matchedOption = quickOptions.find((opt) => {
        const range = opt.getDateRange();
        return isSameDay(range.from, value[0]) && isSameDay(range.to, value[1]);
      });
      return matchedOption?.value ?? null;
    }

    return defaultQuickOption;
  };

  const selectedQuickOption = useSignal<string | null>(getInitialQuickOption());
  const confirmedQuickOption = useSignal<string | null>(
    getInitialQuickOption()
  );
  const isManualSelect = useSignal(selectedQuickOption.value === null);
  const confirmedIsManualSelect = useSignal(selectedQuickOption.value === null);
  React.useEffect(() => {
    const v = getInitialQuickOption();
    confirmedQuickOption.value = v;
    confirmedIsManualSelect.value = v === null;
  }, [value]);

  // 同步状态到外部
  const syncToExternalValue = (state: DateRangeState) => {
    onChange?.(emitValue(state));
  };

  // 初始化时同步一次状态
  React.useEffect(() => {
    if (initialState.from || initialState.to) {
      syncToExternalValue(initialState);
    }
  }, []);

  // 处理快捷选择
  const handleQuickSelect = (option: QuickOption) => {
    if (mode === "range") {
      const range = option.getDateRange();
      dateState.value = range;
      selectedQuickOption.value = option.value;
      isManualSelect.value = false;
      syncToExternalValue(range);

      // 更新当前显示月份为范围的开始月份
      if (range.from) {
        currentMonth.value = range.from;
        if (onMonthChange) {
          onMonthChange(range.from);
        }
      }
    }
  };
  const handleDayClick = (day: Date, modifiers: Record<string, boolean>) => {
    if (mode === "single") {
      dateState.value = { from: day, to: undefined };
      syncToExternalValue(dateState.value);
    } else if (mode === "range") {
      dateState.value = { from: day, to: day };
    }
  };
  // 处理日历选择
  const handleCalendarSelect = (
    val: Date | { from?: Date; to?: Date } | undefined
  ) => {
    if (mode === "single") {
      if (!val) return;

      // 如果是单选模式，保留时间部分
      let newDate: Date;
      if (val instanceof Date) {
        newDate = new Date(val);
        // 保留现有时间
        if (dateState.value.from) {
          newDate.setHours(dateState.value.from.getHours());
          newDate.setMinutes(dateState.value.from.getMinutes());
          newDate.setSeconds(dateState.value.from.getSeconds());
          newDate.setMilliseconds(dateState.value.from.getMilliseconds());
        }
      } else {
        // 不应该发生，因为单选模式下应该收到Date类型
        return;
      }

      const newState = { from: newDate, to: undefined };
      dateState.value = newState;

      // 如果不显示时间，就直接确认并关闭弹窗
      if (!showTime) {
        confirmedDateState.value = newState;
        isManualSelect.value = true;
        confirmedIsManualSelect.value = true;
        selectedQuickOption.value = null;
        confirmedQuickOption.value = null;
        syncToExternalValue(newState);
        isOpen.value = false;
      } else {
        // 如果显示时间，只更新状态，不关闭弹窗，等待确认
        // 这里不调用syncToExternalValue，因为需要等待用户点击确认按钮
      }
    } else if (mode === "range") {
      const oldFrom = dateState.value.from;
      const oldTo = dateState.value.to;
      // range 模式下，如果 val 为 undefined，点击的是开始日期
      if (!val) {
        dateState.value = {
          from: dateState.value.from,
          to: undefined,
        };
        syncToExternalValue(dateState.value);
        return;
      }

      // 范围选择逻辑不变
      const dateValue = val as { from?: Date; to?: Date };

      // 判断新的 from 和 to 是否和之前的 from 和 to 相同，并且 from不等于to

      // from是否等于 to
      const isOldFromEqualto = isSameDay(oldFrom, oldTo);
      const isFromEqualto = isSameDay(dateValue.from, dateValue.to);
      // from是否等于上一次的 from
      const isFromEqualFrom = isSameDay(dateValue.from, oldFrom);
      // to是否等于上一次的 to
      const isToEqualTo = isSameDay(dateValue.to, oldTo);

      // !古法代码，谨慎修改
      if (
        (isFromEqualFrom || isToEqualTo) &&
        !isFromEqualto &&
        !isOldFromEqualto
      ) {
        dateState.value = {
          from: isFromEqualFrom ? dateValue.to : dateValue.from,
          to: isToEqualTo ? dateValue.from : dateValue.to,
        };

        if (!oldTo && dateValue.to) {
          dateState.value = {
            ...dateState.value,
            from: oldFrom,
          };
        }
        syncToExternalValue(dateState.value);
        return;
      }
      const newState = {
        from: dateValue.from ?? dateState.value.from,
        to: dateValue.to ?? dateState.value.to,
      };
      dateState.value = newState;

      if (newState.from && newState.to && Array.isArray(quickOptions)) {
        const matchedOption = quickOptions.find((opt) => {
          const range = opt.getDateRange();
          return (
            isSameDay(range.from, newState.from) &&
            isSameDay(range.to, newState.to)
          );
        });
        selectedQuickOption.value = matchedOption?.value ?? null;
        isManualSelect.value = !matchedOption;
      }

      syncToExternalValue(newState);
    }
  };

  // 处理时间选择
  const handleTimeChange = (date: Date) => {
    if (mode === "single" && dateState.value.from) {
      const newDate = new Date(dateState.value.from);
      newDate.setHours(date.getHours());
      newDate.setMinutes(date.getMinutes());
      newDate.setSeconds(date.getSeconds());

      const newState = { from: newDate, to: undefined };
      dateState.value = newState;

      // 不调用syncToExternalValue，因为需要等待用户确认
    }
  };

  // 计算显示文本
  const currentFormatDisplay = useComputed(() =>
    formatter(emitValue(dateState.value), effectiveFormat)
  );

  // 获取显示文本
  const getDisplayText = () => {
    const state = confirmedDateState.value;
    if (!state.from) return placeholder;

    if (
      confirmedQuickOption.value &&
      !confirmedIsManualSelect.value &&
      Array.isArray(quickOptions)
    ) {
      const option = quickOptions.find(
        (opt) => opt.value === confirmedQuickOption.value
      );
      if (option) return option.label;
    }

    return formatter(emitValue(state), effectiveFormat);
  };

  // 处理确认
  const handleConfirm = () => {
    isOpen.value = false;
    confirmedDateState.value = dateState.value;
    confirmedQuickOption.value = selectedQuickOption.value;
    confirmedIsManualSelect.value = isManualSelect.value;

    if (
      dateState.value.from &&
      dateState.value.to &&
      Array.isArray(quickOptions)
    ) {
      const matchedOption = quickOptions.find((opt) => {
        const range = opt.getDateRange();
        return (
          isSameDay(range.from, dateState.value.from) &&
          isSameDay(range.to, dateState.value.to)
        );
      });
      if (matchedOption) {
        selectedQuickOption.value = matchedOption.value;
        confirmedQuickOption.value = matchedOption.value;
        isManualSelect.value = false;
        confirmedIsManualSelect.value = false;
      } else {
        selectedQuickOption.value = null;
        confirmedQuickOption.value = null;
        isManualSelect.value = false;
        confirmedIsManualSelect.value = false;
      }
    }

    // 在确认时将日期状态同步到外部
    syncToExternalValue(dateState.value);
    onConfirm?.(emitValue(dateState.value));
  };

  const handleClickOutside = React.useCallback((e: MouseEvent) => {
    if (!isOpen.value && datePickerRef.current && popoverContentRef.current) {
      // if (datePickerRef.current && popoverContentRef.current) {
      // 检查点击元素是否在日期选择器内部
      const target = e.target as Node;
      if (
        !popoverContentRef.current.contains(target) &&
        !datePickerRef.current.contains(target)
      ) {
        // 点击外部时关闭日期选择器并恢复之前确认的状态
        // isOpen.value = false;
        // dateState.value = { ...confirmedDateState.value };
        // selectedQuickOption.value = confirmedQuickOption.value;
        // isManualSelect.value = confirmedIsManualSelect.value;
        handleConfirm();
      }
    }
  }, []);
  const handleClickOutsideMobile = React.useCallback((e: Event) => {
    if (isOpen.value && datePickerRef.current && popoverContentRef.current) {
      // if (datePickerRef.current && popoverContentRef.current) {
      // 检查点击元素是否在日期选择器内部
      const target = e.target as Node;
      if (
        !popoverContentRef.current.contains(target) &&
        !datePickerRef.current.contains(target)
      ) {
        // 点击外部时关闭日期选择器并恢复之前确认的状态
        // isOpen.value = false;
        // dateState.value = { ...confirmedDateState.value };
        // selectedQuickOption.value = confirmedQuickOption.value;
        // isManualSelect.value = confirmedIsManualSelect.value;
        handleConfirm();
      }
    }
  }, []);

  // 添加和移除点击事件监听器
  React.useEffect(() => {
    if (isOpen.value) {
      // 使用setTimeout确保监听器在当前事件循环后添加
      const timer = setTimeout(() => {
        document.addEventListener("mousedown", handleClickOutside);
      }, 0);
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("touchstart", handleClickOutsideMobile);
      return () => {
        clearTimeout(timer);
        setTimeout(() => {
          document.removeEventListener("mousedown", handleClickOutside);
          document.removeEventListener("touchstart", handleClickOutsideMobile);
        }, 0);
      };
    }

    return undefined;
  }, [isOpen.value, handleClickOutside]);

  // 处理取消
  const handleCancel = () => {
    // 取消时，恢复到已确认的状态
    dateState.value = { ...confirmedDateState.value };
    selectedQuickOption.value = confirmedQuickOption.value;
    isManualSelect.value = confirmedIsManualSelect.value;
    isOpen.value = false;
  };

  return (
    <>
      <Popover
        modal={true}
        open={isOpen.value}
        onOpenChange={(open) => (isOpen.value = open)}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "hover:text-gray-2 relative justify-between text-left font-normal transition-all duration-300",
              !confirmedDateState.value.from && "text-muted-foreground",
              className
            )}
            onClick={onClick}
          >
            <span className={cn(textBtnClassName)}>{getDisplayText()}</span>
            {icon ? icon(isOpen) : <CalendarIcon className="h-4 w-4" />}
            {/* <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform duration-300",
                isOpen.value ? "rotate-[-180deg] text-primary-1" : "rotate-[0deg] text-gray-2"
              )}
            /> */}
            {/* 日期 icon */}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="z-50 mr-6 w-auto overflow-hidden rounded-3xl border-none bg-white p-0 shadow-lg"
          align="start"
          alignOffset={offset.x}
          sideOffset={offset.y}
          ref={(el) => {
            if (el) {
              datePickerRef.current = el;
              popoverContentRef.current = el;
            }
          }}
        >
          <div
            className="flex"
            onClick={(e) => {
              // 阻止事件冒泡，防止点击内部元素时关闭
              e.stopPropagation();
            }}
          >
            {mode === "range" ? (
              <div className="flex">
                {quickOptions && quickOptions.length > 0 && (
                  <div className="flex w-32 flex-col space-y-2 bg-blue-50 px-3 py-8">
                    {quickOptions.map((option) => (
                      <Button
                        key={option.value}
                        variant="ghost"
                        className={cn(
                          "justify-start px-2",
                          selectedQuickOption.value === option.value &&
                            "bg-blue-100 text-blue-700"
                        )}
                        onClick={() => handleQuickSelect(option)}
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            ) : null}

            <div className="">
              {mode === "single" ? (
                <div className="flex flex-col">
                  <div className="flex">
                    <Calendar
                      month={currentMonth.value}
                      onMonthChange={handleMonthChange}
                      mode="single"
                      selected={dateState.value.from}
                      onSelect={handleCalendarSelect}
                      locale={zhCN}
                      classNames={
                        {
                          // day_range_start:
                          //   "flex w-6 h-6 m-1 day-range-start p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full aria-selected:bg-primary-2 aria-selected:text-white",
                          // day_range_end:
                          //   "flex w-6 h-6 m-1 day-range-end p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full aria-selected:bg-primary-2 aria-selected:text-white",
                          // day_selected:
                          //   "flex w-6 h-6 m-1 p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full bg-primary-2 text-white hover:bg-primary-2 hover:text-white focus:bg-primary-2 focus:text-white",
                        }
                      }
                    />
                    {showTime && (
                      <div className="flex w-32 flex-col border-l">
                        <div
                          className="flex h-10 items-center justify-center border-b text-xl font-medium"
                          style={{
                            color: "var(--gray-1, #101019)",
                            fontFamily: "Avenir",
                            fontSize: "1rem",
                            fontStyle: "normal",
                            fontWeight: "800",
                            lineHeight: "150%",
                            fontVariantNumeric: "lining-nums proportional-nums",
                          }}
                        >
                          <span>
                            {dateState.value.from
                              ? String(
                                  dateState.value.from.getHours()
                                ).padStart(2, "0")
                              : "00"}
                          </span>
                          <span className="px-1">:</span>
                          <span>
                            {dateState.value.from
                              ? String(
                                  dateState.value.from.getMinutes()
                                ).padStart(2, "0")
                              : "00"}
                          </span>
                        </div>
                        <div className="h-[240px] overflow-hidden">
                          <TimePicker
                            value={dateState.value.from}
                            onChange={handleTimeChange}
                            showSecond={timePickerConfig.showSecond}
                            className="flex-1 p-0"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {showTime && (
                    <div className="flex items-center justify-between border-t px-4 py-3">
                      <div></div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleCancel}
                          className="rounded-full px-3 py-1 text-indigo-500"
                        >
                          取消
                        </Button>
                        <Button
                          size="sm"
                          className="bg-primary-2 rounded-full px-4 py-1 text-white"
                          onClick={handleConfirm}
                        >
                          确定
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white">
                  <Calendar
                    month={currentMonth.value}
                    onMonthChange={handleMonthChange}
                    mode="range"
                    min={min}
                    max={max}
                    selected={dateState.value}
                    onSelect={handleCalendarSelect}
                    locale={zhCN}
                    numberOfMonths={2}
                    className=""
                    classNames={{
                      day_range_start:
                        "flex w-6 h-6 m-1 day-range-start p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full aria-selected:bg-primary-2 aria-selected:text-white",
                      day_range_end:
                        "flex w-6 h-6 m-1 day-range-end p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full aria-selected:bg-primary-2 aria-selected:text-white",
                      day_selected:
                        "flex w-6 h-6 m-1 p-[3px_4px] leading-1 flex-col justify-center items-center gap-2.5 !rounded-full bg-primary-2 text-white hover:bg-primary-2 hover:text-white focus:bg-primary-2 focus:text-white",
                    }}
                  />

                  <div className="flex items-center justify-between border-t px-8 py-5">
                    <div className="text-sm font-[600]">
                      {currentFormatDisplay}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleCancel}
                        className="box-shadow-none rounded-full border-none bg-white px-1 text-center text-base font-medium leading-normal text-indigo-500"
                      >
                        取消
                      </Button>
                      <Button
                        size="sm"
                        className="bg-primary-2 rounded-full px-6 py-4 text-base font-medium leading-5 text-white"
                        onClick={handleConfirm}
                      >
                        确定
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
