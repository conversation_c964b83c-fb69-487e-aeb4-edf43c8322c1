import { ApiLessonSummary } from "@/types/api/lesson";
import useS<PERSON> from "swr";

import { CourseSummary, WidgetStatus, WidgetType } from "@/types/app/course";
import { get } from "../utils/fetcher";

const useCourseModel = (knowledgeId: number) => {
  const { data, isLoading, error, mutate } = useSWR(
    `/api/v1/lesson/info?knowledgeId=${knowledgeId}`,
    (url) => get<ApiLessonSummary>(url, {}),
    { revalidateOnFocus: false }
  );

  const parse = (_data: ApiLessonSummary | undefined) => {
    if (!_data) return undefined;
    const {
      lessonId,
      lessonName,
      theme,
      totalWidgetNum,
      lessonWidgets,
      nextQuestionParams,
      currentWidgetIndex,
      lessonVersion,
    } = _data;

    const lessonSummary: CourseSummary = {
      lessonId,
      name: lessonName,
      theme: theme,
      total: totalWidgetNum,
      currentWidgetIndex,
      nextQuestionParams,
      lessonVersion,
      widgets: lessonWidgets.map((widget) => {
        const { widgetIndex, widgetName, widgetType, status, cdnUrl } = widget;
        return {
          index: widgetIndex,
          name: widgetName,
          type: widgetType as WidgetType,
          status: status as WidgetStatus,
          cdnUrl,
        };
      }),
    };

    return lessonSummary;
  };

  return {
    data: parse(data),
    isLoading,
    error,
    mutate,
  };
};

export { useCourseModel };
