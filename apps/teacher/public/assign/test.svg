<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#181;&#139;&#233;&#170;&#140;&#229;&#155;&#190;&#230;&#160;&#135;">
<g id="&#228;&#185;&#166;&#230;&#156;&#172;">
<g id="Rectangle 34624304" filter="url(#filter0_di_916_4519)">
<rect x="1" y="2" width="40" height="50" rx="9" fill="url(#paint0_linear_916_4519)"/>
</g>
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M12 16C12 17.1046 11.1046 18 10 18C8.89543 18 8 17.1046 8 16C8 14.8954 8.89543 14 10 14C11.1046 14 12 14.8954 12 16ZM14 16C14 14.8954 14.8954 14 16 14H31C32.1046 14 33 14.8954 33 16C33 17.1046 32.1046 18 31 18H16C14.8954 18 14 17.1046 14 16ZM12 25C12 26.1046 11.1046 27 10 27C8.89543 27 8 26.1046 8 25C8 23.8954 8.89543 23 10 23C11.1046 23 12 23.8954 12 25ZM14 25C14 23.8954 14.8954 23 16 23H24C25.1046 23 26 23.8954 26 25C26 26.1046 25.1046 27 24 27H16C14.8954 27 14 26.1046 14 25Z" fill="url(#paint1_linear_916_4519)"/>
</g>
<g id="&#229;&#175;&#185;&#229;&#143;&#183;">
<foreignObject x="30.5055" y="17.206" width="28.4945" height="38.794"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_0_916_4519_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624307" filter="url(#filter1_dii_916_4519)" data-figma-bg-blur-radius="10">
<path d="M41.9749 34.5499C38.8841 31.9007 41.0273 26.8463 45.0804 27.2263L46.9107 27.3979C47.5366 27.4566 48.0251 27.9648 48.0591 28.5925L49 46L42.8518 35.5954C42.6189 35.2012 42.3226 34.8479 41.9749 34.5499Z" fill="#B8FF04" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<foreignObject x="14" y="12" width="50" height="50"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_1_916_4519_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624306" filter="url(#filter2_dii_916_4519)" data-figma-bg-blur-radius="10">
<rect x="24" y="22" width="30" height="30" rx="15" fill="url(#paint2_linear_916_4519)" fill-opacity="0.3" shape-rendering="crispEdges"/>
<rect x="24.5" y="22.5" width="29" height="29" rx="14.5" stroke="url(#paint3_linear_916_4519)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Vector 5562" filter="url(#filter3_d_916_4519)">
<path d="M33 37.5L37.028 41.3027C37.4341 41.686 38.0754 41.6625 38.4523 41.2505L46 33" stroke="url(#paint4_linear_916_4519)" stroke-opacity="0.9" stroke-width="4" stroke-linecap="round" shape-rendering="crispEdges"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_di_916_4519" x="-3" y="0" width="48" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.913725 0 0 0 0 0.588235 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_4519"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_4519" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_4519"/>
</filter>
<filter id="filter1_dii_916_4519" x="30.5055" y="17.206" width="28.4945" height="38.794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.27451 0 0 0 0 0.898039 0 0 0 0 0.388235 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_4519"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_4519" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.235294 0 0 0 0 0.933333 0 0 0 0 0.603922 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_4519"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_916_4519" result="effect3_innerShadow_916_4519"/>
</filter>
<clipPath id="bgblur_0_916_4519_clip_path" transform="translate(-30.5055 -17.206)"><path d="M41.9749 34.5499C38.8841 31.9007 41.0273 26.8463 45.0804 27.2263L46.9107 27.3979C47.5366 27.4566 48.0251 27.9648 48.0591 28.5925L49 46L42.8518 35.5954C42.6189 35.2012 42.3226 34.8479 41.9749 34.5499Z"/>
</clipPath><filter id="filter2_dii_916_4519" x="14" y="12" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.27451 0 0 0 0 0.898039 0 0 0 0 0.388235 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_4519"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_4519" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.235294 0 0 0 0 0.933333 0 0 0 0 0.603922 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_4519"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_916_4519" result="effect3_innerShadow_916_4519"/>
</filter>
<clipPath id="bgblur_1_916_4519_clip_path" transform="translate(-14 -12)"><rect x="24" y="22" width="30" height="30" rx="15"/>
</clipPath><filter id="filter3_d_916_4519" x="29" y="30" width="21" height="16.5755" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.262745 0 0 0 0 0.909804 0 0 0 0 0.45098 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_4519"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_4519" result="shape"/>
</filter>
<linearGradient id="paint0_linear_916_4519" x1="8.5" y1="2" x2="35" y2="52" gradientUnits="userSpaceOnUse">
<stop stop-color="#37F2B3"/>
<stop offset="1" stop-color="#1DCC69"/>
</linearGradient>
<linearGradient id="paint1_linear_916_4519" x1="33" y1="36" x2="7" y2="14.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#68FF9E"/>
<stop offset="1" stop-color="#CEFFD9"/>
</linearGradient>
<linearGradient id="paint2_linear_916_4519" x1="31.5" y1="30" x2="42.8878" y2="51.3906" gradientUnits="userSpaceOnUse">
<stop stop-color="#CAFFD2" stop-opacity="0.8"/>
<stop offset="1" stop-color="#4EFF77" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint3_linear_916_4519" x1="31" y1="22" x2="54.1054" y2="54.277" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="1" stop-color="#81FFB1"/>
</linearGradient>
<linearGradient id="paint4_linear_916_4519" x1="36" y1="40" x2="41.3266" y2="45.6364" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C0FF89"/>
</linearGradient>
</defs>
</svg>
