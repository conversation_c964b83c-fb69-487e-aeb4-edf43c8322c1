import { useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';

/**
 * 优化的导航 hook
 * 使用 RAF 和防抖来提升导航性能
 */
export function useOptimizedNavigation() {
  const router = useRouter();
  const isNavigatingRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const navigate = useCallback((
    path: string,
    options?: {
      beforeNavigation?: () => void;
      afterNavigation?: () => void;
      debounceMs?: number;
    }
  ) => {
    const { beforeNavigation, afterNavigation, debounceMs = 100 } = options || {};

    // 防止重复导航
    if (isNavigatingRef.current) {
      return;
    }

    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 防抖处理
    timeoutRef.current = setTimeout(() => {
      isNavigatingRef.current = true;

      // 使用 RAF 优化执行时机
      requestAnimationFrame(() => {
        beforeNavigation?.();

        // 下一帧执行导航
        requestAnimationFrame(() => {
          router.push(path);

          // 导航后的清理和回调
          requestAnimationFrame(() => {
            afterNavigation?.();
            isNavigatingRef.current = false;
          });
        });
      });
    }, debounceMs);
  }, [router]);

  const prefetch = useCallback((path: string) => {
    // 预加载页面
    router.prefetch(path);
  }, [router]);

  return {
    navigate,
    prefetch,
    isNavigating: isNavigatingRef.current
  };
}

/**
 * 预加载 hook
 * 在用户可能访问的页面上预加载资源
 */
export function usePrefetch() {
  const router = useRouter();

  const prefetchOnHover = useCallback((path: string) => {
    return {
      onMouseEnter: () => {
        router.prefetch(path);
      }
    };
  }, [router]);

  const prefetchOnVisible = useCallback((path: string) => {
    return {
      onFocus: () => {
        router.prefetch(path);
      }
    };
  }, [router]);

  return {
    prefetchOnHover,
    prefetchOnVisible
  };
}