export type PeriodGroup = {
  title: string;
  periods: ScheduleDayItem[];
};
export type SchedulePeriod = {
  time_span: number; // 1: 上午, 2: 下午, 3: 晚上
  period_no: number;
  start_time: string;
  end_time: string;
  day_of_week: number;
};

// 更新年级模板 params
export type GradeScheduleTemplateParams = {
  id?: number;
  /* 学校ID */
  school_id: number;
  /* 学年ID */
  school_year_id: number;
  /* 年级 */
  grade: number;
  /* 学期ID */
  semester_id: number;
  /* 开始日期 */
  start_date: string;
  /* 循环周期 */
  week_cycle: number;
  /* 课表 */
  schedule_periods: SchedulePeriod[];
};
export interface ScheduleData {
  schedule: Schedule;
  tpl_info: TplInfo;
}

export interface TplInfo {
  template_id: number;
  template_name: string;
  grade_level: number;
  semester_id: number;
  semester_name: string;
  start_date: string;
  week_cycle: number;
  period_info: PeriodInfo[];
}

export interface PeriodInfo {
  schedule_tpl_period_id: number;
  schedule_tpl_period_period_no: number;
  schedule_tpl_period_time_span: string;
  schedule_tpl_period_start_time: string;
  schedule_tpl_period_end_time: string;
}

export interface Schedule {
  '1': ScheduleDayItem[];
  '2': ScheduleDayItem[];
  '3': ScheduleDayItem[];
  '4': ScheduleDayItem[];
  '5': ScheduleDayItem[];
  '6': ScheduleDayItem[];
  '7': ScheduleDayItem[];
}

export interface ScheduleDayItem {
  schedule_tpl_period_id: number;
  schedule_tpl_period_period_no: number;
  schedule_tpl_period_time_span: string;
  schedule_tpl_period_start_time: string;
  schedule_tpl_period_end_time: string;
  class_schedule_course: string;
  class_schedule_course_id: number;
  class_schedule_teacher_id: number;
  teacher_name: string;
  class_schedule_study_type: number;
  is_modify?: number;
  is_delete?: number;
}

// 班级相关类型
export interface ClassBaseParams {
  /* 班级ID */
  class_id?: string;
  /* 班级名称 */
  class_name?: string;
  /* 学校ID */
  class_school_id: number;
  /* 年级 */
  class_grade: number;
  /* 学年ID */
  class_school_year_id: number;
  /* 班级类型 */
  class_type: number;
  /* 学习类型 */
  class_study_type: number;
  /* 是否考试 */
  class_is_test: number; // 0 or 1
  /* 学科类型 */
  subject_type: number;
  /* 合作状态 */
  collaboration_status: number;
  /* 学科使用状态 */
  subject_usage_status: number;
}

export type ClassCreateParams = ClassBaseParams;

export interface ClassUpdateParams {
  /* 班级ID */
  class_id: number;
  /* 班级名称 */
  class_name?: string;
  /* 是否考试 */
  class_is_test?: number; // 0 or 1
  /* 学习类型 */
  class_study_type?: number;
  /* 学科类型 */
  subject_type?: number;
  /* 合作状态 */
  collaboration_status?: number;
  /* 学科使用状态 */
  subject_usage_status?: number;
}

export interface ClassDetailParams {
  class_id: string;
}

export interface ClassDeleteParams {
  class_id: string;
}

export interface ClassDetail extends ClassBaseParams {
  class_id: string;
  create_time: string;
  update_time: string;
}

/**
 * 年级信息
 */
export interface GradeInfo {
  /** 年级ID */
  grade_id: number;
  /** 年级名称，如"高一年级" */
  grade_name: string;
  /** 是否展开年级节点 */
  is_expand: boolean;
  /** 年级下的班级分组 */
  children: ClassGroupInfo[];
}

/**
 * 班级分组信息（如真实班/测试班分组）
 */
export interface ClassGroupInfo {
  /** 是否为测试班分组，0-真实班分组，1-测试班分组 */
  is_test: number;
  /** 分组名称，如"真实班"、"测试班" */
  group_name: string;
  /** 是否展开分组节点 */
  is_expand: boolean;
  /** 分组下的班级列表 */
  children: ClassInfo[];
}

/**
 * 班级信息
 */
export interface ClassInfo {
  /** 班级ID */
  class_id: number;
  /** 学校ID */
  class_school_id: string;
  /** 年级ID */
  class_grade: string;
  /** 学年ID */
  class_school_year_id: string;
  /** 班级类型：1-行政班，2-教学班 */
  class_type: number;
  /** 学习类型：1-平板班，2-传统班 */
  class_study_type: number;
  /** 是否为测试班：0-真实班，1-测试班 */
  class_is_test: number;
  /** 班级名称，如"高一(1)班" */
  class_name: string;
  /** 文理类型：1-不分文理，3-理科班，2-文科班 */
  subject_type: number;
  /** 合作状态：1-正常，2-暂停，3-终止 */
  collaboration_status: number;
  /** 学科使用状态：1-自由使用，2-要求使用，3-不使用 */
  subject_usage_status: number;
  /** 创建时间 */
  create_time: string;
  /** 更新时间 */
  update_time: string;
}

export interface TeacherJobInfo {
  jobType: number;
  jobInfos: JobInfo[];
  jobSubject: string;
}

export interface JobInfo {
  jobGrade: number;
  jobClass: number[];
}

export interface TeacherSchedule {
  '1': TeacherScheduleItem[];
  '2': TeacherScheduleItem[];
  '3': TeacherScheduleItem[];
  '4': TeacherScheduleItem[];
  '5': TeacherScheduleItem[];
  '6': TeacherScheduleItem[];
  '7': TeacherScheduleItem[];
}

export interface TeacherScheduleItem {
  schedule_id: number;
  grade: number;
  grade_name: string;
  class_id: number;
  class_name: string;
  schedule_tpl_period_time_span: string;
  schedule_tpl_period_start_time: string;
  schedule_tpl_period_end_time: string;
  class_schedule_course_id: number;
  class_schedule_course: string;
  class_schedule_teacher_id: number;
  teacher_name: string;
  class_schedule_study_type: number;
  class_schedule_study_type_name: string;
  is_tmp: number;
  at_day: string;
  origin_schedule_id: number;
}
