import { AxiosInstance, AxiosRequestConfig } from "axios";

export interface GilAxiosInstance extends AxiosInstance {
  request<R = unknown, D = unknown>(config: AxiosRequestConfig<D> & { showToast?: boolean }): Promise<R>;
  get<R = unknown, D = unknown>(url: string, config?: AxiosRequestConfig<D> & { showToast?: boolean }): Promise<R>;
  post<R = unknown, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig<D> & { showToast?: boolean }): Promise<R>;
  put<R = unknown, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig<D> & { showToast?: boolean }): Promise<R>;
  delete<R = unknown, D = unknown>(url: string, config?: AxiosRequestConfig<D> & { showToast?: boolean }): Promise<R>;
}

export type GilResponse<T> = {
  code: number;
  message: string;
  data: T;
};

export type GilResponseData<T> = GilResponse<T>["data"];