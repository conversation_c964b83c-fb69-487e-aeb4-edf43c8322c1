import { request } from '@umijs/max';
import {
  AddStudentParams,
  BatchImportStudentsParams,
  EditStudentParams,
  SchoolStudentStats,
  StudentListParams,
  StudentListResponse,
  TransferStudentParams,
} from './type';

// 获取学生列表
export async function getStudentList(params: StudentListParams) {
  return request<API.ResponseBody<StudentListResponse>>(`/api/v1/student/list`, {
    method: 'GET',
    params,
  });
}

// 获取学校学生统计信息
export async function getSchoolStudentStats(schoolId: number) {
  return request<API.ResponseBody<SchoolStudentStats>>(`/api/v1/student/stats`, {
    method: 'GET',
    params: { schoolId },
  });
}

// 添加单个学生
export async function addStudent(params: AddStudentParams) {
  return request<API.ResponseBody<{ userID: number }>>(`/api/v1/userSchool/createStudent`, {
    method: 'POST',
    data: params,
  });
}

// 编辑学生信息
export async function editStudent(params: EditStudentParams) {
  return request<API.ResponseBody<{ success: boolean }>>(`/api/v1/userSchool/updateStudent`, {
    method: 'POST',
    data: params,
  });
}

// 学生转班
export async function transferStudent(params: TransferStudentParams) {
  return request<API.ResponseBody<{ success: boolean }>>(`/api/v1/userSchool/transStudent`, {
    method: 'POST',
    data: params,
  });
}

// 批量导入学生
export async function batchImportStudents(params: BatchImportStudentsParams) {
  return request<API.ResponseBody<{ success: boolean; failedCount: number }>>(
    `/api/v1/student/batch-import`,
    {
      method: 'POST',
      data: params,
    },
  );
}

// 批量导出学生
export async function exportStudents(params: StudentListParams) {
  return request<Blob>(`/api/v1/student/export`, {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

// 查看学生密码
export async function getStudentPassword(userID: number) {
  return request<API.ResponseBody<{ password: string }>>(`/api/v1/student/password`, {
    method: 'GET',
    params: { userID },
  });
}

// 发起账号审批
export async function initiateAccountApproval(schoolId: number) {
  return request<API.ResponseBody<{ approvalId: number }>>(`/api/v1/student/initiate-approval`, {
    method: 'POST',
    data: { schoolId },
  });
}

// 获取申报历史
export async function getApprovalHistory(schoolId: number) {
  return request<
    API.ResponseBody<{
      list: Array<{
        approvalId: number;
        approvalTime: string;
        approvalStatus: number;
        approvalCount: number;
      }>;
    }>
  >(`/api/v1/student/approval-history`, {
    method: 'GET',
    params: { schoolId },
  });
}

// 重置密码
export async function resetPassword(params: { userID: number; schoolID: number }) {
  return request<API.ResponseBody<string>>(`/api/v1/userSchool/resetPassword`, {
    method: 'POST',
    data: params,
  });
}
