import { Select, SelectContent, SelectItem, SelectTrigger } from "@/ui/select";
import { cn } from "@/utils/utils";
import { ChevronDownIcon } from "lucide-react";
import { useMemo } from "react";

export type FieldKeys = {
  label: string;
  value: string;
};

export type SelectOptions = {
  label: string;
  value: string;
};

interface AssignSelectProps {
  style?: React.CSSProperties;
  value: string;
  className?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any[];
  fieldNames?: {
    label: string;
    value: string;
  };
  placeholder?: string;
  align?: "center" | "start" | "end";
  variant?: "default" | "borderless" | "border";
  size?: "default" | "sm" | "md" | "lg" | "slg";
  onValueChange: (value: string) => void;
}

export function AssignSelect({
  style = {},
  className = "",
  value = "",
  options = [],
  fieldNames = { label: "label", value: "value" },
  placeholder = "请选择",
  align = "start",
  variant = "default",
  size = "default",
  onValueChange,
}: AssignSelectProps) {
  const innerOptions = useMemo(() => {
    return options.map((option) => ({
      label: option[fieldNames.label],
      value: String(option[fieldNames.value]),
    }));
  }, [options, fieldNames]);

  const variantClass = useMemo(() => {
    if (variant === "borderless") {
      return "";
    }
    if (variant === "border") {
      return "border border-slate-300";
    }
    return "";
  }, [variant]);

  const triggerName = useMemo(() => {
    return (
      innerOptions.find((option) => option.value === value)?.label ||
      placeholder
    );
  }, [innerOptions, value, placeholder]);

  const sizeClass = useMemo(() => {
    if (size === "default") {
      return "";
    }
    if (size === "sm") {
      return "h-7 px-2 py-0.5 bg-white rounded";
    }
    if (size === "md") {
      return "px-3 py-1.25 bg-white rounded-md";
    }
    if (size === "lg") {
      return "h-9 px-3 py-2 bg-white rounded-md";
    }
    if (size === "slg") {
      return "h-11 px-3 py-2 bg-white rounded-lg";
    }

    return "";
  }, [size]);

  return (
    <Select value={value} onValueChange={onValueChange} >
      <SelectTrigger
        className={cn("cursor-pointer", variantClass, sizeClass, className)}
        style={style}
        asChild
      >
        <div className="group inline-flex select-none items-center justify-between gap-1">
          <div className="text-gray-2 text-sm font-normal leading-normal">
            {triggerName}
          </div>
          <ChevronDownIcon className="size-3.5 group-data-[state=open]:rotate-180" />
        </div>
      </SelectTrigger>

      <SelectContent className="cursor-pointer max-h-59 min-w-30" align={align}>
        {innerOptions.map((option) => (
          <SelectItem key={option.value} value={option.value} className="hover:bg-primary-6 hover:text-primary-1 cursor-pointer">
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
