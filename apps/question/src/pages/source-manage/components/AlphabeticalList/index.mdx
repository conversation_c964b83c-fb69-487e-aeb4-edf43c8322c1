---
title: AlphabeticalList 字母选项组件
description: 一个用于创建和管理字母选项（A、B、C、D等）的表单组件，支持单选和多选两种模式
---

# AlphabeticalList 字母选项组件

一个用于创建和管理字母选项（A、B、C、D等）的表单组件，支持添加、删除、上下移动选项，以及选择正确答案。现已支持**单选/多选模式**，可用于单选题和多选题场景。

## 代码演示

### 单选模式（默认）

```tsx
import { Form } from 'antd';
import AlphabeticalList from './components/AlphabeticalList';

const Demo = () => (
  <Form>
    <Form.Item
      name="options"
      label="选项"
      rules={[{ required: true, message: '请填写所有选项内容' }]}
    >
      <AlphabeticalList />
    </Form.Item>
  </Form>
);
```

### 多选模式

```tsx
<Form.Item name="options" label="选项">
  <AlphabeticalList mode="multiple" />
</Form.Item>
```

### 禁用状态

```tsx
<Form.Item name="options" label="选项">
  <AlphabeticalList disabled />
</Form.Item>
```

## API

### AlphabeticalList Props

| 参数      | 说明                       | 类型                                   | 默认值    |
| --------- | -------------------------- | -------------------------------------- | --------- |
| value     | 组件的值                   | `AlphabeticalListValue`                | -         |
| onChange  | 值变化时的回调             | `(value: AlphabeticalListValue) => void` | -         |
| disabled  | 是否禁用                   | `boolean`                              | `false`   |
| status    | 校验状态                   | `'error' \| 'warning'`                 | -         |
| help      | 帮助文本                   | `React.ReactNode`                      | -         |
| mode      | 选项模式（单选/多选）      | `'single' \| 'multiple'`               | `'single'`|

### AlphabeticalListValue

```ts
// 单选模式
{
  selectedKey: string; // 选中的选项字母
  items: { key: string; value: string }[];
}
// 多选模式
{
  selectedKey: string[]; // 选中的选项字母数组
  items: { key: string; value: string }[];
}
```

## 功能特性

- 默认显示 A、B、C、D 四个选项
- 支持添加新选项（最多26个，对应A-Z）
- 支持删除选项（至少保留1个）
- 支持上下移动选项位置
- 支持**单选/多选**（mode 属性，单选用 Radio，多选用 Checkbox）
- 支持选择正确答案（多选时可多选）
- 支持表单验证
- 支持禁用状态

## 注意事项

1. 组件会自动维护字母标签（A、B、C、D等），移动选项时只移动内容和选中状态
2. 选项数量限制在1-26个之间
3. 表单验证默认在提交时触发
4. 所有选项都必须填写内容才能通过验证
5. 多选模式下 selectedKey 类型为 string[]

## 最佳实践

1. 建议在表单中使用 `validateTrigger={['onSubmit']}` 来控制验证时机
2. 可以通过 `rules` 属性自定义验证规则
3. 使用 `disabled` 属性在只读场景下禁用所有操作
4. 可以通过 `help` 属性显示帮助文本
5. 多选题场景请设置 `mode="multiple"`

## 相关组件

- [Form](https://ant.design/components/form-cn/) - 表单组件
- [Input](https://ant.design/components/input-cn/) - 输入框组件
- [Radio](https://ant.design/components/radio-cn/) - 单选框组件
- [Checkbox](https://ant.design/components/checkbox-cn/) - 多选框组件