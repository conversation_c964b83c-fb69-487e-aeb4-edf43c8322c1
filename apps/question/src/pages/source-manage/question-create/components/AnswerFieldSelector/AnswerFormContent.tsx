import { Feature } from '@/components/TiptapEditor/src/utils/enum';
import AlphabeticalList from '@/pages/source-manage/components/AlphabeticalList';
import EditableTiptap from '@/pages/source-manage/components/EditableTiptap';
import { Form, Input, Radio } from 'antd';
import React from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';
import ConditionalDisplay from '../shared/ConditionalDisplay';
import { useAnswerFieldSync } from './hooks';
import type { AnswerFormContentProps } from './types';
import { validateFormListAnswerItem } from './validate';

/**
 * 答案表单内容组件 - 在这里使用同步hook
 */
const AnswerFormContent: React.FC<AnswerFormContentProps> = ({
  fields,
  errors,
  questionAnswerMode,
  activeFillIdx,
  fieldManager,
  isMatchLine,
  questionOptionMatrixName,
}) => {
  const { form } = useQuestionCreate();
  // 在组件顶层调用同步hook
  useAnswerFieldSync({
    isMatchLine,
    activeFillIdx,
    questionOptionMatrixName,
    form,
    fields,
  });

  return (
    <>
      {fields.map(({ key, name, ...restField }, index) => {
        const isVisible = index === activeFillIdx;

        return (
          <div
            key={key}
            style={{
              display: isVisible ? 'block' : 'none',
              opacity: isVisible ? 1 : 0,
              transition: 'opacity 0.2s ease-in-out',
            }}
          >
            {/* 表单错误显示 */}
            {errors.length > 0 && (
              <Form.Item>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            )}

            {/* 单选题答案 */}
            <ConditionalDisplay visible={questionAnswerMode === 1}>
              <Form.Item
                name={[name, 'singleChoice']}
                label="答案"
                rules={validateFormListAnswerItem(1, questionAnswerMode)}
                {...restField}
              >
                <AlphabeticalList />
              </Form.Item>
            </ConditionalDisplay>

            {/* 多选题答案 */}
            <ConditionalDisplay visible={questionAnswerMode === 2}>
              <Form.Item
                name={[name, 'multipleChoice']}
                label="答案"
                rules={validateFormListAnswerItem(2, questionAnswerMode)}
                {...restField}
              >
                <AlphabeticalList mode="multiple" />
              </Form.Item>
            </ConditionalDisplay>

            {/* 填空题答案 */}
            <ConditionalDisplay visible={questionAnswerMode === 3}>
              <Form.Item
                name={[name, 'fillBlank']}
                label="答案"
                rules={validateFormListAnswerItem(3, questionAnswerMode)}
                {...restField}
              >
                {fieldManager.uuids.length === 0 ? (
                  <Input disabled />
                ) : (
                  <EditableTiptap
                    textarea
                    includeFeatures={[Feature.formula]}
                    stripOuterNode={true}
                  />
                )}
              </Form.Item>
            </ConditionalDisplay>

            {/* 判断题答案 */}
            <ConditionalDisplay visible={questionAnswerMode === 4}>
              <Form.Item
                name={[name, 'judge']}
                label="答案"
                rules={validateFormListAnswerItem(4, questionAnswerMode)}
                {...restField}
              >
                <Radio.Group>
                  <Radio value="true">正确</Radio>
                  <Radio value="false">错误</Radio>
                </Radio.Group>
              </Form.Item>
            </ConditionalDisplay>

            {/* 简答题答案 */}
            <ConditionalDisplay visible={questionAnswerMode === 5}>
              <Form.Item
                name={[name, 'shortAnswer']}
                label="答案"
                rules={validateFormListAnswerItem(5, questionAnswerMode)}
                {...restField}
              >
                <EditableTiptap textarea excludeFeatures={[Feature.insertQs]} />
              </Form.Item>
            </ConditionalDisplay>
          </div>
        );
      })}
    </>
  );
};

export default AnswerFormContent;
