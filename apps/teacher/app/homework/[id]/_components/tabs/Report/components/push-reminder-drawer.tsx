"use client";

import { ChevronLeft } from "lucide-react";
import { Sheet, SheetContent } from "@/ui/sheet";
import { Textarea } from "@/ui/textarea";
import { useState } from "react";
import { cn } from "@/utils/utils";
import { DrawerCard } from "../../../../../../../components/common/drawer-card";
import { useStudentRemind } from "../hooks/useStudentRemind";
import { Button } from "@/ui/tch-button";

interface PushReminderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentId?: number;
}

export function PushReminderDrawer({ open, onOpenChange, studentId }: PushReminderDrawerProps) {
  const [reminderMessage, setReminderMessage] = useState("今天答题状态不是很好，是不是课程学习有点难，是否有需要我帮忙的吗？");
  const { handleBatchRemind } = useStudentRemind();

  const handleSendReminder = () => {
    if (studentId) {
      handleBatchRemind([studentId], reminderMessage);
      onOpenChange(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className={cn(
          "gap-0 w-full sm:max-w-md overflow-y-auto px-6 bg-[#F4F7FE]",
          "flex flex-col"
        )}
        closeable={false}
      >
        {/* 抽屉头部 */}
        <div className="flex items-center justify-between py-5 sticky top-0 bg-[#F4F7FE] z-10">
          <div className="flex items-center gap-[0.625rem]">
            <div
              className="flex items-center justify-center cursor-pointer bg-white rounded-full w-9 h-9 shadow-sm"
              onClick={() => onOpenChange(false)}
            >
              <ChevronLeft className="h-5 w-5 text-[#64698a]" />
            </div>
            <span className="text-lg font-medium text-[#444963]">
              推送提醒消息
            </span>
          </div>
        </div>

        {/* 内容区域 */}
        <DrawerCard
          className="h-full"
          title="push提醒"
          footer={
            <div className="flex justify-end gap-3">
              <Button
                className="h-9 rounded-[1.125rem]"
                onClick={handleSendReminder}
                disabled={!reminderMessage.trim()}
              >
                发送提醒
              </Button>
            </div>
          }
        >
          <div className="relative">
            <Textarea
              placeholder="今天答题状态不是很好，是不是课程学习有点难，是否有需要我帮忙的吗？"
              className="min-h-[200px] resize-none"
              value={reminderMessage}
              onChange={(e) => setReminderMessage(e.target.value)}
              maxLength={500}
            />
            <div className="absolute bottom-2 right-2 text-xs text-gray-400">{reminderMessage.length}/500</div>
          </div>
        </DrawerCard>
      </SheetContent>
    </Sheet>
  );
}
