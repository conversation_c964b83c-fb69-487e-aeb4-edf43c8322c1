import UploadModal from '@/components/UploadModal';
import CooperationModal from '@/pages/partner-school/list/components/CooperationModal';
import { downloadFile, post } from '@/services/fetcher';
import { resetPassword } from '@/services/student-management';
import { StudentInfo } from '@/services/student-management/type';
import { StudentIsTest, StudentIsTestMap, StudentStatusMap } from '@/types/students';
import { UploadOutlined } from '@ant-design/icons';
import { ProForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { request, useModel, useSearchParams } from '@umijs/max';
import { Button, message, Popconfirm, Spin, Table, Typography } from 'antd';
import type { ColumnType } from 'antd/es/table';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import GradeTree, { GradeTreeProps, NodeInfo, NodeType } from '../../../../../components/GradeTree';
import ExportStudents from './components/ExportStudents';
import StudentForm from './components/StudentForm';
import TransferClassForm from './components/TransferClassForm';

const { Title, Text } = Typography;

// 选中节点信息类型
interface SelectedNodeInfo {
  nodeType: NodeType;
  nodeId: number;
  nodeName: string;
  nodeInfo?: NodeInfo<NodeType>;
}

// 筛选条件类型
interface FilterValues {
  searchKeyword?: string;
  userIsTest?: number;
  userStatus?: number;
}

interface SchoolStatsResponse {
  list: any[];
  allStudent: number;
  freeServiceStudent: number;
  payServiceStudent: number;
  outServiceStudent: number;
  defaultServiceStudent: number;
}

// API 请求参数类型
interface FetchStudentListParams {
  schoolId: number;
  page: number;
  pageSize: number;
  key?: string;
  userIsTest?: number;
  userStatus?: number;
  userGrade?: number;
  userClass?: number;
}

type BatchCreateStudentResponse = {
  allCount: number;
  successCount: number;
  failedCount: number;
  success: null;
  failed: null;
};
type BatchCreateStudentParams = {
  objectKey: string;
  schoolId: number;
  schoolYear: number;
};
const StudentManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const schoolIdParam = searchParams.get('id');
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolIdParam ? Number(schoolIdParam) : schoolInfo?.schoolId || 0;
  const schoolName = schoolInfo?.schoolName || '未知学校';
  const { trigger: batchCreateStudent, error: batchCreateStudentError } = useSWRMutation(
    '/api/v1/userSchool/batchCreateStudent',
    post<BatchCreateStudentResponse, BatchCreateStudentParams>,
  );
  const {
    data: studentData,
    isMutating: isLoadingStudentList,
    trigger: refreshStudentList,
  } = useSWRMutation(
    `/api/v1/userSchool/listStudent`,
    post<SchoolStatsResponse, FetchStudentListParams>,
  );

  // 状态管理
  const [selectedNode, setSelectedNode] = useState<SelectedNodeInfo>({
    nodeType: NodeType.School,
    nodeId: Number(schoolId),
    nodeName: schoolName,
    nodeInfo: {
      class_school_id: Number(schoolId),
      school_name: schoolName,
    },
  });
  const [selectedStudentList, setSelectedStudentList] = useState<StudentInfo[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isUploadModalVisible, setIsUploadModalVisible] = useState<boolean>(false);
  // 使用单个对象管理筛选条件，减少状态数量
  const [filterValues, setFilterValues] = useState<FilterValues>({
    searchKeyword: '',
    userIsTest: undefined,
    userStatus: undefined,
  });

  // 统一管理请求参数
  const [requestParams, setRequestParams] = useState<FetchStudentListParams>({
    schoolId: Number(schoolId),
    page: currentPage,
    pageSize: pageSize,
    key: filterValues.searchKeyword,
    userIsTest: filterValues.userIsTest,
    userStatus: filterValues.userStatus,
  });

  const [isStudentFormVisible, setIsStudentFormVisible] = useState<boolean>(false);
  const [isTransferClassVisible, setIsTransferClassVisible] = useState<boolean>(false);
  // 当前编辑的学生信息
  const [selectedStudent, setSelectedStudent] = useState<StudentInfo | null>(null);
  // 合作审批弹窗
  const [cooperationModalState, setCooperationModalState] = useState<{
    visible: boolean;
    defaultActiveKey: string;
  }>({
    visible: false,
    defaultActiveKey: 'approval',
  });

  const selectNodeInfo = useMemo(() => {
    let gradeName = '';
    let className = '';
    let gradeId = 0;
    let classId = 0;
    if (selectedNode.nodeType === NodeType.Grade) {
      gradeName = (selectedNode.nodeInfo as any)?.grade_name || '';
      gradeId = selectedNode.nodeId;
    } else if (selectedNode.nodeType === NodeType.Class) {
      gradeName = (selectedNode.nodeInfo as any).gradeInfo?.grade_name || '';
      gradeId = (selectedNode.nodeInfo as any).gradeInfo?.grade_id || 0;
      className = (selectedNode.nodeInfo as any).class_name || '';
      classId = selectedNode.nodeId;
    }
    return { gradeName, gradeId, className, classId };
  }, [selectedNode]);

  useEffect(() => {
    if (batchCreateStudentError) {
      message.error('批量导入失败');
    }
  }, [batchCreateStudentError]);

  // 重置表单状态
  const resetFormState = () => {
    setSelectedStudent(null);
    setIsStudentFormVisible(false);
  };

  // 初始化加载
  useEffect(() => {
    // 初始化请求学生列表
    refreshStudentList(requestParams);

    // 组件卸载时清理状态
    return () => {
      resetFormState();
    };
  }, [refreshStudentList]);

  // 当请求参数变化时，自动刷新学生列表
  useEffect(() => {
    refreshStudentList(requestParams);
  }, [requestParams, refreshStudentList]);

  // 处理树节点选择 - 从依赖中移除 selectedNode，使用闭包引用当前状态
  const handleTreeSelect = useCallback<GradeTreeProps['onNodeSelect']>(
    (selectedKey: React.Key, selectedNodeType: NodeType, nodeInfo?: NodeInfo<NodeType>) => {
      if (!selectedKey) return;

      let nodeName = '';
      let nodeId = 0;

      if (selectedNodeType === NodeType.Grade && nodeInfo) {
        const gradeInfo = nodeInfo as NodeInfo<NodeType.Grade>;
        nodeName = gradeInfo.grade_name || '';
        nodeId = gradeInfo.grade_id;
      } else if (selectedNodeType === NodeType.Class && nodeInfo) {
        const classInfo = nodeInfo as NodeInfo<NodeType.Class>;
        nodeName = `${classInfo.gradeInfo.grade_name} ${classInfo.class_name}`;
        nodeId = classInfo.class_id;
      } else if (selectedNodeType === NodeType.School && nodeInfo) {
        const schoolInfo = nodeInfo as NodeInfo<NodeType.School>;
        nodeName = schoolInfo.school_name;
        nodeId = schoolInfo.class_school_id;
      } else {
        return;
      }

      // 使用函数式更新，获取最新的 selectedNode 而不依赖于它
      setSelectedNode((prevSelectedNode) => {
        // 有变化的时候才更新 - 检查节点ID或节点类型是否发生变化
        if (prevSelectedNode.nodeId !== nodeId || prevSelectedNode.nodeType !== selectedNodeType) {
          // 返回新状态
          return {
            nodeInfo: nodeInfo,
            nodeType: selectedNodeType,
            nodeId,
            nodeName,
          };
        }

        // 无变化则返回原状态
        return prevSelectedNode;
      });
    },
    [schoolId, setSelectedNode],
  );

  useEffect(() => {
    const { nodeType, nodeId, nodeInfo } = selectedNode;
    // 更新请求参数
    setRequestParams((prev) => {
      const newParams: FetchStudentListParams = {
        ...prev,
        page: 1,
        userGrade: undefined,
        userClass: undefined,
      };

      if (nodeType === NodeType.Grade) {
        newParams.userGrade = Number(nodeId);
      } else if (nodeType === NodeType.Class) {
        newParams.userClass = Number(nodeId);
        newParams.userGrade = (nodeInfo as any).gradeInfo?.grade_id || undefined;
      }

      return newParams;
    });

    // 选择节点变化时重置页码
    setCurrentPage(1);
    setSelectedStudentList([]);
    // console.log('selectedNode', selectedNode);
  }, [selectedNode]);

  // 处理筛选条件变化，合并多个处理函数
  const handleFilterChange = useCallback((values: FilterValues) => {
    setFilterValues(values);
    setCurrentPage(1);
    setSelectedStudentList([]);

    // 更新请求参数
    setRequestParams((prev) => ({
      ...prev,
      page: 1,
      key: values.searchKeyword,
      userIsTest: values.userIsTest,
      userStatus: values.userStatus,
    }));
  }, []);

  // 处理分页变化
  const handlePageChange = (page: number, newPageSize?: number) => {
    setCurrentPage(page);

    if (newPageSize) {
      setPageSize(newPageSize);
    }

    // 更新请求参数
    setRequestParams((prev) => ({
      ...prev,
      page,
      pageSize: newPageSize || prev.pageSize,
    }));
  };

  // 处理编辑学生
  const handleEditStudent = (record: StudentInfo) => {
    setIsStudentFormVisible(true);
    setSelectedStudent(record);
  };

  const handleStudentFormSuccess = () => {
    resetFormState();
    setSelectedStudentList([]);
    // 刷新学生列表
    refreshStudentList(requestParams);
  };

  const handleTransferClassSuccess = () => {
    setIsTransferClassVisible(false);
    setSelectedStudentList([]);
    // 刷新学生列表
    refreshStudentList(requestParams);
  };

  const handleViewApproval = (type: number) => {
    setCooperationModalState({
      visible: true,
      defaultActiveKey: type === 1 ? 'approval' : 'history',
    });
  };

  // 处理重置密码
  const handleResetPassword = async (userID: number) => {
    try {
      const res = await resetPassword({ userID, schoolID: Number(schoolId) });
      if (res.code === 0) {
        message.success(res.data || '密码重置成功');
      } else {
        message.error('密码重置失败');
      }
    } catch (error) {
      message.error('密码重置失败');
    }
  };

  // 表格列定义
  const columns: ColumnType<StudentInfo>[] = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
      fixed: 'left' as const,
      width: 100,
      ellipsis: true,
    },
    {
      title: '学生ID',
      dataIndex: 'userID',
      key: 'userID',
      width: 100,
      ellipsis: true,
    },
    {
      title: '学号',
      dataIndex: 'userNumber',
      key: 'userNumber',
      width: 120,
      ellipsis: true,
    },
    {
      title: '账号',
      dataIndex: 'userAccount',
      key: 'userAccount',
      width: 120,
      ellipsis: true,
    },
    {
      title: '年级',
      dataIndex: 'gradeName',
      key: 'gradeName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      width: 100,
      ellipsis: true,
    },
    {
      title: '是否测试',
      dataIndex: 'userIsTest',
      key: 'userIsTest',
      width: 100,
      render: (isTest: number) => StudentIsTestMap[isTest as keyof typeof StudentIsTestMap] || '-',
    },
    {
      title: '账号状态',
      dataIndex: 'userStatus',
      key: 'userStatus',
      width: 100,
      render: (status: number) => {
        return StudentStatusMap[status as keyof typeof StudentStatusMap];
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: unknown, record: StudentInfo) => (
        <div className="space-x-2 whitespace-nowrap">
          {selectedNode.nodeType === NodeType.Class && (
            <>
              <Button type="link" size="small" onClick={() => handleEditStudent(record)}>
                编辑
              </Button>
              <Popconfirm
                title="确定重置密码吗？"
                onConfirm={() => handleResetPassword(record.userID)}
              >
                <Button type="link" size="small">
                  重置密码
                </Button>
              </Popconfirm>
            </>
          )}
        </div>
      ),
    },
  ];

  // 渲染状态面板
  const renderStatusPanel = () => {
    return (
      <>
        <div className="bg-white px-6 py-3 rounded-sm shadow-sm mb-4">
          <div className="flex flex-col">
            <div className="flex justify-between items-center mb-2">
              <Title level={4} className="mb-3 font-bold">
                {selectedNode.nodeName}
              </Title>

              {selectedNode.nodeType === NodeType.School && (
                <div className="space-x-3 flex items-center">
                  <Button
                    type="link"
                    onClick={() => handleViewApproval(2)}
                    className="text-blue-600 hover:text-blue-500"
                  >
                    查看申报历史
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => handleViewApproval(1)}
                    className="bg-blue-600 hover:bg-blue-500 border-none"
                  >
                    发起账号审批
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center text-sm">
            <Text className="text-gray-500 mr-4 whitespace-nowrap">
              {selectedNode.nodeType === NodeType.Class ? '班级学生数：' : '在校学生数：'}
              <span className="text-gray-700 font-medium">{studentData?.allStudent}人</span>
            </Text>
            <Text className="text-gray-500 mr-3 whitespace-nowrap">合作状态：</Text>
            <Text className="text-amber-500 mr-3 whitespace-nowrap">
              {studentData?.freeServiceStudent || 0}人试用
            </Text>
            <Text className="text-green-600 mr-3 whitespace-nowrap">
              {studentData?.payServiceStudent || 0}人付费
            </Text>
            <Text className="text-red-500 mr-3 whitespace-nowrap">
              {studentData?.outServiceStudent || 0}人停用
            </Text>
            {/* 尚未启用 defaultServiceStudent */}
            <Text className="text-gray-500 whitespace-nowrap">
              {studentData?.defaultServiceStudent || 0}人未启用
            </Text>
          </div>
        </div>
        {schoolInfo && (
          <CooperationModal
            visible={cooperationModalState.visible}
            onVisibleChange={() =>
              setCooperationModalState({ ...cooperationModalState, visible: false })
            }
            isHiddenStaff={true}
            defaultActiveKey={cooperationModalState.defaultActiveKey}
            school={schoolInfo}
          />
        )}
      </>
    );
  };

  // 渲染操作指引
  const renderOperationGuide = () => {
    if (selectedNode.nodeType !== NodeType.School) return null;

    return (
      <div className="bg-blue-50 py-2 px-4 rounded-lg mb-3 border border-blue-200">
        <Title level={5} className="text-blue-700 mb-2">
          操作指引
        </Title>
        <ol className="list-decimal pl-5 text-blue-700">
          <li>请先点击年级，完成班级创建</li>
          <li>请批量上传学生信息，完成账号创建</li>
        </ol>
      </div>
    );
  };

  const ExportStudentsMemo = useMemo(() => {
    let gradeName = '';
    let className = '';
    let gradeId = 0;
    let classId = 0;

    if (selectedNode.nodeType === NodeType.Grade) {
      gradeName = (selectedNode.nodeInfo as any)?.grade_name || '';
      gradeId = selectedNode.nodeId;
    } else if (selectedNode.nodeType === NodeType.Class) {
      gradeName = (selectedNode.nodeInfo as any).gradeInfo?.grade_name || '';
      gradeId = (selectedNode.nodeInfo as any).gradeInfo?.grade_id || 0;
      className = (selectedNode.nodeInfo as any).class_name || '';
      classId = selectedNode.nodeId;
    }

    // console.log('弹窗信息：', selectedNode.nodeType, selectedNode.nodeInfo, selectedNode);
    // console.log('弹窗信息2：', gradeName, gradeId);
    return (
      <ExportStudents
        gradeName={gradeName}
        className={className}
        schoolName={schoolName}
        gradeId={gradeId}
        classId={classId}
        schoolId={schoolId}
        selectedStudentList={selectedStudentList}
      />
    );
  }, [selectedNode, schoolId, schoolName, selectedStudentList]);

  // 渲染表格工具栏
  const renderTableToolbar = () => {
    return (
      <div className="flex mb-4 flex-col w-full">
        <ProForm
          layout="inline"
          className="mb-0"
          submitter={false}
          initialValues={filterValues}
          onValuesChange={(_, values) => handleFilterChange(values as FilterValues)}
        >
          <ProFormText
            name="searchKeyword"
            placeholder="按姓名/学号搜索"
            width="md"
            fieldProps={{
              allowClear: true,
            }}
          />
          <ProFormSelect
            name="userIsTest"
            placeholder="是否测试"
            width="xs"
            allowClear
            options={[
              { label: '否', value: StudentIsTest.NO },
              { label: '是', value: StudentIsTest.YES },
            ]}
          />
          <ProFormSelect
            name="userStatus"
            placeholder="合作状态"
            width="xs"
            allowClear
            options={Object.entries(StudentStatusMap).map(([key, value]) => ({
              label: value,
              value: Number(key),
            }))}
          />
        </ProForm>

        {selectedNode.nodeType !== NodeType.School && (
          <div className="mt-4 flex items-center justify-between">
            <div className="space-x-2">
              <Button
                size="small"
                type="primary"
                onClick={() => {
                  setIsUploadModalVisible(true);
                }}
                icon={<UploadOutlined />}
              >
                批量添加学生
              </Button>
              {selectedNode.nodeType === NodeType.Class && (
                <>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => {
                      setSelectedStudent(null);
                      setIsStudentFormVisible(true);
                    }}
                  >
                    添加学生
                  </Button>
                  {selectedStudentList.length > 0 && (
                    <Button size="small" onClick={() => setIsTransferClassVisible(true)}>
                      转班
                    </Button>
                  )}
                </>
              )}
            </div>
            <div>{ExportStudentsMemo}</div>
          </div>
        )}
      </div>
    );
  };

  // 使用 useMemo 缓存 GradeTree 组件
  const memoizedGradeTree = useMemo(
    () => <GradeTree canSelectedSchool={true} editableClass onNodeSelect={handleTreeSelect} />,
    [handleTreeSelect],
  );

  const UploadModalMemo = useMemo(() => {
    const downloadTemplate = async () => {
      // console.log('下载模板: ', selectedNode, selectNodeInfo);
      const { gradeId, classId } = selectNodeInfo;
      let query = '';
      if (schoolId) {
        query += `schoolID=${schoolId}`;
      }
      if (gradeId) {
        query += `&gradeID=${gradeId}`;
      }
      if (classId) {
        query += `&classID=${classId}`;
      }

      try {
        const res = await request(`/api/v1/userSchool/studentTpl?${query} `, {
          method: 'GET',
        });

        // 将 base64 转换为 Blob
        const byteCharacters = atob(res.data.file_content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/vnd.ms-excel' });
        downloadFile(blob, res.data.file_name);
      } catch (error) {
        message.error('下载模板失败');
      }
    };

    const importData = async (fileId: string) => {
      try {
        await batchCreateStudent({
          objectKey: fileId,
          schoolId: schoolId,
          schoolYear: schoolInfo?.schoolYear.school_year_id || 0,
        });
        if (batchCreateStudentError) {
          return {
            success: false,
            error: batchCreateStudentError.message,
          };
        }

        return {
          success: true,
        };
      } catch (error) {
        return {
          success: false,
          error: '导入失败',
        };
      }
    };

    return (
      <UploadModal
        title="批量导入学生"
        visible={isUploadModalVisible}
        onClose={() => setIsUploadModalVisible(false)}
        importData={importData}
        downloadTemplate={downloadTemplate}
        onSuccess={(data) => {
          if (data.closeModal) {
            setIsUploadModalVisible(false);
          }
          const payload = { ...requestParams, page: 1 };
          setRequestParams(payload);
        }}
      />
    );
  }, [isUploadModalVisible, schoolId, selectedNode, batchCreateStudent]);

  return (
    <>
      <div className="flex bg-transparent rounded overflow-hidden h-[calc(100vh-308px)]">
        {/* 左侧树形导航 */}
        <div className="w-60 bg-white overflow-hidden flex flex-col mr-3">
          {/* 年级班级树 */}
          <div className="flex-1 overflow-y-auto">{memoizedGradeTree}</div>
        </div>

        {/* 右侧内容区域 */}
        <div className="h-full flex-1 overflow-y-auto">
          {/* 状态面板 */}
          {renderStatusPanel()}

          {/* 操作指引 */}
          {renderOperationGuide()}

          {/* 学生列表 */}
          <div className="bg-white p-4 rounded-sm shadow-sm overflow-y-auto">
            {/* 表格工具栏 */}
            {renderTableToolbar()}

            {/* 学生表格 */}
            <Spin spinning={isLoadingStudentList}>
              <Table
                scroll={{ x: 'max-content' }}
                columns={columns}
                dataSource={studentData?.list || []}
                rowKey="userID"
                rowSelection={
                  selectedNode.nodeType === NodeType.Class
                    ? {
                        selectedRowKeys: selectedStudentList.map((s) => s.userID),
                        type: 'checkbox',
                        onChange: (_, selectedRows) => {
                          setSelectedStudentList(selectedRows);
                        },
                      }
                    : undefined
                }
                pagination={{
                  size: 'small',
                  current: currentPage,
                  pageSize,
                  total: studentData?.allStudent || 0,
                  onChange: handlePageChange,
                  showSizeChanger: true,
                  showTotal: (total) => <span className="text-blue-500">共 {total} 条</span>,
                }}
              />
            </Spin>
          </div>
          <div>
            {selectedNode.nodeType === NodeType.Class && (
              <>
                <StudentForm
                  schoolId={schoolId}
                  gradeId={
                    selectedNode.nodeType === NodeType.Class && selectedNode.nodeInfo
                      ? (selectedNode.nodeInfo as any).gradeInfo?.grade_id || ''
                      : ''
                  }
                  classId={selectedNode.nodeId}
                  visible={isStudentFormVisible}
                  onCancel={resetFormState}
                  onSuccess={handleStudentFormSuccess}
                  initialValues={selectedStudent || undefined}
                  isEdit={!!selectedStudent}
                />
                <TransferClassForm
                  studentList={selectedStudentList}
                  schoolId={schoolId}
                  classId={selectedNode.nodeId}
                  visible={isTransferClassVisible}
                  onCancel={() => setIsTransferClassVisible(false)}
                  onSuccess={handleTransferClassSuccess}
                />
              </>
            )}
          </div>
        </div>
      </div>
      {UploadModalMemo}
    </>
  );
};

export default StudentManagement;
