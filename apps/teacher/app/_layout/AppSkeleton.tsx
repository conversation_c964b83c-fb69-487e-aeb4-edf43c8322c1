"use client";
import PageLoading from "@/components/PageLoading";
import { useMount } from "ahooks";
import React from "react";

export const AppSkeleton: React.FC = () => {
  const [isReady, setIsReady] = React.useState(false);

  useMount(() => {
    setIsReady(true);
  });

  if (!isReady) {
    return null;
  }

  /**
   * 平板端不要全局骨架屏
   */
  if (typeof window !== "undefined" && window.TecJsb) {
    return null;
  }

  return (
    <div className="-z-1 bg-fill-light fixed left-0 top-0 h-full w-full overflow-hidden">
      <PageLoading />
    </div>
  );
};

export default AppSkeleton;
