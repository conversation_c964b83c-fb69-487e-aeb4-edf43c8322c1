/**
 * 优化的 localStorage 工具函数
 * 使用异步操作和错误处理来避免阻塞主线程
 */

// 异步设置 localStorage
export const setStorageAsync = (key: string, value: any): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      const serializedValue = JSON.stringify(value);

      // 使用 requestIdleCallback 在浏览器空闲时执行
      if (typeof requestIdleCallback !== "undefined") {
        requestIdleCallback(() => {
          try {
            localStorage.setItem(key, serializedValue);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      } else {
        // 降级到 setTimeout
        setTimeout(() => {
          try {
            localStorage.setItem(key, serializedValue);
            resolve();
          } catch (error) {
            reject(error);
          }
        }, 0);
      }
    } catch (error) {
      reject(error);
    }
  });
};

// 同步获取 localStorage（读取通常很快，不需要异步）
export const getStorageSync = <T = any>(
  key: string,
  defaultValue?: T
): T | null => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue || null;
    }
    if (typeof item === "string") {
      return item as T;
    }
    return JSON.parse(item);
  } catch (error) {
    console.error(`Failed to get ${key} from localStorage:`, error);
    return defaultValue || null;
  }
};

// 异步移除 localStorage
export const removeStorageAsync = (key: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (typeof requestIdleCallback !== "undefined") {
      requestIdleCallback(() => {
        try {
          localStorage.removeItem(key);
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    } else {
      setTimeout(() => {
        try {
          localStorage.removeItem(key);
          resolve();
        } catch (error) {
          reject(error);
        }
      }, 0);
    }
  });
};

// 使用 requestAnimationFrame 优化的异步操作
export const rafAsync = (callback: () => void): Promise<void> => {
  return new Promise((resolve) => {
    requestAnimationFrame(() => {
      callback();
      resolve();
    });
  });
};

// 优化的路由跳转函数
export const optimizedNavigation = (
  router: any,
  path: string,
  beforeNavigation?: () => void,
  afterNavigation?: () => void
): Promise<void> => {
  return new Promise((resolve) => {
    // 在下一帧执行导航前的操作
    requestAnimationFrame(() => {
      beforeNavigation?.();

      // 再下一帧执行路由跳转
      requestAnimationFrame(() => {
        router.push(path);

        // 导航后的操作
        if (afterNavigation) {
          requestAnimationFrame(() => {
            afterNavigation();
            resolve();
          });
        } else {
          resolve();
        }
      });
    });
  });
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
