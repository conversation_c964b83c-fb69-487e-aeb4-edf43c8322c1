import styles from '@/pages/source-manage/question-create/index.module.less';
import { Form, Input } from 'antd';
import React, { memo, useCallback, useState } from 'react';
import { useQuestionCreate } from '../../../contexts/QuestionCreateContext';
import QuestionStem from '../../QuestionStem';
import QuestionTitleBar from '../../QuestionTitleBar';
import CollapsibleContainer from '../../shared/CollapsibleContainer';
import NestedQuestionAnswerSection from '../NestedQuestionAnswerSection';

interface NestedQuestionFormListProps {
  parentName: number;
}

/**
 * 三级题目Form.List组件
 * 负责管理三级嵌套子题目的增删改查逻辑
 */
const NestedQuestionFormList: React.FC<NestedQuestionFormListProps> = ({ parentName }) => {
  const {
    form,
    formActions: { deletingItems, nestedAddRefs, nestedRemoveRefs, handleDeleteWithAnimation },
  } = useQuestionCreate();

  // 获取三级题目列表
  const subQuestionList =
    Form.useWatch(['subQuestionList', parentName, 'subQuestionList'], form) || [];

  // 为每个小题独立管理activeFillIdx
  const [activeFillIndices, setActiveFillIndices] = useState<Record<number, number>>({});
  const onNestedQuestionFillAreaClick = useCallback(
    (e: { index: number }, questionIndex: number) => {
      setActiveFillIndices((prev) => ({
        ...prev,
        [questionIndex]: e.index,
      }));
    },
    [],
  );

  return (
    <Form.List name={[parentName, 'subQuestionList']}>
      {(subFields, { add: subAdd, remove: subRemove }) => {
        // 保存嵌套的add/remove方法
        nestedAddRefs.current.set(parentName, subAdd);
        nestedRemoveRefs.current.set(parentName, subRemove);

        return (
          <>
            {subFields.map(({ key: subKey, name: subName, ...subRestField }) => {
              // 获取三级题目的作答方式
              const questionAnswerMode = subQuestionList[subName]?.questionAnswerMode;
              const currentFillIdx = activeFillIndices[subName] || 0;

              return (
                <CollapsibleContainer
                  key={subKey}
                  header={
                    <QuestionTitleBar
                      level="level2"
                      title={`小小题 ${subName + 1}`}
                      onDelete={() =>
                        handleDeleteWithAnimation(`level2-${subKey}`, () => subRemove(subName))
                      }
                      required={true}
                    />
                  }
                  className={`${styles.level2Container} ${deletingItems.has(`level2-${subKey}`) ? styles.deleting : ''}`}
                >
                  {/* 题目id，隐藏，供form提交时使用 */}
                  <Form.Item name={[subName, 'questionId']} label="questionId" hidden>
                    <Input />
                  </Form.Item>
                  {/* 三级题目主题干 */}
                  <QuestionStem
                    name={[subName, 'questionStem']}
                    questionAnswerMode={questionAnswerMode}
                    onQuestionFillAreaClick={(e) => onNestedQuestionFillAreaClick(e, subName)}
                  />

                  {/* 三级题目的作答方式和答案组件 */}
                  <NestedQuestionAnswerSection
                    parentName={parentName}
                    subName={subName}
                    restField={subRestField}
                    currentFillIdx={currentFillIdx}
                  />
                </CollapsibleContainer>
              );
            })}
          </>
        );
      }}
    </Form.List>
  );
};

export default memo(NestedQuestionFormList);
