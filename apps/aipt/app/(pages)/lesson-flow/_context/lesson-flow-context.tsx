import { FC, createContext, useContext, useEffect, useState } from "react";
interface LessonFlowContextType {
  interactiveNodes: any[],
}
const LessonFlowContext = createContext<LessonFlowContextType>({
  interactiveNodes: [],
});

type LessonFlowProviderProps = {
  children: React.ReactNode;
  interactiveNodes: any[];
}
export const LessonFlowProvider: FC<LessonFlowProviderProps> = (props) => {
  const { children } = props;
  const [interactiveNodes, setInteractiveNodes] = useState<any[]>(props.interactiveNodes);

  useEffect(() => {
    setInteractiveNodes(props.interactiveNodes);
  }, [props.interactiveNodes]);

  const value = {
    interactiveNodes,
  }
  return <LessonFlowContext value={value}>{children}</LessonFlowContext>;
};

export const useLessonFlow = () => {
  return useContext(LessonFlowContext);
};
