"use client";

import { EmptyContent } from "@/components/common/empty-content";
import { OfflineCommunicationDrawer } from "@/components/common/offline-communication";
import { useApp } from "@/hooks";
import CloseIcon from "@/public/icons/ic_close.svg";
import {
  getStudentCourseDetail,
  praiseStudent,
  sendStudentEvaluate,
} from "@/services";
import { StudentInfo } from "@/types/course";
import { ScrollArea } from "@/ui/scroll-area";
import GilAvatar from "@/ui/tch-avatar";
import { Button } from "@/ui/tch-button";
import { SheetClose } from "@/ui/tch-sheet";
import { Textarea } from "@/ui/textarea";
import { toast } from "@/ui/toast";
import { umeng, UmengCategory, UmengCourseAction } from "@/utils";
import { cn } from "@/utils/utils";
import { useMount, useRequest } from "ahooks";
import to from "await-to-js";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { match } from "ts-pattern";
interface StudentDetailSheetProps {
  student: StudentInfo & { classId?: number };
  classroomId: string;
  setStudent: (student: StudentInfo | null) => void;
  isInClass: boolean;
  isSubjectTeacher: boolean;
  subject: string;
}

export function StudentDetail(props: StudentDetailSheetProps) {
  const router = useRouter();
  const { userInfo } = useApp();
  const [isOfflineCommunicationOpen, setIsOfflineCommunicationOpen] =
    useState(false);

  const [evaluateMessage, setEvaluateMessage] = useState("");
  const [isPraised, setIsPraised] = useState(false);

  const getStudentCourseDetailRequest = useRequest(getStudentCourseDetail, {
    defaultParams: [
      {
        classroomId: props.classroomId,
        studentId: props.student.studentId.toString(),
      },
    ],
    onSuccess: (data) => {
      setEvaluateMessage(data.evaluateContent);
      setIsPraised(data.isHandled);
    },
  });

  const praiseStudentRequest = useRequest(
    () =>
      praiseStudent({
        classroomID: props.classroomId,
        studentIDs: [props.student.studentId],
        teacherID: userInfo!.userID,
        teacherName: userInfo!.userName,
        source: "drawer",
      }),
    {
      manual: true,
      debounceWait: 500,
      onSuccess: (data) => {
        toast.success(data.message);
        setIsPraised(true);
      },
    }
  );

  const sendStudentEvaluateRequest = useRequest(sendStudentEvaluate, {
    manual: true,
  });

  useMount(() => {
    umeng.trackEvent(
      UmengCategory.COURSE,
      UmengCourseAction.CLASSROOM_REPORT_SINGLE_CLICK1,
      {
        single_detail_open: "对单人学生打开查看详情",
      }
    );

    umeng.trackEvent(
      UmengCategory.COURSE,
      UmengCourseAction.CLASSROOM_REPORT_SINGLE_CLICK2,
      {
        classroom_status: props.isInClass ? "课中ongoing" : "课后after",
      }
    );
  });

  return (
    <>
      <div className="flex h-full flex-col leading-normal">
        <div className="bg-fill-gray-2 h-18 text-gray-1 flex items-center justify-between px-6">
          <span className="text-xl font-semibold">
            {props.student.studentName}课堂学情
          </span>

          <SheetClose>
            <CloseIcon
              onClick={() => props.setStudent(null)}
              className="h-5 w-5 cursor-pointer active:opacity-80"
            />
          </SheetClose>
        </div>

        <ScrollArea
          className="bg-fill-gray-2 flex-1 overflow-hidden"
          viewportClassName="[&>div:first-child]:flex! [&>div:first-child]:flex-col [&>div:first-child]:min-h-full"
        >
          <div className="bg-fill-gray-2 space-y-4 px-6">
            {/* 个人信息区域 */}
            <div className="h-47.5 bg-primary-5 rounded-[1.25rem] p-6">
              <div className="flex items-center gap-5">
                <GilAvatar
                  src={props.student.avatarUrl}
                  alt={props.student.studentName}
                  className="h-14 w-14 cursor-default"
                />

                <span className="text-gray-1 flex-1 text-xl font-semibold">
                  {props.student.studentName}
                </span>

                <Button
                  className="text-gray-3 h-7 rounded-[0.875rem] px-2.5 text-xs font-medium"
                  onClick={() => setIsOfflineCommunicationOpen(true)}
                >
                  去线下处理
                </Button>
              </div>

              <div className="border-gray-1/5 my-5 border-t"></div>

              {/* 学习数据区域 */}
              <div className="flex items-center justify-between px-3">
                <div className="flex flex-col items-center">
                  <div className="text-gray-1 flex items-baseline">
                    <span className="text-2xl/normal font-extrabold">
                      {getStudentCourseDetailRequest.data?.totalStudyTime}
                    </span>
                    <span className="text-xs/normal">分钟</span>
                  </div>

                  <span className="text-gray-4 text-xs/normal">总学习时长</span>
                </div>

                <span className="bg-gray-1/5 h-6 w-px"></span>

                <div className="flex flex-col items-center">
                  <div className="flex flex-col items-baseline">
                    <div className="text-gray-1 flex items-baseline">
                      {/* <span className="text-sm/normal">+</span> */}
                      {match(getStudentCourseDetailRequest.data?.accuracyRate)
                        .when(
                          (v) => typeof v === "number" && v > -1,
                          () => (
                            <>
                              <span className="text-2xl/normal font-extrabold">
                                {getStudentCourseDetailRequest.data
                                  ?.accuracyRate &&
                                  Math.floor(
                                    getStudentCourseDetailRequest.data
                                      ?.accuracyRate
                                  )}
                              </span>
                              <span className="text-sm/normal">%</span>
                            </>
                          )
                        )
                        .otherwise(() => (
                          <span className="text-sm/normal">--</span>
                        ))}
                    </div>
                    <span className="text-gray-4 text-xs/normal">
                      课堂正确率
                    </span>
                  </div>
                </div>

                <span className="bg-gray-1/5 h-6 w-px"></span>

                <div className="flex flex-col items-center">
                  <div className="text-gray-1 flex items-baseline">
                    <span className="text-2xl/normal font-extrabold">
                      {getStudentCourseDetailRequest.data?.maxCorrectStreak}
                    </span>
                    <span className="text-xs/normal">道</span>
                  </div>
                  <span className="text-gray-4 text-xs/normal">连对题数</span>
                </div>

                {/* <span className="bg-gray-1/5 h-6 w-px"></span> */}

                {/* <div className="flex flex-col items-center">
                  <div className="text-gray-1 flex items-baseline">
                    <span className="text-2xl/normal font-extrabold">
                      {getStudentCourseDetailRequest.data?.questionCount}
                    </span>
                    <span className="text-xs/normal">次</span>
                  </div>
                  <span className="text-gray-4 text-xs/normal">提问次数</span>
                </div>

                <span className="bg-gray-1/5 h-6 w-px"></span> */}

                {/* <div className="flex flex-col items-center">
                  <div className="text-gray-1 flex items-baseline">
                    <span className="text-2xl/normal font-extrabold">
                      {getStudentCourseDetailRequest.data?.violationTime}
                    </span>
                    <span className="text-xs/normal">分钟</span>
                  </div>
                  <span className="text-gray-4 text-xs/normal">走神时长</span>
                </div> */}
              </div>
            </div>

            {/* 学习评价区域 */}
            <div className="rounded-2xl bg-white p-5">
              <h3 className="mb-2.5 text-base font-semibold">学习评价</h3>

              <Textarea
                value={evaluateMessage}
                onChange={(e) => setEvaluateMessage(e.target.value)}
                placeholder="请输入学习评价..."
                className="h-21.5 border-line-3 text-gray-4 hover:border-line-3 resize-none px-3 py-1.5 text-sm"
              />

              <div className="mt-4 flex items-center justify-end">
                {getStudentCourseDetailRequest.data?.pushTime && (
                  <span className="text-gray-4 mr-auto text-xs/normal">
                    已在{" "}
                    {dayjs(getStudentCourseDetailRequest.data?.pushTime).format(
                      "YYYY-MM-DD HH:mm"
                    )}{" "}
                    推送
                  </span>
                )}
                {getStudentCourseDetailRequest.data?.isEvaluated ? (
                  <Button
                    size="lg"
                    radius="full"
                    className="text-gray-5 cursor-not-allowed bg-[#F2F4F9] hover:bg-[#F2F4F9] active:bg-[#F2F4F9]"
                  >
                    已发送评价
                  </Button>
                ) : (
                  <Button
                    size="lg"
                    type="primary"
                    radius="full"
                    className={cn(
                      !evaluateMessage &&
                        "bg-primary-3 hover:bg-primary-3 active:bg-primary-3 cursor-not-allowed active:opacity-80"
                    )}
                    disabled={!evaluateMessage}
                    onClick={async () => {
                      if (!evaluateMessage) {
                        return;
                      }

                      if (sendStudentEvaluateRequest.loading) {
                        return;
                      }

                      if (!getStudentCourseDetailRequest.data) {
                        throw new Error("获取课堂信息失败");
                      }

                      const [error] = await to(
                        sendStudentEvaluateRequest.runAsync({
                          classId: getStudentCourseDetailRequest.data.classId,
                          classroomId: props.classroomId,
                          content: evaluateMessage,
                          evaluateType: "class_comment",
                          schoolId: getStudentCourseDetailRequest.data.schoolId,
                          studentId: props.student.studentId,
                          teacherId: userInfo!.userID,
                        })
                      );

                      if (error) {
                        return;
                      }

                      toast.success("评价成功");

                      props.setStudent(null);
                    }}
                  >
                    发送
                  </Button>
                )}
              </div>
            </div>

            {/* 学习记录区域 */}
            <div className="rounded-2xl bg-white p-5">
              <h3 className="mb-2.5 text-base font-semibold">学习记录</h3>

              <div className="space-y-8">
                {getStudentCourseDetailRequest.data?.learningRecords.map(
                  (record, index) => {
                    return (
                      <div key={index} className="flex h-20 items-center gap-4">
                        <div className="bg-fill-gray-2 text-gray-2 relative flex h-8 w-8 items-center justify-center rounded-full text-xs font-extrabold">
                          {index + 1}

                          {index !== 0 && (
                            <span className="-top-17.25 h-14.75 bg-fill-gray-1 absolute left-4 w-0.5" />
                          )}
                        </div>

                        <div
                          className="border-line-1 bg-primary-6 h-full flex-1 cursor-pointer rounded-xl border p-4 pb-3"
                          onClick={() => {
                            if (!record.taskId || !record.assignId) return;

                            router.push(
                              `/homework/${record.taskId}?source=course&tab=report&assignId=${record.assignId}&view=student&studentId=${props.student.studentId}&studentName=${props.student.studentName}&courseId=${record.resourceId}`
                            );
                            umeng.trackEvent(
                              UmengCategory.COURSE,
                              UmengCourseAction.CLASSROOM_REPORT_SINGLE_HOMEWORK_REPORT_CLICK1,
                              {
                                single_detail_homework_report_clicked:
                                  record.chapterId,
                              }
                            );

                            umeng.trackEvent(
                              UmengCategory.COURSE,
                              UmengCourseAction.CLASSROOM_REPORT_SINGLE_HOMEWORK_REPORT_CLICK2,
                              {
                                classroom_status: props.isInClass
                                  ? "课中ongoing"
                                  : "课后after",
                              }
                            );
                          }}
                        >
                          <div className="text-gray-2 mb-1 text-base font-medium">
                            {record.chapterName}
                          </div>

                          <div className="flex h-6 items-center gap-2 text-xs">
                            <div className="flex items-center gap-1">
                              <span className="text-gray-4">时长</span>
                              <span className="text-gray-3 font-extrabold">
                                {dayjs
                                  .duration(record.duration, "seconds")
                                  .format("HH:mm:ss")}
                              </span>
                            </div>

                            <div className="bg-line-2 h-3 w-px" />

                            <div className="flex items-center gap-1">
                              <span className="text-gray-4">正确率</span>
                              <span className="text-gray-3 font-extrabold">
                                {record.accuracyRate === -1
                                  ? "--"
                                  : `${Math.floor(record.accuracyRate)}%`}
                              </span>
                            </div>

                            <div className="bg-line-2 h-3 w-px" />

                            <div className="flex items-center gap-1">
                              <span className="text-gray-4">进度</span>
                              <span className="text-gray-3 font-extrabold">
                                {Math.floor(record.progress)}%
                              </span>
                            </div>

                            {record.taskId !== 0 && record.assignId !== 0 && (
                              <div className="text-primary-1 ml-auto">
                                查看详情 {">>"}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  }
                )}

                {!getStudentCourseDetailRequest.data?.learningRecords
                  .length && <EmptyContent title="暂无学习记录" />}
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="mt-auto bg-white px-6 py-4">
            <Button
              type="primary"
              radius="full"
              className={cn(
                `h-9 w-full text-sm font-medium`,
                (isPraised || !props.isInClass) &&
                  "bg-primary-3 hover:bg-primary-3 active:bg-primary-3 cursor-not-allowed active:opacity-80"
              )}
              onClick={() => {
                if (
                  isPraised ||
                  praiseStudentRequest.loading ||
                  !props.isInClass
                ) {
                  return;
                }

                praiseStudentRequest.run();

                umeng.trackEvent(
                  UmengCategory.COURSE,
                  UmengCourseAction.CLASSROOM_REPORT_SINGLE_LIKE,
                  {
                    single_like_action: {
                      subject: props.subject,
                      job: props.isSubjectTeacher ? "任课教师" : "班主任",
                    },
                  }
                );
              }}
            >
              {isPraised ? "太好了，您已经鼓励了！" : "他今天很棒！鼓励吧"}
            </Button>
          </div>
        </ScrollArea>
      </div>

      {/* 线下沟通 Sheet */}
      <OfflineCommunicationDrawer
        className="w-130 max-w-130!"
        open={isOfflineCommunicationOpen}
        onOpenChange={setIsOfflineCommunicationOpen}
      />
    </>
  );
}
