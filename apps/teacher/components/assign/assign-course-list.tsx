import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { ScrollArea } from "@/ui/scroll-area";
import { cn } from "@/utils/utils";
import { Skeleton } from "antd";
import { AssignCard } from "./assign-card";
import { AssignCourseItem } from "./assign-course-item";
import { AssignEmpty } from "./assign-empty";

interface AssignCourseListProps {
  style?: React.CSSProperties;
  className?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function AssignCourseList({
  style = {},
  className = "",
  onScroll = () => {},
}: AssignCourseListProps) {
  const { bizTreeResourceList, isFetchingBizTreeResourceList } =
    useAssignCourseContext();

  if (isFetchingBizTreeResourceList) {
    return (
      <div className="flex h-full w-full flex-1 flex-col gap-2">
        <AssignCard>
          <Skeleton active className="h-full w-full" />
        </AssignCard>
      </div>
    );
  }

  if (!bizTreeResourceList || !bizTreeResourceList?.length) {
    return <AssignEmpty type="normal" className="flex-1" content="暂无内容" />;
  }

  return (
    <ScrollArea orientation="vertical" className="flex-1" onScroll={onScroll}>
      <div
        className={cn("flex w-full flex-col gap-2", className)}
        style={style}
      >
        {bizTreeResourceList.map((item) => (
          <AssignCourseItem
            key={item.bizTreeNodeId}
            data={item}
            showPreview={true}
          />
        ))}
      </div>
    </ScrollArea>
  );
}
