import { cn } from "@/utils/utils";

interface AssignHeadingProps {
    style?: React.CSSProperties,
    content: string,
    className?: string,
    showPrefix?: boolean,
    prefix?: string,
}

export function AssignHeading({
    content,
    style = {},
    className = "",
    showPrefix = false,
    prefix = "",
}: AssignHeadingProps) {
    return (
        <div className={cn("flex items-center h-6 select-none", className)} style={style}>
            {showPrefix && <span className="px-1 mr-1 h-4.5 leading-4.5 bg-orange-1 rounded inline-block text-center  text-white text-xs font-medium">{prefix}</span>}
            <h1 className="text-base font-medium leading-6 text-gray-1 ">{content}</h1>
        </div>
    )
}

