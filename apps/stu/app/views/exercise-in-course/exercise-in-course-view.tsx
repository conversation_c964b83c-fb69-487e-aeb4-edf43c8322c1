"use client";

import { useQuestionList } from "@/app/models/question-list-model";
import { exitLesson } from "@/app/utils/device";
import { CourseWidget } from "@/types/app/course";
import { StudyType } from "@repo/core/enums";
import { PreviewQuestionData } from "@repo/core/exercise/model/types";
import { ExercisePreview } from "@repo/core/exercise/preview/view/exercise-view";
import { FC } from "react";
import { ExerciseView } from "../exercise/exercise-view";
// import { toggleVConsole } from "@/app/hooks/debugger/use-vconsole";
import Button from "@repo/ui/components/press-button";
import { useSearchParams } from "next/navigation";
import { ExerciseHeaderActionView } from "../exercise/components/header-action-view";

interface ExerciseInCourseViewProps {
  activeInCourse: boolean;
  widgetIndex: number;
  exerciseData: CourseWidget<"exercise">;
  onComplete: (totalTimeSpent?: number) => void;
  onBack?: () => void;
}

/**
 * 课程中的练习组件
 * 根据 hasNextQuestion 决定显示练习组件还是预览组件
 */
export const ExerciseInCourseView: FC<ExerciseInCourseViewProps> = ({
  activeInCourse,
  widgetIndex,
  exerciseData,
  onComplete,
  onBack = () => {
    exitLesson();
  },
}) => {
  const rawQuestionData = exerciseData.data;
  const hasQuestion = rawQuestionData.hasNextQuestion;

  // 从 url 中获取 studySessionId
  const searchParams = useSearchParams();
  const studySessionId = parseInt(searchParams.get("studySessionId") || "0");

  const {
    data: fullData,
    isLoading,
    error,
  } = useQuestionList({
    studySessionId: hasQuestion ? undefined : studySessionId,
    widgetIndex, // 🔥 新增：AI 课中的练习需要传递 widgetIndex
  });

  if (!hasQuestion && error) {
    return (
      <div
        className="exercise-course-widget flex h-full w-full items-center justify-center"
        // onDoubleClick={toggleVConsole}
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="text-sm text-red-500">加载失败: {error?.message}</div>
          <Button className="w-24" onClick={() => onComplete()}>
            继续
          </Button>
        </div>
      </div>
    );
  }

  console.log("fullData", { fullData, widgetIndex, rawQuestionData });

  // 如果有题目信息，使用原有的练习组件逻辑
  if (hasQuestion) {
    const questionData = rawQuestionData;
    return (
      <ExerciseView
        activeInCourse={activeInCourse}
        widgetIndex={widgetIndex}
        studySessionId={questionData.studySessionId}
        studyType={StudyType.AI_COURSE}
        questionData={questionData}
        onComplete={onComplete}
        onBack={onBack}
      />
    );
  } else {
    // refresh();
  }

  // 🔥 优化：保持布局结构一致，避免闪烁
  if (!hasQuestion && isLoading) {
    // 保持相同的容器结构，但不显示内容
    return (
      <div className="exercise-course-widget h-full w-full opacity-0">
        {/* 保持结构但不显示内容，避免布局跳跃 */}
      </div>
    );
  }

  // 为了使用预览组件，我们需要构造预览数据
  const previewQuestions: PreviewQuestionData[] =
    fullData?.questions.map((question) => {
      const studentAnswer = fullData.studentAnswers.find(
        (answer) => answer.questionId === question.questionId
      );

      // 🔥 构造符合 PreviewQuestionInfo 类型的数据
      return {
        questionInfo: question,
        studentAnswer: studentAnswer,
      };
    }) || [];

  // 如果没有题目信息，显示空状态
  if (!hasQuestion && !previewQuestions.length) {
    return (
      <div
        className="exercise-course-widget flex h-full w-full items-center justify-center"
        // onDoubleClick={toggleVConsole}
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="text-sm text-gray-500">暂无练习数据</div>
          <Button className="w-24" onClick={() => onComplete()}>
            继续
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ExercisePreview
      progressDisplayMode="progress"
      questionList={previewQuestions}
      questionId={
        previewQuestions[previewQuestions.length - 1]?.questionInfo.questionId
      }
      initialIndex={0}
      studyType={StudyType.AI_COURSE}
      onBack={onBack}
      onContinue={() => onComplete()}
      headerRight={(questionId: string) => (
        <ExerciseHeaderActionView
          questionId={questionId}
          widgetIndex={widgetIndex}
        />
      )}
    />
  );
};
