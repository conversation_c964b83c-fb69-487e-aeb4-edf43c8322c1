# Model 层测试工具库

`@repo/test-utils/model` 是专门为 Model 层设计的测试工具库，提供统一的测试断言、SWR Hook 测试和 Zod Schema 测试功能。

## 🚀 特性

- ✅ **统一断言系统** - 支持 `expect().not.toThrow()` 等完整的正向和反向断言
- ✅ **SWR Hook 测试** - 标准化的 Hook 测试环境，支持 Mock 和状态等待
- ✅ **Zod Schema 测试** - 自动生成测试用例，支持类型转换和边界条件测试
- ✅ **详细错误提示** - 提供调试建议和详细的错误信息
- ✅ **Vitest 集成** - 完美支持 Vitest 测试运行器
- ✅ **TypeScript 支持** - 完整的类型定义和智能提示

## 📦 安装

```bash
# 测试库已包含在 Monorepo 中，直接引用即可
import { test, expect } from '@repo/test-utils/model';
```

## 🎯 核心 API

### 基础测试函数

```typescript
import { test, expect, getTestStats, resetTestStats } from '@repo/test-utils/model';

// 运行测试
test('示例测试', () => {
  expect(1 + 1).toBe(2);
  expect(() => { return 'success' }).not.toThrow();
});

// 获取测试统计
const stats = getTestStats();
console.log(`通过率: ${stats.passRate}%`);
```

### SWR Hook 测试

```typescript
import { 
  renderSWRHook, 
  createMockResponse, 
  waitForSWRState, 
  swrStateCheckers 
} from '@repo/test-utils/model';

// 测试 Hook
const { result } = renderSWRHook(
  () => useUsers(),
  { mocks: { get: createMockResponse(mockData) } }
);

// 等待状态变化
await waitForSWRState(result, swrStateCheckers.isLoaded);
```

### 类型安全测试

```typescript
import { test, expect } from '@repo/test-utils/model';

// 类型安全的数据测试
test('should handle user data correctly', () => {
  const userData = {
    userId: 123,
    userName: 'test',
    email: '<EMAIL>'
  };

  expect(userData.userId).toBe(123);
  expect(userData.userName).toBe('test');
});
```

## 📚 详细文档

- [使用示例](./examples.md) - 完整的使用示例和最佳实践
- [测试规范](.cursor/rules/test/test-model-guide.mdc) - Model 层测试规范文档

## 🔧 支持的断言

### 正向断言
- `toBe(expected)` - 严格相等
- `toEqual(expected)` - 深度相等
- `toThrow(expectedError?)` - 函数抛出异常
- `toBeTruthy()` - 真值断言
- `toBeFalsy()` - 假值断言
- `toBeDefined()` - 定义断言
- `toBeUndefined()` - 未定义断言
- `toBeNull()` - 空值断言
- `toContain(expected)` - 数组包含
- `toHaveLength(expected)` - 长度断言

### 反向断言
所有正向断言都支持 `.not` 反向操作：
- `expect(value).not.toBe(unexpected)`
- `expect(() => func()).not.toThrow()`
- `expect(array).not.toContain(value)`

## 🛠️ SWR 测试工具

### Mock 响应生成器
- `createMockResponse(data, delay?)` - 成功响应
- `createMockError(message, delay?)` - 错误响应
- `createNetworkError(delay?)` - 网络错误
- `createTimeoutError(delay?)` - 超时错误
- `createPaginatedMockResponse(items, page, pageSize, delay?)` - 分页响应

### 状态检查器
- `swrStateCheckers.isLoaded` - 检查加载完成
- `swrStateCheckers.hasData` - 检查有数据
- `swrStateCheckers.hasError` - 检查有错误
- `swrStateCheckers.isMutating` - 检查正在变更

## 🧪 Schema 测试功能

### 自动测试生成
- 根据 Schema 类型自动生成有效和无效样本
- 支持对象、数组、字符串、数字、布尔值等类型
- 自动处理可选字段和嵌套结构

### 测试配置选项
- `testBoundaryConditions` - 边界条件测试
- `testTypeCoercion` - 类型转换测试
- `customTests` - 自定义测试用例

## 📊 测试报告

测试完成后自动生成统计信息：

```typescript
const stats = getTestStats();
// {
//   total: 10,
//   passed: 9,
//   failed: 1,
//   results: [...],
//   passRate: "90.0"
// }
```

## 🔍 调试支持

### 详细错误信息
- SWR Hook 超时时显示当前状态
- Schema 验证失败时显示具体错误位置
- Mock 设置失败时提供解决建议

### 调试工具
- `console.log` 状态输出
- `test.only` 隔离测试
- `describe.skip` 跳过测试组

## 🎯 最佳实践

1. **测试组织**
   ```typescript
   describe('ModuleName Model', () => {
     describe('Hooks', () => { /* Hook 测试 */ });
     describe('Schemas', () => { /* Schema 测试 */ });
     describe('Transformers', () => { /* 转换函数测试 */ });
   });
   ```

2. **Mock 管理**
   ```typescript
   vi.mock('@repo/lib/utils/fetcher', () => ({
     get: vi.fn(),
     post: vi.fn(),
   }));
   
   beforeEach(() => {
     vi.clearAllMocks();
   });
   ```

3. **错误测试**
   ```typescript
   // 总是测试错误情况
   test('应该处理 API 错误', async () => {
     const { result } = renderSWRHook(
       () => useData(),
       { mocks: { get: createMockError('API Error') } }
     );
     
     await waitForSWRState(result, swrStateCheckers.hasError);
     expect(result.current.error).toBeDefined();
   });
   ```

## 🚀 快速开始

1. **创建测试文件**
   ```bash
   touch app/models/user/__tests__/user-model.test.ts
   ```

2. **编写基础测试**
   ```typescript
   import { describe, test, expect, vi } from 'vitest';
   import { renderSWRHook, createMockResponse } from '@repo/test-utils/model';
   
   vi.mock('@repo/lib/utils/fetcher', () => ({
     get: vi.fn(),
     post: vi.fn(),
   }));
   
   describe('User Model', () => {
     test('useUsers 应该获取用户列表', async () => {
       const mockUsers = [{ id: 1, name: 'John' }];
       
       const { result } = renderSWRHook(
         () => useUsers(),
         { mocks: { get: createMockResponse(mockUsers) } }
       );
       
       await waitForSWRState(result, state => !state.isLoading);
       expect(result.current.users).toEqual(mockUsers);
     });
   });
   ```

3. **运行测试**
   ```bash
   # 🚀 推荐：使用统一测试脚本
   pnpm test:models -F=stu user
   
   # 或直接运行 vitest
   pnpm vitest run app/models/user
   ```

## 🛠️ 测试脚本集成

本测试库已集成到项目的统一测试脚本中，推荐使用以下命令运行测试：

### 🚀 推荐命令（pnpm）
```bash
# 测试指定应用的所有 Model
pnpm test:models -F=<app>

# 测试指定 Model 模块
pnpm test:models -F=<app> <model-name>

# 运行覆盖率测试
pnpm test:models -F=<app> --coverage
```

### 📊 输出示例
```
📊 测试结果详情 - STU 应用
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
测试文件          │ 路径                             │ 测试数 │ 状态   │ 耗时  
──────────────┼────────────────────────────────┼─────┼──────┼─────
transformers  │ exercise-session/__tests__/tra... │ 7   │ ✅ 通过 │ 5ms 
schemas       │ exercise-session/__tests__/sch... │ 18  │ ✅ 通过 │ 6ms 
simple-vitest │ exercise-session/__tests__/sim... │ 10  │ ✅ 通过 │ 17ms
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

## 🔗 相关链接

- [Model 层测试规范](.cursor/rules/test/test-model-guide.mdc)
- [Vitest 官方文档](https://vitest.dev/)
- [Testing Library 文档](https://testing-library.com/)
- [Zod 文档](https://zod.dev/)

---

**版本**: v2.0  
**最后更新**: 2025年6月3日  
**维护者**: [项目团队]