import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { cn } from "@/utils/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { AssignButton } from "./assign-button";
import { AssignCancelAlert } from "./assign-cancel-alert";
import { AssignJoinedResource } from "./assign-joined-resource";
import { AssignTimeButton } from "./assign-time-button";

interface AssignFooterProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignFooter({
  style = {},
  className = "",
}: AssignFooterProps) {
  const { gotoTchAssignPage } = useTchNavigation();
  const {
    checkedClasses,
    treeId,
    bizTreeNodeId,

    // 有用的
    currentAssignStep,

    selectedAiCourses,
    selectedPractices,

    hasInvalidTimeRanges,

    goToSetTime,
    confirmAssign,
  } = useAssignCourseContext();
  const cancelAssignOpen = useSignal(false);

  const disabledNext = useComputed(() => {
    return (
      (selectedAiCourses.value.length === 0 &&
        selectedPractices.value.length === 0) ||
      checkedClasses.value.length === 0
    );
  });

  // 不确定是不是真的需要这个聚合值，先凑活了
  const totalCounter = useComputed(() => {
    return selectedAiCourses.value.length + selectedPractices.value.length;
  });

  // 这也不用computed，直接获取就行
  const umengCancelStep = useComputed(() => {
    if (
      treeId.value &&
      totalCounter.value > 0 &&
      checkedClasses.value.length === 0
    ) {
      // 树id和课程数量都存在,没有选择班级，是复制时取消布置
      return "";
    }
    const ret: string[] = [];
    if (currentAssignStep.value === "select-target") {
      if (checkedClasses.value.length > 0) {
        ret.push("class_selected");
      }
      if (treeId.value && bizTreeNodeId.value) {
        ret.push("chapter_selected");
      }
      if (totalCounter.value > 0) {
        ret.push("content_selected");
      }
    } else if (currentAssignStep.value === "set-time") {
      ret.push("time_set");
    }
    return ret.join("/");
  });

  // 需要清理下
  const { run: onComplete, loading } = useRequest(
    async () => {
      await confirmAssign();
    },
    {
      manual: true,
    }
  );

  // TODO: Check一下这里怎么2个埋点？？
  const umengCancelTrack = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_2,
      {
        cancel_status: "已取消",
      }
    );
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_3,
      {
        unfinish_step: umengCancelStep.value,
      }
    );
  };

  // Done
  const onCancelAssign = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_1,
      {
        cancel_clicked: "已点击",
      }
    );
    if (totalCounter.value > 0) {
      cancelAssignOpen.value = true;
    } else {
      umengCancelTrack();
      gotoTchAssignPage(true);
    }
  };

  return (
    <div
      className={cn(
        "h-17 flex select-none items-center justify-end gap-4 border-t border-slate-200 bg-white px-8 shadow-[0px_8px_32px_0px_rgba(16,18,25,0.10)]",
        className
      )}
      style={style}
    >
      <AssignTimeButton content="预估时长" />

      <AssignButton
        variant="outline"
        className="px-5 outline-slate-300"
        onClick={onCancelAssign}
      >
        取消布置
      </AssignButton>

      {/* 已加入的资源列表 */}
      <AssignJoinedResource />
      {currentAssignStep.value === "set-time" ? (
        <AssignButton
          className="px-5"
          onClick={onComplete}
          disabled={
            totalCounter.value === 0 || hasInvalidTimeRanges.value || loading
          }
        >
          完成并布置
        </AssignButton>
      ) : (
        <AssignButton
          className="px-5"
          onClick={goToSetTime}
          disabled={disabledNext.value}
        >
          下一步
        </AssignButton>
      )}
      <AssignCancelAlert
        open={cancelAssignOpen.value}
        onCancel={() => {
          cancelAssignOpen.value = false;
          umeng.trackEvent(
            UmengCategory.ASSIGN,
            UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_2,
            {
              cancel_status: "未取消",
            }
          );
        }}
        onOk={() => {
          cancelAssignOpen.value = false;
          umengCancelTrack();
          gotoTchAssignPage(true);
        }}
      />
    </div>
  );
}
