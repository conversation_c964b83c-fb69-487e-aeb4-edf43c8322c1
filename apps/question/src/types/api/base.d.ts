declare namespace API {
  interface Response<T = any> {
    code: number;
    message: string;
    data: T;
  }

  interface PageData<T = any> {
    total: number;
    list: T[];
    page: number;
    pageSize: number;
  }
  interface CommentPageData<T = any> {
    pageInfo: {
      total: number;
      page: number;
      pageSize: number;
    };
    list: T[];
  }

  interface PageParams {
    page: number;
    pageSize?: number;
  }
}
