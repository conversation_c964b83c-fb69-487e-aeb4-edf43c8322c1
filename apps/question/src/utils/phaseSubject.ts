import { getPageState } from './common';

/**
 * 获取初始学段科目
 * @param phaseSubjects 学段科目数据
 * @param hasSubject 是否需要科目
 * @returns 学段科目
 */
export const getInitPhaseSubject = (
  phaseSubjects: API.EnumConstantData['phaseSubjectRelation'],
  hasSubject: boolean = true,
) => {
  const firstPhase = phaseSubjects.find((item) => item.isDefault) || phaseSubjects[0];
  if (!firstPhase) {
    return [];
  }
  const phaseValue = firstPhase.value;
  if (!hasSubject) {
    return [phaseValue];
  }
  const subjectList = firstPhase?.subjectList || [];
  const firstSubject = subjectList.find((item) => item.isDefault) || subjectList[0];
  if (!firstSubject) {
    return [phaseValue];
  }
  return [phaseValue, firstSubject.value];
};

/**
 * 树类型
 */
export enum TreeTypeEnum {
  BASE_TREE = 'baseTree',
  BIZ_TREE = 'bizTree',
}

export const TreeTypeOptions = [
  {
    label: '知识点查题',
    value: TreeTypeEnum.BASE_TREE,
  },
  {
    label: '章节查题',
    value: TreeTypeEnum.BIZ_TREE,
  },
];

export const isBaseTree = (treeType: TreeTypeEnum) => {
  return treeType === TreeTypeEnum.BASE_TREE;
};

export const isBizTree = (treeType: TreeTypeEnum) => {
  return treeType === TreeTypeEnum.BIZ_TREE;
};

export const getManageQuestionPagePhaseSubjectInitValue = (
  key: string,
  phaseSubjects: API.EnumConstantData['phaseSubjectRelation'],
  hasSubject: boolean = true,
) => {
  const pageState = getPageState(key);
  if (pageState && pageState.length > 0) {
    return pageState;
  }
  return getInitPhaseSubject(phaseSubjects, hasSubject);
};
