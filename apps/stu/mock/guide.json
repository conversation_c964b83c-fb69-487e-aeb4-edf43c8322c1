{"avatar": {"durationInFrames": 5832, "fps": 24, "height": 10, "url": "https://static.xiaoluxue.cn/algo/demo2/course/e5dbf0d43f174da891585a5da9eabff0/video/c167af7362f0493a9728449d8d0b9848.mp4", "width": 10}, "content": [{"content": [{"content": "向量法证明直线垂直的原理", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "3", "inFrame": 0, "level": 1, "outFrame": 186, "pic": null, "tag": "h3"}, {"content": [{"content": "证明两条直线垂直，关键在于证明其", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "向量相互垂直", "inFrame": 283, "notation": {"color": "#FF4E3366", "duration": 12, "inFrame": 283, "outFrame": 319, "strokeWidth": 2, "type": "circle"}, "outFrame": 319, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 188, "level": 2, "outFrame": 321, "pic": null, "tag": "default"}, {"content": [{"content": "判断向量垂直的代数工具是数量积：若两个非零向量的", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "数量积为零", "inFrame": 463, "notation": {"color": "#FF4E3366", "duration": 4, "inFrame": 463, "outFrame": 476, "strokeWidth": 2, "type": "circle"}, "outFrame": 476, "tag": "normal"}, {"content": "，则", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "它们相互垂直", "inFrame": 491, "notation": {"color": "#33AAFF66", "duration": 12, "inFrame": 491, "outFrame": 527, "strokeWidth": 2, "type": "underline"}, "outFrame": 527, "tag": "normal"}, {"content": "。此原理适用于空间向量。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 323, "level": 2, "outFrame": 757, "pic": null, "tag": "default"}, {"content": [{"content": "例题", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "5", "inFrame": 0, "level": 1, "outFrame": 0, "pic": null, "tag": "h3"}, {"content": [{"content": "在平行六面体 \\(ABCD-A_1B_1C_1D_1\\) 中，已知 \\(AB=4\\)，\\(AD=4\\)，", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(AA_1=5\\)", "inFrame": 948, "notation": {"color": "#FF4E3366", "duration": 12, "inFrame": 948, "outFrame": 985, "strokeWidth": 2, "type": "circle"}, "outFrame": 985, "tag": "normal"}, {"content": "；底面 \\(\\angle DAB=60^\\circ\\)；面 \\(ABB_1A_1\\) 中 \\(\\angle BAA_1=60^\\circ\\)；面 \\(ADD_1A_1\\) 中 \\(\\angle DAA_1=60^\\circ\\)。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "点 \\(M\\) 是棱 \\(D_1C_1\\) 的中点，点 \\(N\\) 是棱 \\(C_1B_1\\) 的中点", "inFrame": 1220, "notation": {"color": "#33AAFF66", "duration": 37, "inFrame": 1220, "outFrame": 1332, "strokeWidth": 2, "type": "underline"}, "outFrame": 1332, "tag": "normal"}, {"content": "。求证：", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "直线 \\(MN \\perp\\) 直线 \\(AC_1\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 760, "level": 2, "outFrame": 1334, "pic": null, "tag": "default"}, {"content": [{"content": "证明思路", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "4", "inFrame": 1342, "level": 2, "outFrame": 1449, "pic": null, "tag": "h4"}, {"content": [{"content": "证明向量 \\(\\overrightarrow{MN}\\) 与 \\(\\overrightarrow{AC_1}\\) 的", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "数量积为 \\(0\\)", "inFrame": 1502, "notation": {"color": "#FF4E3366", "duration": 10, "inFrame": 1502, "outFrame": 1534, "strokeWidth": 2, "type": "circle"}, "outFrame": 1534, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 1449, "level": 3, "outFrame": 1534, "pic": null, "tag": "default"}, {"content": [{"content": "选择基底向量及其性质", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "8", "inFrame": 1544, "level": 2, "outFrame": 1592, "pic": null, "tag": "h4"}, {"content": [{"content": "设 \\(\\overrightarrow{AB} = \\boldsymbol{a}\\)，\\(\\overrightarrow{AD} = \\boldsymbol{b}\\)，\\(\\overrightarrow{AA_1} = \\boldsymbol{c}\\)", "inFrame": 1592, "notation": {"color": "#33AAFF66", "duration": 42, "inFrame": 1592, "outFrame": 1718, "strokeWidth": 2, "type": "underline"}, "outFrame": 1718, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 1592, "level": 3, "outFrame": 1734, "pic": null, "tag": "default"}, {"content": [{"content": "", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(|\\boldsymbol{a}| = 4\\)", "inFrame": 1778, "notation": {"color": "#FF4E3366", "duration": 12, "inFrame": 1778, "outFrame": 1816, "strokeWidth": 2, "type": "circle"}, "outFrame": 1816, "tag": "normal"}, {"content": "，\\(|\\boldsymbol{b}| = 4\\)，\\(|\\boldsymbol{c}| = 5\\)", "inFrame": 1816, "notation": {"color": "#FF4E3366", "duration": 29, "inFrame": 1816, "outFrame": 1903, "strokeWidth": 2, "type": "circle"}, "outFrame": 1903, "tag": "normal"}], "icon": null, "inFrame": 1737, "level": 3, "outFrame": 1905, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(\\boldsymbol{a}\\) 与 \\(\\boldsymbol{b}\\) 的夹角为 ", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(60^\\circ\\)", "inFrame": 1959, "notation": {"color": "#FF4E3366", "duration": 6, "inFrame": 1959, "outFrame": 1978, "strokeWidth": 2, "type": "circle"}, "outFrame": 1978, "tag": "normal"}], "icon": null, "inFrame": 1907, "level": 3, "outFrame": 1978, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(\\boldsymbol{a}\\) 与 \\(\\boldsymbol{c}\\) 的夹角为 ", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(60^\\circ\\)", "inFrame": 1959, "notation": {"color": "#FF4E3366", "duration": 6, "inFrame": 1959, "outFrame": 1978, "strokeWidth": 2, "type": "circle"}, "outFrame": 1978, "tag": "normal"}], "icon": null, "inFrame": 1980, "level": 3, "outFrame": 2044, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(\\boldsymbol{b}\\) 与 \\(\\boldsymbol{c}\\) 的夹角为 ", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(60^\\circ\\)", "inFrame": 1959, "notation": {"color": "#FF4E3366", "duration": 6, "inFrame": 1959, "outFrame": 1978, "strokeWidth": 2, "type": "circle"}, "outFrame": 1978, "tag": "normal"}], "icon": null, "inFrame": 2044, "level": 3, "outFrame": 2110, "pic": null, "tag": "ul"}, {"content": [{"content": "表示向量 \\(\\overrightarrow{MN}\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "5", "inFrame": 2113, "level": 2, "outFrame": 2357, "pic": null, "tag": "h4"}, {"content": [{"content": "\\(M\\) 为 \\(D_1C_1\\) 中点，\\(\\overrightarrow{D_1C_1} = \\overrightarrow{AB} = \\boldsymbol{a}\\)，则 \\(\\overrightarrow{MC_1} = \\dfrac{1}{2}\\boldsymbol{a}\\)。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 0, "level": 3, "outFrame": 0, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(N\\) 为 \\(C_1B_1\\) 中点，\\(\\overrightarrow{C_1B_1} = \\overrightarrow{DA} = -\\boldsymbol{b}\\)，则 \\(\\overrightarrow{C_1N} = -\\dfrac{1}{2}\\boldsymbol{b}\\)。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 2359, "level": 3, "outFrame": 2618, "pic": null, "tag": "ul"}, {"content": [{"content": "", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(\\overrightarrow{MN} = \\overrightarrow{MC_1} + \\overrightarrow{C_1N} = \\dfrac{1}{2}\\boldsymbol{a} - \\dfrac{1}{2}\\boldsymbol{b}\\)", "inFrame": 2643, "notation": {"color": "#33AAFF66", "duration": 48, "inFrame": 2643, "outFrame": 2787, "strokeWidth": 2, "type": "underline"}, "outFrame": 2787, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 2620, "level": 3, "outFrame": 2803, "pic": null, "tag": "ul"}, {"content": [{"content": "表示向量 \\(\\overrightarrow{AC_1}\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "9", "inFrame": 2812, "level": 2, "outFrame": 2864, "pic": null, "tag": "h4"}, {"content": [{"content": "\\(\\overrightarrow{AC_1} = \\overrightarrow{AB} + \\overrightarrow{BC} + \\overrightarrow{CC_1} = \\boldsymbol{a} + \\boldsymbol{b} + \\boldsymbol{c}\\)", "inFrame": 2867, "notation": {"color": "#33AAFF66", "duration": 50, "inFrame": 2867, "outFrame": 3019, "strokeWidth": 2, "type": "underline"}, "outFrame": 3019, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 2867, "level": 3, "outFrame": 3035, "pic": null, "tag": "default"}, {"content": [{"content": "计算数量积 \\(\\overrightarrow{MN} \\cdot \\overrightarrow{AC_1}\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "3", "inFrame": 3039, "level": 2, "outFrame": 3401, "pic": null, "tag": "h4"}, {"content": [{"content": "展开数量积： \\[\\overrightarrow{MN} \\cdot \\overrightarrow{AC_1} = \\left(\\dfrac{1}{2}\\boldsymbol{a} - \\dfrac{1}{2}\\boldsymbol{b}\\right) \\cdot (\\boldsymbol{a} + \\boldsymbol{b} + \\boldsymbol{c}) = \\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{a} + \\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{b} + \\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{c} - \\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{a} - \\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{b} - \\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{c} \\]", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 3404, "level": 3, "outFrame": 3863, "pic": null, "tag": "default"}, {"content": [{"content": "各项计算：", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 3865, "level": 3, "outFrame": 4101, "pic": null, "tag": "default"}, {"content": [{"content": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{a} = \\dfrac{1}{2}|\\boldsymbol{a}|^2 = \\dfrac{1}{2} \\times 4^2 = 8\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 4103, "level": 3, "outFrame": 4126, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{b} = \\dfrac{1}{2}|\\boldsymbol{a}||\\boldsymbol{b}|\\cos 60^\\circ = \\dfrac{1}{2} \\times 4 \\times 4 \\times \\dfrac{1}{2} = 4\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 4129, "level": 3, "outFrame": 4384, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{c} = \\dfrac{1}{2}|\\boldsymbol{a}||\\boldsymbol{c}|\\cos 60^\\circ = \\dfrac{1}{2} \\times 4 \\times 5 \\times \\dfrac{1}{2} = 5\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 4386, "level": 3, "outFrame": 4613, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{a} = -\\dfrac{1}{2}|\\boldsymbol{b}||\\boldsymbol{a}|\\cos 60^\\circ = \\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "-4", "inFrame": 4815, "notation": {"color": "#FF4E3366", "duration": 5, "inFrame": 4815, "outFrame": 4831, "strokeWidth": 2, "type": "circle"}, "outFrame": 4831, "tag": "normal"}], "icon": null, "inFrame": 4616, "level": 3, "outFrame": 4833, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{b} = -\\dfrac{1}{2}|\\boldsymbol{b}|^2 = -8\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 4835, "level": 3, "outFrame": 5009, "pic": null, "tag": "ul"}, {"content": [{"content": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{c} = -\\dfrac{1}{2}|\\boldsymbol{b}||\\boldsymbol{c}|\\cos 60^\\circ = -5\\)", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5011, "level": 3, "outFrame": 5226, "pic": null, "tag": "ul"}, {"content": [{"content": "合并结果：", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5235, "level": 3, "outFrame": 5253, "pic": null, "tag": "default"}, {"content": [{"content": "\\[8 + 4 + 5 - 4 - 8 - 5 = 0\\]", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5253, "level": 3, "outFrame": 5316, "pic": null, "tag": "default"}, {"content": [{"content": "证明结论", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "5", "inFrame": 5332, "level": 2, "outFrame": 5348, "pic": null, "tag": "h4"}, {"content": [{"content": "由于 ", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(\\overrightarrow{MN} \\cdot \\overrightarrow{AC_1} = 0\\)", "inFrame": 5356, "notation": {"color": "#FF4E3366", "duration": 23, "inFrame": 5356, "outFrame": 5425, "strokeWidth": 2, "type": "circle"}, "outFrame": 5425, "tag": "normal"}, {"content": "，且向量 \\(\\overrightarrow{MN}\\) 与 \\(\\overrightarrow{AC_1}\\) 均为非零向量（\\(\\overrightarrow{MN} = \\dfrac{1}{2}(\\boldsymbol{a}-\\boldsymbol{b}) \\neq \\boldsymbol{0}\\)，\\(\\overrightarrow{AC_1} = \\boldsymbol{a}+\\boldsymbol{b}+\\boldsymbol{c} \\neq \\boldsymbol{0}\\)），因此 ", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "\\(\\overrightarrow{MN} \\perp \\overrightarrow{AC_1}\\)", "inFrame": 5484, "notation": {"color": "#33AAFF66", "duration": 16, "inFrame": 5484, "outFrame": 5534, "strokeWidth": 2, "type": "underline"}, "outFrame": 5534, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5348, "level": 3, "outFrame": 5539, "pic": null, "tag": "default"}, {"content": [{"content": "所以，", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}, {"content": "直线 \\(MN \\perp\\) 直线 \\(AC_1\\)", "inFrame": 5548, "notation": {"color": "#FF4E3366", "duration": 19, "inFrame": 5548, "outFrame": 5606, "strokeWidth": 2, "type": "circle"}, "outFrame": 5606, "tag": "normal"}, {"content": "。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5541, "level": 3, "outFrame": 5611, "pic": null, "tag": "default"}, {"content": [{"content": "方法总结", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": "3", "inFrame": 5625, "level": 2, "outFrame": 5645, "pic": null, "tag": "h4"}, {"content": [{"content": "即使选择非正交基底，只要已知基向量的模长和夹角，利用数量积仍可有效判断空间直线的垂直关系。", "inFrame": 0, "notation": null, "outFrame": 0, "tag": "normal"}], "icon": null, "inFrame": 5647, "level": 3, "outFrame": 5839, "pic": null, "tag": "default"}], "footer": null, "header": null, "subtitles": [{"id": 1, "inFrame": 6, "outFrame": 97, "text": "同学们，我们已经学习了如何用向量证明直线平行"}, {"id": 2, "inFrame": 97, "outFrame": 192, "text": "向量在证明直线垂直方面同样非常强大"}, {"id": 3, "inFrame": 192, "outFrame": 325, "text": "要证明两条直线垂直，一个关键的思路就是证明向量相互垂直"}, {"id": 4, "inFrame": 325, "outFrame": 370, "text": "而判断向量是否垂直"}, {"id": 5, "inFrame": 370, "outFrame": 470, "text": "一个重要的代数工具就是向量的数量积——如果两个非零向量的数量"}, {"id": 6, "inFrame": 470, "outFrame": 532, "text": "积为零，那么它们就相互垂直"}, {"id": 7, "inFrame": 532, "outFrame": 623, "text": "这个概念大家在学习平面向量时应该已经接触过"}, {"id": 8, "inFrame": 623, "outFrame": 689, "text": "现在我们将其应用到空间向量中"}, {"id": 9, "inFrame": 689, "outFrame": 762, "text": "通过一个具体的例子来应用这个方法"}, {"id": 10, "inFrame": 762, "outFrame": 816, "text": "例题：在平行六面体"}, {"id": 11, "inFrame": 816, "outFrame": 897, "text": "\\(ABCD-A_1B_1C_1D_1\\) 中，已知"}, {"id": 12, "inFrame": 897, "outFrame": 991, "text": "\\(AB=4\\)，\\(AD=4\\)，\\(AA_1=5\\)"}, {"id": 13, "inFrame": 991, "outFrame": 1038, "text": "底面 \\(\\angle DAB=60^\\circ\\)"}, {"id": 14, "inFrame": 1038, "outFrame": 1121, "text": "面 \\(ABB_1A_1\\) 中 \\(\\angle BAA_1=60^\\circ\\)"}, {"id": 15, "inFrame": 1121, "outFrame": 1215, "text": "面 \\(ADD_1A_1\\) 中 \\(\\angle DAA_1=60^\\circ\\)"}, {"id": 16, "inFrame": 1215, "outFrame": 1337, "text": "点 \\(M\\) 是棱 \\(D_1C_1\\) 的中点，点 \\(N\\) 是棱 \\(C_1B_1\\) 的中点"}, {"id": 17, "inFrame": 1337, "outFrame": 1426, "text": "求证：直线 \\(MN \\perp\\) 直线 \\(AC_1\\)"}, {"id": 18, "inFrame": 1426, "outFrame": 1539, "text": "证明思路：证明向量 \\(\\overrightarrow{MN}\\) 与 \\(\\overrightarrow{AC_1}\\) 的数量积为 \\(0\\)"}, {"id": 19, "inFrame": 1539, "outFrame": 1592, "text": "步骤1：选择基底"}, {"id": 20, "inFrame": 1592, "outFrame": 1599, "text": "设"}, {"id": 21, "inFrame": 1599, "outFrame": 1616, "text": "\\(\\overrightarrow{AB} = \\boldsymbol{a}\\)"}, {"id": 22, "inFrame": 1616, "outFrame": 1684, "text": "\\(\\overrightarrow{AD} = \\boldsymbol{b}\\)"}, {"id": 23, "inFrame": 1684, "outFrame": 1734, "text": "\\(\\overrightarrow{AA_1} = \\boldsymbol{c}\\)"}, {"id": 24, "inFrame": 1734, "outFrame": 1735, "text": "由题意知："}, {"id": 25, "inFrame": 1735, "outFrame": 1874, "text": "\\(|\\boldsymbol{a}| = 4\\)，\\(|\\boldsymbol{b}| = 4\\)，\\(|\\boldsymbol{c}| = 5\\)"}, {"id": 26, "inFrame": 1874, "outFrame": 1933, "text": "\\(\\boldsymbol{a}\\) 与"}, {"id": 27, "inFrame": 1933, "outFrame": 1959, "text": "\\(\\boldsymbol{b}\\) 的夹角为"}, {"id": 28, "inFrame": 1959, "outFrame": 1965, "text": "\\(60^\\circ\\)"}, {"id": 29, "inFrame": 1965, "outFrame": 1999, "text": "\\(\\boldsymbol{a}\\) 与"}, {"id": 30, "inFrame": 1999, "outFrame": 2026, "text": "\\(\\boldsymbol{c}\\) 的夹角为"}, {"id": 31, "inFrame": 2026, "outFrame": 2033, "text": "\\(60^\\circ\\)"}, {"id": 32, "inFrame": 2033, "outFrame": 2052, "text": "\\(\\boldsymbol{b}\\) 与"}, {"id": 33, "inFrame": 2052, "outFrame": 2090, "text": "\\(\\boldsymbol{c}\\) 的夹角为"}, {"id": 34, "inFrame": 2090, "outFrame": 2114, "text": "\\(60^\\circ\\)"}, {"id": 35, "inFrame": 2114, "outFrame": 2151, "text": "步骤2：表示向量"}, {"id": 36, "inFrame": 2151, "outFrame": 2173, "text": "\\(\\overrightarrow{MN}\\)， 因为"}, {"id": 37, "inFrame": 2173, "outFrame": 2215, "text": "\\(M\\) 是 \\(D_1C_1\\) 的中点"}, {"id": 38, "inFrame": 2215, "outFrame": 2285, "text": "\\(\\overrightarrow{D_1C_1} = \\overrightarrow{AB} = \\boldsymbol{a}\\)"}, {"id": 39, "inFrame": 2285, "outFrame": 2292, "text": "故"}, {"id": 40, "inFrame": 2292, "outFrame": 2362, "text": "\\(\\overrightarrow{MC_1} = \\dfrac{1}{2}\\boldsymbol{a}\\)"}, {"id": 41, "inFrame": 2362, "outFrame": 2416, "text": "因为\\(N\\) 是 \\(C_1B_1\\) 的中点"}, {"id": 42, "inFrame": 2416, "outFrame": 2491, "text": "\\(\\overrightarrow{C_1B_1} = \\overrightarrow{DA} = -\\boldsymbol{b}\\)"}, {"id": 43, "inFrame": 2491, "outFrame": 2498, "text": "故"}, {"id": 44, "inFrame": 2498, "outFrame": 2621, "text": "\\(\\overrightarrow{C_1N} = \\dfrac{1}{2}(-\\boldsymbol{b}) = -\\dfrac{1}{2}\\boldsymbol{b}\\)"}, {"id": 45, "inFrame": 2621, "outFrame": 2643, "text": "因此"}, {"id": 46, "inFrame": 2643, "outFrame": 2806, "text": "\\(\\overrightarrow{MN} = \\overrightarrow{MC_1} + \\overrightarrow{C_1N} = \\dfrac{1}{2}\\boldsymbol{a} - \\dfrac{1}{2}\\boldsymbol{b}\\)"}, {"id": 47, "inFrame": 2806, "outFrame": 2841, "text": "步骤3：表示向量"}, {"id": 48, "inFrame": 2841, "outFrame": 2864, "text": "\\(\\overrightarrow{AC_1}\\)"}, {"id": 49, "inFrame": 2864, "outFrame": 3040, "text": "\\(\\overrightarrow{AC_1} = \\overrightarrow{AB} + \\overrightarrow{BC} + \\overrightarrow{CC_1} = \\boldsymbol{a} + \\boldsymbol{b} + \\boldsymbol{c}\\)"}, {"id": 50, "inFrame": 3040, "outFrame": 3083, "text": "步骤4：计算数量积"}, {"id": 51, "inFrame": 3083, "outFrame": 3097, "text": "\\(\\overrightarrow{MN} \\cdot"}, {"id": 52, "inFrame": 3097, "outFrame": 3160, "text": "\\overrightarrow{AC_1} ="}, {"id": 53, "inFrame": 3160, "outFrame": 3243, "text": "\\left(\\dfrac{1}{2}\\boldsymbol{"}, {"id": 54, "inFrame": 3243, "outFrame": 3249, "text": "a} -"}, {"id": 55, "inFrame": 3249, "outFrame": 3268, "text": "\\dfrac{1}{2}\\boldsymbol{b}\\rig"}, {"id": 56, "inFrame": 3268, "outFrame": 3342, "text": "ht) \\cdot (\\boldsymbol{a} +"}, {"id": 57, "inFrame": 3342, "outFrame": 3360, "text": "\\boldsymbol{b} +"}, {"id": 58, "inFrame": 3360, "outFrame": 3376, "text": "\\boldsymbol{c})"}, {"id": 59, "inFrame": 3376, "outFrame": 3453, "text": "= \\dfrac{1}{2}\\boldsymbol{a}"}, {"id": 60, "inFrame": 3453, "outFrame": 3473, "text": "\\cdot \\boldsymbol{a} +"}, {"id": 61, "inFrame": 3473, "outFrame": 3534, "text": "\\dfrac{1}{2}\\boldsymbol{a}"}, {"id": 62, "inFrame": 3534, "outFrame": 3562, "text": "\\cdot \\boldsymbol{b} +"}, {"id": 63, "inFrame": 3562, "outFrame": 3591, "text": "\\dfrac{1}{2}\\boldsymbol{a}"}, {"id": 64, "inFrame": 3591, "outFrame": 3621, "text": "\\cdot \\boldsymbol{c} -"}, {"id": 65, "inFrame": 3621, "outFrame": 3675, "text": "\\dfrac{1}{2}\\boldsymbol{b}"}, {"id": 66, "inFrame": 3675, "outFrame": 3707, "text": "\\cdot \\boldsymbol{a} -"}, {"id": 67, "inFrame": 3707, "outFrame": 3750, "text": "\\dfrac{1}{2}\\boldsymbol{b}"}, {"id": 68, "inFrame": 3750, "outFrame": 3778, "text": "\\cdot \\boldsymbol{b} -"}, {"id": 69, "inFrame": 3778, "outFrame": 3819, "text": "\\dfrac{1}{2}\\boldsymbol{b}"}, {"id": 70, "inFrame": 3819, "outFrame": 3868, "text": "\\cdot \\boldsymbol{c}\\)"}, {"id": 71, "inFrame": 3868, "outFrame": 3911, "text": "代入数值计算各项："}, {"id": 72, "inFrame": 3911, "outFrame": 4099, "text": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{a} = \\dfrac{1}{2}|\\boldsymbol{a}|^2 = \\dfrac{1}{2} \\times 4^2 = 8\\)"}, {"id": 73, "inFrame": 4099, "outFrame": 4382, "text": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{b} = \\dfrac{1}{2}|\\boldsymbol{a}||\\boldsymbol{b}|\\cos 60^\\circ = \\dfrac{1}{2} \\times 4 \\times 4 \\times \\dfrac{1}{2} = 4\\)"}, {"id": 74, "inFrame": 4382, "outFrame": 4612, "text": "\\(\\dfrac{1}{2}\\boldsymbol{a} \\cdot \\boldsymbol{c} = \\dfrac{1}{2}|\\boldsymbol{a}||\\boldsymbol{c}|\\cos 60^\\circ = \\dfrac{1}{2} \\times 4 \\times 5 \\times \\dfrac{1}{2} = 5\\)"}, {"id": 75, "inFrame": 4612, "outFrame": 4831, "text": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{a} = -\\dfrac{1}{2}|\\boldsymbol{b}||\\boldsymbol{a}|\\cos 60^\\circ = -4\\)"}, {"id": 76, "inFrame": 4831, "outFrame": 4983, "text": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{b} = -\\dfrac{1}{2}|\\boldsymbol{b}|^2 = -8\\)"}, {"id": 77, "inFrame": 4983, "outFrame": 5230, "text": "\\(-\\dfrac{1}{2}\\boldsymbol{b} \\cdot \\boldsymbol{c} = -\\dfrac{1}{2}|\\boldsymbol{b}||\\boldsymbol{c}|\\cos 60^\\circ = -5\\)"}, {"id": 78, "inFrame": 5230, "outFrame": 5324, "text": "合并结果：  \\[8 + 4 + 5 - 4 - 8 - 5 = 0\\]"}, {"id": 79, "inFrame": 5324, "outFrame": 5356, "text": "结论：由于"}, {"id": 80, "inFrame": 5356, "outFrame": 5425, "text": "\\(\\overrightarrow{MN} \\cdot \\overrightarrow{AC_1} = 0\\)"}, {"id": 81, "inFrame": 5425, "outFrame": 5484, "text": "且两向量均为非零向量，故"}, {"id": 82, "inFrame": 5484, "outFrame": 5540, "text": "\\(\\overrightarrow{MN} \\perp \\overrightarrow{AC_1}\\)"}, {"id": 83, "inFrame": 5540, "outFrame": 5618, "text": "即直线 \\(MN \\perp\\) 直线 \\(AC_1\\)"}, {"id": 84, "inFrame": 5618, "outFrame": 5751, "text": "总结：即使选择非正交基底，只要已知基向量的模长和夹角"}, {"id": 85, "inFrame": 5751, "outFrame": 5839, "text": "利用数量积仍可有效判断空间直线的垂直关系"}]}