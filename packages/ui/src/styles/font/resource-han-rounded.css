@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 200;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-ExtraLight.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 300;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Light.ttf") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 400;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Regular.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 500;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Medium.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 600;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Bold.ttf") format("truetype");  
  font-display: swap;
}
@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 700;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Bold.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "ResourceHanRounded";
  font-style: normal;
  font-weight: 800;
  src: url("https://static.xiaoluxue.com/assets/fonts/ResourceHanRoundedCN-Heavy.ttf") format("truetype");
  font-display: swap;
}