declare namespace API {
  /**
   * 基础树列表
   */
  interface BaseTreeListRequestParams {
    phaseList?: number[];
    subjectList?: number[];
    baseTreeNameKey?: string;
    baseTreeId?: number;
    page?: number;
    pageSize?: number;
    [property: string]: any;
  }
  interface BaseTreeItem {
    baseTreeId: number;
    baseTreeName: string;
    baseTreeVersion: string;
    phaseEnum: number;
    subjectEnum: number;
    [property: string]: any;
  }

  interface BaseTreeListResponse {
    code: number;
    message: string;
    data: {
      list: BaseTreeItem[];
      formatList?: Common.TreeListItem[];
      page: number;
      pageSize: number;
      total: number;
    };
    [property: string]: any;
  }

  /**
   * 基础树详情
   */
  interface BaseTreeDetailRequestParams {
    baseTreeId: number;
  }
  interface BaseTreeNode {
    baseTreeNodeChildren: BaseTreeNode[];
    /**
     * 基础树节点id
     */
    baseTreeNodeId: number;
    /**
     * 基础树节点level，根节点level是1
     */
    baseTreeNodeLevel: number;
    /**
     * 基础树节点名称
     */
    baseTreeNodeName: string;
    /**
     * 基础树节点兄弟序号
     */
    baseTreeNodeSiblingOrder: number;
    /**
     * 基础树父节点id
     */
    baseTreeParentNodeId?: number;
    [property: string]: any;
  }

  interface BaseTreeDetailData {
    baseTreeDetail: BaseTreeNode;
    baseTreeId: number;
    baseTreeName: string;
    baseTreeVersion: string;
    phase: number;
    subject: number;
    treeData?: Common.BaseTreeNodeAntdType[];
    [property: string]: any;
  }

  interface BaseTreeDetailResponse {
    code: number;
    message: string;
    data: BaseTreeDetailData;
  }
}
