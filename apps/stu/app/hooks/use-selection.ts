import { useEffect, useMemo, useState } from "react";

export const useSelection = () => {
  const [range, setRange] = useState<Range | null>(null);
  const pos = useMemo(() => {
    const [...rects] = range?.getClientRects() ?? [
      { left: 0, top: 0, width: 0 },
    ];
    const rect = rects.length
      ? rects.reduce((rect, cur) => {
          if (cur.top !== rect.top) return rect;
          return {
            left: rect.left,
            top: rect.top,
            width: rect.width + cur.width / 2, // 不知道为什么半个宽度就是正好的，一个宽度就是顶满了
          };
        })
      : ({} as DOMRect);
    const { left, width, top } = rect;
    return { x: left + width / 2, y: top };
  }, [range]);
  const nodes = useMemo(() => {
    if (!range) return [];
    const nodes: HTMLElement[] = [];
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          // todo))为了兼容img这样的element类型，还有其他的不该被选中的element混进来了
          if (range.intersectsNode(node)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_REJECT;
        },
      }
    );
    let currentNode: Node | null = treeWalker.currentNode;
    while (currentNode) {
      if (range.intersectsNode(currentNode)) {
        if (
          currentNode.nodeName === "IMG" &&
          (currentNode as HTMLElement).dataset?.lineId
        ) {
          nodes.push(currentNode as HTMLElement);
        } else if (currentNode.nodeType === Node.TEXT_NODE) {
          const parent = currentNode.parentElement?.closest("[data-line-id]");
          if (parent) {
            nodes.push(parent as HTMLElement);
          }
        }
      }
      currentNode = treeWalker.nextNode();
    }
    // 去重
    return Array.from(new Set(nodes));
  }, [range]);

  const result = useMemo(() => {
    let lastRootId: string;
    return nodes.reduce(
      (acc, node) => {
        const rootId = node.dataset.rootId!;
        if (!lastRootId) {
          lastRootId = rootId;
        }
        if (rootId !== lastRootId) {
          return acc;
        }
        const lineId = node.dataset.lineId!;
        const textureId = node.dataset.textureId;
        const charId = node.dataset.charId;
        const type = node.dataset.type ?? "text";
        let index = acc.findIndex(
          (item) => item.lineId === lineId && item.textureId === textureId
        );
        if (index === -1) {
          index = acc.length;
        }
        let obj;
        if (type === "pic") {
          obj = {
            lineId,
            textureId: undefined,
            start: undefined,
            end: undefined,
            type: "pic" as const,
          };
        } else {
          obj = acc[index] ?? {
            lineId,
            textureId: textureId as string,
            start: charId as string,
            end: charId as string,
            type: "text" as const,
          };
          if (Number(charId) > Number(obj.end)) {
            obj.end = charId as string;
          }
        }
        acc[index] = obj;
        return acc;
      },
      [] as (
        | {
            lineId: string;
            textureId: string;
            start: string;
            end: string;
            type: "text";
          }
        | {
            lineId: string;
            textureId: undefined;
            start: undefined;
            end: undefined;
            type: "pic";
          }
      )[]
    );
  }, [nodes]);

  useEffect(() => {
    const updateRange = () => {
      const selection = window.getSelection();
      const range =
        selection && !selection.isCollapsed && selection.rangeCount > 0
          ? selection.getRangeAt(0)
          : null;
      setRange(range);
    };

    // PC端
    document.addEventListener("selectionchange", updateRange);
    // 移动端
    document.addEventListener("touchend", updateRange);
    document.addEventListener("mouseup", updateRange);

    return () => {
      document.removeEventListener("selectionchange", updateRange);
      document.removeEventListener("touchend", updateRange);
      document.removeEventListener("mouseup", updateRange);
    };
  }, []);

  return { ranges: result, pos };
};
