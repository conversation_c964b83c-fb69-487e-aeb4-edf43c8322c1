import { Card, Space } from 'antd';
import type React from 'react';
import { memo, useMemo } from 'react';
import QuestionAnswerAnalysisDetail from './QuestionAnswerAnalysisDetail';
import QuestionInfo from './QuestionInfo';

interface QuestionItemProps {
  question: API.QuestionItemType;
  children?: React.ReactNode | null;
  childrenContainerStyle?: React.CSSProperties;
  footerExtraBtns?: React.ReactNode | null;
}

const QuestionItem: React.FC<QuestionItemProps> = ({
  question,
  children,
  childrenContainerStyle,
  footerExtraBtns,
}) => {
  const {
    questionExplanation,
    questionAnswer,
    questionAnswerPreview,
    questionDifficult,
    questionId,
    extractQuestionAnswers,
    labelQuestionTypeNameZh,
  } = useMemo(() => question || ({} as any), [question]);

  if (!question) {
    return null;
  }

  return (
    <Card
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%', overflow: 'hidden' }}>
        <QuestionInfo question={question}>
          <div style={childrenContainerStyle}>{children}</div>
        </QuestionInfo>

        {(questionExplanation || questionAnswerPreview) && (
          <QuestionAnswerAnalysisDetail
            questionTypeName={labelQuestionTypeNameZh}
            questionExplanation={questionExplanation}
            questionAnswer={questionAnswerPreview}
            questionDifficult={questionDifficult}
            questionId={questionId}
            extractQuestionAnswers={extractQuestionAnswers || []}
            footerExtraBtns={footerExtraBtns}
          />
        )}
      </Space>
    </Card>
  );
};

export default memo(QuestionItem);
