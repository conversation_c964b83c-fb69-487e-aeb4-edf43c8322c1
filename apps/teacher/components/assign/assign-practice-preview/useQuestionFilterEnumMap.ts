import { questionFilterExtraOptions } from "@/configs";
import { getQuestionFilterEnum } from "@/services/assign-homework";
import { QuestionFilterEnumDatasMap } from "@/types/assign";
import { convertQuestionFilterEnumToMap } from "@/utils/assign/assign";
import { useRequest } from "ahooks";

export default function useQuestionFilterEnumMap() {
    const { data: questionFilterEnumMap } = useRequest<QuestionFilterEnumDatasMap, void[]>(async () => {
        try {
            const res = await getQuestionFilterEnum()
            const result = res;
            return convertQuestionFilterEnumToMap({
                ...questionFilterExtraOptions,
                ...result,
            })
        } catch (e) {
            return {} as QuestionFilterEnumDatasMap
        }
    }, {
        cacheKey: 'question-filter-enum-map',
        staleTime: 1000 * 60 * 60 * 1, // 1 hour
    })


    return {
        questionFilterEnumMap
    };
}