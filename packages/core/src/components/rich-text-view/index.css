/* RichTextView 组件样式 */
.rich-text-view {
  line-height: 2.5;
  white-space: pre-wrap;
}
.rich-text-view p {
  margin: 0;
}
.rich-text-view img {
  display: initial;
  vertical-align: initial;
}
/* 点强调样式 */
.rich-text-view .dot {
  -webkit-text-emphasis: dot;
  text-emphasis: dot;
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under left;
}
/* 波浪下划线样式 */
.rich-text-view .wavy {
  text-decoration: underline wavy;
}
/* 下划线样式 */
.rich-text-view u {
  white-space: pre-wrap !important;
}
.rich-text-view [data-tiptype='question-blank_filling'] {
  position: relative;
  display: inline-block;
  width: 60px;
  min-height: 1.5em;
  margin: 0 4px;
  line-height: 1.5;
  text-align: center;
  vertical-align: text-bottom;
  border-bottom: 1px solid #333;
}
.rich-text-view [data-tiptype='question-blank_filling']::after {
  position: absolute;
  right: 0;
  bottom: 2px;
  left: 0;
  display: inline-block;
  line-height: 1;
  content: attr(data-index);
}
/* 标题样式 */
.rich-text-view h1,
.rich-text-view h2,
.rich-text-view h3,
.rich-text-view h4,
.rich-text-view h5,
.rich-text-view h6 {
  margin: 5px 0 !important;
  font-weight: 500;
  line-height: 1.1;
  text-wrap: pretty;
}
.rich-text-view h1 {
  font-size: 1.4rem;
}
.rich-text-view h2 {
  font-size: 1.2rem;
}
.rich-text-view h3 {
  font-size: 1.1rem;
}
.rich-text-view h4,
.rich-text-view h5,
.rich-text-view h6 {
  font-size: 1rem;
}
/* 自定义样式 */
.rich-text-view .solid {
  border-top: 1px solid black !important;
}
.rich-text-view .dashed {
  border-top: 1px dashed black !important;
}
.rich-text-view table {
  width: 100%;
  border-collapse: collapse;
}
.rich-text-view th,
.rich-text-view td {
  padding: 2px 8px;
  border: 1px solid #ddd;
}
.rich-text-view th {
  text-align: left;
  background-color: #f2f2f2;
}
 /* 数学公式和普通文字的横向间距 */
.rich-text-view .katex {
  padding-left: 4px;
  padding-right: 4px;
}
/* 数学公式上下外边距，防止换行时两行公式挤在一起 */
.rich-text-view .katex .base {
  margin-top: 4px;
  margin-bottom: 4px;
  /* 注意: 这里不能使用 `white-space: normal;`, `y = \sqrt[3]{x^{3}}` 这样的公式会异常 */
  /* white-space: normal; */
  /* width: auto; */
}

.rich-text-view .katex .base .formula-space-auto-break {
  white-space: normal;
  width: auto;
}

.rich-text-view .katex .base .formula-space-nowrap {
  white-space: nowrap;
  width: fit-content;
}
