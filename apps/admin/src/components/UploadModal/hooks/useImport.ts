import { request } from '@umijs/max';
import { message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { ImportResult, QueryStatusResponse, UseImportParams, UseImportReturn } from '../types';

interface ResultData {
  total: number;
  success: number;
  failed: number;
  failed_items?: any[];
  download_url?: string;
}
// 轮询间隔
const POLLING_INTERVAL = 5000;
const useImport = ({
  fileId,
  importData,
  onSuccess,
  needPolling = true,
}: UseImportParams): UseImportReturn => {
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult>({
    status: 'idle',
  });
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const pollingCountRef = useRef(0);
  const MAX_POLLING_COUNT = 100; // 最大轮询次数，防止无限轮询

  // 清理定时器
  const clearPollingTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    pollingCountRef.current = 0;
  };

  // 解析 resulText 字段
  const parseResultText = (resulText: string): ResultData | null => {
    try {
      if (!resulText) return null;
      return JSON.parse(resulText) as ResultData;
    } catch (e) {
      console.error('解析 resulText 失败:', e);
      return null;
    }
  };

  // 使用 request 实现轮询
  const startPolling = () => {
    if (!importing || !fileId) return;

    const doPolling = async () => {
      try {
        pollingCountRef.current += 1;
        if (pollingCountRef.current > MAX_POLLING_COUNT) {
          clearPollingTimer();
          setImporting(false);
          setImportResult((prev) => ({
            ...prev,
            status: 'failed',
          }));
          message.error('导入超时，请重试');
          return;
        }

        const data = await request<QueryStatusResponse>(`/api/v1/loopQuery?uniqueKey=${fileId}`);
        // console.log(`loopQuery data`, data);

        if (data?.data) {
          const taskStatus = data.data.taskStatus;
          const parsedResult = parseResultText(data.data.resulText);

          // 更新导入结果状态
          setImportResult({
            status: taskStatus === 3 ? 'success' : taskStatus === 4 ? 'failed' : 'processing',
            taskId: fileId || undefined,
            total: parsedResult?.total || 0,
            success: parsedResult?.success || 0,
            failed: parsedResult?.failed || 0,
            downloadUrl: parsedResult?.download_url,
            failedItems: parsedResult?.failed_items,
          });

          // 当 taskStatus 为 3(成功) 或 4(失败) 时停止轮询，但不关闭弹窗
          if (taskStatus === 3 || taskStatus === 4) {
            clearPollingTimer();
            setImporting(false);

            const failed = parsedResult?.failed || 0;
            let closeModal = false;
            if (taskStatus === 3 && failed === 0) {
              message.success('导入成功');
              closeModal = true;
            }
            if (taskStatus === 3 && failed > 0) {
              message.warning(
                `导入操作成功，但有 ${failed} 条数据导入失败，请下载失败原因文件查看`,
              );
            }
            if (taskStatus === 4) {
              message.error('导入失败');
            }
            onSuccess?.({ ...data.data, keepOpen: true, closeModal });
          } else {
            // 继续轮询
            timerRef.current = setTimeout(doPolling, POLLING_INTERVAL);
          }
        } else {
          // 无数据，继续轮询
          timerRef.current = setTimeout(doPolling, POLLING_INTERVAL);
        }
      } catch (error) {
        console.error('轮询查询错误:', error);
        // 出错后仍继续轮询，但增加轮询间隔
        timerRef.current = setTimeout(doPolling, POLLING_INTERVAL);
      }
    };

    // 立即开始第一次轮询
    doPolling();
  };

  // 导入文件改变，重置结果状态
  useEffect(() => {
    setImportResult({
      status: 'idle',
    });
  }, [fileId]);

  // 监视 importing 和 fileId 变化，管理轮询状态
  useEffect(() => {
    // console.log('useImport ==> ', importing, fileId);
    if (importing && fileId && needPolling) {
      startPolling();
    } else {
      clearPollingTimer();
    }

    return () => {
      clearPollingTimer();
    };
  }, [importing, fileId, needPolling]);

  // 开始导入
  const handleImport = async () => {
    if (!fileId) {
      message.error('请先上传文件');
      return;
    }

    // 如果上次导入结果为成功或失败，则提示更换文件
    if (importResult.status === 'success' || importResult.status === 'failed') {
      message.error('请更换文件，再进行导入');
      return;
    }

    setImporting(true);
    setImportResult({
      status: 'processing',
      taskId: fileId,
    });

    try {
      const result = await importData(fileId);
      console.log('importDataresult', result);

      if (!result.success) {
        // 如果导入调用本身失败，立即停止轮询
        setImporting(false);
        setImportResult({
          status: 'failed',
          taskId: fileId,
        });
        message.error(result.error || '导入失败');
      }
      // 如果成功，轮询状态更新会在 useEffect 中自动开始
      if (!needPolling) {
        setImporting(false);
        onSuccess?.({ ...result, keepOpen: true, closeModal: true });
      }
    } catch (error) {
      // 如果发生异常，立即停止轮询
      setImporting(false);
      setImportResult({
        status: 'failed',
        taskId: fileId,
      });
      message.error('导入失败');
      console.error('导入失败:', error);
    }
  };

  // 下载失败原因
  const handleDownloadFailReasons = async () => {
    if (!importResult.taskId) return;

    try {
      // 如果有下载链接，直接使用
      if (importResult.downloadUrl) {
        window.open(importResult.downloadUrl, '_blank');
        return;
      }
    } catch (error) {
      message.error('下载失败');
      console.error('下载失败:', error);
    }
  };

  // 重置状态
  const resetImportState = () => {
    clearPollingTimer();
    setImporting(false);
    setImportResult({ status: 'idle' });
  };

  return {
    importing,
    importResult,
    handleImport,
    handleDownloadFailReasons,
    resetImportState,
  };
};

export default useImport;
