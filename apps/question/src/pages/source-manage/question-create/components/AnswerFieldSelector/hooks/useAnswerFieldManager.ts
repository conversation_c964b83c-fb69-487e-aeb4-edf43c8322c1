import { Form } from 'antd';
import lodash from 'lodash';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useQuestionCreate } from '../../../contexts/QuestionCreateContext';
import { compareUuidArrays, getQuestionStemFillPointUuids } from '../../../utils/questionBlank';
import type { FormListOperations } from '../types';

export interface UseAnswerFieldManagerOptions {
  /** 是否启用匹配线模式（m选n模式） */
  isMatchLine: boolean;
  /** 当前活跃的插入点索引 */
  activeFillIdx: number;
  /** 题干内容 */
  questionStem: string;
  /** 表单字段名称 */
  fieldName: string | (string | number)[];
  /** 表单字段路径 */
  questionOptionMatrixName: string | (string | number)[];
}

/**
 * 答案字段管理 Hook
 * 用于处理答案字段的添加、删除、切换同步等逻辑
 */
export const useAnswerFieldManager = ({
  isMatchLine,
  // activeFillIdx,
  questionStem,
  fieldName,
  questionOptionMatrixName,
}: UseAnswerFieldManagerOptions) => {
  const { form, activeIndex } = useQuestionCreate();
  // Form.List 操作方法的引用
  const formListRef = useRef<FormListOperations | null>(null);

  // 保存上一次的UUID数组，用于精确对比
  const prevUuidsRef = useRef<string[]>([]);

  // // 保存上一次的活跃索引，用于切换同步
  // const prevActiveFillIdxRef = useRef(activeFillIdx);

  // 保存上一次的题目标签
  const prevActiveIndexRef = useRef(activeIndex);

  /**
   * 从题干中提取UUID列表
   */
  const uuids = useMemo(() => {
    return getQuestionStemFillPointUuids(questionStem);
  }, [questionStem]);

  /**
   * 使用 useMemo 优化字段路径计算，避免 useWatch 动态路径警告
   */
  const allAnswerFieldPath = useMemo(() => {
    return questionOptionMatrixName;
  }, [questionOptionMatrixName]);

  const firstAnswerFieldPath = useMemo(() => {
    return Array.isArray(questionOptionMatrixName)
      ? [...questionOptionMatrixName, 0]
      : [questionOptionMatrixName, 0];
  }, [questionOptionMatrixName]);

  /**
   * 监听所有组答案的变化
   */
  const allAnswerValues = Form.useWatch(allAnswerFieldPath, form);

  /**
   * 监听第一组答案的变化，用于新增字段时的默认值
   */
  const firstAnswerValue = Form.useWatch(firstAnswerFieldPath, form);

  /**
   * 检查值是否为空
   */
  const isEmpty = useCallback((value: any): boolean => {
    if (lodash.isEmpty(value)) return true;

    // 对于对象，检查所有值是否都为空
    if (lodash.isPlainObject(value)) {
      return lodash.every(value, (val) => {
        if (lodash.isString(val)) return lodash.isEmpty(val.trim());
        return lodash.isEmpty(val);
      });
    }

    return false;
  }, []);

  /**
   * 检查值是否有内容
   */
  const hasContent = useCallback((value: any): boolean => {
    if (!lodash.isPlainObject(value)) return false;

    return lodash.some(value, (val) => {
      if (lodash.isString(val)) return !lodash.isEmpty(val.trim());
      return !lodash.isEmpty(val);
    });
  }, []);

  /**
   * 清除表单错误
   */
  const clearFormErrors = useCallback(() => {
    setTimeout(() => {
      if (!form || !formListRef.current) return;

      const fieldNameArray = Array.isArray(fieldName) ? fieldName : [fieldName];
      form.setFields([
        {
          name: fieldNameArray,
          errors: [],
        },
      ]);
    }, 0);
  }, [form, fieldName]);

  /**
   * 设置Form.List操作方法的引用
   */
  const setFormListRef = useCallback((operations: FormListOperations) => {
    formListRef.current = operations;
  }, []);

  // /**
  //  * 处理插入点切换时的同步逻辑
  //  */
  // useEffect(() => {
  //   // 如果不是匹配模式，不处理切换同步
  //   if (
  //     !isMatchLine ||
  //     !formListRef.current ||
  //     !allAnswerValues ||
  //     !Array.isArray(allAnswerValues)
  //   ) {
  //     prevActiveFillIdxRef.current = activeFillIdx;
  //     return;
  //   }

  //   // 如果是首次初始化，不处理
  //   if (prevActiveFillIdxRef.current === activeFillIdx) {
  //     return;
  //   }
  //   console.log('activeFillIdx3', activeFillIdx);
  //   const prevIndex = prevActiveFillIdxRef.current;
  //   const currentIndex = activeFillIdx;

  //   // 检查之前编辑的插入点是否有内容
  //   if (prevIndex < allAnswerValues.length && allAnswerValues[prevIndex]) {
  //     const prevValue = allAnswerValues[prevIndex];

  //     if (hasContent(prevValue) && currentIndex < allAnswerValues.length) {
  //       const currentValue = allAnswerValues[currentIndex];

  //       // 如果当前插入点为空，将之前插入点的内容同步过来
  //       if (isEmpty(currentValue)) {
  //         const currentFieldPath = Array.isArray(questionOptionMatrixName)
  //           ? [...questionOptionMatrixName, currentIndex]
  //           : [questionOptionMatrixName, currentIndex];

  //         // 延迟执行，避免与输入冲突
  //         requestAnimationFrame(() => {
  //           form.setFieldValue(currentFieldPath, lodash.cloneDeep(prevValue));
  //         });
  //       }
  //     }
  //   }

  //   // 更新上一次的索引
  //   prevActiveFillIdxRef.current = activeFillIdx;
  // }, [
  //   activeFillIdx,
  //   isMatchLine,
  //   allAnswerValues,
  //   questionOptionMatrixName,
  //   form,
  //   hasContent,
  //   isEmpty,
  // ]);

  /**
   * 根据插入点变化动态管理字段
   */
  useEffect(() => {
    if (
      !formListRef.current
      // || uuids.filter((item) => item).length === 0
      //  || prevUuidsRef.current.filter((item) => item).length === 0
    ) {
      prevUuidsRef.current = [];
      return;
    }

    // 如果是首次初始化，不处理
    if (prevActiveIndexRef.current !== activeIndex) {
      prevActiveIndexRef.current = activeIndex;
      prevUuidsRef.current = uuids;
      return;
    }

    const { fields, add, remove } = formListRef.current;
    // 如果是初始化，只更新 prevUuidsRef 并确保至少有一个字段
    if (prevUuidsRef.current.length === 0) {
      prevUuidsRef.current = uuids;
      if (fields.length === 0) {
        add({});
      }
      return;
    }

    // 对比UUID数组的变化，精确添加和删除字段
    const { added, removed } = compareUuidArrays(uuids, prevUuidsRef.current);

    // 添加新字段（在指定位置插入）
    if (added.length > 0) {
      added.forEach((item) => {
        const defaultValue =
          isMatchLine && firstAnswerValue ? lodash.cloneDeep(firstAnswerValue) : {};
        add(defaultValue, item.index);
      });
    }

    // 如果插入点为空，则不删除
    if (fields.length === 1 && uuids.length === 0) {
      return;
    }

    // 删除字段（从后往前删除，避免索引变化）
    if (removed.length > 0) {
      const sortedRemoved = removed.sort((a, b) => b.index - a.index);
      sortedRemoved.forEach((item) => {
        remove(item.index);
      });
    }

    // 更新 prevUuidsRef 以便下一次对比
    prevUuidsRef.current = uuids;

    // 重置表单错误提示
    clearFormErrors();
  }, [uuids, fieldName, form, isMatchLine, firstAnswerValue, clearFormErrors]);

  return {
    /**
     * UUID列表
     */
    uuids,
    /**
     * 所有答案值
     */
    allAnswerValues,
    /**
     * 第一组答案值
     */
    firstAnswerValue,
    /**
     * 设置Form.List操作方法的引用
     */
    setFormListRef,
    /**
     * 工具方法
     */
    utils: {
      isEmpty,
      hasContent,
      clearFormErrors,
    },
  };
};
