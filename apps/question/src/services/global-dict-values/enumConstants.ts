import { request } from '@umijs/max';

export async function queryEnumConstants(options?: { [key: string]: any }) {
  const res = await request<API.Response<API.EnumConstantData>>('/api/v1/enums/get/consts', {
    method: 'GET',
    ...(options || {}),
  });

  const colorMap = {
    1: '#52c41a',
    2: '#55baf2',
    3: '#1677ff',
    4: '#ff7a45',
    5: '#f5222d',
  };

  res.data.questionDifficultList = res.data.questionDifficultList.map((item) => ({
    ...item,
    color: colorMap[item.value],
  }));

  const statusMap = {
    1: 'Processing',
    2: 'Success',
    3: 'Error',
    4: 'Default',
  };

  res.data.auditStatusList = res.data.auditStatusList.map((item) => ({
    ...item,
    status: statusMap[item.value],
  }));

  return res;
}
