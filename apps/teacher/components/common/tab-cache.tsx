"use client";

import { useEffect, useState } from "@preact-signals/safe-react/react";
import { ReactNode } from "react";

interface TabCacheProps {
  tabs: Record<string | symbol, ReactNode>;
  activeTab: string;
}

/**
 * TabCache组件用于维护所有标签页组件的状态
 * 只显示当前激活的标签页,同时将其他标签页保持在DOM中但隐藏
 */
export default function TabCache({ activeTab, tabs }: TabCacheProps) {
  // 跟踪哪些标签页至少渲染过一次
  const [renderedTabs, setRenderedTabs] = useState<Set<string | symbol>>(
    new Set([activeTab])
  );

  // 将当前激活的标签页添加到已渲染标签页集合中
  useEffect(() => {
    // 将新标签页添加到已渲染集合
    if (!renderedTabs.has(activeTab)) {
      setRenderedTabs((prev) => new Set([...prev, activeTab]));
    }
  }, [activeTab, renderedTabs]);

  return (
    <div className="relative h-full">
      {Object.entries(tabs).map(([tab, component]) => {
        const tabKey = tab as string;
        // 只渲染至少查看过一次的标签页
        // if (!renderedTabs.has(tabKey)) {
        //   return null;
        // }

        const isActive = activeTab === tabKey;

        return (
          <div
            key={tab}
            className={`absolute inset-0 h-full w-full transition-opacity duration-200 ${
              isActive
                ? "z-10 opacity-100"
                : "pointer-events-none z-0 opacity-0"
            }`}
            aria-hidden={!isActive}
            data-tab-content={tab}
          >
            {component}
          </div>
        );
      })}
    </div>
  );
}
