export const treeMockData = {
  id: 1,
  name: "高中数学基础树",
  parentId: 0,
  baseTreeNodeSiblingOrder: 0,
  baseTreeNodeLevel: 0,
  children: [
    {
      id: 2,
      name: "预备知识",
      parentId: 1,
      baseTreeNodeSiblingOrder: 0,
      baseTreeNodeLevel: 1,
      children: [
        {
          id: 3,
          name: "集合",
          parentId: 2,
          baseTreeNodeSiblingOrder: 0,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 4,
              name: "集合的概念与表示",
              parentId: 3,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 5,
                  name: "集合的含义",
                  parentId: 4,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 6,
                      name: "判断自然语言描述内容能否组成集合",
                      parentId: 5,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 7,
                      name: "常用数集及其记法",
                      parentId: 5,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 8,
                      name: "集合的确定性、互异性、无序性",
                      parentId: 5,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 9,
                  name: "集合的表示法",
                  parentId: 4,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 10,
                      name: "列举法表示集合",
                      parentId: 9,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 11,
                      name: "描述法表示集合",
                      parentId: 9,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 12,
                      name: "区间",
                      parentId: 9,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 13,
                  name: "元素与集合关系的判断",
                  parentId: 4,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 14,
                      name: "判断元素与集合的属于关系",
                      parentId: 13,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 15,
                      name: "元素与集合的属于关系的应用",
                      parentId: 13,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 16,
              name: "集合的基本关系",
              parentId: 3,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 17,
                  name: "集合的相等",
                  parentId: 16,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 18,
                      name: "判断两个集合是否相同",
                      parentId: 17,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 19,
                      name: "两个集合相等的应用",
                      parentId: 17,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 20,
                  name: "集合的包含关系判断及应用",
                  parentId: 16,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 21,
                      name: "判断两个集合的包含关系",
                      parentId: 20,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 22,
                      name: "Venn图表集合的包含关系",
                      parentId: 20,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 23,
                      name: "集合的包含关系的应用",
                      parentId: 20,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 24,
                      name: "集合中元素个数的最值",
                      parentId: 20,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 25,
                  name: "子集与真子集",
                  parentId: 16,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 26,
                      name: "子集的判断与求解",
                      parentId: 25,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 27,
                      name: "空集及空集的性质",
                      parentId: 25,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 28,
                      name: "子集的个数",
                      parentId: 25,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 29,
              name: "集合的基本运算",
              parentId: 3,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 30,
                  name: "并集及其运算",
                  parentId: 29,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 31,
                      name: "求集合的并集",
                      parentId: 30,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 32,
                      name: "Venn图表示并集",
                      parentId: 30,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 33,
                      name: "集合并集关系的应用",
                      parentId: 30,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 34,
                  name: "交集及其运算",
                  parentId: 29,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 35,
                      name: "求集合的交集",
                      parentId: 34,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 36,
                      name: "Venn图表示交集",
                      parentId: 34,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 37,
                      name: "集合交集关系的应用",
                      parentId: 34,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 38,
                  name: "补集及其运算",
                  parentId: 29,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 39,
                      name: "全集及其运算",
                      parentId: 38,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 40,
                      name: "求集合的补集",
                      parentId: 38,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 41,
                      name: "Venn图表示补集",
                      parentId: 38,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 42,
                      name: "集合补集关系的应用",
                      parentId: 38,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 43,
                  name: "交、并、补集的混合运算",
                  parentId: 29,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 44,
                      name: "集合的交并补混合运算",
                      parentId: 43,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 45,
                      name: "Venn图表示交并补混合运算",
                      parentId: 43,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 46,
                      name: "集合交并补混合关系的应用",
                      parentId: 43,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 47,
          name: "常用逻辑用语",
          parentId: 2,
          baseTreeNodeSiblingOrder: 1,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 48,
              name: "充分条件与必要条件",
              parentId: 47,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 49,
                  name: "充分条件必要条件的判断",
                  parentId: 48,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 50,
                      name: "充分条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 51,
                      name: "必要条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 52,
                      name: "充分不必要条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 53,
                      name: "必要不充分条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 54,
                      name: "充要条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 55,
                      name: "既不充分也不必要条件的判断",
                      parentId: 49,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 56,
                  name: "充分条件必要条件的应用",
                  parentId: 48,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 57,
                      name: "充分条件的应用与判定定理",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 58,
                      name: "必要条件的应用与性质定理",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 59,
                      name: "充分不必要条件的应用",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 60,
                      name: "必要不充分条件的应用",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 61,
                      name: "充要条件的应用",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 62,
                      name: "既不充分也不必要条件的应用",
                      parentId: 56,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 63,
              name: "全称量词与存在量词",
              parentId: 47,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 64,
                  name: "全称量词和全称量词命题",
                  parentId: 63,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 65,
                      name: "全称量词命题及其改写",
                      parentId: 64,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 66,
                      name: "全称量词命题的真假判断",
                      parentId: 64,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 67,
                      name: "全称量词命题真假的应用",
                      parentId: 64,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 68,
                  name: "存在量词和存在量词命题",
                  parentId: 63,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 69,
                      name: "存在量词命题及其改写",
                      parentId: 68,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 70,
                      name: "存在量词命题的真假判断",
                      parentId: 68,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 71,
                      name: "存在量词命题真假的应用",
                      parentId: 68,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 72,
              name: "命题的否定",
              parentId: 47,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 73,
                  name: "全称量词命题的否定",
                  parentId: 72,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 74,
                      name: "求全称量词命题的否定",
                      parentId: 73,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 75,
                      name: "全称量词命题否定的真假判断",
                      parentId: 73,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 76,
                      name: "全称量词命题否定真假的应用",
                      parentId: 73,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 77,
                  name: "存在量词命题的否定",
                  parentId: 72,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 78,
                      name: "求存在量词命题的否定",
                      parentId: 77,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 79,
                      name: "存在量词命题否定的真假判断",
                      parentId: 77,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 80,
                      name: "存在量词命题否定真假的应用",
                      parentId: 77,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 81,
              name: "其他命题",
              parentId: 47,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 82,
                  name: "四种命题",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 83,
                  name: "四种命题间的逆否关系",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 84,
                  name: "四种命题的真假关系",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 85,
                  name: "逻辑联结词“或”、“且”、“非”",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 86,
                  name: "复合命题及其真假",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 87,
                  name: "命题的真假判断与应用",
                  parentId: 81,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 88,
          name: "相等关系与不等关系",
          parentId: 2,
          baseTreeNodeSiblingOrder: 2,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 89,
              name: "等式与不等式的性质",
              parentId: 88,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 90,
                  name: "等式与不等式的性质",
                  parentId: 89,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 91,
                  name: "不等关系与不等式",
                  parentId: 89,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 92,
                  name: "不等式比较大小",
                  parentId: 89,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 93,
              name: "基本不等式及其应用",
              parentId: 88,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 94,
                  name: "运用基本不等式比较大小",
                  parentId: 93,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 95,
                  name: "运用基本不等式求最值",
                  parentId: 93,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 96,
                  name: "运用“1”的代换构造基本不等式",
                  parentId: 93,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 97,
                  name: "运用基本不等式解决实际问题",
                  parentId: 93,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 98,
              name: "其他不等式",
              parentId: 88,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 99,
                  name: "分式不等式",
                  parentId: 98,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 100,
                  name: "指、对数不等式的解法",
                  parentId: 98,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 101,
                  name: "不等式的综合",
                  parentId: 98,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 102,
                  name: "其他不等式的解法",
                  parentId: 98,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 103,
          name: "一、二次函数及方程、不等式",
          parentId: 2,
          baseTreeNodeSiblingOrder: 3,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 104,
              name: "一元二次函数与方程、不等式",
              parentId: 103,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 105,
                  name: "二次函数的性质与图象",
                  parentId: 104,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 106,
                      name: "二次函数的定义域",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 107,
                      name: "二次函数的值域",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 108,
                      name: "二次函数的单调性与单调区间",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 109,
                      name: "二次函数的最值",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 110,
                      name: "二次函数的图象及其对称性",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 111,
                      name: "由二次函数的性质求解析式或参数",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 112,
                      name: "二次函数的应用",
                      parentId: 105,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 113,
                  name: "一元二次不等式及其应用",
                  parentId: 104,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 114,
                      name: "解一元二次不等式",
                      parentId: 113,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 115,
                      name: "由一元二次不等式的解求参数",
                      parentId: 113,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 116,
                      name: "一元二次不等式恒成立问题",
                      parentId: 113,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 117,
                  name: "一元二次方程的根的分布与系数的关系",
                  parentId: 104,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 118,
              name: "一次函数与方程、不等式",
              parentId: 103,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 119,
                  name: "一次函数的性质与图象",
                  parentId: 118,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 120,
                  name: "二元一次不等式组",
                  parentId: 118,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 121,
                  name: "二元一次不等式的几何意义",
                  parentId: 118,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 122,
                  name: "二元一次不等式（组）与平面区域",
                  parentId: 118,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 123,
                  name: "简单线性规划",
                  parentId: 118,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 124,
      name: "函数",
      parentId: 1,
      baseTreeNodeSiblingOrder: 1,
      baseTreeNodeLevel: 1,
      children: [
        {
          id: 125,
          name: "函数概念与性质",
          parentId: 124,
          baseTreeNodeSiblingOrder: 0,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 126,
              name: "函数概念及其表示",
              parentId: 125,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 127,
                  name: "函数的概念及其构成要素",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 128,
                  name: "判断两个函数是否为同一函数",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 129,
                  name: "函数的定义域及其求法",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 130,
                      name: "简单函数的定义域",
                      parentId: 129,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 131,
                      name: "复合函数的定义域",
                      parentId: 129,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 132,
                      name: "抽象函数的定义域",
                      parentId: 129,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 133,
                      name: "由定义域求解函数或参数",
                      parentId: 129,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 134,
                  name: "函数的值域",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 135,
                      name: "简单函数的值域",
                      parentId: 134,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 136,
                      name: "复合函数的值域",
                      parentId: 134,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 137,
                      name: "抽象函数的值域",
                      parentId: 134,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 138,
                      name: "由值域求解函数或参数",
                      parentId: 134,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 139,
                  name: "函数解析式的求解及常用方法",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 140,
                  name: "函数的表示方法",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 141,
                      name: "解析法表示函数",
                      parentId: 140,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 142,
                      name: "列表法表示函数",
                      parentId: 140,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 143,
                      name: "图象法表示函数",
                      parentId: 140,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 144,
                  name: "函数的图象与图象的变换",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 145,
                      name: "画出函数的图象",
                      parentId: 144,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 146,
                      name: "由函数解析式求解函数图象",
                      parentId: 144,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 147,
                      name: "由函数图象求解函数或参数",
                      parentId: 144,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 148,
                      name: "函数图象的简单变换",
                      parentId: 144,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 149,
                  name: "分段函数的解析式求法及其图象的作法",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 150,
                  name: "映射",
                  parentId: 126,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 151,
              name: "函数性质",
              parentId: 125,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 152,
                  name: "函数的单调性",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 153,
                      name: "函数的单调性与函数图象的特征",
                      parentId: 152,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 154,
                      name: "定义法求解函数的单调性",
                      parentId: 152,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 155,
                      name: "求函数的单调区间",
                      parentId: 152,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 156,
                      name: "由函数的单调性求解函数或参数",
                      parentId: 152,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 157,
                      name: "复合函数的单调性",
                      parentId: 152,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 158,
                  name: "函数的最值",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 159,
                      name: "函数的最值与函数图象的特征",
                      parentId: 158,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 160,
                      name: "求函数的最值",
                      parentId: 158,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 161,
                      name: "由函数的最值求解函数或参数",
                      parentId: 158,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 162,
                      name: "复合函数的最值",
                      parentId: 158,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 163,
                  name: "函数的奇偶性",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 164,
                      name: "奇函数偶函数的判断",
                      parentId: 163,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 165,
                      name: "奇函数偶函数的性质",
                      parentId: 163,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 166,
                      name: "奇偶函数图象的对称性",
                      parentId: 163,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 167,
                      name: "奇偶性与单调性的综合",
                      parentId: 163,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 168,
                      name: "抽象函数的奇偶性",
                      parentId: 163,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 169,
                  name: "函数的周期性",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 170,
                      name: "函数周期性的判断与求解",
                      parentId: 169,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 171,
                      name: "由函数的周期性求解函数或参数",
                      parentId: 169,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 172,
                      name: "抽象函数的周期性",
                      parentId: 169,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 173,
                      name: "类周期函数",
                      parentId: 169,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 174,
                  name: "函数的连续性",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 175,
                  name: "函数恒成立问题",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 176,
                  name: "函数的值",
                  parentId: 151,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 177,
          name: "幂函数、指数函数、对数函数",
          parentId: 124,
          baseTreeNodeSiblingOrder: 1,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 178,
              name: "幂函数",
              parentId: 177,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 179,
                  name: "幂函数的概念",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 180,
                      name: "幂函数的特征及辨识",
                      parentId: 179,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 181,
                      name: "求幂函数的解析式",
                      parentId: 179,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 182,
                      name: "由幂函数的解析式求解参数",
                      parentId: 179,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 183,
                  name: "幂函数的定义域",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 184,
                      name: "求幂函数的定义域",
                      parentId: 183,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 185,
                      name: "幂函数型复合函数的定义域",
                      parentId: 183,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 186,
                  name: "幂函数的值域",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 187,
                      name: "求幂函数的值域",
                      parentId: 186,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 188,
                      name: "幂函数型复合函数的值域",
                      parentId: 186,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 189,
                  name: "幂函数的图象",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 190,
                      name: "幂函数图象特征与幂指数的关系",
                      parentId: 189,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 191,
                      name: "幂函数及幂函数型复合函数图象过定点",
                      parentId: 189,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 192,
                  name: "幂函数的单调性与最值",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 193,
                      name: "求幂函数及幂函数型复合函数的单调性",
                      parentId: 192,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 194,
                      name: "由幂函数的单调性求解参数",
                      parentId: 192,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 195,
                      name: "求幂函数及幂函数型复合函数的最值",
                      parentId: 192,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 196,
                      name: "由幂函数的最值求解参数",
                      parentId: 192,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 197,
                  name: "幂函数的奇偶性",
                  parentId: 178,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 198,
                      name: "求解幂函数的奇偶性",
                      parentId: 197,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 199,
                      name: "幂函数的奇偶性与函数图象的对称性",
                      parentId: 197,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 200,
              name: "指数函数",
              parentId: 177,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 201,
                  name: "有理数指数幂及根式",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 202,
                      name: "有理数指数幂与根式的互化",
                      parentId: 201,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 203,
                      name: "有理数指数幂及根式化简运算求值",
                      parentId: 201,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 204,
                  name: "指数函数的概念",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 205,
                      name: "指数函数的特征及解析式",
                      parentId: 204,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 206,
                      name: "由指数函数的解析式求解参数",
                      parentId: 204,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 207,
                  name: "指数函数的定义域",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 208,
                  name: "指数函数的值域",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 209,
                  name: "指数函数的图象",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 210,
                      name: "指数函数图象特征与底数的关系",
                      parentId: 209,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 211,
                      name: "指数函数及指数型复合函数的图象",
                      parentId: 209,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 212,
                  name: "指数函数的单调性与最值",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 213,
                      name: "求指数函数及指数型复合函数的单调性",
                      parentId: 212,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 214,
                      name: "由指数函数的单调性求解参数",
                      parentId: 212,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 215,
                      name: "求指数函数及指数型复合函数的最值",
                      parentId: 212,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 216,
                      name: "由指数函数的最值求解参数",
                      parentId: 212,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 217,
                  name: "指数函数的实际应用",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 218,
                  name: "指数函数综合题",
                  parentId: 200,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 219,
              name: "对数函数",
              parentId: 177,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 220,
                  name: "对数的概念",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 221,
                  name: "指数式与对数式的互化",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 222,
                  name: "对数的运算性质",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 223,
                      name: "对数运算求值",
                      parentId: 222,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 224,
                      name: "对数方程求解",
                      parentId: 222,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 225,
                      name: "换底公式的应用",
                      parentId: 222,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 226,
                  name: "对数函数的定义",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 227,
                  name: "对数函数的定义域",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 228,
                      name: "求对数函数的定义域",
                      parentId: 227,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 229,
                      name: "求对数型复合函数的定义域",
                      parentId: 227,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 230,
                  name: "对数函数的值域",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 231,
                      name: "求对数函数的值域",
                      parentId: 230,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 232,
                      name: "求对数型复合函数的值域",
                      parentId: 230,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 233,
                  name: "对数函数的图象",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 234,
                      name: "对数函数图象特征与底数的关系",
                      parentId: 233,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 235,
                      name: "对数函数及对数型复合函数的图象",
                      parentId: 233,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 236,
                  name: "对数函数的单调性与最值",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 237,
                      name: "求对数函数及对数型复合函数的单调性",
                      parentId: 236,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 238,
                      name: "由对数函数的单调性求解参数",
                      parentId: 236,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 239,
                      name: "求对数函数及对数型复合函数的最值",
                      parentId: 236,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 240,
                      name: "由对数函数的最值求解参数",
                      parentId: 236,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 241,
                      name: "对数值大小的比较",
                      parentId: 236,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 242,
                  name: "指数函数与对数函数的关系",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 243,
                  name: "反函数",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 244,
                  name: "对数函数图象与性质的综合应用",
                  parentId: 219,
                  baseTreeNodeSiblingOrder: 10,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 245,
          name: "三角函数",
          parentId: 124,
          baseTreeNodeSiblingOrder: 2,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 246,
              name: "角与弧度",
              parentId: 245,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 247,
                  name: "任意角的概念",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 248,
                  name: "终边相同的角",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 249,
                      name: "终边相同的角（角度制）",
                      parentId: 248,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 250,
                      name: "终边相同的角（弧度制）",
                      parentId: 248,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 251,
                  name: "象限角、轴线角",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 252,
                  name: "弧度制",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 253,
                  name: "弧长公式",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 254,
                  name: "扇形面积公式",
                  parentId: 246,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 255,
              name: "三角函数概念和性质",
              parentId: 245,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 256,
                  name: "三角函数的概念",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 257,
                      name: "任意角的三角函数的定义",
                      parentId: 256,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 258,
                      name: "三角函数线",
                      parentId: 256,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 259,
                  name: "三角函数的性质",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 260,
                      name: "三角函数的定义域",
                      parentId: 259,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 261,
                      name: "三角函数值的符号",
                      parentId: 259,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 262,
                      name: "单位圆与周期性",
                      parentId: 259,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 263,
                      name: "三角函数的周期性",
                      parentId: 259,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 264,
                  name: "诱导公式",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 265,
                      name: "诱导公式",
                      parentId: 264,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 266,
                      name: "运用诱导公式化简求值",
                      parentId: 264,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 267,
                  name: "正弦函数",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 268,
                      name: "正弦函数的图象",
                      parentId: 267,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 269,
                      name: "正弦函数的定义域和值域",
                      parentId: 267,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 270,
                      name: "正弦函数的单调性",
                      parentId: 267,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 271,
                      name: "正弦函数的奇偶性和对称性",
                      parentId: 267,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 272,
                  name: "余弦函数",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 273,
                      name: "余弦函数的图象",
                      parentId: 272,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 274,
                      name: "余弦函数的定义域和值域",
                      parentId: 272,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 275,
                      name: "余弦函数的单调性",
                      parentId: 272,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 276,
                      name: "余弦函数的对称性",
                      parentId: 272,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 277,
                  name: "正切函数",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 278,
                      name: "正切函数的图象",
                      parentId: 277,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 279,
                      name: "正切函数的定义域和值域",
                      parentId: 277,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 280,
                      name: "正切函数的单调性和周期性",
                      parentId: 277,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 281,
                      name: "正切函数的奇偶性与对称性",
                      parentId: 277,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 282,
                  name: "复合三角函数",
                  parentId: 255,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 283,
                      name: "五点法作函数y=Asin（ωx+φ）的图象",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 284,
                      name: "函数y=Asin（ωx+φ）的图象变换",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 285,
                      name: "由y=Asin（ωx+φ）的部分图象确定其解析式",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 286,
                      name: "y=Asin（ωx+φ）中参数的物理意义",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 287,
                      name: "复合三角函数的单调性",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 288,
                      name: "反三角函数",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 289,
                      name: "三角函数的最值",
                      parentId: 282,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 290,
              name: "同角三角函数的基本关系",
              parentId: 245,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 291,
                  name: "同角三角函数间的基本关系",
                  parentId: 290,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 292,
                      name: "同角正弦、余弦的平方和为1",
                      parentId: 291,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 293,
                      name: "同角正弦、余弦的商为正切",
                      parentId: 291,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 294,
                  name: "三角函数恒等式的证明",
                  parentId: 290,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 295,
              name: "三角恒等变换",
              parentId: 245,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 296,
                  name: "两角和与差的三角函数",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 297,
                      name: "求两角和与差的三角函数值",
                      parentId: 296,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 298,
                      name: "两角和与差的三角函数的逆用",
                      parentId: 296,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 299,
                  name: "二倍角的三角函数",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 300,
                      name: "求二倍角的三角函数值",
                      parentId: 299,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 301,
                      name: "二倍角的三角函数的逆用",
                      parentId: 299,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 302,
                  name: "半角的三角函数",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 303,
                  name: "三角函数的积化和差公式",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 304,
                  name: "三角函数的和差化积公式",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 305,
                  name: "三角函数的恒等变换及化简求值",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 306,
                  name: "三角函数中的恒等变换应用",
                  parentId: 295,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 307,
              name: "三角函数应用",
              parentId: 245,
              baseTreeNodeSiblingOrder: 4,
              baseTreeNodeLevel: 3,
              children: [],
            },
          ],
        },
        {
          id: 308,
          name: "函数应用",
          parentId: 124,
          baseTreeNodeSiblingOrder: 3,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 309,
              name: "二分法与求方程近似解",
              parentId: 308,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 310,
                  name: "函数的零点",
                  parentId: 309,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 311,
                      name: "求函数的零点",
                      parentId: 310,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 312,
                      name: "由函数的零点求解函数或参数",
                      parentId: 310,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 313,
                  name: "函数零点的判定定理",
                  parentId: 309,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 314,
                      name: "判定函数零点的存在性",
                      parentId: 313,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 315,
                      name: "求解函数零点所在区间",
                      parentId: 313,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 316,
                      name: "由函数零点所在区间求解函数或参数",
                      parentId: 313,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 317,
                  name: "函数的零点与方程根的关系",
                  parentId: 309,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 318,
                      name: "求解方程根的存在性和分布",
                      parentId: 317,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 319,
                      name: "由方程根的分布求解函数或参数",
                      parentId: 317,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 320,
                  name: "二分法的定义与应用",
                  parentId: 309,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 321,
                      name: "二分法及其适用条件",
                      parentId: 320,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 322,
                      name: "二分法求函数零点的近似值",
                      parentId: 320,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 323,
                      name: "二分法求方程的近似解",
                      parentId: 320,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 324,
                  name: "函数与方程的综合运用",
                  parentId: 309,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 325,
              name: "函数与数学模型",
              parentId: 308,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 326,
                  name: "函数最值的应用",
                  parentId: 325,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 327,
                  name: "分段函数的应用",
                  parentId: 325,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 328,
                  name: "根据实际问题选择函数类型",
                  parentId: 325,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 329,
                  name: "带绝对值的函数",
                  parentId: 325,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 330,
                  name: "对勾函数",
                  parentId: 325,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 331,
          name: "数列",
          parentId: 124,
          baseTreeNodeSiblingOrder: 4,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 332,
              name: "数列概念",
              parentId: 331,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 333,
                  name: "数列的概念及简单表示法",
                  parentId: 332,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 334,
                      name: "由数列若干项归纳出通项公式",
                      parentId: 333,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 335,
                      name: "由数列若干项求下一项或其中的项",
                      parentId: 333,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 336,
                      name: "由通项公式求解或判断数列中的项",
                      parentId: 333,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 337,
                      name: "由实际问题归纳出数列的通项",
                      parentId: 333,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 338,
                  name: "数列的函数特性",
                  parentId: 332,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 339,
                      name: "数列的单调性",
                      parentId: 338,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 340,
                      name: "数列的最大项最小项",
                      parentId: 338,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 341,
                      name: "数列的图象",
                      parentId: 338,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 342,
              name: "等差数列",
              parentId: 331,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 343,
                  name: "等差数列的性质",
                  parentId: 342,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 344,
                      name: "等差数列的概念与判定",
                      parentId: 343,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 345,
                      name: "等差中项及其性质",
                      parentId: 343,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 346,
                  name: "等差数列的通项公式",
                  parentId: 342,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 347,
                      name: "由等差数列中若干项求通项公式或其中的项",
                      parentId: 346,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 348,
                      name: "等差数列通项公式的应用",
                      parentId: 346,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 349,
                  name: "等差数列的前n项和",
                  parentId: 342,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 350,
                      name: "求等差数列的前n项和",
                      parentId: 349,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 351,
                      name: "由等差数列的前n项和求解数列",
                      parentId: 349,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 352,
                      name: "等差数列前n项和的性质",
                      parentId: 349,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 353,
              name: "等比数列",
              parentId: 331,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 354,
                  name: "等比数列的性质",
                  parentId: 353,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 355,
                      name: "等比数列的概念与判定",
                      parentId: 354,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 356,
                      name: "等比中项及其性质",
                      parentId: 354,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 357,
                  name: "等比数列的通项公式",
                  parentId: 353,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 358,
                      name: "由等比数列中若干项求通项公式或其中的项",
                      parentId: 357,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 359,
                      name: "等比数列通项公式的应用",
                      parentId: 357,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 360,
                  name: "等比数列的前n项和",
                  parentId: 353,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 361,
                      name: "求等比数列的前n项和",
                      parentId: 360,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 362,
                      name: "由等比数列的前n项和求解数列",
                      parentId: 360,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 363,
                      name: "等比数列前n项和的性质",
                      parentId: 360,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 364,
              name: "数列综合",
              parentId: 331,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 365,
                  name: "数列的应用",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 366,
                  name: "数列的求和",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 367,
                      name: "倒序相加法",
                      parentId: 366,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 368,
                      name: "错位相减法",
                      parentId: 366,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 369,
                      name: "裂项相消法",
                      parentId: 366,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 370,
                      name: "数列求和的其他方法",
                      parentId: 366,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 371,
                  name: "数列递推式",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 372,
                  name: "数列与函数的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 373,
                  name: "数列的极限",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 374,
                  name: "数列与不等式的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 375,
                  name: "数列与向量的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 376,
                  name: "等差数列与等比数列的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 377,
                  name: "数列与三角函数的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 378,
                  name: "数列与解析几何的综合",
                  parentId: 364,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 379,
              name: "数学归纳法",
              parentId: 331,
              baseTreeNodeSiblingOrder: 4,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 380,
                  name: "数学归纳法",
                  parentId: 379,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 381,
                      name: "数学归纳法的适用条件与步骤",
                      parentId: 380,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 382,
                      name: "数学归纳法证明命题",
                      parentId: 380,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 383,
                  name: "用数学归纳法证明不等式",
                  parentId: 379,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 384,
          name: "一元函数导数及其应用",
          parentId: 124,
          baseTreeNodeSiblingOrder: 5,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 385,
              name: "导数概念及其意义",
              parentId: 384,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 386,
                  name: "变化的快慢与变化率",
                  parentId: 385,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 387,
                      name: "平均变化率",
                      parentId: 386,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 388,
                      name: "瞬时变化率",
                      parentId: 386,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 389,
                  name: "导数及其几何意义",
                  parentId: 385,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 390,
                      name: "变化率的极限与导数的概念",
                      parentId: 389,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 391,
                      name: "含Δx表达式的极限计算与导数的关系",
                      parentId: 389,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 392,
                      name: "导数与切线的斜率",
                      parentId: 389,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 393,
                      name: "函数图象趋势与导数大小的关系",
                      parentId: 389,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 394,
                  name: "极限及其运算",
                  parentId: 385,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 395,
              name: "导数运算",
              parentId: 384,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 396,
                  name: "基本初等函数的导数",
                  parentId: 395,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 397,
                  name: "导数的加法与减法法则",
                  parentId: 395,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 398,
                  name: "导数的乘法与除法法则",
                  parentId: 395,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 399,
                  name: "简单复合函数的导数",
                  parentId: 395,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 400,
              name: "导数在研究函数中的应用",
              parentId: 384,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 401,
                  name: "利用导数研究函数的单调性",
                  parentId: 400,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 402,
                      name: "利用导数求解函数的单调性和单调区间",
                      parentId: 401,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 403,
                      name: "由函数的单调性求解函数或参数（导数法）",
                      parentId: 401,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 404,
                  name: "利用导数研究函数的极值",
                  parentId: 400,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 405,
                      name: "函数在某点取得极值的条件",
                      parentId: 404,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 406,
                      name: "利用导数求解函数的极值",
                      parentId: 404,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 407,
                      name: "由函数的极值求解函数或参数",
                      parentId: 404,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 408,
                  name: "利用导数研究函数的最值",
                  parentId: 400,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 409,
                      name: "利用导数求解函数的最值",
                      parentId: 408,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 410,
                      name: "由函数的最值求解函数或参数（导数法）",
                      parentId: 408,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 411,
                  name: "利用导数研究曲线上某点切线方程",
                  parentId: 400,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 412,
                      name: "利用导数求解曲线在某点上的切线方程",
                      parentId: 411,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 413,
                      name: "由函数的切线方程求解函数或参数",
                      parentId: 411,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 414,
                      name: "导数与曲线在某点上的法线",
                      parentId: 411,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 415,
                  name: "不等式恒成立的问题",
                  parentId: 400,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 416,
              name: "定积分",
              parentId: 384,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 417,
                  name: "定积分、微积分基本定理",
                  parentId: 416,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 418,
                  name: "定积分的应用",
                  parentId: 416,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 419,
                  name: "用定积分求简单几何体的体积",
                  parentId: 416,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 420,
      name: "几何与代数",
      parentId: 1,
      baseTreeNodeSiblingOrder: 2,
      baseTreeNodeLevel: 1,
      children: [
        {
          id: 421,
          name: "平面向量及其应用",
          parentId: 420,
          baseTreeNodeSiblingOrder: 0,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 422,
              name: "平面向量的概念",
              parentId: 421,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 423,
                  name: "平面向量的概念与平面向量的模",
                  parentId: 422,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 424,
                      name: "平面向量的概念与几何表示",
                      parentId: 423,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 425,
                      name: "平面向量的模",
                      parentId: 423,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 426,
                      name: "平面向量中的零向量与单位向量",
                      parentId: 423,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 427,
                  name: "平面向量的相等与共线",
                  parentId: 422,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 428,
                      name: "平面向量的相等向量",
                      parentId: 427,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 429,
                      name: "平面向量的平行向量（共线向量）",
                      parentId: 427,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 430,
              name: "平面向量的运算",
              parentId: 421,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 431,
                  name: "平面向量的线性运算",
                  parentId: 430,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 432,
                      name: "平面向量的加法",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 433,
                      name: "平面向量的减法",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 434,
                      name: "平面向量加法的三角形法则和平行四边形法则",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 435,
                      name: "平面向量的加减混合运算",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 436,
                      name: "两个平面向量的和或差的模的最值",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 437,
                      name: "平面向量的数乘与线性运算",
                      parentId: 431,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 438,
                  name: "平面向量的数量积运算",
                  parentId: 430,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 439,
                      name: "平面向量数量积的含义与物理意义",
                      parentId: 438,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 440,
                      name: "平面向量数量积的性质及其运算",
                      parentId: 438,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 441,
                      name: "平面向量的投影向量",
                      parentId: 438,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 442,
                      name: "平面向量的数量投影",
                      parentId: 438,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 443,
              name: "平面向量基本定理及坐标表示",
              parentId: 421,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 444,
                  name: "平面向量的基本定理",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 445,
                      name: "平面向量的基底",
                      parentId: 444,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 446,
                      name: "用平面向量的基底表示平面向量",
                      parentId: 444,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 447,
                  name: "平面向量的正交分解及坐标表示",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 448,
                  name: "平面向量的坐标运算",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 449,
                      name: "平面向量加减法的坐标运算",
                      parentId: 448,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 450,
                      name: "平面向量数乘和线性运算的坐标运算",
                      parentId: 448,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 451,
                      name: "平面向量数量积的坐标运算",
                      parentId: 448,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 452,
                  name: "平面向量共线（平行）的坐标表示",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 453,
                  name: "线段的定比分点",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 454,
                  name: "数量积表示两个平面向量的夹角",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 455,
                  name: "数量积判断两个平面向量的垂直关系",
                  parentId: 443,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 456,
              name: "平面向量应用与解三角形",
              parentId: 421,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 457,
                  name: "平面向量在物理中的应用",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 458,
                  name: "平面向量的综合题",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 459,
                  name: "正弦定理",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 460,
                      name: "利用正弦定理解三角形",
                      parentId: 459,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 461,
                      name: "正弦定理与三角形解的存在性和个数",
                      parentId: 459,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 462,
                      name: "正弦定理与三角形的外接圆",
                      parentId: 459,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 463,
                  name: "余弦定理",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 464,
                  name: "三角形中的几何计算",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 465,
                  name: "解三角形",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 466,
                  name: "三角形的形状判断",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 467,
                  name: "三角形五心",
                  parentId: 456,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 468,
          name: "复数",
          parentId: 420,
          baseTreeNodeSiblingOrder: 1,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 469,
              name: "虚数单位i、复数",
              parentId: 468,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 470,
                  name: "虚数单位i及其性质",
                  parentId: 469,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 471,
                  name: "复数的实部与虚部",
                  parentId: 469,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 472,
                  name: "纯虚数",
                  parentId: 469,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 473,
                  name: "复数集C及其关系和运算",
                  parentId: 469,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 474,
                  name: "复数的相等",
                  parentId: 469,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 475,
              name: "复数的代数表示法及其几何意义",
              parentId: 468,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 476,
                  name: "复数对应复平面中的点",
                  parentId: 475,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 477,
                  name: "由复平面中的点确定复数",
                  parentId: 475,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 478,
                  name: "共轭复数",
                  parentId: 475,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 479,
                  name: "复数的模",
                  parentId: 475,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 480,
                  name: "复数与复平面中的轨迹问题",
                  parentId: 475,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 481,
              name: "复数的运算",
              parentId: 468,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 482,
                  name: "复数的加、减运算及其几何意义",
                  parentId: 481,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 483,
                  name: "复数的乘法及乘方运算",
                  parentId: 481,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 484,
                  name: "复数的除法运算",
                  parentId: 481,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 485,
                  name: "复数的混合运算",
                  parentId: 481,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 486,
              name: "复数的三角表示",
              parentId: 468,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 487,
                  name: "复数的代数形式与三角形式互化",
                  parentId: 486,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 488,
                  name: "复数的辐角和辐角主值",
                  parentId: 486,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 489,
                  name: "复数乘、除运算的三角表示及其几何意义",
                  parentId: 486,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 490,
          name: "立体几何初步",
          parentId: 420,
          baseTreeNodeSiblingOrder: 2,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 491,
              name: "基本立体图形",
              parentId: 490,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 492,
                  name: "立体图形的结构特征",
                  parentId: 491,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 493,
                      name: "构成空间几何体的基本元素",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 494,
                      name: "棱柱的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 495,
                      name: "棱锥的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 496,
                      name: "棱台的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 497,
                      name: "圆柱的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 498,
                      name: "圆锥的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 499,
                      name: "圆台的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 500,
                      name: "球的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 501,
                      name: "简单组合体的结构特征",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 8,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 502,
                      name: "球内接多面体",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 9,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 503,
                      name: "球内接旋转体",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 10,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 504,
                      name: "球外切几何体",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 11,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 505,
                      name: "多面体欧拉公式",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 12,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 506,
                      name: "多面体和旋转体表面上的最短距离问题",
                      parentId: 492,
                      baseTreeNodeSiblingOrder: 13,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 507,
                  name: "立体图形的表面积与体积",
                  parentId: 491,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 508,
                      name: "棱柱、棱锥、棱台的侧面积和表面积",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 509,
                          name: "棱柱的侧面积和表面积",
                          parentId: 508,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 510,
                          name: "棱锥的侧面积和表面积",
                          parentId: 508,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 511,
                          name: "棱台的侧面积和表面积",
                          parentId: 508,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 512,
                      name: "棱柱、棱锥、棱台的体积",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 513,
                          name: "棱柱的体积",
                          parentId: 512,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 514,
                          name: "棱锥的体积",
                          parentId: 512,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 515,
                          name: "棱台的体积",
                          parentId: 512,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 516,
                      name: "旋转体（圆柱、圆锥、圆台）的表面积和侧面积",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 517,
                          name: "圆柱的侧面积和表面积",
                          parentId: 516,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 518,
                          name: "圆锥的侧面积和表面积",
                          parentId: 516,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 519,
                          name: "圆台的侧面积和表面积",
                          parentId: 516,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 520,
                      name: "旋转体（圆柱、圆锥、圆台）的体积",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 521,
                          name: "圆柱的体积",
                          parentId: 520,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 522,
                          name: "圆锥的体积",
                          parentId: 520,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 523,
                          name: "圆台的体积",
                          parentId: 520,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 524,
                      name: "球的体积和表面积",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 525,
                          name: "球的表面积",
                          parentId: 524,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 526,
                          name: "球的体积",
                          parentId: 524,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 527,
                      name: "球面距离及相关计算",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 528,
                      name: "组合几何体的面积、体积问题",
                      parentId: 507,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 529,
                  name: "立体图形的直观图",
                  parentId: 491,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 530,
                      name: "直观图",
                      parentId: 529,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 531,
                          name: "中心投影及中心投影作图法",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 532,
                          name: "平行投影及平行投影作图法",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 533,
                          name: "平面图形的直观图",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 534,
                          name: "空间几何体的直观图",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 535,
                          name: "斜二测法画直观图",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 536,
                          name: "由斜二测直观图还原图形",
                          parentId: 530,
                          baseTreeNodeSiblingOrder: 5,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 537,
                      name: "三视图",
                      parentId: 529,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 538,
                          name: "简单空间图形的三视图",
                          parentId: 537,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 539,
                          name: "由三视图还原实物图",
                          parentId: 537,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 540,
                          name: "由三视图求面积、体积",
                          parentId: 537,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              id: 541,
              name: "基本图形位置关系",
              parentId: 490,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 542,
                  name: "基本事实（公理）",
                  parentId: 541,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 543,
                      name: "平面的概念、画法及表示",
                      parentId: 542,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 544,
                      name: "平面的基本性质及推论",
                      parentId: 542,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 545,
                          name: "点直线平面的交点交线及包含关系的符号语言表示",
                          parentId: 544,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 546,
                          name: "点和直线确定平面及其数量",
                          parentId: 544,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 547,
                          name: "平面的交线及其性质",
                          parentId: 544,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 548,
                          name: "平面分割空间",
                          parentId: 544,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 549,
                      name: "平行公理",
                      parentId: 542,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 550,
                      name: "空间图形的公理",
                      parentId: 542,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 551,
                  name: "直线与直线的位置关系",
                  parentId: 541,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 552,
                      name: "异面直线及其所成的角",
                      parentId: 551,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 553,
                      name: "异面直线的判定",
                      parentId: 551,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 554,
                      name: "空间中直线与直线之间的位置关系",
                      parentId: 551,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 555,
                      name: "共面直线及四点共面",
                      parentId: 551,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 556,
                      name: "空间中直线与直线平行",
                      parentId: 551,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 557,
                  name: "直线与平面的位置关系",
                  parentId: 541,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 558,
                      name: "空间中直线与平面之间的位置关系",
                      parentId: 557,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 559,
                      name: "直线与平面平行",
                      parentId: 557,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 560,
                      name: "直线与平面垂直",
                      parentId: 557,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 561,
                      name: "三垂线定理",
                      parentId: 557,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 562,
                  name: "平面与平面的位置关系",
                  parentId: 541,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 563,
                      name: "平面与平面之间的位置关系",
                      parentId: 562,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 564,
                      name: "平面与平面平行",
                      parentId: 562,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 565,
                      name: "平面与平面垂直",
                      parentId: 562,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 566,
          name: "空间向量与立体几何",
          parentId: 420,
          baseTreeNodeSiblingOrder: 3,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 567,
              name: "空间直角坐标系",
              parentId: 566,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 568,
                  name: "空间直角坐标系",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 569,
                  name: "空间中的点的坐标",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 570,
                  name: "空间两点间的距离公式",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 571,
                  name: "空间中两点中点坐标及点关于点对称点坐标",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 572,
                  name: "关于空间直角坐标系原点坐标轴坐标平面对称点的坐标",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 573,
                  name: "空间中的点在坐标平面内的射影",
                  parentId: 567,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 574,
              name: "空间向量及其运算",
              parentId: 566,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 575,
                  name: "空间向量及其线性运算",
                  parentId: 574,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 576,
                      name: "空间向量的概念及属性",
                      parentId: 575,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 577,
                      name: "空间向量的加减运算",
                      parentId: 575,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 578,
                      name: "空间向量的数乘及线性运算",
                      parentId: 575,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 579,
                  name: "空间向量的共线与共面",
                  parentId: 574,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 580,
                  name: "空间向量的数量积运算",
                  parentId: 574,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 581,
                  name: "空间向量的夹角与距离求解公式",
                  parentId: 574,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 582,
                  name: "空间向量的投影向量与投影",
                  parentId: 574,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 583,
              name: "空间向量基本定理及坐标表示",
              parentId: 566,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 584,
                  name: "空间向量基本定理、正交分解及坐标表示",
                  parentId: 583,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 585,
                      name: "空间向量基本定理及空间向量的基底",
                      parentId: 584,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 586,
                      name: "空间向量基底表示空间向量",
                      parentId: 584,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 587,
                      name: "空间向量单位正交基底及其表示空间向量",
                      parentId: 584,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 588,
                  name: "空间向量运算的坐标表示",
                  parentId: 583,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 589,
                      name: "空间向量线性运算的坐标表示",
                      parentId: 588,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 590,
                      name: "空间向量数量积的坐标表示",
                      parentId: 588,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 591,
              name: "空间向量的应用",
              parentId: 566,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 592,
                  name: "空间向量的数量积判断向量的共线与垂直",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 593,
                  name: "空间点、线、面的位置",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 594,
                  name: "空间直线的方向向量、空间直线的向量参数方程",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 595,
                  name: "平面的法向量",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 596,
                  name: "直线与平面所成的角",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 597,
                      name: "几何法求解直线与平面所成的角",
                      parentId: 596,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 598,
                      name: "空间向量法求解直线与平面所成的角",
                      parentId: 596,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 599,
                  name: "二面角的平面角及求法",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 600,
                      name: "几何法求解二面角及两平面的夹角",
                      parentId: 599,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 601,
                      name: "空间向量法求解二面角及两平面的夹角",
                      parentId: 599,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 602,
                  name: "点、线、面间的距离计算",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 603,
                      name: "空间中点到直线的距离及两平行直线间的距离",
                      parentId: 602,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 604,
                      name: "空间中点到平面的距离",
                      parentId: 602,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 605,
                      name: "空间中两平行平面间的距离及平行于平面的直线到平面的距离",
                      parentId: 602,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 606,
                  name: "空间向量语言表述线线的垂直、平行关系",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 607,
                  name: "空间向量语言表述线面的垂直、平行关系",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 608,
                  name: "空间向量语言表述面面的垂直、平行关系",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 609,
                  name: "空间向量方法证明线、面的位置关系定理",
                  parentId: 591,
                  baseTreeNodeSiblingOrder: 10,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 610,
          name: "平面解析几何",
          parentId: 420,
          baseTreeNodeSiblingOrder: 4,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 611,
              name: "直线与方程",
              parentId: 610,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 612,
                  name: "直线的几何要素",
                  parentId: 611,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 613,
                      name: "确定直线位置的几何要素",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 614,
                      name: "直线的倾斜角",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 615,
                      name: "直线的斜率",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 616,
                      name: "直线的图象特征与倾斜角、斜率的关系",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 617,
                      name: "三点共线",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 618,
                      name: "两条直线平行与倾斜角、斜率的关系",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 619,
                      name: "两条直线垂直与倾斜角、斜率的关系",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 620,
                      name: "平面中直线的方向向量和法向量",
                      parentId: 612,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 621,
                  name: "直线的方程",
                  parentId: 611,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 622,
                      name: "直线的点斜式方程",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 623,
                      name: "直线的斜截式方程",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 624,
                      name: "直线的两点式方程",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 625,
                      name: "直线的截距式方程",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 626,
                      name: "中点坐标公式",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 627,
                      name: "直线的一般式方程与直线的性质",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 628,
                      name: "直线的一般式方程与直线的平行关系",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 629,
                      name: "直线的一般式方程与直线的垂直关系",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 630,
                      name: "待定系数法求直线方程",
                      parentId: 621,
                      baseTreeNodeSiblingOrder: 8,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 631,
                  name: "直线方程的应用",
                  parentId: 611,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 632,
                      name: "两条直线的交点坐标",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 633,
                      name: "方程组解的个数与两直线的位置关系",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 634,
                      name: "过两条直线交点的直线系方程",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 635,
                      name: "恒过定点的直线",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 636,
                      name: "与直线关于点、直线对称的直线方程",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 637,
                      name: "两点间的距离公式",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 638,
                      name: "点到直线的距离公式",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 639,
                      name: "两条平行直线间的距离",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 640,
                      name: "两直线的夹角与到角问题",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 8,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 641,
                      name: "与直线有关的动点轨迹方程",
                      parentId: 631,
                      baseTreeNodeSiblingOrder: 9,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 642,
              name: "圆与方程",
              parentId: 610,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 643,
                  name: "圆的方程",
                  parentId: 642,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 644,
                      name: "圆的标准方程",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 645,
                          name: "根据圆的几何属性求圆的标准方程",
                          parentId: 644,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 646,
                          name: "由圆的标准方程求圆的几何属性",
                          parentId: 644,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 647,
                      name: "圆的一般方程",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 648,
                          name: "根据圆的几何属性求圆的一般式方程",
                          parentId: 647,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 649,
                          name: "由圆的一般式方程求圆的几何属性",
                          parentId: 647,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 650,
                          name: "圆的一般式方程与标准方程的互化",
                          parentId: 647,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 651,
                          name: "二元二次方程表示圆的条件",
                          parentId: 647,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 652,
                      name: "其他形式的圆和圆弧的方程",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 653,
                      name: "经过三点的圆的方程",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 654,
                      name: "点与圆的位置关系",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 655,
                      name: "关于点、直线对称的圆的方程",
                      parentId: 643,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 656,
                  name: "圆的方程的应用",
                  parentId: 642,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 657,
                      name: "圆的切线方程",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 658,
                          name: "过圆上一点的圆的切线方程",
                          parentId: 657,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 659,
                          name: "过圆外一点的圆的切线方程",
                          parentId: 657,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 660,
                          name: "切线段及切点弦的长度",
                          parentId: 657,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 661,
                          name: "切点弦及所在直线的方程",
                          parentId: 657,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 662,
                      name: "直线与圆相交的性质",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 663,
                          name: "直线及坐标轴被圆截得的弦及弦长",
                          parentId: 662,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 664,
                          name: "过圆内一点的弦及弦长的最值",
                          parentId: 662,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 665,
                      name: "直线与圆的位置关系",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 666,
                          name: "根据圆心到直线距离与圆的半径求解直线与圆的位置关系",
                          parentId: 665,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 667,
                          name: "根据联立直线和圆的方程解的情况求解直线与圆的位置关系",
                          parentId: 665,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 668,
                          name: "由直线与圆的位置关系求解直线与圆的方程或参数",
                          parentId: 665,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 669,
                          name: "圆上的点到定点的距离及其最值",
                          parentId: 665,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 670,
                          name: "圆上的点到直线的距离及其最值",
                          parentId: 665,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 671,
                      name: "圆与圆的位置关系及其判定",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 672,
                          name: "根据两圆的圆心距与两圆半径之和求解圆与圆的位置关系",
                          parentId: 671,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 673,
                          name: "根据联立两圆方程解的情况求解圆与圆的位置关系",
                          parentId: 671,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 674,
                          name: "由圆与圆的位置关系求解圆的方程或参数",
                          parentId: 671,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 675,
                      name: "两圆的公切线条数及方程的确定",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 676,
                      name: "圆系方程",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 677,
                      name: "相交弦所在直线的方程",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 678,
                      name: "直线和圆的方程的应用",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 679,
                      name: "圆方程的综合应用",
                      parentId: 656,
                      baseTreeNodeSiblingOrder: 8,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 680,
              name: "圆锥曲线与方程",
              parentId: 610,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 681,
                  name: "椭圆",
                  parentId: 680,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 682,
                      name: "圆锥曲线的实际背景及作用",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 683,
                      name: "椭圆的定义",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 684,
                      name: "椭圆的标准方程",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 685,
                          name: "根据定义求椭圆的标准方程",
                          parentId: 684,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 686,
                          name: "根据椭圆的几何特征求标准方程",
                          parentId: 684,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 687,
                          name: "根据椭圆上的点求椭圆的标准方程",
                          parentId: 684,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 688,
                          name: "根据abc及其关系式求椭圆的标准方程",
                          parentId: 684,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 689,
                          name: "由方程表示椭圆求解椭圆的标准方程或参数",
                          parentId: 684,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 690,
                      name: "椭圆的焦点和焦距",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 691,
                          name: "求椭圆的焦点和焦距",
                          parentId: 690,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 692,
                          name: "由椭圆的焦点焦距求解椭圆方程或参数",
                          parentId: 690,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 693,
                          name: "椭圆上的点与焦点的距离",
                          parentId: 690,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 694,
                      name: "椭圆的几何特征",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 695,
                          name: "椭圆的范围",
                          parentId: 694,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 696,
                          name: "椭圆的顶点",
                          parentId: 694,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 697,
                          name: "椭圆的长短轴",
                          parentId: 694,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 698,
                          name: "椭圆的对称性",
                          parentId: 694,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 699,
                      name: "椭圆的离心率",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 700,
                          name: "求椭圆的离心率",
                          parentId: 699,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 701,
                          name: "由椭圆的离心率求解方程或参数",
                          parentId: 699,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 702,
                          name: "椭圆的离心率与椭圆形状的关系",
                          parentId: 699,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 703,
                      name: "椭圆的其他性质",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 704,
                          name: "椭圆的准线及第二定义",
                          parentId: 703,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 705,
                          name: "椭圆相关动点轨迹",
                          parentId: 703,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 706,
                      name: "直线与椭圆的综合",
                      parentId: 681,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 707,
                          name: "直线与椭圆的位置关系及公共点个数",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 708,
                          name: "由直线与椭圆位置关系及公共点个数求解方程或参数",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 709,
                          name: "椭圆的切线方程及性质",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 710,
                          name: "椭圆的弦及弦长",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 711,
                          name: "椭圆的焦点弦及焦半径",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 712,
                          name: "椭圆的焦点三角形",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 5,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 713,
                          name: "椭圆的中点弦",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 6,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 714,
                          name: "椭圆与平面向量",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 7,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 715,
                          name: "椭圆的定点及定值问题",
                          parentId: 706,
                          baseTreeNodeSiblingOrder: 8,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  id: 716,
                  name: "抛物线",
                  parentId: 680,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 717,
                      name: "抛物线的定义",
                      parentId: 716,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 718,
                      name: "抛物线的标准方程",
                      parentId: 716,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 719,
                          name: "根据定义求抛物线的标准方程",
                          parentId: 718,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 720,
                          name: "根据抛物线上的点求抛物线的标准方程",
                          parentId: 718,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 721,
                          name: "抛物线的四种标准方程及其图象",
                          parentId: 718,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 722,
                      name: "抛物线的焦点与准线",
                      parentId: 716,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 723,
                          name: "求抛物线的准线方程",
                          parentId: 722,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 724,
                          name: "求抛物线的焦点和焦准距",
                          parentId: 722,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 725,
                          name: "由准线求抛物线的方程或参数",
                          parentId: 722,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 726,
                          name: "由抛物线的焦点或焦准距求解抛物线方程或参数",
                          parentId: 722,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 727,
                          name: "抛物线上的点到准线及其平行线的距离",
                          parentId: 722,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 728,
                      name: "直线与抛物线的综合",
                      parentId: 716,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 729,
                          name: "直线与抛物线的位置关系及公共点的个数",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 730,
                          name: "由直线与抛物线位置关系及公共点个数求解方程或参数",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 731,
                          name: "抛物线的切线方程及性质",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 732,
                          name: "抛物线的弦及弦长",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 733,
                          name: "抛物线的焦点弦及焦半径",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 734,
                          name: "抛物线的焦点三角形",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 5,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 735,
                          name: "抛物线的中点弦",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 6,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 736,
                          name: "抛物线与平面向量",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 7,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 737,
                          name: "抛物线的定点及定值问题",
                          parentId: 728,
                          baseTreeNodeSiblingOrder: 8,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  id: 738,
                  name: "双曲线",
                  parentId: 680,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 739,
                      name: "双曲线的定义",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 740,
                      name: "双曲线的标准方程",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 741,
                          name: "根据定义求双曲线的标准方程",
                          parentId: 740,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 742,
                          name: "根据双曲线的几何特征求标准方程",
                          parentId: 740,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 743,
                          name: "根据双曲线上的点求双曲线的标准方程",
                          parentId: 740,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 744,
                          name: "根据abc及其关系式求双曲线的标准方程",
                          parentId: 740,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 745,
                          name: "由方程表示双曲线求解双曲线的标准方程或参数",
                          parentId: 740,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 746,
                      name: "双曲线的焦点和焦距",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 747,
                          name: "求双曲线的焦点和焦距",
                          parentId: 746,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 748,
                          name: "由双曲线的焦点焦距求解双曲线方程或参数",
                          parentId: 746,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 749,
                          name: "双曲线上的点与焦点的距离",
                          parentId: 746,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 750,
                      name: "双曲线的渐近线",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 751,
                          name: "求双曲线的渐近线方程",
                          parentId: 750,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 752,
                          name: "由双曲线的渐近线方程求解双曲线的标准方程或参数",
                          parentId: 750,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 753,
                      name: "双曲线的几何特征",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 754,
                          name: "双曲线的范围",
                          parentId: 753,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 755,
                          name: "双曲线的顶点",
                          parentId: 753,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 756,
                          name: "双曲线的实轴和虚轴",
                          parentId: 753,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 757,
                          name: "双曲线的对称性",
                          parentId: 753,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 758,
                      name: "双曲线的离心率",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 759,
                          name: "求双曲线的离心率",
                          parentId: 758,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 760,
                          name: "由双曲线的离心率求解方程或参数",
                          parentId: 758,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 761,
                          name: "双曲线的离心率与双曲线形状的关系",
                          parentId: 758,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 762,
                      name: "双曲线的其他性质",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 763,
                          name: "双曲线的准线及第二定义",
                          parentId: 762,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 764,
                          name: "双曲线相关动点轨迹",
                          parentId: 762,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                    {
                      id: 765,
                      name: "直线与双曲线的综合",
                      parentId: 738,
                      baseTreeNodeSiblingOrder: 7,
                      baseTreeNodeLevel: 5,
                      children: [
                        {
                          id: 766,
                          name: "直线与双曲线的位置关系及公共点个数",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 0,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 767,
                          name: "由直线与双曲线位置关系及公共点个数求解方程或参数",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 1,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 768,
                          name: "双曲线的切线方程及性质",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 2,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 769,
                          name: "双曲线的弦及弦长",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 3,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 770,
                          name: "双曲线的焦点弦及焦半径",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 4,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 771,
                          name: "双曲线的焦点三角形",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 5,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 772,
                          name: "双曲线的中点弦",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 6,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 773,
                          name: "双曲线与平面向量",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 7,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                        {
                          id: 774,
                          name: "双曲线的定点及定值问题",
                          parentId: 765,
                          baseTreeNodeSiblingOrder: 8,
                          baseTreeNodeLevel: 6,
                          children: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  id: 775,
                  name: "圆锥曲线综合",
                  parentId: 680,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 776,
                      name: "曲线与方程",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 777,
                      name: "圆锥曲线的共同特征",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 778,
                      name: "直线与圆锥曲线的综合",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 779,
                      name: "圆锥曲线的综合",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 780,
                      name: "圆与圆锥曲线的综合",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 781,
                      name: "圆锥曲线的轨迹问题",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 782,
                      name: "轨迹方程",
                      parentId: 775,
                      baseTreeNodeSiblingOrder: 6,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 783,
      name: "概率与统计",
      parentId: 1,
      baseTreeNodeSiblingOrder: 3,
      baseTreeNodeLevel: 1,
      children: [
        {
          id: 784,
          name: "概率",
          parentId: 783,
          baseTreeNodeSiblingOrder: 0,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 785,
              name: "样本空间与随机事件",
              parentId: 784,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 786,
                  name: "样本点与样本空间",
                  parentId: 785,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 787,
                  name: "随机事件",
                  parentId: 785,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 788,
                      name: "随机事件、基本事件及必然事件、不可能事件",
                      parentId: 787,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 789,
                      name: "事件的包含关系及相等",
                      parentId: 787,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 790,
                      name: "事件的并事件（和事件）",
                      parentId: 787,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 791,
                      name: "事件的交事件（积事件）",
                      parentId: 787,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 792,
                  name: "互斥事件与对立事件",
                  parentId: 785,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 793,
                      name: "事件的互斥（互不相容）及互斥事件",
                      parentId: 792,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 794,
                      name: "事件的互为对立及对立事件",
                      parentId: 792,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 795,
              name: "随机事件的概率",
              parentId: 784,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 796,
                  name: "概率及其性质",
                  parentId: 795,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 797,
                      name: "概率及有包含关系的事件的概率",
                      parentId: 796,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 798,
                      name: "互斥事件的概率加法公式",
                      parentId: 796,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 799,
                      name: "对立事件的概率关系及计算",
                      parentId: 796,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 800,
                      name: "并事件积事件的概率关系及计算",
                      parentId: 796,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 801,
                  name: "古典概型",
                  parentId: 795,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 802,
                      name: "等可能事件和等可能事件的概率",
                      parentId: 801,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 803,
                      name: "古典概型及其概率计算公式",
                      parentId: 801,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 804,
                      name: "列举法计算基本事件数及事件发生的概率",
                      parentId: 801,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 805,
                  name: "频率与概率",
                  parentId: 795,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 806,
                      name: "频率及频率的稳定性",
                      parentId: 805,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 807,
                      name: "模拟方法估计概率",
                      parentId: 805,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 808,
                  name: "几何概型",
                  parentId: 795,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 809,
                  name: "概率的应用",
                  parentId: 795,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 810,
              name: "随机事件的独立性与条件概率",
              parentId: 784,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 811,
                  name: "相互独立事件和相互独立事件的概率乘法公式",
                  parentId: 810,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 812,
                      name: "由两事件交事件的概率判断两事件的相互独立性",
                      parentId: 811,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 813,
                      name: "相互独立事件的概率乘法公式",
                      parentId: 811,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 814,
                  name: "条件概率",
                  parentId: 810,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 815,
                      name: "求解条件概率",
                      parentId: 814,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 816,
                      name: "条件概率乘法公式及应用",
                      parentId: 814,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 817,
                  name: "全概率公式",
                  parentId: 810,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 818,
                  name: "贝叶斯公式",
                  parentId: 810,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 819,
              name: "离散型随机变量及其分布列",
              parentId: 784,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 820,
                  name: "离散型随机变量及其分布列",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 821,
                  name: "离散型随机变量的均值（数学期望）",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 822,
                  name: "离散型随机变量的方差与标准差",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 823,
                  name: "两点分布（0-1分布）",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 824,
                  name: "二项分布",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 825,
                      name: "n重伯努利试验与二项分布",
                      parentId: 824,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 826,
                      name: "n次独立重复试验中恰好发生k次的概率",
                      parentId: 824,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 827,
                      name: "二项分布的均值（数学期望）与方差",
                      parentId: 824,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 828,
                  name: "超几何分布",
                  parentId: 819,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 829,
              name: "正态分布",
              parentId: 784,
              baseTreeNodeSiblingOrder: 4,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 830,
                  name: "连续型随机变量",
                  parentId: 829,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 831,
                  name: "正态分布曲线的特点及曲线所表示的意义",
                  parentId: 829,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 832,
          name: "统计",
          parentId: 783,
          baseTreeNodeSiblingOrder: 1,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 833,
              name: "抽样",
              parentId: 832,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 834,
                  name: "简单随机抽样",
                  parentId: 833,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 835,
                      name: "简单随机抽样及其适用条件",
                      parentId: 834,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 836,
                      name: "抽签法简单随机抽样及其步骤",
                      parentId: 834,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 837,
                      name: "随机数法简单随机抽样及其步骤",
                      parentId: 834,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 838,
                      name: "求随机数法抽样的样本",
                      parentId: 834,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 839,
                  name: "分层随机抽样",
                  parentId: 833,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 840,
                      name: "分层随机抽样及其适用条件",
                      parentId: 839,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 841,
                      name: "分层随机抽样的比例分配与各层个体数及抽取样本量",
                      parentId: 839,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 842,
                      name: "由分层随机抽样的样本平均数估计总体平均数",
                      parentId: 839,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 843,
                  name: "系统抽样方法",
                  parentId: 833,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 844,
                  name: "收集数据的方法",
                  parentId: 833,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 845,
                      name: "普查与抽样",
                      parentId: 844,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 846,
                      name: "调查试验观察查询等其他方法获取统计数据",
                      parentId: 844,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 847,
              name: "统计图表",
              parentId: 832,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 848,
                  name: "分布和频率分布表",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 849,
                      name: "根据统计数据确定极差组距和组数",
                      parentId: 848,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 850,
                      name: "列频率分布表及补全频率分布表",
                      parentId: 848,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 851,
                      name: "频率分布表的应用",
                      parentId: 848,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 852,
                  name: "频率分布直方图",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 853,
                      name: "画频率分布直方图",
                      parentId: 852,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 854,
                      name: "补全频率分布直方图",
                      parentId: 852,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 855,
                      name: "频率分布直方图的应用",
                      parentId: 852,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 856,
                  name: "频率分布折线图、密度曲线",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 857,
                  name: "茎叶图",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 858,
                  name: "散点图",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 859,
                  name: "统计图表获取信息",
                  parentId: 847,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 860,
                      name: "条形统计图",
                      parentId: 859,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 861,
                      name: "折线统计图",
                      parentId: 859,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 862,
                      name: "扇形统计图",
                      parentId: 859,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 863,
                      name: "其他形式的统计图表",
                      parentId: 859,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 864,
              name: "用样本估计总体",
              parentId: 832,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 865,
                  name: "用样本估计总体的集中趋势参数",
                  parentId: 864,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 866,
                      name: "平均数",
                      parentId: 865,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 867,
                      name: "中位数",
                      parentId: 865,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 868,
                      name: "众数",
                      parentId: 865,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 869,
                  name: "用样本估计总体的离散程度参数",
                  parentId: 864,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 870,
                      name: "标准差",
                      parentId: 869,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 871,
                      name: "方差",
                      parentId: 869,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 872,
                      name: "极差",
                      parentId: 869,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 873,
                  name: "百分位数",
                  parentId: 864,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 874,
              name: "成对数据的统计相关性",
              parentId: 832,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 875,
                  name: "变量间的相关关系",
                  parentId: 874,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 876,
                  name: "样本相关系数",
                  parentId: 874,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 877,
              name: "一元线性回归模型",
              parentId: 832,
              baseTreeNodeSiblingOrder: 4,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 878,
                  name: "最小二乘法",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 879,
                  name: "经验回归方程与经验回归直线",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 880,
                  name: "回归分析",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 881,
                  name: "残差及残差图",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 882,
                  name: "非线性回归模型",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 883,
                  name: "决定系数与模型的拟合效果",
                  parentId: 877,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 884,
              name: "列联表与独立性检验",
              parentId: 832,
              baseTreeNodeSiblingOrder: 5,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 885,
                  name: "分类变量与2×2列联表",
                  parentId: 884,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 886,
                  name: "等高堆积条形图",
                  parentId: 884,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 887,
                  name: "独立性检验",
                  parentId: 884,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 888,
                  name: "实际推断原理和假设检验",
                  parentId: 884,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 889,
          name: "计数原理",
          parentId: 783,
          baseTreeNodeSiblingOrder: 2,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 890,
              name: "两个基本计数原理",
              parentId: 889,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 891,
                  name: "分类加法计数原理",
                  parentId: 890,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 892,
                  name: "分步乘法计数原理",
                  parentId: 890,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 893,
                  name: "计数原理的应用",
                  parentId: 890,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 894,
                      name: "代数与函数中的计数问题",
                      parentId: 893,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 895,
                      name: "几何图形中的计数问题",
                      parentId: 893,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 896,
                      name: "数字问题",
                      parentId: 893,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 897,
                      name: "染色问题",
                      parentId: 893,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 898,
                      name: "加法计数原理与乘法计数原理的综合应用",
                      parentId: 893,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
              ],
            },
            {
              id: 899,
              name: "排列与组合",
              parentId: 889,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 900,
                  name: "排列及排列数公式",
                  parentId: 899,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 901,
                      name: "排列数的化简计算及证明",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 902,
                      name: "简单排列问题",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 903,
                      name: "部分位置的元素有限制的排列问题",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 904,
                      name: "部分元素不相邻的排列问题",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 905,
                      name: "部分元素相邻的排列问题",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 906,
                      name: "其他排列形式及其计算",
                      parentId: 900,
                      baseTreeNodeSiblingOrder: 5,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 907,
                  name: "组合及组合数公式",
                  parentId: 899,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [
                    {
                      id: 908,
                      name: "组合数的化简计算及证明",
                      parentId: 907,
                      baseTreeNodeSiblingOrder: 0,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 909,
                      name: "简单组合问题",
                      parentId: 907,
                      baseTreeNodeSiblingOrder: 1,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 910,
                      name: "人员及物品分配问题",
                      parentId: 907,
                      baseTreeNodeSiblingOrder: 2,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 911,
                      name: "从不同类别人员物品中进行挑选的组合问题",
                      parentId: 907,
                      baseTreeNodeSiblingOrder: 3,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                    {
                      id: 912,
                      name: "其他组合形式及计算",
                      parentId: 907,
                      baseTreeNodeSiblingOrder: 4,
                      baseTreeNodeLevel: 5,
                      children: [],
                    },
                  ],
                },
                {
                  id: 913,
                  name: "排列组合的综合应用",
                  parentId: 899,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 914,
              name: "二项式定理",
              parentId: 889,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 915,
                  name: "二项展开式的通项与项的系数",
                  parentId: 914,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 916,
                  name: "二项式系数与二项式系数的和",
                  parentId: 914,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 917,
                  name: "二项式系数的性质",
                  parentId: 914,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 918,
                  name: "二项式定理的应用",
                  parentId: 914,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 919,
      name: "数学知识的延伸",
      parentId: 1,
      baseTreeNodeSiblingOrder: 4,
      baseTreeNodeLevel: 1,
      children: [
        {
          id: 920,
          name: "算法与框图",
          parentId: 919,
          baseTreeNodeSiblingOrder: 0,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 921,
              name: "算法",
              parentId: 920,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 922,
                  name: "算法及其特点",
                  parentId: 921,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 923,
                  name: "排序问题与算法的多样性",
                  parentId: 921,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 924,
                  name: "伪代码（算法语句）",
                  parentId: 921,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 925,
                  name: "秦九韶算法",
                  parentId: 921,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 926,
                  name: "进位制",
                  parentId: 921,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 927,
              name: "框图",
              parentId: 920,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 928,
                  name: "流程图的概念",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 929,
                  name: "顺序结构",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 930,
                  name: "选择结构",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 931,
                  name: "循环结构",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 932,
                  name: "程序框图的三种基本逻辑结构的应用",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 933,
                  name: "程序框图",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 934,
                  name: "工序流程图（即统筹图）",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 935,
                  name: "绘制程序框图解决问题",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 936,
                  name: "流程图的作用",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 937,
                  name: "结构图",
                  parentId: 927,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 938,
          name: "推理与证明",
          parentId: 919,
          baseTreeNodeSiblingOrder: 1,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 939,
              name: "推理",
              parentId: 938,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 940,
                  name: "归纳推理",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 941,
                  name: "合情推理的含义与作用",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 942,
                  name: "类比推理",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 943,
                  name: "进行简单的合情推理",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 944,
                  name: "演绎推理",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 945,
                  name: "合情推理和演绎推理之间的联系和差异",
                  parentId: 939,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 946,
              name: "证明",
              parentId: 938,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 947,
                  name: "分析法和综合法",
                  parentId: 946,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 948,
                  name: "反证法",
                  parentId: 946,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 949,
          name: "几何证明选讲",
          parentId: 919,
          baseTreeNodeSiblingOrder: 2,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 950,
              name: "三角形",
              parentId: 949,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 951,
                  name: "平行截割定理",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 952,
                  name: "平行线等分线段定理",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 953,
                  name: "平行线分线段成比例定理",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 954,
                  name: "相似三角形的判定",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 955,
                  name: "相似三角形的性质",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 956,
                  name: "直角三角形的射影定理",
                  parentId: 950,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 957,
              name: "圆",
              parentId: 949,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 958,
                  name: "圆周角定理",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 959,
                  name: "圆内接多边形的性质与判定",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 960,
                  name: "圆的切线的判定定理的证明",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 961,
                  name: "圆的切线的性质定理的证明",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 962,
                  name: "弦切角",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 963,
                  name: "与圆有关的比例线段",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 964,
                  name: "球的性质",
                  parentId: 957,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 965,
              name: "圆锥曲线",
              parentId: 949,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 966,
                  name: "平行投影",
                  parentId: 965,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 967,
                  name: "平面与圆柱面的截线",
                  parentId: 965,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 968,
                  name: "平面与圆锥面的截线",
                  parentId: 965,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 969,
                  name: "圆锥曲线的几何性质",
                  parentId: 965,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 970,
          name: "矩阵与变换",
          parentId: 919,
          baseTreeNodeSiblingOrder: 3,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 971,
              name: "线性变换与二阶矩阵",
              parentId: 970,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 972,
                  name: "矩阵",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 973,
                  name: "矩阵与向量的乘法",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 974,
                  name: "旋转变换",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 975,
                  name: "反射变换",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 976,
                  name: "伸缩变换",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 977,
                  name: "投影变换",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 978,
                  name: "变换、矩阵的相等",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 979,
                  name: "几种特殊的矩阵变换",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 980,
                  name: "矩阵变换的性质",
                  parentId: 971,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 981,
              name: "变换的复合与二阶矩阵的乘法",
              parentId: 970,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 982,
                  name: "矩阵与矩阵的乘法的意义",
                  parentId: 981,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 983,
                  name: "复合变换与二阶矩阵的乘法",
                  parentId: 981,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 984,
                  name: "矩阵乘法的性质",
                  parentId: 981,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 985,
              name: "逆变换与逆矩阵",
              parentId: 970,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 986,
                  name: "逆变换与逆矩阵",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 987,
                  name: "逆矩阵的意义",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 988,
                  name: "逆矩阵可能不存在的证明",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 989,
                  name: "逆矩阵的简单性质（唯一性等）",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 990,
                  name: "行列式",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 991,
                  name: "二阶行列式与逆矩阵",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 992,
                  name: "二元一次方程组的矩阵形式",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 993,
                  name: "逆矩阵与二元一次方程组",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 994,
                  name: "系数矩阵的逆矩阵解方程组",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 995,
                  name: "线性方程组解的存在性，唯一性",
                  parentId: 985,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 996,
              name: "变换的不变量与矩阵的特征向量",
              parentId: 970,
              baseTreeNodeSiblingOrder: 3,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 997,
                  name: "矩阵特征值的定义",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 998,
                  name: "特征向量的定义",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 999,
                  name: "特征向量的意义",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1000,
                  name: "特征值与特征向量的计算",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1001,
                  name: "特征值、特征向量的应用",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1002,
                  name: "矩阵的应用",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1003,
                  name: "三阶矩阵",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1004,
                  name: "高阶矩阵",
                  parentId: 996,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 1005,
          name: "坐标系与参数方程",
          parentId: 919,
          baseTreeNodeSiblingOrder: 4,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 1006,
              name: "坐标系",
              parentId: 1005,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1007,
                  name: "坐标系的作用",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1008,
                  name: "平面直角坐标系与曲线方程",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1009,
                  name: "极坐标系",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1010,
                  name: "简单曲线的极坐标方程",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1011,
                  name: "平面直角坐标轴中的伸缩变换",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1012,
                  name: "极坐标刻画点的位置",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1013,
                  name: "极坐标系和平面直角坐标系的区别",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1014,
                  name: "点的极坐标和直角坐标的互化",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1015,
                  name: "坐标系的选择及意义",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1016,
                  name: "柱坐标系与球坐标系",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1017,
                  name: "柱坐标刻画点的位置",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 10,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1018,
                  name: "球坐标刻画点的位置",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 11,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1019,
                  name: "柱、球坐标系与空间直角坐标系的区别",
                  parentId: 1006,
                  baseTreeNodeSiblingOrder: 12,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1020,
              name: "参数方程",
              parentId: 1005,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1021,
                  name: "参数方程的概念",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1022,
                  name: "参数的意义",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1023,
                  name: "参数方程化成普通方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1024,
                  name: "参数方程的优越性",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1025,
                  name: "直线的参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1026,
                  name: "圆的参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1027,
                  name: "椭圆的参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1028,
                  name: "双曲线的参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1029,
                  name: "抛物线的参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1030,
                  name: "渐开线的生成过程及其参数方程",
                  parentId: 1020,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 1031,
          name: "不等式选讲",
          parentId: 919,
          baseTreeNodeSiblingOrder: 5,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 1032,
              name: "绝对值不等式",
              parentId: 1031,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1033,
                  name: "绝对值不等式",
                  parentId: 1032,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1034,
                  name: "绝对值三角不等式",
                  parentId: 1032,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1035,
                  name: "绝对值不等式的解法",
                  parentId: 1032,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1036,
              name: "不等式的证明",
              parentId: 1031,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1037,
                  name: "不等式的证明",
                  parentId: 1036,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1038,
                  name: "比较法",
                  parentId: 1036,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1039,
                  name: "综合法与分析法",
                  parentId: 1036,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1040,
                  name: "反证法与放缩法证明不等式",
                  parentId: 1036,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1041,
              name: "柯西不等式与排序不等式",
              parentId: 1031,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1042,
                  name: "二维形式的柯西不等式",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1043,
                  name: "一般形式的柯西不等式",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1044,
                  name: "柯西不等式的几何意义",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1045,
                  name: "柯西不等式在函数极值中的应用",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1046,
                  name: "排序不等式",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1047,
                  name: "平均值不等式",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1048,
                  name: "平均值不等式在函数极值中的应用",
                  parentId: 1041,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 1049,
          name: "初等数论初步",
          parentId: 919,
          baseTreeNodeSiblingOrder: 6,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 1050,
              name: "整数的整除",
              parentId: 1049,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1051,
                  name: "整除的概念和性质",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1052,
                  name: "带余除法",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1053,
                  name: "素数与合数",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1054,
                  name: "最大公因数",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1055,
                  name: "最小公倍数",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1056,
                  name: "辗转相除法",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1057,
                  name: "用辗转相除计算最大公约数",
                  parentId: 1050,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1058,
              name: "同余",
              parentId: 1049,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1059,
                  name: "同余的性质",
                  parentId: 1058,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1060,
                  name: "同余的概念及同余方程",
                  parentId: 1058,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1061,
              name: "不定方程与数论应用",
              parentId: 1049,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1062,
                  name: "信息的加密与去密",
                  parentId: 1061,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1063,
                  name: "大数分解和公开密钥",
                  parentId: 1061,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1064,
                  name: "不定方程和方程组",
                  parentId: 1061,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1065,
                  name: "多项式的除法定理",
                  parentId: 1061,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1066,
                  name: "因式分解定理",
                  parentId: 1061,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 1067,
          name: "几何与代数拓展",
          parentId: 919,
          baseTreeNodeSiblingOrder: 7,
          baseTreeNodeLevel: 2,
          children: [
            {
              id: 1068,
              name: "数学史与优选法",
              parentId: 1067,
              baseTreeNodeSiblingOrder: 0,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1069,
                  name: "数学史选讲",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1070,
                  name: "优选法的概念",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1071,
                  name: "单峰函数",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1072,
                  name: "黄金分割法—0.618法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1073,
                  name: "分数法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1074,
                  name: "分数法的最优性",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1075,
                  name: "对分法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1076,
                  name: "分批试验法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1077,
                  name: "双因素盲人爬山法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1078,
                  name: "正交试验设计方法",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 9,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1079,
                  name: "正交试验的应用",
                  parentId: 1068,
                  baseTreeNodeSiblingOrder: 10,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1080,
              name: "几何类拓展",
              parentId: 1067,
              baseTreeNodeSiblingOrder: 1,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1081,
                  name: "几何中的变换（对称、平移、旋转）",
                  parentId: 1080,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1082,
                  name: "三角形的面积公式",
                  parentId: 1080,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1083,
                  name: "表面展开图",
                  parentId: 1080,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
            {
              id: 1084,
              name: "代数类拓展",
              parentId: 1067,
              baseTreeNodeSiblingOrder: 2,
              baseTreeNodeLevel: 3,
              children: [
                {
                  id: 1085,
                  name: "三角方程",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 0,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1086,
                  name: "三角不等式",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 1,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1087,
                  name: "递归数列及其性质",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 2,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1088,
                  name: "复数的指数形式",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 3,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1089,
                  name: "复数欧拉公式",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 4,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1090,
                  name: "棣莫弗定理",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 5,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1091,
                  name: "根与系数的关系",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 6,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1092,
                  name: "实系数多项式虚根成对定理",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 7,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
                {
                  id: 1093,
                  name: "函数迭代",
                  parentId: 1084,
                  baseTreeNodeSiblingOrder: 8,
                  baseTreeNodeLevel: 4,
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

export const kownledgeTreeList = [
  {
    id: "1",
    name: "高中必修数学基础树",
  },
  {
    id: "2",
    name: "高中选修数学基础树高中选修数学基础树高中选修数学基础树",
  },
  {
    id: "3",
    name: "初中数学基础树",
  },
  {
    id: "4",
    name: "初中化学基础树",
  },
  {
    id: "5",
    name: "高中英语基础树",
  },
];

export const studentList = [
  {
    id: "1",
    name: "张三",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "2",
    name: "李四",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "3",
    name: "王五",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "4",
    name: "赵六",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "5",
    name: "孙七",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "6",
    name: "周八",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "7",
    name: "吴九",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "8",
    name: "郑十",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "9",
    name: "王十一",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "10",
    name: "冯十二",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
  {
    id: "11",
    name: "陈十三",
    avatar: "http://img.92fa.com/pic/TX1505_22.jpg",
  },
];

export const classList = [
  {
    id: "1",
    name: "高一1班",
    grade: "高一",
    checked: false,
    students: studentList,
  },
  {
    id: "2",
    name: "高一2班",
    grade: "高一",
    checked: true,
    students: studentList.map(item => ({...item, name: item.name + "2"})),
  },
  {
    id: "3",
    name: "高一3班",
    grade: "高一",
    checked: false,
    students: studentList,
  },
  {
    id: "4",
    name: "高一4班",
    grade: "高一",
    checked: false,
    students: studentList,
  },
  {
    id: "5",
    name: "高一5班",
    grade: "高一",
    checked: false,
    students: studentList,
  },
  {
    id: "6",
    name: "高二1班",
    grade: "高二",
    checked: false,
    students: studentList,
  },
  {
    id: "7",
    name: "高二2班",
    grade: "高二",
    checked: false,
    students: studentList,
  },
  {
    id: "8",
    name: "高二3班",
    grade: "高二",
    checked: false,
    students: studentList,
  },
  {
    id: "9",
    name: "高二4班",
    grade: "高二",
    checked: false,
    students: studentList,
  },
  {
    id: "10",
    name: "高二5班",
    grade: "高二",
    checked: false,
    students: studentList,
  },
];
