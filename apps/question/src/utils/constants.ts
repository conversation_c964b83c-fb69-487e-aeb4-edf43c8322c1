export const sortOptions = [
  { label: '最新题目', value: 'createTime' },
  { label: '最多使用', value: 'useCount' },
];

export const treeStatusOptions = [
  { label: '全部', value: -1 },
  { label: '已上架', value: 1 },
  { label: '未上架', value: 0 },
];

export const questionFilterOptions = [
  {
    name: '题型',
    // 请求参数字段
    field: 'questionType',
    // 枚举常量字段
    optionField: 'subjectLabelQuestionTypeRelationList',
    options: [],
    value: [],
  },
  {
    name: '难度',
    field: 'questionDifficult',
    optionField: 'questionDifficultList',
    options: [],
    value: [],
  },
  {
    name: '年份',
    field: 'questionYears',
    optionField: 'yearList',
    options: [],
    value: [],
  },
  {
    name: '省份',
    field: 'questionProvince',
    optionField: 'provinceList',
    options: [],
    value: [],
  },
];

export const mergeQuestionFilterOptions = (
  constants: API.EnumConstantData,
  subject: number = 0,
  showEmptyOption: boolean = true,
) => {
  const filterOptions = questionFilterOptions.map((item) => {
    const { optionField } = item;
    let options = [];
    if (optionField === 'subjectLabelQuestionTypeRelationList') {
      options = getQuestionTypeOptions(constants, subject);
    } else {
      options = constants[optionField as keyof API.EnumConstantData] || [];
    }
    return { ...item, options };
  });

  return showEmptyOption ? filterOptions : filterOptions.filter((item) => item.options.length > 0);
};

export const getQuestionTypeOptions = (
  constants: API.EnumConstantData,
  subject: number,
): API.EnumConstantItem<number>[] => {
  if (!subject) {
    return [];
  }
  const subjectLabelQuestionTypeRelationList = constants.subjectLabelQuestionTypeRelationList || [];
  const subjectItem = subjectLabelQuestionTypeRelationList.find((item) => item.value === subject);
  const options = subjectItem?.labelQuestionTypeList || [];
  return options;
};

export const treeShelfStatusOptions = [
  { label: '全部', value: -1 },
  { label: '已上架', value: 1 },
  { label: '未上架', value: 0 },
];

export const questionDifficultColors: Record<
  API.EnumConstantData['questionDifficultList'][number]['value'],
  string
> = {
  1: 'green',
  2: 'cyan',
  3: 'blue',
  4: 'orange',
  5: 'red',
};

export const MAIN_AFFIX_MAX_HEIGHT = `calc(100vh - 108px - 32px - 16px)`;
export const MANAGE_QUESTION_PAGE_TREE_HEIGHT = `calc(100vh - ${332 + 32 + 16}px)`;
export const EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT = `calc(100vh - 238px - 24px)`;
export const EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT_2 = `calc(100vh - 238px - 24px - 41px)`;
