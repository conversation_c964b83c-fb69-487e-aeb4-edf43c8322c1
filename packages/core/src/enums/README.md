# Core Enums 模块

本模块包含项目中使用的各种枚举定义和管理器。

## 题目类型枚举 (Question Types)

### 模块位置
`@repo/core/enums/question`

### 包含内容

#### 1. QUESTION_TYPE 枚举
定义了系统支持的所有题目类型：
- `QUESTION_TYPE_SINGLE_CHOICE = 1` - 单选题
- `QUESTION_TYPE_MULTIPLE_CHOICE = 2` - 多选题  
- `QUESTION_TYPE_FILL_IN_THE_BLANK = 3` - 填空题
- `QUESTION_TYPE_JUDGMENT = 4` - 判断题
- `QUESTION_TYPE_QA = 5` - 问答题
- `QUESTION_TYPE_PARENT_CHILD = 7` - 综合题 (母子题)

#### 2. QUESTION_TYPE_OPTIONS 配置
每个题目类型的完整配置信息，包括：
- `label`: 完整的中文名称
- `value`: 枚举值
- `shortName`: 简短名称
- `icon`: Material Icons 图标名
- `bg`: Tailwind CSS 背景色类名

#### 3. questionTypeEnumManager 管理器
基于 EnumManager 的管理器实例，提供便捷的操作方法：
- `getLabelByValue(value)` - 根据值获取标签
- `getValueByLabel(label)` - 根据标签获取值
- `getEnumByValue(value)` - 根据值获取完整配置对象

#### 4. 辅助函数
- `getQuestionTypeShortName(type)` - 获取题目类型简短名称
- `getQuestionTypeIcon(type)` - 获取题目类型图标
- `getQuestionTypeBg(type)` - 获取题目类型背景色

### 使用示例

```typescript
import { 
  QUESTION_TYPE, 
  questionTypeEnumManager, 
  getQuestionTypeShortName,
  getQuestionTypeIcon,
  getQuestionTypeBg 
} from '@repo/core/enums/question';

// 基本使用
const questionType = QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE;

// 获取标签
const label = questionTypeEnumManager.getLabelByValue(questionType); // "单选题"

// 获取简短名称
const shortName = getQuestionTypeShortName(questionType); // "单选"

// 获取图标
const icon = getQuestionTypeIcon(questionType); // "radio_button_checked"

// 获取背景色
const bgColor = getQuestionTypeBg(questionType); // "bg-blue-5"

// 在React组件中使用
const QuestionTypeTag = ({ type }: { type: QUESTION_TYPE }) => (
  <span className={`question-type-tag px-2 py-1 rounded ${getQuestionTypeBg(type)}`}>
    <i className="material-icons question-type-icon">{getQuestionTypeIcon(type)}</i>
    {getQuestionTypeShortName(type)}
  </span>
);
```

### 设计原则

1. **类型安全**: 使用 TypeScript 枚举确保类型安全
2. **一致性**: 遵循项目中其他枚举的命名和结构规范
3. **扩展性**: 通过配置数组可以轻松添加新的属性
4. **复用性**: 提供管理器和辅助函数简化常见操作
5. **语义化**: 提供语义化的class名称，便于查找和样式管理

### 注意事项

1. 枚举值与后端Go代码保持一致
2. 注释掉的`QUESTION_TYPE_CLOZE`类型暂不使用，保留供将来扩展
3. `QUESTION_TYPE_PARENT_CHILD`为特殊类型，用于处理无法明确分类的复合题目
4. 所有辅助函数都提供了默认值处理，避免运行时错误 