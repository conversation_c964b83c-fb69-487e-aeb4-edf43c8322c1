import React from "react";

export const AuthContext = React.createContext<{
  token: string | null,
  schoolId: number | null,
  /**
   * 退出登录
   */
  logout: () => void,
  /**
   * 登录
   */
  login: (payload: {token: string, schoolId: number}) => void,
} | null>(null);

export function useAuth() {
  const context = React.useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within a AuthProvider.");
  }

  return context;
}
