import { getCoursePracticeList } from "@/services/assign";
import { BizTreeDetailData } from "@/types/assign";
import { CoursePracticeItem } from "@/types/assign/course";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useRef } from "react";
import { AssignInitialStateContextType } from "./useAssignInitialState";

export const useBizTreeResourceList = ({
    subject,
    bizTreeId,
    bizTreeNodeId,
    defaultAiCourseIds,
    defaultPracticeIds,
    ready,
    mode,
    bizTreeDetail,
}: {
    subject?: number;
    bizTreeId?: number;
    bizTreeNodeId?: number;
    defaultAiCourseIds?: number[];
    defaultPracticeIds?: number[];
    ready: Signal<boolean>;
    mode: AssignInitialStateContextType["mode"]
    bizTreeDetail?: BizTreeDetailData
}) => {
    const isInitialted = useRef(mode.value === "create")

    useRequest(async () => {
        if (isInitialted.current) {
            return;
        }

        try {
            if (subject === undefined || bizTreeId === undefined || bizTreeNodeId === undefined || !bizTreeDetail) {
                return;
            }

            const res = await getCoursePracticeList({
                subject,
                bizTreeId,
                bizTreeNodeId: bizTreeDetail.bizTreeDetail.bizTreeNodeId,
            });
            for (let i = 0; i < res.length; i++) {
                const item = res[i]
                if (item.aiCourse.id && defaultAiCourseIds?.includes(item.aiCourse.id)) {
                    selectAiCourse(item)
                } else if (item.practice.questionSetId && defaultPracticeIds?.includes(item.practice.questionSetId)) {
                    selectPractice(item)
                }
            }
        } catch (err) {
            console.error(err)
        } finally {
            isInitialted.current = true
        }
    }, {
        ready: isInitialted.current === false && !!bizTreeDetail
    })

    const {
        data: bizTreeResourceList,
        loading: isFetchingBizTreeResourceList
    } = useRequest(async () => {
        if (subject === undefined || bizTreeId === undefined || bizTreeNodeId === undefined) {
            return [];
        }

        return await getCoursePracticeList({
            subject,
            bizTreeId,
            bizTreeNodeId
        });
    }, {
        refreshDeps: [subject, bizTreeId, bizTreeNodeId],
        ready: subject !== undefined && ready.value
    });

    // Note: 这个其实是原始需求问题，其实存的是知识点和下面的资源，而不是单独的资源，要不学生端也对不上
    const selectedAiCourses = useSignal<CoursePracticeItem[]>([]);
    const selectedPractices = useSignal<CoursePracticeItem[]>([]);

    const selectAiCourse = (aiCourse: CoursePracticeItem) => {
        if (selectedAiCourses.value.find((item) => item.aiCourse.id === aiCourse.aiCourse.id)) {
            return;
        }

        selectedAiCourses.value = [...selectedAiCourses.value, aiCourse];
    }

    const selectPractice = (practice: CoursePracticeItem) => {
        if (selectedPractices.value.find((item) => item.practice.questionSetId === practice.practice.questionSetId)) {
            return;
        }
        if (practice.practice.questionSetId) {
            practice.practice.id = practice.practice.questionSetId;
        }

        selectedPractices.value = [...selectedPractices.value, practice];
    }
    const unselectAiCourse = (aiCourse: CoursePracticeItem) => {
        selectedAiCourses.value = selectedAiCourses.value.filter((item) => item.aiCourse.id !== aiCourse.aiCourse.id);
    }
    const unselectPractice = (practice: CoursePracticeItem) => {
        selectedPractices.value = selectedPractices.value.filter((item) => item.practice.questionSetId !== practice.practice.questionSetId);
    }

    const clearSelectedResources = () => {
        selectedAiCourses.value = [];
        selectedPractices.value = [];
    }

    return {
        selectedAiCourses,
        selectedPractices,
        selectAiCourse,
        selectPractice,
        unselectAiCourse,
        unselectPractice,
        clearSelectedResources,

        bizTreeResourceList,
        isFetchingBizTreeResourceList,
    }
}