import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const name = searchParams.get("name");

    if (!name) {
      return NextResponse.json({ error: "缺少文件名参数" }, { status: 400 });
    }

    // 安全检查：只允许特定的文件名
    const allowedFiles = ["user-agreement", "privacy-policy"];
    if (!allowedFiles.includes(name)) {
      return NextResponse.json({ error: "不允许访问该文件" }, { status: 403 });
    }

    const filePath = path.join(process.cwd(), "public/md", `${name}.md`);
    const content = await fs.readFile(filePath, "utf-8");
    
    return new NextResponse(content, {
      status: 200,
      headers: {
        "Content-Type": "text/markdown; charset=utf-8",
      },
    });
  } catch (error) {
    console.error("读取 markdown 文件失败:", error);
    return NextResponse.json(
      { error: "文件不存在或读取失败" },
      { status: 404 }
    );
  }
} 