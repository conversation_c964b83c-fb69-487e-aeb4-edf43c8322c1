import { Feature } from '@/components/TiptapEditor/src/utils/enum';
import { Form } from 'antd';
import React, { memo } from 'react';
import EditableTiptap from '../../../components/EditableTiptap';

interface QuestionStemProps {
  /**
   * 表单字段名称，支持嵌套路径
   * 例如：'questionAnswerMode' 或 [name, 'questionAnswerMode']
   */
  name: string | (string | number)[];
  /**
   * 题目类型
   */
  questionAnswerMode?: number;
  /**
   * 表单字段的其他属性（来自 Form.List 的 restField）
   */
  restField?: any;

  /**
   * 标签文本
   */
  label?: React.ReactNode;

  /**
   * 填空点点击回调
   */
  onQuestionFillAreaClick?: (e: { index: number }) => void;
}

/**
 * 作答方式选择器组件
 * 统一管理所有题目的作答方式选择逻辑
 */
const QuestionStem: React.FC<QuestionStemProps> = ({
  name,
  questionAnswerMode,
  restField,
  label = '',
  onQuestionFillAreaClick,
}) => {
  return (
    <Form.Item
      name={name}
      label={label}
      {...restField}
      rules={[{ required: true, message: '请输入题干' }]}
    >
      <EditableTiptap
        textarea
        excludeFeatures={questionAnswerMode === 5 ? [Feature.insertQs] : []}
        onQuestionFillAreaClick={onQuestionFillAreaClick}
      />
    </Form.Item>
  );
};

export default memo(QuestionStem);
