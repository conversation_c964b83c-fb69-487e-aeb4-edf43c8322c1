"use client";

import { CommentUnderline } from "@repo/core/guide/components/comment-underline";
import { MergedReference } from "@repo/core/types/data/comment";
import { cn } from "@repo/ui/lib/utils";
import "katex/contrib/mhchem";
import "katex/dist/katex.min.css";
import React, { isValidElement, memo, ReactElement } from "react";
import { renderFormulasByKatex } from "../rich-text-view/utils";
import "./style.css";

// 递归处理 ReactNode 的接口
interface ProcessNodeResult {
  nodes: React.ReactNode[];
  charCount: number;
}

type Children =
  | ReactElement<unknown, string>
  | string
  | number
  | bigint
  | Iterable<Children>
  | boolean
  | null
  | undefined;
// 使用 katex 直接渲染替代 better-react-mathjax，解决不能选中问题
export const MathContent = memo(
  ({
    lineId,
    textureId,
    children,
    rootId,
    rootTitle,
    startCharId = 0,
    references,
    onClick,
    isAllUnderlined,
  }: {
    lineId?: string;
    textureId?: string;
    rootId?: string;
    rootTitle?: string;
    children: Children;
    startCharId?: number;
    references?: MergedReference[string][string];
    onClick?: (ref: MergedReference[string][string][number]) => void;
    isAllUnderlined?: boolean;
  }) => {
    // 正则匹配所有公式（块级和行内）
    const formulaRegex =
      /\\\[([\s\S]*?)\\\]|\$\$([\s\S]*?)\$\$|\\\(([\s\S]*?)\\\)|\$([\s\S]*?)\$/g;

    const getRef = (charId: number) => {
      return references?.find((r) => r.end >= charId && r.start <= charId);
    };

    // 处理 DOM 节点中的数学公式
    const processDOMNode = (
      node: Node,
      currentCharId: number
    ): { html: string; charCount: number } => {
      const charId = currentCharId;

      if (node.nodeType === Node.TEXT_NODE) {
        // 文本节点：处理其中的数学公式
        const text = node.textContent || "";
        const processedResult = processTextWithFormulas(text, charId);

        // 将 React 节点转换为 HTML 字符串
        let html = "";
        processedResult.nodes.forEach((reactNode) => {
          if (typeof reactNode === "string") {
            html += reactNode;
          } else if (React.isValidElement(reactNode)) {
            const props = reactNode.props as Record<string, unknown>;
            const tagName = reactNode.type as string;

            // 构建属性字符串
            const attrs = Object.entries(props)
              .filter(
                ([key]) =>
                  key !== "children" && key !== "dangerouslySetInnerHTML"
              )
              .map(([key, value]) => {
                if (key === "className") return `class="${value}"`;
                if (typeof value === "string") return `${key}="${value}"`;
                if (typeof value === "number") return `${key}="${value}"`;
                return "";
              })
              .filter(Boolean)
              .join(" ");

            if (props.dangerouslySetInnerHTML) {
              html += `<${tagName}${attrs ? " " + attrs : ""}>${(props.dangerouslySetInnerHTML as { __html: string }).__html}</${tagName}>`;
            } else {
              const children =
                typeof props.children === "string" ? props.children : "";
              html += `<${tagName}${attrs ? " " + attrs : ""}>${children}</${tagName}>`;
            }
          }
        });

        return { html, charCount: processedResult.charCount };
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 元素节点：递归处理子节点
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();

        // 获取元素的属性
        const attrs = Array.from(element.attributes)
          .map((attr) => `${attr.name}="${attr.value}"`)
          .join(" ");

        // 递归处理子节点
        let childrenHtml = "";
        let totalCharCount = 0;

        for (const child of Array.from(element.childNodes)) {
          const result = processDOMNode(child, charId + totalCharCount);
          childrenHtml += result.html;
          totalCharCount += result.charCount;
        }

        const html = `<${tagName}${attrs ? " " + attrs : ""}>${childrenHtml}</${tagName}>`;
        return { html, charCount: totalCharCount };
      }

      // 其他类型的节点（注释等）直接返回
      return { html: "", charCount: 0 };
    };

    // 处理 dangerouslySetInnerHTML 中的数学公式
    const processDangerouslySetInnerHTML = (
      htmlString: string
    ): { html: string; charCount: number } => {
      // 创建临时 DOM 容器
      const parser = new DOMParser();
      const doc = parser.parseFromString(
        `<div>${htmlString}</div>`,
        "text/html"
      );
      const container = doc.body.firstChild as Element;

      if (!container) {
        return { html: htmlString, charCount: 0 };
      }

      // 递归处理容器中的所有子节点
      let resultHtml = "";
      let totalCharCount = 0;

      for (const child of Array.from(container.childNodes)) {
        const result = processDOMNode(child, totalCharCount);
        resultHtml += result.html;
        totalCharCount += result.charCount;
      }

      return { html: resultHtml, charCount: totalCharCount };
    };

    // 处理包含公式的文本
    const processTextWithFormulas = (
      text: string,
      currentCharId: number
    ): ProcessNodeResult => {
      const result: React.ReactNode[] = [];
      let lastIndex = 0;
      let match;
      let charId = currentCharId;

      // 重置正则表达式的 lastIndex
      formulaRegex.lastIndex = 0;

      while ((match = formulaRegex.exec(text)) !== null) {
        // 公式前的普通文本
        if (match.index > lastIndex) {
          const plainText = text.slice(lastIndex, match.index);
          // 每个字用span包裹
          for (const char of plainText) {
            const reference = getRef(charId);
            result.push(
              <CommentUnderline
                className="math-content-char"
                key={charId}
                lineId={lineId}
                textureId={textureId}
                charId={charId}
                content={char}
                rootId={rootId}
                rootTitle={rootTitle}
                onClick={onClick}
                mergedReference={reference}
                isAllUnderlined={isAllUnderlined}
              >
                {char}
              </CommentUnderline>
            );
            charId++;
          }
        }

        // 公式内容
        const formula = match[1] || match[2] || match[3] || match[4] || "";
        // 判断displayMode
        const displayMode = Boolean(match[1] || match[2]);

        const reference = getRef(charId);

        result.push(
          <CommentUnderline
            className={cn(
              "math-content-formula",
              displayMode && "inline-block"
            )}
            key={charId}
            lineId={lineId}
            textureId={textureId}
            charId={charId}
            content={match[0] ?? formula}
            rootId={rootId}
            rootTitle={rootTitle}
            onClick={onClick}
            mergedReference={reference}
            isAllUnderlined={isAllUnderlined}
          >
            <span
              dangerouslySetInnerHTML={{
                __html: renderFormulasByKatex(match[0] ?? formula.trim()),
              }}
            />
          </CommentUnderline>
        );
        charId++;
        lastIndex = match.index + match[0].length;
      }

      // 末尾剩余普通文本
      if (lastIndex < text.length) {
        const remainingText = text.slice(lastIndex);
        for (const char of remainingText) {
          const reference = getRef(charId);
          result.push(
            <CommentUnderline
              className="math-content-char"
              key={charId}
              lineId={lineId}
              textureId={textureId}
              charId={charId}
              content={char}
              rootId={rootId}
              rootTitle={rootTitle}
              onClick={onClick}
              mergedReference={reference}
              isAllUnderlined={isAllUnderlined}
            >
              {char}
            </CommentUnderline>
          );
          charId++;
        }
      }

      return {
        nodes: result,
        charCount: charId - currentCharId,
      };
    };

    // 递归处理 ReactNode
    const processReactNode = (
      node: React.ReactNode,
      currentCharId: number
    ): ProcessNodeResult => {
      let charId = currentCharId;
      const result: React.ReactNode[] = [];

      // 处理字符串类型
      if (typeof node === "string") {
        const processedText = processTextWithFormulas(node, charId);
        result.push(...processedText.nodes);
        charId += processedText.charCount;
      }
      // 处理数字类型
      else if (typeof node === "number" || typeof node === "bigint") {
        const text = String(node);
        const processedText = processTextWithFormulas(text, charId);
        result.push(...processedText.nodes);
        charId += processedText.charCount;
      }
      // 处理 React 元素
      else if (isValidElement(node)) {
        const element = node as ReactElement;
        // 如果是 MathContent 组件，避免无限递归
        if (typeof element.type !== "string") {
          console.error(
            `MathContent组件不能处理组件实例${element.type.name},因为我无法监控你的组件的渲染状态,请把MathContent包裹在你的html外面`
          );
          result.push(element);
        } else {
          // 对于其他 React 元素，递归处理其子元素
          const nodeProps = element.props as Record<string, unknown>;

          // 检查是否有 dangerouslySetInnerHTML，如果有则不能设置 children
          const hasDangerouslySetInnerHTML =
            nodeProps.dangerouslySetInnerHTML != null;

          let clonedElement;
          if (hasDangerouslySetInnerHTML) {
            // 如果有 dangerouslySetInnerHTML，处理其中的数学公式
            const htmlString = (
              nodeProps.dangerouslySetInnerHTML as { __html: string }
            ).__html;
            const processedResult = processDangerouslySetInnerHTML(htmlString);
            // 使用类型断言来处理 TypeScript 的类型检查
            const props = {
              ...nodeProps,
              key: element.key || `processed-${charId}`,
              dangerouslySetInnerHTML: {
                __html: processedResult.html,
              },
            };

            // 使用类型断言来绕过 TypeScript 的类型检查
            clonedElement = React.cloneElement(
              element,
              props as React.HTMLAttributes<HTMLElement>
            );
            charId += processedResult.charCount;
          } else {
            // 如果没有 dangerouslySetInnerHTML，递归处理子元素
            const childrenResult = processReactNode(
              nodeProps.children as React.ReactNode,
              charId
            );

            // 使用类型断言来处理 TypeScript 的类型检查
            const props = {
              ...nodeProps,
              key: element.key || `processed-${charId}`,
              children: childrenResult.nodes,
            };

            // 使用类型断言来绕过 TypeScript 的类型检查
            clonedElement = React.cloneElement(
              element,
              props as React.HTMLAttributes<HTMLElement>
            );
            charId += childrenResult.charCount;
          }
          result.push(clonedElement);
        }
      }
      // 处理数组类型
      else if (Array.isArray(node)) {
        for (const item of node) {
          const itemResult = processReactNode(item, charId);
          result.push(...itemResult.nodes);
          charId += itemResult.charCount;
        }
      }
      // 处理 null、undefined、boolean 等其他类型
      else if (node != null && typeof node !== "boolean") {
        // 对于其他类型，尝试转换为字符串处理
        const text = String(node);
        const processedText = processTextWithFormulas(text, charId);
        result.push(...processedText.nodes);
        charId += processedText.charCount;
      }

      return {
        nodes: result,
        charCount: charId - currentCharId,
      };
    };

    return (
      <span className="math-content">
        {processReactNode(children, startCharId).nodes}
      </span>
    );
  }
);
MathContent.displayName = "MathContent";
