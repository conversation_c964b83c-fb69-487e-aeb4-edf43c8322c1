import { AvatarDropdown, AvatarName } from '@/components';
import ErrorBoundaryWrapper from '@/components/ErrorBoundary';
import { EditorDefaultConfigProvider } from '@/components/TiptapEditor';
import { uploadQuestionAttachHelper } from '@/services/api/question';
import { UserOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import '@ant-design/v5-patch-for-react-19';
import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { App as AntdApp } from 'antd';
import { MathJaxContext } from 'better-react-mathjax';
import 'mathlive';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import { currentUser as queryCurrentUser } from './services/ant-design-pro/api';
import { logout } from './services/api/user';
import { queryEnumConstants } from './services/global-dict-values/enumConstants';
import { queryRegionDict } from './services/global-dict-values/regionDict';
import { getLoginUrl, parseQueryString, saveTokenFormUrl } from './utils';
import { validateFingerprint } from './utils/fingerprint';
import { checkCurrentLocationPermission } from './utils/menu';

const isDev = process.env.NODE_ENV === 'development';

const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  token?: string;
  globalDictValues?: {
    // phaseSubjects: API.PhaseSubjectType[];
    enumConstants: API.EnumConstantData;
    regionDict: API.RegionListData;
  };
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  // 解析 url 中的 token
  saveTokenFormUrl();

  const fetchUserInfo = async () => {
    const msg = (await queryCurrentUser(
      { platformId: 2 },
      {
        skipErrorHandler: true,
      },
    )) as any;

    try {
      if (msg?.code === 401) {
        history.push(getLoginUrl());
      } else if (msg?.code === 0) {
        return msg.data;
      } else {
        throw new Error(msg.msg);
      }
    } catch (err) {
      console.error(err);
      return undefined;
    }
  };

  const { fp } = parseQueryString(window.location.search);
  const { isValid } = await validateFingerprint(fp || '', { debug: true });

  if (!isValid) {
    logout();
    history.push(getLoginUrl(true, '当前设备与登录设备不一致，请重新登录！'));
    return {
      fetchUserInfo,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }
  const fetchEnumConstants = async () => {
    const enumConstantsData = await queryEnumConstants();
    return enumConstantsData.data;
  };
  const fetchRegionDict = async () => {
    const regionListData = await queryRegionDict();
    return regionListData.data;
  };
  // 如果不是登录页面，执行
  const { location } = history;
  if (![loginPath, '/user/register', '/user/register-result'].includes(location.pathname)) {
    const currentUser = await fetchUserInfo();
    checkCurrentLocationPermission(currentUser?.menus || []);
    const enumConstants = await fetchEnumConstants();
    const regionDict = await fetchRegionDict();

    const colorMap = {
      1: '#52c41a',
      2: '#55baf2',
      3: '#1677ff',
      4: '#ff7a45',
      5: '#f5222d',
    };

    enumConstants.questionDifficultList = enumConstants.questionDifficultList.map((item) => ({
      ...item,
      color: colorMap[item.value],
    }));

    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings as Partial<LayoutSettings>,
      globalDictValues: {
        enumConstants,
        regionDict,
      },
      token: localStorage.getItem('token') || undefined,
    };
  } else {
    // 如果是登录页面，则重定向到远程登录页面
    history.push(getLoginUrl(true));
  }

  return {
    fetchUserInfo,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  const editorDefaultConfig = {
    clickToEdit: true,
    imageUploadHandler: uploadQuestionAttachHelper,
  };
  return {
    // actionsRender: () => [<Question key="doc" />, <SelectLang key="SelectLang" />],
    avatarProps: {
      // src: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      icon: <UserOutlined />,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    // waterMarkProps: {
    //   content: initialState?.currentUser?.name + ' rain',
    // },
    title: '内容管理平台',
    siderWidth: 200,
    // footerRender: () => <Footer />,
    onPageChange: () => {
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser) {
        // history.push(getLoginUrl(true));
      }
    },
    bgLayoutImgList: [
      {
        src: '/img/layoutBg-1.webp',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: '/img/layoutBg-2.webp',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: '/img/layoutBg-3.webp',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    // links: isDev
    //   ? [
    //       <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //         <LinkOutlined />
    //         <span>OpenAPI 文档</span>
    //       </Link>,
    //     ]
    //   : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <AntdApp className="admin-app-wrapper" style={{ height: '100%' }}>
          <EditorDefaultConfigProvider {...editorDefaultConfig}>
            <ErrorBoundaryWrapper>
              <MathJaxContext
                config={{
                  tex: {
                    inlineMath: [
                      ['$', '$'],
                      ['\\(', '\\)'],
                    ], // 行内公式起止符号
                    displayMath: [
                      ['$$', '$$'],
                      ['\\[', '\\]'],
                    ], // 块级公式起止符号
                  },
                  // chtml: {
                  //   fontURL: 'https://video.xiaoluxue.cn/fonts/woff-v2',
                  // },
                }}
                // src={'https://video.xiaoluxue.cn/mathjax/es5/tex-mml-chtml.js'}
                asyncLoad
              >
                {children}
              </MathJaxContext>
            </ErrorBoundaryWrapper>

            {isDev && (
              <SettingDrawer
                disableUrlParams
                enableDarkTheme
                settings={initialState?.settings}
                onSettingChange={(settings) => {
                  setInitialState((preInitialState) => ({
                    ...preInitialState,
                    settings,
                  }));
                }}
              />
            )}
          </EditorDefaultConfigProvider>
        </AntdApp>
      );
    },
    style: {
      height: '100vh',
    },
    contentStyle: {
      flex: 1,
      overflow: 'hidden',
    },
    // menu: {
    //   params: initialState?.currentUser,
    //   request: async (params, defaultMenuData) => {
    //     const menus = params.menus;
    //     const res = await queryMenu(params);
    //     return res.data;
    //   },
    // },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
const env = process.env;
const baseURL = env.NODE_ENV === 'development' ? '' : API_BASE_URL || '';
export const request: RequestConfig = {
  // baseURL: 'https://proapi.azurewebsites.net',
  baseURL,
  ...errorConfig,
};
