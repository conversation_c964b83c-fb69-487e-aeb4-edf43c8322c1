"use client";
import { AutoHeightInput } from "@/app/components/common/auto-height-input";
import { useSendMessageViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import IcBulb from "@/public/icons/bulb.svg";
import IcAudio from "@/public/icons/ic_audio.svg";
import IcClose2 from "@/public/icons/ic_close2.svg";
import IcKeyboard from "@/public/icons/ic_keyboard.svg";
import IcSend from "@/public/icons/icon_send.svg";
import { cn } from "@repo/ui/lib/utils";
import styles from "./send-message.module.css";

export default function SendMessage(props: {
  initialMessage?: string;
  initialInputStatus?: "text" | "voice";
  onSendMessage: (message: string, inputStatus: "text" | "voice") => void;
  disabled?: boolean;
}) {
  const {
    inputStatus,
    voiceText,
    voiceStatus,
    permission,
    voiceToText,
    onTextChange,
    onSwitchMode,
    onStartVoice,
    onStopVoice,
    onCloseVoice,
    onSend,
  } = useSendMessageViewModel(props);

  return (
    <div className="flex w-full flex-[0_0_auto] items-center justify-center px-6 pb-6">
      {inputStatus === "text" ? (
        <div className="flex w-full flex-col gap-3 rounded-xl bg-white px-4 pb-4 pt-1 shadow-md shadow-[rgba(64,43,26,0.03)]">
          <AutoHeightInput
            attr={{
              disabled: props.disabled,
              placeholder: props.disabled
                ? "已达今日对话上限..."
                : "输入你的问题...",
            }}
            className="max-h-25.5 -mx-3"
            value={voiceText}
            onChange={onTextChange}
          />
          <div className="flex flex-[0_0_auto] items-end justify-between">
            <div className="flex items-center text-[10px] font-medium text-[#33302D4D]">
              <IcBulb className="size-3" />
              老师可以看到提问内容，以了解你的学习情况哦
            </div>
            <div className="flex items-center gap-3">
              <button onClick={onSwitchMode}>
                <IcAudio className="size-8" />
              </button>
              <button disabled={props.disabled} onClick={onSend}>
                <IcSend
                  className={cn(
                    "size-8",
                    props.disabled || !voiceText.trim().length
                      ? "text-main-red/70"
                      : "text-main-red"
                  )}
                />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <>
          {voiceStatus === "stop" ? (
            <button
              disabled={props.disabled}
              onClick={onStartVoice}
              className="relative flex h-14 w-full items-center justify-center rounded-xl bg-white px-6 shadow-[0px_4px_20px_0px_rgba(35,42,64,0.02)]"
            >
              <div className="text-text-2 text-base font-normal leading-normal">
                点击开始说话
                {permission && (
                  <div className="text-sm text-red-500">{permission}</div>
                )}
              </div>
              <div
                className="absolute right-4"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onSwitchMode();
                }}
              >
                <IcKeyboard className="h-8 w-8" />
              </div>
            </button>
          ) : (
            <div
              className={`${styles.recording} relative mr-1 flex h-14 w-full items-center justify-center rounded-xl px-6`}
              onClick={onStopVoice}
            >
              <div className="text-text-2 text-base font-normal leading-normal">
                {voiceToText}
              </div>
              <button
                disabled={props.disabled}
                className="absolute right-5 flex h-[22px] w-[22px] items-center justify-center rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onCloseVoice();
                }}
              >
                <IcClose2 />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
