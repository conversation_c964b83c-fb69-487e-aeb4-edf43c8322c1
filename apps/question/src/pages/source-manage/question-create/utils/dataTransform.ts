/**
 * 后端数据转换为表单格式的工具函数
 * 学习 formToBackend.ts 的设计模式，通过 questionAnswerMode 直接处理不同类型的题目
 */

import { getQuestionStemFillPointUuids } from './questionBlank';

/**
 * 生成标准选项（A、B、C、D）
 */
const generateStandardOptions = () => {
  return [
    { optionKey: 'A', optionVal: '' },
    { optionKey: 'B', optionVal: '' },
    { optionKey: 'C', optionVal: '' },
    { optionKey: 'D', optionVal: '' },
  ];
};

/**
 * 将后端选项格式转换为表单格式
 * @param options 后端选项数组 [{ optionKey, optionVal }]
 * @returns 表单选项格式 [{ key, value }]
 */
const transformOptionsToForm = (options: any[]) => {
  return options?.map((option) => ({
    key: option.optionKey,
    value: option.optionVal || '',
  }));
};

/**
 * 提取答案选项键数组
 * @param questionAnswer 答案数据
 * @param rowIndex 行索引
 * @returns 选中的键数组
 */
const extractAnswerKeys = (questionAnswer: any, rowIndex: number = 0): string[] => {
  const answerOptionMatrix = questionAnswer?.answerOptionMatrix;
  if (!answerOptionMatrix?.length || !answerOptionMatrix[rowIndex]) {
    return [];
  }

  return (
    answerOptionMatrix[rowIndex]?.map((ans: any) => ans.optionKey).filter((key: string) => key) ||
    []
  );
};
/**
 * 处理单选题（questionAnswerMode = 0）
 * @returns  综合的选项矩阵
 */
const processComprehensiveQuestion = (): any[] => {
  const items = transformOptionsToForm(generateStandardOptions());
  return [
    {
      singleChoice: {
        selectedKey: 'A',
        items: items,
      },
      multipleChoice: {
        selectedKey: ['A'],
        items: items,
      },
      fillBlank: '',
      judge: '',
      shortAnswer: '',
    },
  ];
};

/**
 * 处理单/多选题（questionAnswerMode = 1、2）
 * @param questionData 题目数据
 * @returns 单/多选题的选项矩阵
 */
const processChoiceQuestion = (questionData: any): any[] => {
  const { questionContent, questionAnswer } = questionData;
  const questionOptions = questionContent?.questionOptionMatrix || [];
  const fillPointNums = getQuestionStemFillPointUuids(questionContent?.questionStem || '').length;
  const questionOptionsLength = questionOptions.length;

  // 没有选项和填空点时，生成默认结构
  if (fillPointNums === 0 && questionOptionsLength === 0) {
    return processComprehensiveQuestion();
  }

  // 填空点小于等于选项数量时，生成选项矩阵
  if (fillPointNums <= questionOptionsLength) {
    return questionOptions
      .map((optionRow: any[], rowIndex: number) => {
        const answerKeys = extractAnswerKeys(questionAnswer, rowIndex);
        const items = transformOptionsToForm(optionRow);

        return {
          singleChoice: {
            selectedKey: answerKeys[0] || 'A',
            items: items,
          },
          multipleChoice: {
            selectedKey: answerKeys.length > 0 ? answerKeys : ['A'],
            items: items,
          },
        };
      })
      .slice(0, fillPointNums ? fillPointNums : 1);
  }
  // 填空点大于选项数量时，生成选项矩阵，并填充空行
  if (fillPointNums > questionOptionsLength) {
    return questionOptions
      .map((optionRow: any[], rowIndex: number) => {
        const answerKeys = extractAnswerKeys(questionAnswer, rowIndex);
        const items = transformOptionsToForm(optionRow);

        return {
          singleChoice: {
            selectedKey: answerKeys[0] || 'A',
            items: items,
          },
          multipleChoice: {
            selectedKey: answerKeys.length > 0 ? answerKeys : ['A'],
            items: items,
          },
        };
      })
      .concat(
        Array.from({ length: fillPointNums - questionOptionsLength }, () => {
          const items = transformOptionsToForm(generateStandardOptions());
          return {
            singleChoice: {
              selectedKey: 'A',
              items: items,
            },
            multipleChoice: {
              selectedKey: ['A'],
              items: items,
            },
          };
        }),
      );
  }

  // 其他情况，生成默认结构,填空题没有插入点时候，要生成一个空行
  return processComprehensiveQuestion();
};

/**
 * 处理填空题（questionAnswerMode = 3）
 * @param questionData 题目数据
 * @returns 填空题的选项矩阵
 */
const processFillBlankQuestion = (questionData: any): any[] => {
  const { questionContent, questionAnswer } = questionData;
  const answerMatrix = questionAnswer?.answerOptionMatrix || [];
  const items = transformOptionsToForm(generateStandardOptions());
  // 计算填空数量

  const fillPointNums = getQuestionStemFillPointUuids(questionContent?.questionStem || '').length;
  const answerMatrixLength = answerMatrix.length;

  // 没有答案矩阵和填空点时，生成默认结构
  if (fillPointNums === 0 && answerMatrixLength === 0) {
    return processComprehensiveQuestion();
  }
  const commonData = {
    singleChoice: {
      selectedKey: 'A',
      items: items,
    },
    multipleChoice: {
      selectedKey: ['A'],
      items: items,
    },
  };
  // 填空点小于等于答案矩阵长度时，生成选项矩阵
  if (fillPointNums !== 0 && fillPointNums <= answerMatrixLength) {
    return answerMatrix
      .map((answerRow: any[]) => {
        const answerValue = answerRow[0]?.optionVal || '';
        return {
          ...commonData,
          fillBlank: answerValue,
        };
      })
      .slice(0, fillPointNums);
  }
  // 填空点大于答案矩阵长度时，生成选项矩阵，并填充空行
  if (fillPointNums > answerMatrixLength) {
    return answerMatrix
      .map((answerRow: any[]) => {
        const answerValue = answerRow[0]?.optionVal || '';
        return {
          ...commonData,
          fillBlank: answerValue,
        };
      })
      .concat(
        Array.from({ length: fillPointNums - answerMatrixLength }, () => {
          return {
            ...commonData,
            fillBlank: '',
          };
        }),
      );
  }

  // 其他情况，生成默认结构,填空题没有插入点时候，要生成一个空行
  return processComprehensiveQuestion();
};

/**
 * 处理判断题（questionAnswerMode = 4）
 * @param questionData 题目数据
 * @returns 判断题的选项矩阵
 */
const processJudgeQuestion = (questionData: any): any[] => {
  const { questionContent, questionAnswer } = questionData;
  const answerMatrix = questionAnswer?.answerOptionMatrix || [];
  const items = transformOptionsToForm(generateStandardOptions());
  const fillPointNums = getQuestionStemFillPointUuids(questionContent?.questionStem || '').length;
  const answerMatrixLength = answerMatrix.length;

  // 没有答案矩阵和填空点时，生成默认结构
  if (fillPointNums === 0 && answerMatrixLength === 0) {
    return processComprehensiveQuestion();
  }
  const commonData = {
    singleChoice: {
      selectedKey: 'A',
      items: items,
    },
    multipleChoice: {
      selectedKey: ['A'],
      items: items,
    },
  };
  // 填空点小于等于答案矩阵长度时，生成选项矩阵
  if (fillPointNums <= answerMatrixLength) {
    return answerMatrix
      .map((answerRow: any[]) => {
        const judgeValue = answerRow[0]?.optionKey;
        return {
          ...commonData,
          judge: judgeValue,
        };
      })
      .slice(0, fillPointNums ? fillPointNums : 1);
  }
  // 填空点大于答案矩阵长度时，生成选项矩阵，并填充空行
  if (fillPointNums > answerMatrixLength) {
    return answerMatrix
      .map((answerRow: any[]) => {
        const judgeValue = answerRow[0]?.optionKey;
        return {
          ...commonData,
          judge: judgeValue,
        };
      })
      .concat(
        Array.from({ length: fillPointNums - answerMatrixLength }, () => {
          return {
            ...commonData,
            judge: '',
          };
        }),
      );
  }

  // 其他情况，生成默认结构
  return processComprehensiveQuestion();
};

/**
 * 处理简答题（questionAnswerMode = 5）
 * @param questionData 题目数据
 * @returns 简答题的选项矩阵
 */
const processShortAnswerQuestion = (questionData: any): any[] => {
  const { questionAnswer, questionContent } = questionData;
  const fillPointNums = getQuestionStemFillPointUuids(questionContent?.questionStem || '').length;
  const answerMatrix = questionAnswer?.answerOptionMatrix || [];
  const answerValue = answerMatrix?.[0]?.[0]?.optionVal || '';
  const items = transformOptionsToForm(generateStandardOptions());
  return [
    {
      singleChoice: {
        selectedKey: 'A',
        items: items,
      },
      multipleChoice: {
        selectedKey: ['A'],
        items: items,
      },
      shortAnswer: fillPointNums > 0 ? '' : answerValue, // 初始化数据时如果存在多个插入点，要生成一个空行
    },
  ];
};

/**
 * 根据题目类型处理选项矩阵
 * @param questionData 题目数据
 * @returns 处理后的选项矩阵
 */
const processQuestionOptionMatrix = (questionData: any): any[] => {
  const { questionAnswerMode } = questionData;

  switch (questionAnswerMode) {
    case 1: // 单选题
    case 2: // 多选题
      return processChoiceQuestion(questionData);

    case 3: // 填空题
      return processFillBlankQuestion(questionData);

    case 4: // 判断题
      return processJudgeQuestion(questionData);

    case 5: // 简答题
      return processShortAnswerQuestion(questionData);

    default:
      // 默认
      return processComprehensiveQuestion();
  }
};

/**
 * 转换单个题目数据为表单格式
 * @param questionData 后端返回的题目数据
 * @returns 表单格式的题目数据
 */
const transformSingleQuestionToForm = (questionData: any): any => {
  if (!questionData) return {};

  const {
    questionId,
    questionAnswerMode,
    questionDifficult,
    questionContent,
    questionExplanation,
    subQuestionList = [],
    baseTreeNodeIds,
    questionYear,
    provinceCode,
    cityCode,
    areaCode,
    labelQuestionType,
  } = questionData;

  // 基础数据结构
  const formData: any = {
    questionStem: questionContent?.questionStem || '',
    questionAnswerMode: questionAnswerMode || 1,
    isMatchLine: !!questionContent?.isMatchLine || false,
    questionOptionMatrix: processQuestionOptionMatrix(questionData),
    subQuestionList,
  };
  const region = [provinceCode, cityCode, areaCode].filter(Boolean);
  // 添加完整字段信息（如果有questionId）
  if (questionId) {
    formData.questionId = questionId;
    formData.questionDifficult = questionDifficult || 1;
    formData.questionExplanation = questionExplanation || '';
    formData.baseTreeNodeIds = baseTreeNodeIds || [];
    formData.questionYear = questionYear ? questionYear : null;
    formData.region = region;
    formData.labelQuestionType = labelQuestionType ? labelQuestionType : null;
  }

  // 递归处理子题目列表
  if (subQuestionList?.length > 0) {
    formData.subQuestionList = subQuestionList.map((subQuestion: any) =>
      transformSingleQuestionToForm(subQuestion),
    );
  }

  return formData;
};

/**
 * 将后端返回的题目数据转换为表单数据格式
 * @param questionData 后端返回的单个题目数据
 * @returns 转换后的表单数据
 */
export const transformQuestionDataToForm = (questionData: any) => {
  return transformSingleQuestionToForm(questionData);
};
