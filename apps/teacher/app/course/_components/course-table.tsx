import TodayIcon from "@/public/icons/ic_today_bold.svg";
import { Schedule } from "@/types";
import { ScrollArea } from "@/ui/scroll-area";
import { umeng, UmengCategory, UmengCourseAction } from "@/utils";
import { cn } from "@/utils/utils";
import { useCreation, useMount } from "ahooks";
import dayjs from "dayjs";
import { motion, useAnimation } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

interface CourseTableProps {
  weekDays: dayjs.Dayjs[];
  scheduleData: Record<string, Schedule[]>;
  timePoints: number[];
  currentDayIndex: number;
  onPrevWeek: () => void;
  onNextWeek: () => void;
}

// 每小时的高度
const HOUR_HEIGHT = 5;

export function CourseTable({
  weekDays,
  scheduleData,
  timePoints,
  currentDayIndex,
  onPrevWeek,
  onNextWeek,
}: CourseTableProps) {
  const router = useRouter();
  // 当前时刻，3 秒自动刷新
  const [currentTime, setCurrentTime] = useState(dayjs());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  // 获取当前时刻的位置
  const currentTimePosition = useCreation(() => {
    const currentHours = currentTime.hour() + currentTime.minute() / 60;
    const firstHour = timePoints[0];

    const positon = (currentHours - firstHour + 1) * HOUR_HEIGHT;

    return positon;
  }, [currentTime, timePoints]);

  const currentTimeLineRef = useRef<HTMLDivElement | null>(null);

  useMount(() => {
    currentTimeLineRef.current?.scrollIntoView({
      block: "center",
      behavior: "instant",
    });
  });

  // 将时分秒转换为小时数
  const timeToHours = (time: string) => {
    const [hours, minutes] = time.split(":").map((num) => parseInt(num, 10));
    return hours + minutes / 60;
  };

  // 计算课程卡片的位置和大小
  const getSchedulePosition = (
    startTime: string,
    endTime: string,
    firstHour: number
  ) => {
    const startHours = timeToHours(startTime);
    const endHours = timeToHours(endTime);

    return {
      top: (startHours - firstHour + 1) * HOUR_HEIGHT,
      height: (endHours - startHours) * HOUR_HEIGHT,
    };
  };

  const jumpToCourseDetail = (schedule: Schedule) => {
    const { classroom_id, is_in_time_range, is_in_class } = schedule;

    umeng.trackEvent(UmengCategory.COURSE, UmengCourseAction.CLASSROOM_CLICK1, {
      classroom_report_clicked: {
        classroom_id,
      },
    });

    umeng.trackEvent(UmengCategory.COURSE, UmengCourseAction.CLASSROOM_CLICK2, {
      classroom_status: is_in_class
        ? "课中ongoing"
        : is_in_time_range
          ? "完课finished"
          : "课前before",
    });

    if (is_in_time_range) {
      router.push(`/course/${classroom_id}`);
    }
  };

  const controls = useAnimation();

  // 只监听横向 drag
  const handleDragEnd = (_: unknown, info: { offset: { x: number } }) => {
    const { x } = info.offset;
    if (x < -50) {
      onNextWeek();
    } else if (x > 50) {
      onPrevWeek();
    }
    controls.start({ x: 0 }); // 拖完回弹
  };

  return (
    <div className="bg-fill-white flex flex-1 flex-col overflow-hidden rounded-2xl">
      {/* 表头部分 */}
      <div className="border-line-1 grid grid-cols-[3.5rem_repeat(7,1fr)] border-b">
        {/* 时间轴表头 */}
        <span />
        {/* 日期表头 */}
        {weekDays.map((date, i) => {
          const isToday = date.isSame(dayjs(), "day");

          return (
            <div
              key={i}
              className={cn(
                "h-15 flex flex-col px-2 pt-3",
                isToday ? "text-primary-1" : "text-gray-3"
              )}
            >
              <span className="text-xs font-medium leading-normal">
                {date.format("周dd")}
              </span>
              <span
                className={cn(
                  "relative -mt-1 text-xl font-extrabold leading-normal",
                  isToday ? "text-primary-1" : "text-gray-1"
                )}
              >
                {date.date().toString().padStart(2, "0")}
                {isToday && (
                  <TodayIcon className="absolute left-[-0.25rem] top-0.5" />
                )}
              </span>
            </div>
          );
        })}
      </div>

      {/* 滚动区域 */}
      <ScrollArea className="flex-1 overflow-hidden">
        <motion.div
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          onDragEnd={handleDragEnd}
          animate={controls}
          className="touch-none"
        >
          <div className="relative grid grid-cols-[3.5rem_1fr]">
            {/* 时间轴单元格 */}
            <div className="border-line-1 flex flex-col border-r">
              {/* 时间轴内容 */}
              {timePoints.map((hour) => (
                <div
                  key={hour}
                  className="flex h-20 items-end justify-end pr-3"
                >
                  <span className="text-gray-3 translate-y-1/2 text-xs leading-normal opacity-80">
                    {`${String(hour).padStart(2, "0")}:00`}
                  </span>
                </div>
              ))}
            </div>

            {/* 课程内容区域 */}
            <div className="flex">
              {weekDays.map((date) => (
                // 星期列
                <div
                  key={date.format("YYYY-MM-DD")}
                  className="relative flex flex-1 flex-col"
                >
                  {/* 背景格子 */}
                  {timePoints.map((hour) => (
                    <div
                      key={`${date.format("YYYY-MM-DD")}-${hour}`}
                      className="border-line-1 h-20 border-b border-r"
                    />
                  ))}

                  {/* 课程卡片 */}
                  {(scheduleData[date.format("YYYY-MM-DD")] || []).map(
                    (schedule) => {
                      const { top, height } = getSchedulePosition(
                        schedule.schedule_tpl_period_start_time,
                        schedule.schedule_tpl_period_end_time,
                        timePoints[0]
                      );

                      const {
                        grade_name,
                        class_name,
                        is_in_time_range,
                        is_in_class,
                        schedule_tpl_period_start_time,
                        schedule_tpl_period_end_time,
                        course_bg_color,
                        course_border_color,
                        course_icon_color,
                        course_text_color,
                        course_abbreviation,
                      } = schedule;

                      const courseName = `${grade_name}${class_name}`;

                      return (
                        <div
                          key={schedule.schedule_id}
                          className={cn(
                            "px-1.25 absolute left-0 right-0 py-0.5"
                          )}
                          style={{
                            top: `${top}rem`,
                            height: `${height}rem`,
                          }}
                        >
                          <div
                            style={
                              is_in_time_range && !is_in_class
                                ? {}
                                : {
                                    backgroundColor: course_bg_color,
                                    borderColor: course_border_color,
                                  }
                            }
                            className={cn(
                              "bg-primary-6 border-primay-4 min-h-full overflow-hidden rounded-md border p-1.5 active:opacity-80",
                              is_in_time_range
                                ? "cursor-pointer"
                                : "cursor-not-allowed"
                            )}
                            onClick={() => {
                              jumpToCourseDetail(schedule);
                            }}
                          >
                            <div className="flex gap-0.5">
                              <div
                                style={
                                  is_in_time_range && !is_in_class
                                    ? {}
                                    : {
                                        color: course_text_color,
                                      }
                                }
                                className={cn(
                                  "line-clamp-2 flex-1 font-medium leading-normal",
                                  "text-gray-3 text-sm"
                                )}
                              >
                                {courseName}
                              </div>

                              {/* 课程简称 */}
                              <span
                                style={{ backgroundColor: course_icon_color }}
                                className="text-xxs rounded-xs h-4 w-4 flex-none text-center font-medium leading-4 text-white"
                              >
                                {course_abbreviation}
                              </span>
                            </div>

                            <div
                              style={
                                is_in_time_range && !is_in_class
                                  ? {}
                                  : {
                                      color: course_text_color,
                                      opacity: 0.8,
                                    }
                              }
                              className={cn(
                                "text-gray-4 mt-0.5 text-xs leading-normal"
                              )}
                            >
                              {schedule_tpl_period_start_time.substring(0, 5)} -{" "}
                              {schedule_tpl_period_end_time.substring(0, 5)}
                            </div>
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
              ))}
            </div>

            {/* 当前时刻线 */}
            <div
              ref={currentTimeLineRef}
              className="z-999 pointer-events-none absolute left-0 right-0 flex -translate-y-1/2 items-center"
              style={{
                top: `${currentTimePosition}rem`,
              }}
            >
              <span className="text-danger-2 opacity-78 w-14 pr-3 text-end text-xs font-extrabold leading-normal">
                {currentTime.format("HH:mm")}
              </span>

              <span className="bg-danger-2 h-px flex-1" />

              {/* 当前星期定位小球 */}
              <div className="absolute left-[3.5rem] right-0 grid grid-cols-7">
                {Array(7)
                  .fill(null)
                  .map((_, index) => (
                    <span
                      key={index}
                      className={cn(
                        "h-1.5 w-1.5 -translate-x-0.5 rounded-full",
                        index === currentDayIndex
                          ? "bg-carmine-2"
                          : "bg-carmine-2/0"
                      )}
                    />
                  ))}
              </div>
            </div>
          </div>
        </motion.div>
      </ScrollArea>
    </div>
  );
}
