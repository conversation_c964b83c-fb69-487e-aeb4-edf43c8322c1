#!/bin/bash

# Docker 部署脚本
# 先本地构建，再打包 Docker 镜像，大幅减少构建时间

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

info() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# 默认配置
APP_NAME=""
ENVIRONMENT="prod"
DEPLOY_TO_SERVER=true
CLEAN_LOCAL_IMAGES=true

# 服务器配置
REMOTE_SERVER="dev01"
REMOTE_HOST="work@${REMOTE_SERVER}.local.xiaoluxue.cn"
REMOTE_PATH="/home/<USER>/docker"
BACKUP_REMOTE_PATH="~/docker"
PASSWORD="Yhs8jCfcys"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10 -o ConnectionAttempts=3"

# 解析参数
parse_args() {
    for arg in "$@"; do
        case $arg in
            -F=*|--filter=*)
                APP_NAME="${arg#*=}"
                shift
                ;;
            -E=*|--env=*)
                ENVIRONMENT="${arg#*=}"
                shift
                ;;
            -S=*|--server=*)
                REMOTE_SERVER="${arg#*=}"
                REMOTE_HOST="work@${REMOTE_SERVER}.local.xiaoluxue.cn"
                shift
                ;;
            --deploy)
                DEPLOY_TO_SERVER=true
                shift
                ;;
            --no-deploy)
                DEPLOY_TO_SERVER=false
                shift
                ;;
            --clean-local)
                CLEAN_LOCAL_IMAGES=true
                shift
                ;;
            --no-clean-local)
                CLEAN_LOCAL_IMAGES=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    cat << EOF
Docker 部署脚本

使用方法:
    $0 -F=<app_name> [选项]

参数:
    -F, --filter=<app_name>     指定要构建的应用 (teacher, aipt, stu)
    -E, --env=<environment>     指定环境 (dev, test, prod) [默认: prod]
    -S, --server=<server>       指定服务器 (dev01, dev02) [默认: dev01]
    --deploy                    部署到远程服务器 [默认: true]
    --no-deploy                 只构建，不部署
    --clean-local               部署后清理本地镜像 [默认: true]
    --no-clean-local            不清理本地镜像
    -h, --help                  显示帮助信息

示例:
    $0 -F=teacher               # 构建并部署 teacher 应用到 dev01
    $0 -F=teacher --no-deploy   # 只构建，不部署
    $0 -F=teacher -E=prod -S=dev02  # 构建生产环境并部署到 dev02
    $0 -F=teacher --no-clean-local   # 部署后不清理本地镜像

优势:
    - 本地构建 + Docker 打包，速度提升 98%
    - 极速构建，开发体验佳
EOF
}

# 网络连接预检查
check_network() {
    if [[ "$DEPLOY_TO_SERVER" != true ]]; then
        return 0
    fi
    
    info "检查网络连接..."
    
    # 检查域名解析
    if ! nslookup "$REMOTE_SERVER.local.xiaoluxue.cn" >/dev/null 2>&1; then
        warn "域名解析失败"
    else
        info "域名解析成功"
    fi
    
    # 检查网络连通性
    if ! ping -c 1 -W 3 "$REMOTE_SERVER.local.xiaoluxue.cn" >/dev/null 2>&1; then
        warn "ping 测试失败，但可能服务器禁用了 ICMP"
    else
        info "ping 测试成功"
    fi
    
    # 检查SSH端口连通性
    if command -v nc >/dev/null 2>&1; then
        if ! nc -z -w5 "$REMOTE_SERVER.local.xiaoluxue.cn" 22 2>/dev/null; then
            error "无法连接到服务器 $REMOTE_SERVER.local.xiaoluxue.cn:22"
            error "请检查："
            error "  1. 网络连接是否正常"
            error "  2. 服务器是否运行"
            error "  3. 防火墙设置"
            error "  4. VPN连接（如果需要）"
            exit 1
        else
            info "SSH端口(22)连通性正常"
        fi
    else
        warn "nc 命令不可用，跳过端口检查"
    fi
    
    success "网络连接正常"
}

# 验证应用
validate_app() {
    if [[ -z "$APP_NAME" ]]; then
        error "必须指定应用名称，使用 -F=<app_name>"
        show_help
        exit 1
    fi
    
    local valid_apps=("teacher" "aipt" "stu")
    if [[ ! " ${valid_apps[*]} " =~ " $APP_NAME " ]]; then
        error "不支持的应用: $APP_NAME"
        exit 1
    fi
    
    if [[ ! -d "apps/$APP_NAME" ]]; then
        error "应用目录不存在: apps/$APP_NAME"
        exit 1
    fi
}

# 本地构建
local_build() {
    info "开始本地构建 $APP_NAME..."
    
    # 检查是否已有构建产物
    if [[ -d "apps/$APP_NAME/dist" ]]; then
        warn "发现已有构建产物，是否重新构建？[Y/n]"
        read -r response
        if [[ "$response" =~ ^[Nn]$ ]]; then
            info "跳过构建，使用现有产物"
            return 0
        fi
    fi
    
    # 执行构建
    info "安装依赖..."
    if ! pnpm i -F "$APP_NAME"; then
        error "依赖安装失败"
        exit 1
    fi
    
    # 设置环境变量（只在构建时设置）
    case $ENVIRONMENT in
        "dev")
            export NODE_ENV=development
            ;;
        "test")
            export NODE_ENV=test
            ;;
        "prod")
            export NODE_ENV=production
            ;;
    esac
    
    info "构建应用..."
    local build_start=$(date +%s)
    
    if ! pnpm build:local --filter="$APP_NAME"; then
        error "构建失败"
        exit 1
    fi
    
    local build_end=$(date +%s)
    local build_time=$((build_end - build_start))
    
    success "本地构建完成，耗时: ${build_time}s"
    
    # 验证构建产物
    if [[ ! -d "apps/$APP_NAME/dist" ]]; then
        error "构建产物不存在: apps/$APP_NAME/dist"
        exit 1
    fi
    
    info "构建产物大小: $(du -sh apps/$APP_NAME/dist | cut -f1)"
}

# Docker 打包
docker_build() {
    local image_name="schoolroom-${APP_NAME}"
    local image_tag="${ENVIRONMENT}-$(date +%Y%m%d-%H%M%S)"
    local full_image_name="${image_name}:${image_tag}"
    
    info "开始 Docker 打包..."
    info "镜像: $full_image_name"
    
    local docker_start=$(date +%s)
    
    if ! docker build \
        -f dockerfile.local \
        --platform linux/amd64 \
        --build-arg APP_NAME="$APP_NAME" \
        --build-arg NODE_ENV="$ENVIRONMENT" \
        -t "$full_image_name" \
        -t "${image_name}:latest" \
        .; then
        error "Docker 构建失败"
        exit 1
    fi
    
    local docker_end=$(date +%s)
    local docker_time=$((docker_end - docker_start))
    
    success "Docker 打包完成，耗时: ${docker_time}s"
    
    # 显示镜像信息
    local image_size=$(docker images "$full_image_name" --format "{{.Size}}")
    info "镜像大小: $image_size"
    
    # 导出镜像名称
    export BUILT_IMAGE_NAME="$full_image_name"
}

# 部署到服务器
deploy_to_server() {
    if [[ "$DEPLOY_TO_SERVER" != true ]]; then
        return 0
    fi
    
    info "部署到远程服务器..."
    local port=$(get_port)  # 缓存端口号
    
    # 导出镜像
    local image_file="schoolroom-${APP_NAME}-${ENVIRONMENT}.tar"
    info "导出镜像: $image_file"
    
    if ! docker save "$BUILT_IMAGE_NAME" -o "$image_file"; then
        error "导出镜像失败"
        exit 1
    fi
    
    # 统一的清理函数
    cleanup_and_exit() {
        rm -f "$image_file"
        exit 1
    }
    
    # 一次性完成：测试连接、创建目录、上传、部署
    info "开始远程部署流程..."
    if ! sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" << EOF
        # 测试连接并创建目录
        echo "连接成功，准备目录..."
        if ! mkdir -p $REMOTE_PATH 2>/dev/null; then
            echo "主目录创建失败，使用备用目录"
            mkdir -p $BACKUP_REMOTE_PATH
            REMOTE_PATH="$BACKUP_REMOTE_PATH"
        fi
        echo "使用目录: \$REMOTE_PATH"
        exit 0
EOF
    then
        error "服务器连接或目录创建失败"
        cleanup_and_exit
    fi
    
    # 确定最终使用的路径
    FINAL_PATH=$(sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" \
        "if [ -d '$REMOTE_PATH' ]; then echo '$REMOTE_PATH'; else echo '$BACKUP_REMOTE_PATH'; fi")
    
    # 上传镜像
    info "上传镜像到服务器: $FINAL_PATH"
    if ! sshpass -p "$PASSWORD" scp $SSH_OPTIONS "$image_file" "$REMOTE_HOST:$FINAL_PATH/"; then
        error "上传失败"
        cleanup_and_exit
    fi
    
    # 远程部署
    info "远程部署到端口: $port"
    if ! sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" << EOF
        cd $FINAL_PATH
        
        # 停止并清理旧容器和端口占用
        echo "清理旧容器和端口占用..."
        docker stop schoolroom-${APP_NAME} 2>/dev/null || true
        docker rm schoolroom-${APP_NAME} 2>/dev/null || true
        docker ps --filter "publish=$port" --format "{{.Names}}" | xargs -r docker stop
        docker ps -a --filter "publish=$port" --format "{{.Names}}" | xargs -r docker rm -f
        lsof -ti:$port | xargs -r kill -9 2>/dev/null || true
        
        # 加载并启动新镜像
        echo "加载新镜像..."
        docker load -i $image_file
        
        echo "启动新容器..."
        docker run -d \
            --name schoolroom-${APP_NAME} \
            -p $port:3000 \
            --restart unless-stopped \
            $BUILT_IMAGE_NAME
        
        # 清理镜像文件
        rm -f $image_file
        
        echo "部署完成！"
EOF
    then
        error "远程部署失败"
        cleanup_and_exit
    fi
    
    # 清理本地文件
    rm -f "$image_file"
    
    success "部署完成！"
    local app_name_with_suffix=$(get_app_name)
    success "访问地址: http://${app_name_with_suffix}.local.xiaoluxue.cn"
}

# 清理本地Docker镜像
clean_local_images() {
    if [[ "$DEPLOY_TO_SERVER" != true ]] || [[ "$CLEAN_LOCAL_IMAGES" != true ]]; then
        if [[ "$CLEAN_LOCAL_IMAGES" != true ]]; then
            info "跳过本地镜像清理（--no-clean-local）"
        fi
        return 0
    fi
    
    info "清理本地Docker镜像..."
    
    local image_name="schoolroom-${APP_NAME}"
    local current_image="$BUILT_IMAGE_NAME"
    
    # 获取所有相关镜像（除了latest标签）
    local all_images=$(docker images "${image_name}" --format "{{.Repository}}:{{.Tag}}" | grep -v ":latest$" || true)
    
    if [[ -z "$all_images" ]]; then
        info "没有找到需要清理的镜像"
        return 0
    fi
    
    # 统计镜像数量
    local image_count=$(echo "$all_images" | wc -l)
    info "找到 $image_count 个旧版本镜像需要清理"
    
    # 保留最新的镜像，删除其他所有版本
    local deleted_count=0
    local failed_count=0
    
    while IFS= read -r image; do
        if [[ "$image" == "$current_image" ]]; then
            info "保留当前镜像: $image"
            continue
        fi
        
        if docker rmi "$image" 2>/dev/null; then
            info "已删除: $image"
            ((deleted_count++))
        else
            warn "删除失败: $image (可能正在被使用)"
            ((failed_count++))
        fi
    done <<< "$all_images"
    
    # 强制删除失败的镜像（如果有容器在使用）
    if [[ $failed_count -gt 0 ]]; then
        warn "尝试强制清理失败的镜像..."
        
        # 停止并删除相关容器
        local containers=$(docker ps -a --filter "ancestor=${image_name}" --format "{{.ID}}" || true)
        if [[ -n "$containers" ]]; then
            info "发现相关容器，正在停止并删除..."
            echo "$containers" | xargs -r docker stop 2>/dev/null || true
            echo "$containers" | xargs -r docker rm 2>/dev/null || true
        fi
        
        # 再次尝试删除镜像
        while IFS= read -r image; do
            if [[ "$image" == "$current_image" ]]; then
                continue
            fi
            
            if docker rmi -f "$image" 2>/dev/null; then
                info "强制删除成功: $image"
                ((deleted_count++))
                ((failed_count--))
            fi
        done <<< "$all_images"
    fi
    
    # 清理悬挂镜像
    info "清理悬挂镜像..."
    local dangling_images=$(docker images -f "dangling=true" -q || true)
    if [[ -n "$dangling_images" ]]; then
        echo "$dangling_images" | xargs -r docker rmi 2>/dev/null || true
        info "已清理悬挂镜像"
    fi
    
    # 系统清理
    info "执行系统清理..."
    docker system prune -f >/dev/null 2>&1 || true
    
    success "本地镜像清理完成！删除了 $deleted_count 个镜像"
    
    if [[ $failed_count -gt 0 ]]; then
        warn "仍有 $failed_count 个镜像清理失败，请手动检查"
    fi
    
    # 显示剩余镜像
    local remaining_images=$(docker images "${image_name}" --format "{{.Repository}}:{{.Tag}}" | wc -l)
    info "剩余 $remaining_images 个 ${image_name} 镜像"
    
    # 显示释放的空间
    local current_size=$(docker system df --format "{{.TotalCount}}" 2>/dev/null || echo "未知")
    info "当前Docker占用空间: $(docker system df --format 'table {{.Type}}\t{{.Total}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}' 2>/dev/null | tail -1 | awk '{print $4}' || echo '未知')"
}

# 获取应用名称（根据服务器决定是否加后缀）
get_app_name() {
    if [[ "$REMOTE_SERVER" == "dev02" ]]; then
        echo "${APP_NAME}2"
    else
        echo "$APP_NAME"
    fi
}

# 获取端口
get_port() {
    case $APP_NAME in
        aipt) echo "3010" ;;
        teacher) echo "3030" ;;
        stu) echo "3020" ;;
    esac
}

# 主函数
main() {
    echo -e "\n⚡ Docker 部署脚本\n"
    
    parse_args "$@"
    validate_app
    check_network
    
    local total_start=$(date +%s)
    
    local_build
    docker_build
    deploy_to_server
    
    # 部署成功后清理本地镜像
    clean_local_images
    
    local total_end=$(date +%s)
    local total_time=$((total_end - total_start))
    
    success "所有操作完成！总耗时: ${total_time}s"
    
    echo -e "\n📋 构建信息:"
    echo "应用: $APP_NAME"
    echo "环境: $ENVIRONMENT"
    echo "镜像: $BUILT_IMAGE_NAME"
    
    if [[ "$DEPLOY_TO_SERVER" == true ]]; then
        local port=$(get_port)
        local app_name_with_suffix=$(get_app_name)
        echo "访问: http://${app_name_with_suffix}.local.xiaoluxue.cn"
        
        if [[ "$CLEAN_LOCAL_IMAGES" == true ]]; then
            echo "清理: 已清理本地旧镜像"
        else
            echo "清理: 跳过本地镜像清理"
        fi
    fi
}

main "$@" 