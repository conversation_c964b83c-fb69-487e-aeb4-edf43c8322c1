import { BizTreeDetailNode, BizTreeListItem } from "@/types/assign";

/**
 * 获取业务树列表处理默认选中的业务树id
 * @param treeList 业务树列表
 * @param treeId 业务树id
 */
export function getDefaultBizTreeId(treeList: BizTreeListItem[], treeId: number | undefined) {
  if (!treeList || treeList.length === 0) {
    return undefined;
  }
  const firstTree = treeList[0];
  if (!treeId) {
    return firstTree.bizTreeId;
  }
  const tree = treeList.find((tree) => tree.bizTreeId === treeId);
  if (!tree) {
    return firstTree.bizTreeId;
  }
  return treeId;
}

export function isPathLowerThan(path1: string, path2?: string) {
  if (!path2) {
    return true;
  }

  const path1Arr = path1.split(".");
  const path2Arr = path2.split(".");

  if (path1Arr.length < path2Arr.length) {
    return true;
  }

  return path1Arr.every((item, index) => {
    return item <= path2Arr[index];
  });
}

export function hasBizTreeNode(tree1: BizTreeDetailNode, isMatched: (node: BizTreeDetailNode) => boolean) {
  function walk(node: BizTreeDetailNode) {
    if (isMatched(node)) {
      return true;
    }
    if (node.bizTreeNodeChildren) {
      return node.bizTreeNodeChildren.some(walk);
    }
    return false;
  }

  return walk(tree1);
}