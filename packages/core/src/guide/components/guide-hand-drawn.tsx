import { DrawElement } from "@repo/core/types/data/widget-guide";
import { Point, path2D } from "@repo/react-sketch-canvas";
import React, { FC, useMemo } from "react";
import { v4 as uuidv4 } from "uuid";
import { useGuideContext } from "../context/guide-context";
// 荧光笔完全显示后延迟2秒后开始淡出
const highlighterDelayTime = 2;
// 荧光笔淡出时间
const highlighterFadeoutTime = 0.3;

// 为每个DrawElement添加随机ID的类型
interface DrawElementWithId extends DrawElement {
  id: string;
  maskId?: string;
}

interface PartFrames {
  inFrame: number;
  outFrame: number;
}

const splitPathWithFrame = (
  paths: Point[],
  frame: number,
  inFrame: number,
  outFrame: number
) => {
  const totalFrames = outFrame - inFrame || 1;
  const currentFrames = frame - inFrame || 1;
  const pointCount = Math.floor(
    (paths?.length ? paths.length : 0) * (currentFrames / totalFrames)
  );
  return paths.slice(0, pointCount);
};

const findPreOutFrame = (
  rightFrame: number,
  leftFrame: number,
  list: DrawElement[]
): number | null => {
  if (list.length === 0) return rightFrame;

  const targetList = list.filter(c => c.outFrame > leftFrame && c.outFrame < rightFrame);
  
  const target = Math.max(...targetList.map(x => x.outFrame));

  return target || null;
};
const processDrawElements = (
  data: DrawElement[],
  fps: number = 30,
  partFrames: PartFrames
): DrawElementWithId[] => {
  let result: DrawElementWithId[] = [];
  const { inFrame: minInFrame, outFrame: maxOutFrame } = partFrames;
  const frameMap = new Map<number, DrawElementWithId[]>();
  const ignoreFrameMap = new Map<number, boolean>();

  data.forEach((item, i) => {
    const newItem: DrawElementWithId = {
      ...item,
      id: item?.id || uuidv4(),
    };

    // 1、maskId 处理逻辑
    result.push(newItem);

    // 2、暂停状态下画的普通画笔路径处理逻辑
    const { inFrame, outFrame, drawMode, endFrame } = newItem;
    // 荧光笔不用处理
    if (endFrame) return;
    // 非暂停状态下画的普通画笔路径不用处理
    if (inFrame !== outFrame) return;
    // 板擦路径，忽略该帧，后边再有该帧下画的也要忽略
    if (!drawMode) {
      ignoreFrameMap.set(inFrame, true);
      // 如果之前存过该暂停下的路径，删除
      if (frameMap.has(inFrame)) {
        frameMap.delete(inFrame);
      }
      return;
    }
    // 如果在忽略帧内，不处理
    if (ignoreFrameMap.has(inFrame)) return;
    // 存入map
    if (frameMap.has(inFrame)) {
      frameMap.get(inFrame)!.push(newItem);
    } else {
      frameMap.set(inFrame, [newItem]);
    }
  });
  for (let [frame, items] of frameMap) {
    const pathsLength = items.reduce(
      (total: number, item: DrawElementWithId) => {
        return total + (item.paths?.length || 0);
      },
      0
    );

    // 固定往前1s，所以是1*fps
    let frameOffset = 1 * fps;
    const preOutFrame =
      findPreOutFrame(frame, frame - frameOffset, data) ||
      frame - frameOffset;
    frameOffset = Math.min(frameOffset, frame - preOutFrame);
    if (frameOffset > 0) {
      const step = pathsLength / frameOffset;
      let preFrame = Math.max(minInFrame, frame - frameOffset);
      items.forEach((item: DrawElementWithId) => {
        item.inFrame = Math.min(preFrame, frame);
        item.outFrame = Math.min(
          frame,
          maxOutFrame,
          preFrame + Math.floor((item.paths?.length || 0) / step)
        );
        preFrame = item.outFrame + 1;
      });
    }
  }

  frameMap.clear();
  ignoreFrameMap.clear();
  return result;
};
// 0709优化：
// 1、增加 memo 相关逻辑，frame 大于 outFrame 不再更新
// 2、svg path 渲染逻辑改用计算当前渲染需要的d内容，而不是计算偏移量
const HandDrawn: FC<{
  data: DrawElement[];
  frame: number;
  partFrames: PartFrames;
}> = ({ data, frame, partFrames }) => {
  const { data: guideWidgetData } = useGuideContext();
  const { avatar } = guideWidgetData;
  const { fps } = avatar;

  // 为每个数据项生成随机ID
  const dataWithIds: DrawElementWithId[] = useMemo(() => {
    return processDrawElements(data, fps, partFrames);
  }, [data, fps, partFrames]);

  const realDataWithIds = dataWithIds.filter((item) => {
    const { endFrame, inFrame, paths } = item;
    if (!paths?.length) return false;
    if (frame < inFrame) return false;
    if (endFrame !== undefined && frame > endFrame) return false;
    return true;
  });

  const eraserPaths = realDataWithIds.filter((path) => !path.drawMode);
  let currentGroup: number = 0;
  const pathGroups = realDataWithIds.reduce<DrawElementWithId[][]>(
    (arrayGroup: DrawElementWithId[][], path: DrawElementWithId) => {
      if (!path.drawMode) {
        currentGroup += 1;
        return arrayGroup;
      }

      // 跳过荧光笔路径，它们会被单独渲染
      if ((path as any).endFrame !== undefined) {
        return arrayGroup;
      }

      if (!arrayGroup[currentGroup]) {
        arrayGroup[currentGroup] = [];
      }

      arrayGroup[currentGroup]!.push(path);
      return arrayGroup;
    },
    [[]]
  );

  // 分离荧光笔路径和普通绘制路径，用于特殊渲染
  const highlighterPaths = realDataWithIds.filter(
    (path) => path.drawMode && (path as any).endFrame !== undefined
  );

  return (
    <div
      data-name="hand-drawn"
      className="z-100 pointer-events-none absolute left-0 top-0 h-full w-full"
    >
      <svg
        width="100%"
        height="100%"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        id="react-sketch-canvas"
        style={{ width: "100%", height: "100%" }}
      >
        {/* 橡皮擦 path 只在 defs 里生成，不直接渲染 */}
        <g id="react-sketch-canvas__eraser-stroke-group" display="none">
          <rect
            id="react-sketch-canvas__mask-background"
            x="0"
            y="0"
            width="100%"
            height="100%"
            fill="white"
          />
          {eraserPaths.map((item) => (
            <EraserPath key={item.id} data={item} frame={frame} />
          ))}
        </g>
        <defs>
          {/* 为每个正常线条生成独立的 mask，只包含该线条绘制时间之后的橡皮擦路径 */}
          {eraserPaths.map((_, i) => (
            <mask
              id={`react-sketch-canvas__eraser-mask-${_.id}`}
              key={`react-sketch-canvas__eraser-mask-${_.id}`}
              maskUnits="userSpaceOnUse"
            >
              <use href={`#react-sketch-canvas__mask-background`} />
              {Array.from(
                { length: eraserPaths.length - i },
                (_, j) => j + i
              ).map((k) => (
                <use
                  key={eraserPaths[k]?.id}
                  href={`#react-sketch-canvas__eraser-${eraserPaths[k]?.id}`}
                />
              ))}
            </mask>
          ))}
        </defs>
        {/* 每条正常线都用自己的 mask 包裹 */}
        {pathGroups.map((pathGroup, i) => (
          <g
            id={`react-sketch-canvas__stroke-group-${i}`}
            key={`react-sketch-canvas__stroke-group-${i}`}
            mask={`url(#react-sketch-canvas__eraser-mask-${eraserPaths[i]?.id || ""})`}
          >
            <NormalLineItems paths={pathGroup} frame={frame} />
          </g>
        ))}
        {/* 荧光笔路径特殊渲染：不受橡皮擦影响 */}
        {highlighterPaths.length > 0 && (
          <g id={`react-sketch-canvas__highlighter-group`}>
            {highlighterPaths.map((path) => (
              <NormalLineItem
                key={`react-sketch-canvas__highlighter-${path.id}`}
                data={path}
                frame={frame}
                fps={fps}
              />
            ))}
          </g>
        )}
      </svg>
    </div>
  );
};

const NormalLineItem: FC<{
  data: DrawElementWithId;
  frame: number;
  fps?: number;
}> = ({ data, frame, fps = 30 }) => {
  const { strokeColor, strokeWidth, id, inFrame, outFrame, endFrame, paths } =
    data;

  const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
  const progressD = path2D(progressPaths);

  let opacity = 1;
  if (endFrame) {
    const startFadeoutFrame = endFrame - highlighterDelayTime * fps;
    if (frame > startFadeoutFrame) {
      opacity = (endFrame - frame) / Math.floor(fps * highlighterFadeoutTime);
    }
  }

  return (
    <path
      id={`react-sketch-canvas__${id}`}
      d={progressD}
      fill="transparent"
      stroke={strokeColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      opacity={opacity}
    />
  );
};

NormalLineItem.displayName = "NormalLineItem";

const NormalLineItems: FC<{ paths: DrawElementWithId[]; frame: number }> = ({
  paths,
  frame,
}) => {
  return (
    <React.Fragment>
      {paths.map((item) => (
        <NormalLineItem key={item.id} data={item} frame={frame} />
      ))}
    </React.Fragment>
  );
};

// 橡皮擦 path 只在 defs 里生成
const EraserPath: FC<{ data: DrawElementWithId; frame: number }> = ({
  data,
  frame,
}) => {
  const { strokeWidth, inFrame, outFrame, id, paths } = data;
  const progressPaths = splitPathWithFrame(paths, frame, inFrame, outFrame);
  const progressD = path2D(progressPaths);
  return (
    <path
      id={`react-sketch-canvas__eraser-${id}`}
      d={progressD}
      fill="none"
      stroke="#000000"
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  );
};

EraserPath.displayName = "EraserPath";

export { HandDrawn };
