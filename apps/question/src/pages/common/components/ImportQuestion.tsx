import ImportQuestionView from '@/components/ImportQuestionView';
import ImportQuestionViewBox from '@/components/ImportQuestionView/ImportQuestionViewBox';
import { formatQuestion } from '@/utils';
import { css } from '@emotion/css';
import { cloneDeep } from 'lodash';
import React, { useMemo } from 'react';
import KnowledgePointSelector from './KnowledgePointSelector';

export type ImportQuestionProps = {
  activeQuestion: API.Review.ImportQuestionAudit['list'][number];
  canEdit: boolean;
  setActiveQuestion: (question: API.Review.ImportQuestionAudit['list'][number]) => void;
};

const ImportQuestion: React.FC<ImportQuestionProps> = ({
  activeQuestion,
  canEdit,
  setActiveQuestion,
}) => {
  const questionData = useMemo(() => {
    const q = formatQuestion({
      ...activeQuestion,
      questionContentPreview: cloneDeep(activeQuestion.questionContent),
      questionAnswerPreview: cloneDeep(activeQuestion.questionAnswer),
    } as unknown as API.QuestionItemType);
    return {
      ...q,
      isMainQuestion: true,
    };
  }, [activeQuestion]);

  return (
    <div className={style}>
      <ImportQuestionView question={questionData} />
      {/* <div key={activeQuestion.questionId} className="section">
        <div className="content">
          <div className="title">题目：</div>
          <RichTextWithMath
            htmlContent={activeQuestion.questionContent.questionStem}
            questionId={activeQuestion.questionId}
          />
        </div>
        <QuestionContent question={activeQuestion} />

        <div>
          <div className="title">解析：</div>
          <RichTextWithMath
            htmlContent={activeQuestion.questionExplanation}
            questionId={activeQuestion.questionId}
          />
        </div>
      </div> */}

      <ImportQuestionViewBox title="知识点" titleIconColor="bg-purple-500" className="mt-4">
        <KnowledgePointSelector
          value={activeQuestion.baseTreeNodeIds || []}
          onChange={(value) => {
            setActiveQuestion({
              ...activeQuestion,
              ...value,
            });
          }}
          canEdit={canEdit}
          baseTreeId={activeQuestion.baseTreeId}
        />
      </ImportQuestionViewBox>

      {/* <div className="section">
        <div className="title">知识点：</div>
        <KnowledgePointSelector
          value={activeQuestion.baseTreeNodeIds || []}
          onChange={(value) => {
            setActiveQuestion({
              ...activeQuestion,
              ...value,
            });
          }}
          canEdit={canEdit}
          baseTreeId={activeQuestion.baseTreeId}
        />
      </div> */}

      {activeQuestion.auditStatus === 3 && (
        <div className="reject-reason">
          {activeQuestion.questionWrong && (
            <>
              <div className="reject-type">题目错误：</div>
              <div className="reject-content">{activeQuestion.questionWrong}</div>
            </>
          )}
          {activeQuestion.answerWrong && (
            <>
              <div className="reject-type">答案错误：</div>
              <div className="reject-content">{activeQuestion.answerWrong}</div>
            </>
          )}
          {activeQuestion.explanationWrong && (
            <>
              <div className="reject-type">解析错误：</div>
              <div className="reject-content">{activeQuestion.explanationWrong}</div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ImportQuestion;

const style = css`
  .section {
    margin-bottom: 24px;
  }

  .title {
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
  }

  .content {
    margin-bottom: 16px;

    .option-item {
      display: flex;
      align-items: start;
      line-height: 2.5;
    }
  }

  .reject-reason {
    margin-bottom: 24px;
    padding: 12px;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 2px;
  }

  .reject-type {
    margin-bottom: 8px;
    color: #ff4d4f;
    font-weight: bold;
  }

  .reject-content {
    color: rgba(0, 0, 0, 0.65);
    & + .reject-type {
      margin-top: 16px;
    }
  }
`;
