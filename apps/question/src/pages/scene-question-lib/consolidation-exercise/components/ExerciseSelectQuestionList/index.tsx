import QuestionList from '@/components/QuestionList';
import { fetchQuestionList } from '@/services/api';
import { useRequest } from '@umijs/max';
import { FC, useEffect } from 'react';

export interface ExerciseSelectQuestionListProps {
  requestParams?: API.QuestionListRequestParams;
  onPageChange?: (page: number, pageSize: number) => void;
}
const ExerciseSelectQuestionList: FC<ExerciseSelectQuestionListProps> = ({
  requestParams,
  onPageChange,
}) => {
  const {
    data: questionListData,
    run: fetchQuestionListRun,
    loading: fetchQuestionListLoading,
  } = useRequest(
    () => {
      if (!requestParams) {
        return Promise.resolve({
          data: {
            total: 0,
            page: 1,
            pageSize: 10,
            list: [],
          },
        });
      }
      return fetchQuestionList(requestParams);
    },
    {
      manual: true,
      throttleInterval: 1000,
    },
  );

  useEffect(() => {
    fetchQuestionListRun();
  }, [requestParams]);

  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    console.log('onQuestionEventHandler: ', question, type);
  };

  return (
    <div>
      <QuestionList
        questions={questionListData?.list ?? []}
        loading={fetchQuestionListLoading}
        pagination={{
          pageSize: questionListData?.pageSize ?? 10,
          total: questionListData?.total ?? 0,
          page: questionListData?.page ?? 1,
        }}
        onPageChange={(page, pageSize) => onPageChange?.(page, pageSize)}
        type="exercise-select"
        onQuestionEventHandler={onQuestionEventHandler}
      />
    </div>
  );
};
export default ExerciseSelectQuestionList;
