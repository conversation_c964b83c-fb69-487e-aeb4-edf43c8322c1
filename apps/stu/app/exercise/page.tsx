"use client";

import { ExerciseView } from "@/app/views/exercise/exercise-view";
import { Suspense, useEffect } from "react";
import { exitLesson, finishLesson, setStatusBar } from "../utils/device";
function ExercisePageContent() {
  useEffect(() => {
    setStatusBar({
      eventType: "setStatusBarVisibility",
      isVisible: false,
    });
  }, []);

  const handleBack = () => {
    exitLesson();
  };

  return (
    <ExerciseView
      onComplete={() => {
        finishLesson();
      }}
      onBack={handleBack}
    />
  );
}

export default function ExercisePage() {
  return (
    <Suspense
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          加载中...
        </div>
      }
    >
      <ExercisePageContent />
    </Suspense>
  );
}
