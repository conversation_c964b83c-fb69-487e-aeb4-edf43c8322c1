"use client";

import IcNoDataIcon from "@repo/core/public/assets/icons/ic_nodata.svg";
import { useRef } from "react";
import QuestionItem from "./QuestionItem";
import { QaContentType, QuestionContext } from "./type";

export interface QuestionListProps {
  /** 题目数据列表 */
  questions: QaContentType[];
  /** 加载状态 */
  loading: boolean;
  /** 点击查看题目详情的回调 */
  onClickView?: (questionId: string) => void;
  /** 自定义底部组件 */
  customFooter?: React.ComponentType<{
    context: Omit<QuestionContext, "options">;
  }>;
  /** 自定义底部按钮 */
  footerButton?: React.ReactNode;
  footerButtonComponent?: React.ComponentType<{ question: QaContentType }>;
  /** 是否显示底部按钮 */
  hasFooterButton?: boolean;
  /** 容器样式类名 */
  className?: string;
  /** 空状态组件 */
  emptyComponent?: React.ReactNode;
  /** 加载状态组件 */
  loadingComponent?: React.ReactNode;
  /** OSS 主机地址 */
  ossHost?: string;
  /** 题目项的额外属性 */
  questionItemProps?: Omit<
    React.ComponentProps<typeof QuestionItem>,
    | "qaContent"
    | "index"
    | "customFooter"
    | "footerButton"
    | "hasFooterButton"
    | "onClickView"
    | "ossHost"
  >;
}

// 默认的空状态组件
const DefaultEmptyComponent = () => (
  <div className="question_list_empty_container flex flex-col items-center justify-center gap-y-2 py-20">
    {/* <div className="w-30 h-30 flex items-center justify-center rounded-lg bg-gray-200">
      <svg
        className="h-12 w-12 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    </div> */}
    <IcNoDataIcon className="w-30 h-30" width={60} height={60} />
    <div className="text-center text-gray-500">暂无题目</div>
  </div>
);

// 默认的加载状态组件
const DefaultLoadingComponent = () => (
  <div className="flex items-center justify-center py-20">
    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
    <span className="ml-2 text-gray-600">加载中...</span>
  </div>
);

export default function QuestionList({
  questions,
  loading,
  onClickView,
  customFooter,
  footerButton,
  footerButtonComponent: FooterButtonComponent,
  hasFooterButton = true,
  className = "px-4",
  emptyComponent,
  loadingComponent,
  ossHost,
  questionItemProps,
}: QuestionListProps) {
  const parentRef = useRef<HTMLDivElement>(null);

  // 如果正在加载，显示加载状态
  if (loading) {
    return loadingComponent || <DefaultLoadingComponent />;
  }

  // 如果没有数据，显示空状态
  if (questions.length === 0) {
    return emptyComponent || <DefaultEmptyComponent />;
  }
  // console.log("questions", questions);
  return (
    <div ref={parentRef} className={`question_list_container ${className}`}>
      <div className="flex flex-col gap-y-4 pb-2">
        {questions.map((qaContent: QaContentType, index: number) => (
          <QuestionItem
            key={`${qaContent.resourceId}_${qaContent.resourceType}_${qaContent.questionId}`}
            qaContent={qaContent}
            index={index}
            customFooter={customFooter}
            footerButton={
              footerButton ||
              (FooterButtonComponent && (
                <FooterButtonComponent question={qaContent} />
              ))
            }
            hasFooterButton={hasFooterButton}
            onClickView={onClickView}
            ossHost={ossHost}
            {...questionItemProps}
          />
        ))}
      </div>
    </div>
  );
}
