# 练习页面 (Exercise Page)

## 概述
练习页面是学生进行各类练习的主要入口，是一个简单的页面容器，负责包装 `ExerciseView` 组件并提供基础的生命周期管理。

## 🏗️ 架构设计

### 组件职责
- **页面容器**：提供Suspense包装和加载状态
- **生命周期管理**：处理练习完成和退出的设备交互
- **简单代理**：将用户操作委托给 `ExerciseView` 处理

### 技术实现

```typescript
function ExercisePageContent() {
  const handleBack = () => {
    exitLesson(); // 直接调用设备退出方法
  };

  return (
    <ExerciseView
      onComplete={() => {
        finishLesson(); // 练习完成时调用设备方法
      }}
      onBack={handleBack} // 退出时的回调
    />
  );
}
```

## 🔄 退出流程架构调整

### 新架构（MVVM架构重构）
```
Page -> ExerciseView -> Context -> ExitViewModel -> Model -> 回调 -> Page -> exitLesson()
```

**优势**：
- **职责分离**：退出逻辑封装在ViewModel层，View层只负责UI
- **架构清晰**：遵循MVVM架构原则，便于维护和测试
- **类型安全**：完整的TypeScript类型定义和推导
- **错误处理**：统一的错误处理机制

### 退出逻辑处理
所有复杂的退出逻辑（包括二次确认弹窗、会话保存等）都已移动到 `ExerciseView` 组件内部处理，页面只需要：

1. 提供简单的 `onBack` 回调
2. 在回调中调用 `exitLesson()` 设备方法
3. `ExerciseView` 内部会根据学习类型自动处理不同的退出流程

## 📁 文件结构

```
apps/stu/app/exercise/
├── page.tsx                 # 主页面文件（简化的容器组件）
├── README.md               # 本文档
└── utils/
    └── device.ts          # 设备交互工具函数
```

## 🔧 依赖组件

- **ExerciseView**：核心练习视图组件（包含完整的退出逻辑）
- **exitLesson()**：设备退出方法
- **finishLesson()**：设备完成方法

## 📊 组件特点

### 简洁性
- 页面代码仅约20行
- 只包含必要的容器逻辑
- 无复杂的状态管理

### 职责分离
- **页面层**：设备交互和生命周期
- **ExerciseView层**：具体的练习逻辑和用户交互

### 易维护性
- 退出逻辑集中管理
- 便于单元测试
- 组件复用性更强

## 🔗 相关文档
- [ExerciseView 组件说明](../views/exercise/README.md)
- [设备交互工具](../utils/device.ts) 