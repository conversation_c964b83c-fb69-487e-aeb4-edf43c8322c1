"use client";

import { cn } from "@repo/ui/lib/utils";
import { useCallback, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

export interface ImagePreviewModalProps {
  currentImage: string | null;
  onClose: () => void;
  className?: string;
}

interface TransformState {
  scale: number;
  translateX: number;
  translateY: number;
}

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  startTranslateX: number;
  startTranslateY: number;
}

interface TouchState {
  isMultiTouch: boolean;
  lastDistance: number;
  lastCenter: { x: number; y: number };
  startTime: number;
  startPos: { x: number; y: number };
}

export const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
  currentImage,
  onClose,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [transform, setTransform] = useState<TransformState>({
    scale: 1,
    translateX: 0,
    translateY: 0,
  });

  const dragStateRef = useRef<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    startTranslateX: 0,
    startTranslateY: 0,
  });

  const touchStateRef = useRef<TouchState>({
    isMultiTouch: false,
    lastDistance: 0,
    lastCenter: { x: 0, y: 0 },
    startTime: 0,
    startPos: { x: 0, y: 0 },
  });

  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 控制显示状态
  useEffect(() => {
    const hasImage = currentImage != null && currentImage.trim() !== "";
    if (hasImage) {
      setIsVisible(true);
      setTransform({ scale: 1, translateX: 0, translateY: 0 });
    } else {
      setIsVisible(false);
    }
  }, [currentImage]);

  // 重置到初始状态
  const resetTransform = useCallback(() => {
    setTransform({ scale: 1, translateX: 0, translateY: 0 });
  }, []);

  // 获取两点间距离
  const getDistance = (touches: TouchList | React.TouchList) => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    if (!touch1 || !touch2) return 0;
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  };

  // 获取中心点
  const getCenter = (touches: TouchList | React.TouchList) => {
    if (touches.length < 2) return { x: 0, y: 0 };
    const touch1 = touches[0];
    const touch2 = touches[1];
    if (!touch1 || !touch2) return { x: 0, y: 0 };
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  };

  // 处理缩放
  const handleScale = useCallback(
    (
      scaleFactor: number,
      centerX?: number,
      centerY?: number,
      forceScreenCenter?: boolean
    ) => {
      setTransform((prev) => {
        const newScale = Math.max(0.5, Math.min(5, prev.scale * scaleFactor));

        // 如果强制使用屏幕中心点或没有提供中心点，使用屏幕中心点作为缩放基点
        if (
          forceScreenCenter ||
          centerX === undefined ||
          centerY === undefined
        ) {
          centerX = 0; // 相对于容器中心的偏移
          centerY = 0;
        }

        // 围绕中心点缩放
        const scaleRatio = newScale / prev.scale;
        const newTranslateX =
          centerX + (prev.translateX - centerX) * scaleRatio;
        const newTranslateY =
          centerY + (prev.translateY - centerY) * scaleRatio;

        return {
          scale: newScale,
          translateX: newTranslateX,
          translateY: newTranslateY,
        };
      });
    },
    []
  );

  // 鼠标滚轮缩放 - 支持遮罩层和图片
  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;

      // 检查图片是否高度小于75vh
      const viewportHeight = window.innerHeight;
      const maxImageHeight = viewportHeight * 0.75; // 75vh

      // 获取图片元素来检查其实际渲染高度
      const imgElement = e.currentTarget.querySelector(
        ".image-preview-modal-image"
      ) as HTMLImageElement;
      const isImageSmallerThan75vh =
        imgElement && imgElement.offsetHeight < maxImageHeight;

      if (isImageSmallerThan75vh) {
        // 图片高度小于75vh时，强制使用屏幕中心点
        handleScale(scaleFactor, undefined, undefined, true);
      } else {
        // 图片高度等于或大于75vh时，使用鼠标位置作为中心点
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = e.clientX - rect.left - rect.width / 2;
        const centerY = e.clientY - rect.top - rect.height / 2;
        handleScale(scaleFactor, centerX, centerY);
      }
    },
    [handleScale]
  );

  // 鼠标拖拽开始
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (transform.scale > 1) {
        dragStateRef.current = {
          isDragging: true,
          startX: e.clientX,
          startY: e.clientY,
          startTranslateX: transform.translateX,
          startTranslateY: transform.translateY,
        };
      }
    },
    [transform.scale, transform.translateX, transform.translateY]
  );

  // 鼠标拖拽移动
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragStateRef.current.isDragging) return;

    e.preventDefault();

    const deltaX = e.clientX - dragStateRef.current.startX;
    const deltaY = e.clientY - dragStateRef.current.startY;

    setTransform((prev) => ({
      ...prev,
      translateX: dragStateRef.current.startTranslateX + deltaX,
      translateY: dragStateRef.current.startTranslateY + deltaY,
    }));
  }, []);

  // 鼠标拖拽结束
  const handleMouseUp = useCallback(() => {
    dragStateRef.current.isDragging = false;
  }, []);

  // 触摸开始
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const touches = e.touches;
      touchStateRef.current.startTime = Date.now();

      if (touches.length === 2) {
        // 双指缩放
        touchStateRef.current.isMultiTouch = true;
        touchStateRef.current.lastDistance = getDistance(touches);
        touchStateRef.current.lastCenter = getCenter(touches);
        dragStateRef.current.isDragging = false;
      } else if (touches.length === 1) {
        // 单指操作
        const touch = touches[0];
        if (touch) {
          touchStateRef.current.isMultiTouch = false;
          touchStateRef.current.startPos = {
            x: touch.clientX,
            y: touch.clientY,
          };

          if (transform.scale > 1) {
            dragStateRef.current = {
              isDragging: true,
              startX: touch.clientX,
              startY: touch.clientY,
              startTranslateX: transform.translateX,
              startTranslateY: transform.translateY,
            };
          }
        }
      }
    },
    [transform.scale, transform.translateX, transform.translateY]
  );

  // 触摸移动
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const touches = e.touches;

      if (touches.length === 2 && touchStateRef.current.isMultiTouch) {
        // 双指缩放
        const distance = getDistance(touches);
        const center = getCenter(touches);

        if (touchStateRef.current.lastDistance > 0) {
          const scaleFactor = distance / touchStateRef.current.lastDistance;

          // 检查图片是否高度小于75vh
          const viewportHeight = window.innerHeight;
          const maxImageHeight = viewportHeight * 0.75; // 75vh

          // 获取图片元素来检查其实际渲染高度
          const imgElement = e.currentTarget.querySelector(
            ".image-preview-modal-image"
          ) as HTMLImageElement;
          const isImageSmallerThan75vh =
            imgElement && imgElement.offsetHeight < maxImageHeight;

          if (isImageSmallerThan75vh) {
            // 图片高度小于75vh时，强制使用屏幕中心点
            handleScale(scaleFactor, undefined, undefined, true);
          } else {
            // 图片高度等于或大于75vh时，使用触摸中心点
            const rect = e.currentTarget.getBoundingClientRect();
            const centerX = center.x - rect.left - rect.width / 2;
            const centerY = center.y - rect.top - rect.height / 2;
            handleScale(scaleFactor, centerX, centerY);
          }
        }

        touchStateRef.current.lastDistance = distance;
        touchStateRef.current.lastCenter = center;
      } else if (
        touches.length === 1 &&
        dragStateRef.current.isDragging &&
        transform.scale > 1
      ) {
        // 单指拖拽
        const touch = touches[0];
        if (touch) {
          const deltaX = touch.clientX - dragStateRef.current.startX;
          const deltaY = touch.clientY - dragStateRef.current.startY;

          setTransform((prev) => ({
            ...prev,
            translateX: dragStateRef.current.startTranslateX + deltaX,
            translateY: dragStateRef.current.startTranslateY + deltaY,
          }));
        }
      }
    },
    [handleScale, transform.scale]
  );

  // 触摸结束
  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (e.touches.length === 0) {
        const endTime = Date.now();
        const touchDuration = endTime - touchStateRef.current.startTime;

        // 检查是否是快速点击
        if (touchDuration < 300 && !touchStateRef.current.isMultiTouch) {
          const changedTouch = e.changedTouches[0];
          if (changedTouch) {
            const deltaX = Math.abs(
              changedTouch.clientX - touchStateRef.current.startPos.x
            );
            const deltaY = Math.abs(
              changedTouch.clientY - touchStateRef.current.startPos.y
            );

            // 如果是短暂点击且位移很小，则关闭预览
            if (deltaX < 10 && deltaY < 10) {
              onClose();
            }
          }
        }

        dragStateRef.current.isDragging = false;
        touchStateRef.current.isMultiTouch = false;
      }
    },
    [onClose]
  );

  // 处理点击关闭
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // 清除之前的计时器
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
        clickTimeoutRef.current = null;
      }

      // 延迟执行关闭，等待可能的双击
      clickTimeoutRef.current = setTimeout(() => {
        onClose();
        clickTimeoutRef.current = null;
      }, 250);
    },
    [onClose]
  );

  // 处理双击重置
  const handleDoubleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // 清除单击计时器
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
        clickTimeoutRef.current = null;
      }

      resetTransform();
    },
    [resetTransform]
  );

  // 键盘事件和全局事件监听
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      } else if (e.key === "+" || e.key === "=") {
        e.preventDefault();
        handleScale(1.1); // 放大，使用屏幕中心点
      } else if (e.key === "-") {
        e.preventDefault();
        handleScale(0.9); // 缩小，使用屏幕中心点
      }
    };

    // 添加全局鼠标事件监听
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    document.addEventListener("keydown", handleKeyDown);

    // 防止页面滚动
    document.body.style.overflow = "hidden";
    document.body.style.touchAction = "none";

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "";
      document.body.style.touchAction = "";

      // 清理计时器
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
        clickTimeoutRef.current = null;
      }
    };
  }, [isVisible, onClose, handleMouseMove, handleMouseUp]);

  if (!isVisible || !currentImage) {
    return null;
  }

  return createPortal(
    <div
      className={cn(
        "image-preview-modal-overlay fixed inset-0 z-[9999] flex select-none items-center justify-center bg-black",
        className
      )}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onWheel={handleWheel}
      onContextMenu={(e) => e.preventDefault()}
      style={{
        pointerEvents: "auto",
        isolation: "isolate",
        touchAction: "none",
      }}
      data-testid="image-preview-modal-overlay"
    >
      <img
        src={currentImage}
        alt=""
        className="image-preview-modal-image max-w-full bg-white object-contain"
        style={{
          height: "75vh",
          transform: `scale(${transform.scale}) translate(${transform.translateX / transform.scale}px, ${transform.translateY / transform.scale}px)`,
          transition: dragStateRef.current.isDragging
            ? "none"
            : "transform 0.2s ease-out",
          cursor:
            transform.scale > 1
              ? dragStateRef.current.isDragging
                ? "grabbing"
                : "grab"
              : "default",
          pointerEvents: "auto", // 允许图片接收事件
        }}
        draggable={false}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onWheel={handleWheel}
        onClick={(e) => {
          e.stopPropagation();
          handleClick(e);
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          handleDoubleClick(e);
        }}
        data-testid="image-preview-modal-image"
      />
    </div>,
    document.body
  );
};

export default ImagePreviewModal;
