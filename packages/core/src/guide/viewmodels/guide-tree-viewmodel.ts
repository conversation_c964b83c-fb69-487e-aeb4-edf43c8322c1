import { Line } from "@repo/core/types/data/widget-guide";
import { useMemo } from "react";
import { v4 as uuidv4 } from "uuid";

export type LineTreeNode = {
  id: string;
  index: number;
  line: Line;
  children: LineTreeNode[];
};

const parseTree = (source: Line[]) => {
  // 提取构建树的逻辑为内部函数，支持递归调用
  const buildTreeNodes = (lines: Line[]) => {
    const result: LineTreeNode[] = [];
    const stack: LineTreeNode[] = [];
    const levelIndexMap = new Map<number, number>();

    for (let i = 0; i < lines.length; i++) {
      const item = lines[i];
      if (!item) continue;
      const { id, level, tag, order } = item;
      const currentIndex = order ?? levelIndexMap.get(level) ?? 0;
      const node: LineTreeNode = {
        id: id ?? uuidv4(),
        index: currentIndex,
        line: item,
        children: [],
      };

      // 处理 block 类型，递归处理其 content
      if (
        tag === "block" &&
        Array.isArray(item.content) &&
        item.content.length > 0
      ) {
        // 检查 content 是否为 Line[] 类型（block 类型的 content 应该是 Line[]）
        const firstContentItem = item.content[0];
        if (
          firstContentItem &&
          typeof firstContentItem === "object" &&
          "tag" in firstContentItem
        ) {
          // content 是 Line[] 类型，递归处理
          const blockChildren = buildTreeNodes(item.content as Line[]);
          node.children.push(...blockChildren);
        }

        // Block节点直接添加到结果中，不参与后续的层级堆栈处理
        if (stack.length === 0) {
          result.push(node);
        } else {
          stack[stack.length - 1]?.children.push(node);
        }

        // Block处理完成后直接continue，不执行后面的堆栈逻辑
        continue;
      }

      if (tag === "ol") {
        levelIndexMap.set(level, currentIndex + 1);
      }

      // 清空堆栈直到找到合适的父节点
      while (
        stack.length > 0 &&
        stack[stack.length - 1] !== undefined &&
        stack[stack.length - 1]?.line.level !== undefined &&
        (stack[stack.length - 1]?.line.level || 0) >= level
      ) {
        stack.pop();
      }

      if (stack.length === 0) {
        // 如果栈为空，说明是顶层节点
        result.push(node);
      } else {
        // 否则添加为当前栈顶节点的子节点
        stack[stack.length - 1]?.children.push(node);
      }
      stack.push(node);

      // 当进入新的层级时，重置该层级以下所有层级的索引
      for (const lv of Array.from(levelIndexMap.keys())) {
        if (lv > level) {
          levelIndexMap.delete(lv);
        }
      }
    }

    return result;
  };

  // 使用内部函数构建树节点
  const result = buildTreeNodes(source);

  const rootLine: Line = {
    tag: "block",
    lineId: uuidv4(),
    level: 0,
    content: source,
    width: "100%",
    inFrame: source[0]?.inFrame ?? 0,
    outFrame: source[source.length - 1]?.outFrame ?? 0,
  };

  const root: LineTreeNode = {
    id: uuidv4(),
    index: 0,
    line: rootLine,
    children: result,
  };

  return root;
};

export const useGuideTreeViewmodel = (source: Line[]) => {
  const tree = useMemo(() => parseTree(source), [source]);

  return tree;
};
