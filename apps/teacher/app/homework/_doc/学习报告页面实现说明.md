# 学习报告页面实现说明

## 1. 概述

学习报告页面是作业系统中的核心功能，用于展示学生在特定作业中的表现数据和统计信息。该页面采用了 Signal 状态管理方案，实现了高效的数据流转和响应式更新。

## 2. 技术架构

### 2.1 状态管理

- 使用 `@preact-signals/safe-react` 提供的 Signal 进行状态管理
- 通过 Context API 实现跨组件的状态共享
- 使用 `useSignalEffect` 监听状态变化并触发副作用

### 2.2 数据流转

```
ReportContext (全局状态)
  ↓
StudyReportPage (页面组件)
  ↓
各子组件 (StudentSection, DataTable 等)
```

## 3. 核心功能模块

### 3.1 报告上下文 (ReportContext)

位置：`apps/tch/app/homework/[id]/context/task-context.tsx`

主要职责：
- 维护全局状态（班级数据、课程列表、加载状态等）
- 提供班级和课程切换功能
- 监听状态变化并触发数据获取

关键状态：
- `classDataSignal`: 当前选中班级的数据
- `courseListSignal`: 可用课程列表
- `loadingSignal`: 加载状态
- `currentClassSignal`: 当前选中的班级

### 3.2 学习报告页面 (StudyReportPage)

位置：`apps/tch/app/homework/[id]/report/page.tsx`

主要职责：
- 展示班级统计数据和学生列表
- 提供学生搜索和筛选功能
- 处理学生详情查看、表扬和提醒等操作

关键状态：
- `homeworkDataSignal`: 作业详情数据
- `searchTermSignal`: 搜索关键词
- `selectedStudentIdSignal`: 选中的学生ID
- `drawerOpenSignal`: 抽屉打开状态

### 3.3 抽屉容器 (DrawerContainer)

位置：`apps/tch/app/homework/[id]/Components/drawer-container.tsx`

主要职责：
- 提供班级和课程选择界面
- 处理班级和课程切换逻辑

关键功能：
- `handleSelectClass`: 处理班级选择
- `handlePreviewCourse`: 处理课程预览

## 4. 数据获取流程

1. 用户选择班级或课程内容
2. `classDataSignal` 状态更新
3. `useSignalEffect` 监听到变化，触发 `fetchHomeworkDetail` 函数
4. 构建请求参数，调用 API 获取数据
5. 更新 `homeworkDataSignal`，UI 自动响应更新

## 5. 优化点

### 5.1 数据处理优化

- 使用共享的数据转换工具函数 (`data-transformers.ts`)
- 避免重复的数据格式化逻辑
- 统一数据结构，提高代码可维护性

### 5.2 性能优化

- 使用 Signal 替代 useState，减少不必要的重渲染
- 按需获取数据，避免冗余请求
- 使用 Map 数据结构优化查找操作

## 6. 使用说明

### 6.1 班级切换

1. 点击顶部导航栏中的"班级"选项
2. 在弹出的抽屉中选择目标班级
3. 系统会自动加载该班级的报告数据

### 6.2 课程内容切换

1. 点击顶部导航栏中的"范围"选项
2. 在弹出的抽屉中选择"课程预览"
3. 系统会加载该课程的相关数据

### 6.3 学生详情查看

1. 在学生列表中点击"查看详情"按钮
2. 系统会打开学生详情抽屉，展示该学生的详细信息

## 7. 后续优化方向

1. 添加更多的数据可视化图表
2. 实现批量操作功能（批量表扬、批量提醒等）
3. 优化移动端适配
4. 添加数据导出功能
5. 实现更细粒度的权限控制
