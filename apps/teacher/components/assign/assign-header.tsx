import { PageHeader } from "../PageHeader";

interface AssignHeaderProps {
  title: string;
  onBack?: () => void;
  className?: string;
}

export function AssignHeader({
  title,
  onBack,
  className = "",
}: AssignHeaderProps) {
  return (
    <PageHeader
      className={`h-17.5 flex items-center gap-3 ${className}`}
      onBack={onBack}
      needBack={!!onBack}
    >
      <h1 className="text-gray-1 truncate whitespace-nowrap text-xl font-medium leading-[normal] tracking-wider">
        {title}
      </h1>
    </PageHeader>
  );
}
