# Model 层测试库使用示例

本文档提供了 `@repo/test-utils/model` 测试库的详细使用示例。

## 📚 目录

- [基础断言测试](#基础断言测试)
- [SWR Hook 测试](#swr-hook-测试)
- [TypeScript 类型测试](#typescript-类型测试)
- [完整的 Model 测试示例](#完整的-model-测试示例)

## 基础断言测试

### 使用增强的 expect 函数

```typescript
import { test, expect } from '@repo/test-utils/model';

// 正向断言
test('基础断言示例', () => {
  expect(1 + 1).toBe(2);
  expect([1, 2, 3]).toEqual([1, 2, 3]);
  expect('hello').toContain('ell');
  expect([1, 2, 3]).toHaveLength(3);
  expect(true).toBeTruthy();
  expect(undefined).toBeUndefined();
});

// 反向断言 (新功能)
test('反向断言示例', () => {
  expect(1 + 1).not.toBe(3);
  expect(() => { return 'success' }).not.toThrow();
  expect(true).not.toBeFalsy();
  expect([1, 2, 3]).not.toContain(4);
  expect('hello').not.toHaveLength(10);
});

// 函数抛出异常测试
test('异常测试示例', () => {
  const throwError = () => { throw new Error('Test error') };
  const noError = () => { return 'success' };
  
  expect(throwError).toThrow();
  expect(throwError).toThrow('Test error');
  expect(noError).not.toThrow();
});
```

## SWR Hook 测试

### 基础 Hook 测试

```typescript
import { renderSWRHook, createMockResponse, waitForSWRState, swrStateCheckers } from '@repo/test-utils/model';
import { vi } from 'vitest';

// Mock fetcher
vi.mock('@repo/lib/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
}));

test('useUsers Hook 测试', async () => {
  const mockUsers = [
    { id: 1, name: 'John', email: '<EMAIL>' },
    { id: 2, name: 'Jane', email: '<EMAIL>' }
  ];

  const { result } = renderSWRHook(
    () => useUsers(),
    {
      mocks: {
        get: createMockResponse(mockUsers)
      }
    }
  );

  // 等待数据加载完成
  await waitForSWRState(result, swrStateCheckers.isLoaded);
  
  expect(result.current.users).toEqual(mockUsers);
  expect(result.current.isLoading).toBe(false);
  expect(result.current.error).toBeUndefined();
});
```

### 错误处理测试

```typescript
import { createMockError, createNetworkError } from '@repo/test-utils/model';

test('useUsers 错误处理测试', async () => {
  const { result } = renderSWRHook(
    () => useUsers(),
    {
      mocks: {
        get: createMockError('Network failed')
      }
    }
  );

  // 等待错误状态
  await waitForSWRState(result, swrStateCheckers.hasError);
  
  expect(result.current.error).toBeDefined();
  expect(result.current.error.message).toContain('Network failed');
  expect(result.current.users).toBeUndefined();
});
```

### 数据提交测试

```typescript
test('useSubmitAnswer Hook 测试', async () => {
  const mockResult = { success: true, id: 123 };

  const { result } = renderSWRHook(
    () => useSubmitAnswer(),
    {
      mocks: {
        post: createMockResponse(mockResult)
      }
    }
  );

  // 执行提交
  await result.current.submitAnswer({ answer: 'A', questionId: 1 });

  expect(result.current.isSubmitting).toBe(false);
  expect(result.current.submitError).toBeUndefined();
});
```

## TypeScript 类型测试

### 类型安全的数据验证

```typescript
import { test, expect } from '@repo/test-utils/model';

interface ApiUser {
  user_id: number;
  user_name: string;
  email: string;
  status: 'active' | 'inactive';
  age?: number;
}

// 类型安全的测试用例
test('should validate user data structure', () => {
  const validUser: ApiUser = {
    user_id: 1,
    user_name: 'John Doe',
    email: '<EMAIL>',
    status: 'active',
    age: 30
  };

  expect(validUser.user_id).toBe(1);
  expect(validUser.user_name).toBe('John Doe');
  expect(validUser.email).toBe('<EMAIL>');
  expect(validUser.status).toBe('active');
  expect(validUser.age).toBe(30);
});

// 边界条件测试
test('should handle edge cases', () => {
  const minAgeUser: ApiUser = {
    user_id: 1,
    user_name: 'Test',
    email: '<EMAIL>',
    status: 'active',
    age: 0
  };

  expect(minAgeUser.age).toBe(0);
});

// 可选字段测试
test('should handle optional fields', () => {
  const userWithoutAge: ApiUser = {
    user_id: 2,
    user_name: 'Jane',
    email: '<EMAIL>',
    status: 'inactive'
  };

  expect(userWithoutAge.age).toBeUndefined();
});
```

## 完整的 Model 测试示例

### 用户模块测试

```typescript
// user-model.test.ts
import { describe, test, expect, vi, beforeEach } from 'vitest';
import { 
  renderSWRHook, 
  createMockResponse, 
  createMockError,
  waitForSWRState,
  swrStateCheckers,
} from '@repo/test-utils/model';
import { useUsers, useCreateUser, useUpdateUser } from './user-model';
import type { ApiUser, CreateUserPayload } from './types';

// Mock fetcher
vi.mock('@repo/lib/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
}));

describe('User Model 测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useUsers Hook', () => {
    test('应该成功获取用户列表', async () => {
      const mockUsers = [
        { user_id: 1, user_name: 'John', email: '<EMAIL>', status: 'active' },
        { user_id: 2, user_name: 'Jane', email: '<EMAIL>', status: 'inactive' }
      ];

      const { result } = renderSWRHook(
        () => useUsers(),
        { mocks: { get: createMockResponse(mockUsers) } }
      );

      await waitForSWRState(result, swrStateCheckers.isLoaded);

      expect(result.current.users).toEqual(mockUsers);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeUndefined();
    });

    test('应该处理网络错误', async () => {
      const { result } = renderSWRHook(
        () => useUsers(),
        { mocks: { get: createMockError('Network Error') } }
      );

      await waitForSWRState(result, swrStateCheckers.hasError);

      expect(result.current.error).toBeDefined();
      expect(result.current.users).toBeUndefined();
    });
  });

  describe('useCreateUser Hook', () => {
    test('应该成功创建用户', async () => {
      const newUser = { name: 'New User', email: '<EMAIL>', role: 'user' as const };
      const mockResponse = { user_id: 3, user_name: 'New User', email: '<EMAIL>', status: 'active' };

      const { result } = renderSWRHook(
        () => useCreateUser(),
        { mocks: { post: createMockResponse(mockResponse) } }
      );

      await result.current.createUser(newUser);

      expect(result.current.isCreating).toBe(false);
      expect(result.current.createError).toBeUndefined();
    });
  });
});

// 类型测试
describe('User Types', () => {
  test('should validate user data structure', () => {
    const validUser: ApiUser = {
      user_id: 1,
      user_name: 'John',
      email: '<EMAIL>',
      status: 'active'
    };

    expect(validUser.user_id).toBe(1);
    expect(validUser.user_name).toBe('John');
    expect(validUser.email).toBe('<EMAIL>');
    expect(validUser.status).toBe('active');
  });
});
```

### 数据转换函数测试

```typescript
// transformers.test.ts
import { test, expect } from '@repo/test-utils/model';
import { transformUsersData } from './transformers';

describe('User Transformers', () => {
  test('transformUsersData 应该正确转换数据', () => {
    const apiData = [
      { user_id: 1, user_name: 'John', status: 'active' },
      { user_id: 2, user_name: 'Jane', status: 'inactive' }
    ];

    const result = transformUsersData(apiData);

    expect(result).toEqual([
      { id: 1, name: 'John', isActive: true },
      { id: 2, name: 'Jane', isActive: false }
    ]);
  });

  test('transformUsersData 应该处理空数组', () => {
    expect(transformUsersData([])).toEqual([]);
  });

  test('transformUsersData 应该拒绝无效数据', () => {
    expect(() => transformUsersData(null)).toThrow();
    expect(() => transformUsersData([{ invalid: 'data' }])).toThrow();
  });
});
```

## 🎯 最佳实践

### 1. 测试组织

```typescript
describe('ModuleName Model', () => {
  describe('Hooks', () => {
    // Hook 测试
  });

  describe('Types', () => {
    // 类型安全测试
  });

  describe('Transformers', () => {
    // 转换函数测试
  });
});
```

### 2. Mock 管理

```typescript
// 在每个测试文件顶部
vi.mock('@repo/lib/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
}));

// 在 beforeEach 中清理
beforeEach(() => {
  vi.clearAllMocks();
});
```

### 3. 错误测试

```typescript
// 总是测试错误情况
test('应该处理 API 错误', async () => {
  const { result } = renderSWRHook(
    () => useData(),
    { mocks: { get: createMockError('API Error') } }
  );

  await waitForSWRState(result, swrStateCheckers.hasError);
  expect(result.current.error).toBeDefined();
});
```

### 4. 边界条件测试

```typescript
// 测试边界条件
test('应该处理空数据', () => {
  expect(transformData([])).toEqual([]);
  expect(transformData(null)).toThrow();
  expect(transformData(undefined)).toThrow();
});
```

## 🔧 调试技巧

### 1. 使用 console.log 调试状态

```typescript
test('调试 Hook 状态', async () => {
  const { result } = renderSWRHook(() => useData());
  
  console.log('Initial state:', result.current);
  
  await waitForSWRState(result, state => {
    console.log('Current state:', state);
    return !state.isLoading;
  });
});
```

### 2. 检查 Mock 调用

```typescript
test('检查 Mock 调用', async () => {
  const mockGet = vi.fn().mockResolvedValue(mockData);
  
  const { result } = renderSWRHook(
    () => useData(),
    { mocks: { get: mockGet } }
  );

  await waitForSWRState(result, swrStateCheckers.isLoaded);

  expect(mockGet).toHaveBeenCalledWith('/api/data', {});
  expect(mockGet).toHaveBeenCalledTimes(1);
});
```

### 3. 使用 test.only 隔离测试

```typescript
test.only('只运行这个测试', () => {
  // 调试特定测试
});
```

---

**更多信息请参考：**
- [测试规范文档](.cursor/rules/test/test-model-guide.mdc)
- [Vitest 官方文档](https://vitest.dev/)
- [Testing Library 文档](https://testing-library.com/)