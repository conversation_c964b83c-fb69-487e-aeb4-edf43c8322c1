import { CLASS_HOUR, CLASS_HOUR_DEVIATION } from "@/configs/assign";
import {
  format,
  isToday,
  isYesterday,
  isThisYear,
  isSameWeek,
  subDays,
  getDay,
  startOfWeek,
  isAfter,
  isBefore,
  addDays,
  endOfDay,
} from "date-fns";
// import { zhCN } from "date-fns/locale";

export function isSameDay(date1: Date, date2: Date) {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

export function formatPublishTime(timestamp: number | Date) {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  const startOfThisWeek = startOfWeek(now, { weekStartsOn: 1 }); // 本周一
  const startOfLastWeek = subDays(startOfThisWeek, 7); // 上周一

  if (isToday(date)) {
    return `今天${format(date, "HH:mm")}发布`;
  } else if (isYesterday(date)) {
    return "昨天发布";
  } else if (isSameDay(subDays(now, 2), date)) {
    return "前天发布";
  } else if (isSameWeek(date, now, { weekStartsOn: 1 })) {
    // 先判断本周（周一至周日，排除今天、昨天、前天）
    const dayOfWeek = getDay(date);
    const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return `本${weekDays[dayOfWeek]}发布`;
  } else if (
    isAfter(date, startOfLastWeek) &&
    isBefore(date, startOfThisWeek)
  ) {
    // 再判断上周（上周一至上周日）
    const dayOfWeek = getDay(date);
    const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return `上${weekDays[dayOfWeek]}发布`;
  } else if (isThisYear(date)) {
    return `${format(date, "M月d日")}发布`;
  } else {
    return `${format(date, "yyyy年M月d日")}发布`;
  }
}

export function minutesToString(totalMinutes: number): string {
  if (totalMinutes < 0) {
      return "0分钟"; // 或者可以抛出错误
  }

  const days = Math.floor(totalMinutes / (24 * 60));
  const remainingMinutes = totalMinutes % (24 * 60);
  const hours = Math.floor(remainingMinutes / 60);
  // 避免出现0.0166666...的情况
  const minutes = Math.floor(remainingMinutes % 60)

  const result = [];

  if (days > 0) {
      result.push(`${days}天`);
      if (hours > 0) {
          result.push(`${hours}小时`);
      }
      if (minutes > 0) {
          result.push(`${minutes}分钟`);
      }
  } else if (hours > 0) {
      result.push(`${hours}小时`);
      if (minutes > 0) {
          result.push(`${minutes}分钟`);
      }
  } else {
      result.push(`${minutes}分钟`);
  }

  return result.join('');
}

/**
 * 计算课时
 * @param totalMinutes 总分钟数
 * @param classDuration 课时时长
 * @param deviation 偏差
 * @returns 课时数
 */
export function calculateClassTime(
  totalMinutes: number,
  classDuration: number = CLASS_HOUR,
  deviation: number = CLASS_HOUR_DEVIATION
): number {
  if (totalMinutes <= 0) {
    return 0;
  }

  const exactClasses = totalMinutes / classDuration;
  const remainder = totalMinutes % classDuration;

  // 情况1：刚好整除
  if (remainder === 0) {
    return exactClasses;
  }

  // 情况2：小于课时时长+偏差
  if (remainder <= deviation) {
    return Math.floor(exactClasses);
  }

  // 情况3：大于课时时长+偏差，向上取整
  return Math.ceil(exactClasses);
}

export function calculateClassTimeStr(
  totalMinutes: number,
  classDuration: number = CLASS_HOUR,
  deviation: number = CLASS_HOUR_DEVIATION
): string {
  if (totalMinutes <= 0) {
    return "0课时";
  }

  const exactClasses = totalMinutes / classDuration;
  const remainder = totalMinutes % classDuration;

  // 情况1：刚好整除
  if (remainder === 0) {
    return `${exactClasses}课时`;
  }

  // 情况2：小于课时时长+偏差
  if (remainder <= deviation) {
    return `约${Math.floor(exactClasses)}课时`;
  }

  // 情况3：大于课时时长+偏差，向上取整
  return `约${Math.ceil(exactClasses)}课时`;
}

/**
 * 获取n天后的23:59:59时间戳
 * @param timestamp 时间戳
 * @param n 天数
 * @returns 时间戳
 */
export function getEndOfDayAfter(timestamp: number, n: number): number {
  const date = new Date(timestamp);
  const targetDate = addDays(date, n); // 添加 n 天
  const endOfTargetDay = endOfDay(targetDate); // 获取那天的 23:59:59
  return endOfTargetDay.getTime(); // 返回时间戳
}

export function getImmediatePublishTimestamp(delta: number = 10 * 60 * 1000): number {
  return Date.now() + delta;
}

export function getFormatTimeStr(timestamp: number, formatStr: string = "yyyy-MM-dd HH:mm"): string {
  const date = new Date(timestamp);
  return format(date, formatStr);
}

