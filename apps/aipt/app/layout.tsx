import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "AI生产工具",
  description: "AI生产工具",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/mathjax/output/chtml/fonts/woff-v2/MathJax_Zero.woff"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/mathjax/output/chtml/fonts/woff-v2/MathJax_Math-BoldItalic.woff"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/mathjax/output/chtml/fonts/woff-v2/MathJax_Math-Italic.woff"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/mathjax/output/chtml/fonts/woff-v2/MathJax_Main-Regular.woff"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
      </head>
      <body>{children}</body>
    </html>
  );
}
