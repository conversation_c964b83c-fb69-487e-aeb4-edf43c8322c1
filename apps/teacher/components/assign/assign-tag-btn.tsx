import { cn } from "@/utils/utils";

interface AssignTagBtnProps {
  style?: React.CSSProperties;
  content: string;
  className?: string;
}

export function AssignTagBtn({
  content,
  style = {},
  className = "",
}: AssignTagBtnProps) {
  return (
    <span
      className={cn(
        "text-xs h-4.5 leading-4.5 mr-1 inline-block rounded bg-orange-1 px-1 text-center font-medium text-white",
        className
      )}
      style={style}
    >
      {content}
    </span>
  );
}
