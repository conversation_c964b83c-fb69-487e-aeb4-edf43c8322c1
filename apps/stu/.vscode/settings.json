{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "cSpell.words": [
    "Banshu",
    "bron",
    "chtml",
    "conver",
    "Doesnt",
    "dolli",
    "heroui",
    "infty",
    "jsbridge",
    "jsons",
    "katex",
    "Ketex",
    "mathbb",
    "mathjax",
    "mathrm",
    "Neuwelt",
    "Offthread",
    "umeng",
    "xiaoluxue"
  ],
}