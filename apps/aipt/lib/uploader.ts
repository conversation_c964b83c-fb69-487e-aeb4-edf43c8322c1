import TTUploader, { STSToken, VodFileOption } from "tt-uploader";
import { get as courseCenterFetcherGet } from "./course-center-fetcher";
import { get } from "./fetcher";
import { getFilenameFromUrl } from "./utils";

const uploadToVolcengine = (
  file: File,
  stsToken: STSToken,
  fileName: string,
  objectKey: string
) => {
  // const urlObj = new URL(url);
  // // 获取域名后的内容（即 pathname，不包括域名和查询参数）
  // let pathAfterDomain = urlObj.pathname;
  // pathAfterDomain = pathAfterDomain.substring(1);

  const uploader = new TTUploader({
    userId: "2103571453", // 建议设置能识别用户的唯一标识 ID，用于上传出错时排查问题，不要传入非 ASCII 编码
    appId: 797888, // 在视频点播控制台应用管理页面创建的应用的 AppID。视频点播的质量监控等都是以这个参数来区分业务方的，务必正确填写
    // 仅视频/普通文件上传时需要配置
    videoConfig: {
      spaceName: "video-widget", //在视频点播控制台创建的空间的名称
    },
  });

  const addFileParams: VodFileOption = {
    file: file,
    fileName: objectKey,
    stsToken,
    type: "video", // 视频须为 video
  };
  console.log("volcengine上传stsToken", stsToken);
  console.log("volcengine调addFile参数", addFileParams);

  const fileKey = uploader.addFile(addFileParams);

  uploader.start(fileKey);

  return new Promise((resolve, reject) => {
    uploader.on("complete", (infor) => {
      console.log("complete");
      console.log(infor.uploadResult);
      resolve(infor.uploadResult);
    });

    uploader.on("error", (infor) => {
      console.log("volcengine上传失败：", infor.extra);
      reject(infor.extra);
    });

    uploader.on("progress", (infor) => {
      console.log(infor.percent);
    });
  });
};

export async function uploadFileToOss(
  fileName: string,
  data: any,
  file: File,
  ossTokenRes?: any
) {
  console.log(fileName, data, file, ossTokenRes);
  const formData = new FormData();
  formData.append("success_action_status", "200");
  formData.append("policy", data.policy);
  formData.append("x-oss-signature", data["x-oss-signature"]);
  formData.append("x-oss-signature-version", data["x-oss-signature-version"]);
  formData.append("x-oss-credential", data["x-oss-credential"]);
  formData.append("x-oss-date", data["x-oss-date"]);
  formData.append("key", data.dir + "/" + ossTokenRes.fileName); // 文件名
  formData.append("x-oss-security-token", data["x-oss-security-token"]);
  formData.append("file", file as unknown as File); // file 必须为最后一个表单域

  const res = await fetch(data.host, {
    method: "POST",
    body: formData,
  });

  if (!res.ok) {
    return {
      code: 1,
      data: null,
    };
  }
  return {
    code: 0,
    data: res,
  };
}

export async function getUploadToken(
  fileName: string,
  type: string,
  extraParams?: Record<string, any>,
  needToVolcengine: boolean = false
) {
  const query = {
    type,
    fileName,
    needVod: needToVolcengine ? "1" : "0",
    ...extraParams,
  };
  console.log("📤 getUploadToken 调用参数:", query);

  const res: any = await get("/api/v1/upload/token", {
    query,
  });
  return {
    token: res.formData.policyToken,
    url: res.url,
    fileName: getFilenameFromUrl(res.url),
    vodPresigned: res.vodStsToken,
    objectKey: res.objectKey,
  };
}

//  课程管理的token 获取
export async function getUploadToken2(fileName: string) {
  const res: any = await courseCenterFetcherGet("/api/v1/common/oss/token", {
    query: { type: "1", fileName },
  });
  return {
    ...res,
    token: res.formData.policyToken,
    url: res.url,
  };
}

export async function uploadFile(
  file: File,
  type: string = "1",
  extraParams?: Record<string, any>,
  needToVolcengine: boolean = false
) {
  let fileName = file.name;
  if (fileName.includes("+")) {
    fileName = fileName.replace(/\+/g, "");
  }
  const ossTokenRes = await getUploadToken(
    fileName,
    type,
    extraParams,
    needToVolcengine
  );
  const { token, url, objectKey } = ossTokenRes;
  const uploadToOss = () => uploadFileToOss(fileName, token, file, ossTokenRes);
  const uploadToVod = () =>
    uploadToVolcengine(file, ossTokenRes.vodPresigned, fileName, objectKey);
  let volcengineRes;
  if (needToVolcengine) {
    // volcengineRes = await uploadToVolcengine(
    //   file,
    //   ossTokenRes.vodPresigned,
    //   fileName,
    //   objectKey
    // );
    const res = await Promise.all([uploadToVod(), uploadToOss()]);
    volcengineRes = res[0];
  } else {
    await uploadToOss();
  }
  return {
    url,
    fileName,
    volcengine: volcengineRes,
  };
}

export async function uploadFile2(file: File) {
  let fileName = file.name;
  if (fileName.includes("+")) {
    fileName = fileName.replace(/\+/g, "");
  }
  const ossTokenRes = await getUploadToken2(fileName);
  const { token, url } = ossTokenRes;
  await uploadFileToOss(fileName, token, file, ossTokenRes);
  return {
    url,
    fileName,
  };
}
