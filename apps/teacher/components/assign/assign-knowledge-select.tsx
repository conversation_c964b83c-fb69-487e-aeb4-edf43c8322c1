import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { AlertDialog } from "@/ui/alertDialog";
import { ScrollArea } from "@/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { cn } from "@/utils/utils";
import { batch, useComputed } from "@preact-signals/safe-react";
import { Skeleton } from "antd";
import { DataNode } from "antd/es/tree";
import { Key, useMemo, useRef, useState } from "react";
import { AssignTree } from "./assign-tree";

interface AssignKnowledgeSelectProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignKnowledgeSelect({
  style = {},
  className = "",
}: AssignKnowledgeSelectProps) {
  const {
    bizTreeList,
    treeId,
    setTreeId,
    bizTreeDetail,
    isFetchingBizTreeDetail,
    bizTreeNodeId,
    bizTreeNode,
    isFetchingBizTreeList,
    bizTreeNodeMap,

    selectedAiCourses,
    selectedPractices,
    clearSelectedResources,
  } = useAssignCourseContext();
  const treeData = useMemo(() => {
    if (bizTreeDetail) {
      return bizTreeDetail.bizTreeDetail.bizTreeNodeChildren;
    }
    return [];
  }, [bizTreeDetail]);

  const [showCancelAlert, setShowCancelAlert] = useState(false);
  const onOk = useRef<() => void>(() => {});

  const handleTypeChange = (value: string) => {
    const newVal = Number(value);
    if (newVal === treeId.value) {
      return;
    }

    if (
      selectedAiCourses.value.length > 0 ||
      selectedPractices.value.length > 0
    ) {
      setShowCancelAlert(true);
      onOk.current = () => {
        setTreeId(newVal);
        bizTreeNodeId.value = undefined;
      };
      return;
    }

    setTreeId(newVal);
    bizTreeNodeId.value = undefined;
  };

  // TODO: 转成Action
  const onSelectTreeNode = (
    keys: Key[] // 当前选中的节点，如果是多选，才会有多个
  ) => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_COURSE_SETUP_CHAPTER_CLICK,
      {}
    );

    batch(() => {
      if (keys.length) {
        bizTreeNodeId.value = Number(keys[0]);
      } else {
        bizTreeNodeId.value = bizTreeDetail?.bizTreeDetail.bizTreeNodeId;
      }
    });
  };

  const expendsKeys = useComputed(() => {
    if (!bizTreeNode.value) {
      return [];
    }
    let cur = bizTreeNode.value;
    const res: number[] = [];
    while (cur.parentNodeId) {
      res.unshift(cur.parentNodeId);
      const parent = bizTreeNodeMap.value.get(cur.parentNodeId);
      if (!parent) {
        break;
      }
      cur = parent;
    }

    return res;
  });

  return (
    <div
      className={cn(
        "flex h-full w-full flex-col gap-3 overflow-hidden",
        className
      )}
      style={style}
    >
      <Select value={String(treeId.value)} onValueChange={handleTypeChange}>
        <SelectTrigger
          className="text-gray-2 group w-full cursor-pointer rounded-md bg-white transition-colors hover:bg-[#f5f7fa] focus-visible:ring-0"
          classNames={{
            icon: "text-gray-3 group-data-[state=open]:rotate-180",
          }}
        >
          <SelectValue placeholder="选择类型" />
        </SelectTrigger>
        <SelectContent className="bg-white">
          {isFetchingBizTreeList ? (
            <Skeleton key="loading" active className="h-full" />
          ) : (
            bizTreeList?.map((tree) => (
              <SelectItem key={tree.bizTreeId} value={String(tree.bizTreeId)}>
                {tree.bizTreeName}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {isFetchingBizTreeDetail ? (
        <Skeleton key="loading" active className="h-full" />
      ) : (
        <ScrollArea className="h-full" orientation="vertical">
          <AssignTree
            className="mt-2"
            // 吓人，什么东西？
            treeData={treeData as unknown as DataNode[]}
            fieldNames={{
              title: "bizTreeNodeName",
              key: "bizTreeNodeId",
              children: "bizTreeNodeChildren",
            }}
            selectedKeys={[bizTreeNodeId.value ?? 0]}
            defaultExpandParent
            defaultExpandedKeys={expendsKeys.value}
            defaultSelectedKeys={[bizTreeNodeId.value ?? 0]}
            onSelect={onSelectTreeNode}
          />
        </ScrollArea>
      )}

      <AlertDialog
        open={showCancelAlert}
        title="确定切换教材吗？"
        description="切换教材后将清空已选课程。是否继续？"
        onCancel={() => setShowCancelAlert(false)}
        onOk={() => {
          setShowCancelAlert(false);
          clearSelectedResources();
          onOk.current?.();
        }}
        variant="warning"
        className={cn("", className)}
        style={style}
      />
    </div>
  );
}
