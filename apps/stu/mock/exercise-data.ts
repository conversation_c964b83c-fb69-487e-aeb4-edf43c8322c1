// 反馈类型
export enum FeedbackType {
  CORRECT = "correct", // 正确
  INCORRECT = "incorrect", // 错误
  PARTIAL = "partial", // 部分正确
  STREAK = "streak", // 连续答对
  COMPLETION = "completion", // 完成练习
}
// 更新后的题目数据，符合新格式
export const mockQuestions = [
  {
    id: "q1irormdco00002",
    type: 1, // 单选题
    isInWrongQuestionBank: false,
    questionTopic: "",
    questionDifficult: 2,
    baseTreeId: 9,
    baseTreeNodeIds: [2940],
    provinceCode: 35,
    cityCode: 0,
    areaCode: 0,
    phase: 3,
    subject: 2,
    questionMd5: "5c65d10015b09df097df8e12af53e46c",
    estimatedDuration: 0,
    questionContent: {
      questionOptionList: [
        {
          optionKey: "A",
          optionVal: "选项 A：单图演示 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">",
        },
        {
          optionKey: "B",
          optionVal: "选项 B：双图演示，观察这两个函数 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">",
        },
        {
          optionKey: "C",
          optionVal: "选项 C：三图演示，分析步骤测试测试 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">",
        },
        {
          optionKey: "D",
          optionVal: "选项 D：四图网格演示 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">",
        },
      ],
      "questionOrder": 0,
      "questionScore": 0,
      "questionOriginName": "",
      "questionStem": "<p style=\"text-align: left\">【图片排版测试题】下列函数中，与<math-field>y = x - 1</math-field>是同一函数的是？</p><p style=\"text-align: left\">单图测试：这是一个单独的图片<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">用于测试单图居中100%宽度显示。</p><p style=\"text-align: left\">双图测试：观察下面两个函数图像<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">它们的定义域是否相同？</p><p style=\"text-align: left\">三图测试：分析以下三个步骤<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">第一个图应该单独一行，后两个图应该一行显示。</p><p style=\"text-align: left\">四图测试：比较下列四个选项<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">应该每行显示两个图片。</p><p style=\"text-align: left\">五图测试：更多选项<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">应该每行两个图片排列。</p><p style=\"text-align: left\">混合测试：前面是单图<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">中间有文字说明，然后是双图<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\">最后总结。</p>",
      "questionOptionMatrix": null,
      "isMatchLine": 0
    },
    questionAnswer: {
      answerOptionList: [
        {
          optionKey: "A",
          optionVal: "$\\{1, 3\\}$",
        },
      ],
    },
    questionExplanation:
      `<p style="text-align: left">求出已知函数的定义域，然后根据判断两函数是同一函数的标准对各个选项逐个化简判断即可求解． 解：函数<math-field>y = x - 1</math-field>的定义域为<math-field>\mathbf{R}</math-field>， 对于<math-field>A</math-field>，<math-field>y = \sqrt[3]{x^3} - 1 = x - 1</math-field>，且定义域为<math-field>\mathbf{R}</math-field>，与已知函数
      `,
    questionExtra: "",
    checkInfo: null,
    questionSceneList: [2],
    questionSource: 0,
    originPaperId: "p1irorkcba0006",
    attachFileList: null,
    subQuestionList: [],
    createrId: 0,
    updaterId: 10,
    createTime: *************,
    updateTime: *************,
  },
  {
    id: "q1",
    type: 3, // 英语填空题
    isInWrongQuestionBank: false,
    questionContent: {
      questionOrder: 6,
      questionScore: 15,
      questionOriginName: "2023春•数学基础测试",
      questionStem:
        '<p style="text-align: left">3. (4分) 下列<span data-tiptype="question-blank_filling" data-index="1"></span>函数中，不是表示<span data-tiptype="question-blank_filling" data-index="2"></span>同一函数的是（ ）</p><p style="text-align: left">3. (4分) 下列<span data-tiptype="question-blank_filling" data-index="1"></span>函数中，不是表示<span data-tiptype="question-blank_filling" data-index="2"></span>同一函数的是（ ）</p><p style="text-align: left">函数是否为同一函数，需要考虑它们的定义域和对应法则是否都相同。 A. 对于 <img src="https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/990ee3cd-5f47-43b5-8828-ad26e8096bef.png"> 和 <math-field>y=\\cos x</math-field>，若将自变量均视为同一个符号（例如 <math-field>t</math-field>），则它们都表示 <math-field>f(t)=\\cos t</math-field>。它们的定义域都是实数集 <math-field>\\mathbb{R}</math-field>，对应法则都是取余弦。因此，它们是同一函数。 B. 对于 <math-field>y=\\ln x^2</math-field> 和 <math-field>y=2\\ln x</math-field>： 函数 <math-field>y=\\ln x^2</math-field> 的定义域为 <math-field>x^2 &gt; 0</math-field>，即 <math-field>x \\neq 0</math-field>。其表达式可以化为</p>',
    },
    questionAnswer: {
      answerOptionList: [
        {
          optionVal: "***********",
        },
        {
          optionVal: "***********",
        },
        {
          optionVal: "***********",
        },
        {
          optionVal: "***********",
        },
      ],
    },
    questionExplanation:
      "牛顿第二定律是经典力学中的基本定律之一，描述了力、质量和加速度之间的关系。",
  },
  {
    id: "q1irormdco0002",
    type: 1, // 单选题
    isInWrongQuestionBank: false,
    questionTopic: "",
    questionDifficult: 2,
    baseTreeId: 9,
    baseTreeNodeIds: [2940],
    provinceCode: 35,
    cityCode: 0,
    areaCode: 0,
    phase: 3,
    subject: 2,
    questionMd5: "5c65d10015b09df097df8e12af53e46c",
    estimatedDuration: 0,
    questionContent: {
      questionOrder: 0,
      questionScore: 0,
      questionOriginName: "",
      questionStem:
        "把集合 $\\{x | x^2 - 4x + 3 = 0\\}$ 用列举法表示，正确的是（ ）．",
      questionOptionList: [
        {
          optionKey: "A",
          optionVal: "$\\{1, 3\\}$",
        },
        {
          optionKey: "B",
          optionVal: "$\\{x | x = 1, x = 3\\}$",
        },
        {
          optionKey: "C",
          optionVal: "$\\{x^2 - 4x + 3 = 0\\}$",
        },
        {
          optionKey: "D",
          optionVal: "$\\{x = 1, x = 3\\}$",
        },
      ],
    },
    questionAnswer: {
      answerOptionList: [
        {
          optionKey: "A",
          optionVal: "$\\{1, 3\\}$",
        },
      ],
    },
    questionExplanation:
      "解方程 $x^2 - 4x + 3 = 0$ 得 $x=1$ 或 $x=3$，用列举法表示方程的解集为 $\\{1,3\\}$．",
    questionExtra: "",
    checkInfo: null,
    questionSceneList: [2],
    questionSource: 0,
    originPaperId: "p1irorkcba0006",
    attachFileList: null,
    subQuestionList: [],
    createrId: 0,
    updaterId: 10,
    createTime: *************,
    updateTime: *************,
  },
  {
    id: "q3",
    type: 2, // 多选题
    isInWrongQuestionBank: false,
    questionContent: {
      questionOrder: 3,
      questionScore: 10,
      questionOriginName: "2023春•计算机基础测试",
      questionStem: "下列哪些是常见的编程语言？",
      questionOptionList: [
        { optionKey: "A", optionVal: "Python" },
        { optionKey: "B", optionVal: "Java" },
        { optionKey: "C", optionVal: "C++" },
        { optionKey: "D", optionVal: "HTML" },
      ],
    },
    questionAnswer: {
      answerOptionList: [
        { optionKey: "A", optionVal: "Python" }, // 🔧 修复：补充缺失的 optionVal
        { optionKey: "B", optionVal: "Java" },   // 🔧 修复：补充缺失的 optionVal
        { optionKey: "C", optionVal: "C++" },    // 🔧 修复：补充缺失的 optionVal
      ],
    },
    questionExplanation: "Python、Java和C++都是常见的编程语言。",
  },
  // {
  //   id: "q4",
  //   type: 4, // 判断题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "历史知识",
  //   questionDifficult: 1,
  //   baseTreeId: 25,
  //   baseTreeNodeIds: [4500],
  //   provinceCode: 11,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 7,
  //   questionMd5: "j4u5d6g7h8i9j0k1l2m3n4o5p6q7r8s9",
  //   estimatedDuration: 45,
  //   questionContent: {
  //     questionOrder: 4,
  //     questionScore: 3,
  //     questionOriginName: "2023春•历史基础测试",
  //     questionStem: "中国古代四大发明包括指南针、造纸术、火药和活字印刷术。",
  //     questionOptionList: [
  //       { optionKey: "true", optionVal: "正确" },
  //       { optionKey: "false", optionVal: "错误" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [{ optionKey: "true" }],
  //   },
  //   questionExplanation: "中国古代四大发明确实包括指南针、造纸术、火药和活字印刷术。这四项发明对世界文明的发展产生了深远影响。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1],
  //   questionSource: 0,
  //   originPaperId: "p2023his001",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 0,
  //   updaterId: 0,
  //   createTime: *************,
  //   updateTime: *************,
  // },

  {
    id: "q6",
    type: 5, // 解答题
    isInWrongQuestionBank: false,
    questionContent: {
      questionOrder: 6,
      questionScore: 15,
      questionOriginName: "2023春•数学基础测试",
      questionStem: "请解释牛顿第二定律，并举例说明其在日常生活中的应用。",
    },
    questionAnswer: {
      answerOptionList: [
        {
          optionVal:
            "牛顿第二定律表述为：物体加速度的大小与作用力成正比，与物体质量成反比，加速度的方向与作用力的方向相同。日常生活中的应用例如：推购物车时，同样的力作用下，购物车里物品越多（质量越大），购物车加速度越小。",
        },
      ],
    },
    questionExplanation:
      "牛顿第二定律是经典力学中的基本定律之一，描述了力、质量和加速度之间的关系。",
  },

  // {
  //   id: "q7-1",
  //   type: 1, // 单选题
  //   isInWrongQuestionBank: false,
  //   questionContent: {
  //     questionOrder: 1,
  //     questionScore: 10,
  //     questionStem: "The Internet is a worldwide network that connects millions of computers. It has ______ our lives in many ways.",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "changed" },
  //       { optionKey: "B", optionVal: "damaged" },
  //       { optionKey: "C", optionVal: "reduced" },
  //       { optionKey: "D", optionVal: "limited" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [{ optionKey: "A", optionVal: "changed" }], // 🔧 修复：补充缺失的 optionVal
  //   },
  // },
  // {
  //   id: "q7-2",
  //   type: 1, // 单选题
  //   isInWrongQuestionBank: false,
  //   questionContent: {
  //     questionOrder: 2,
  //     questionScore: 10,
  //     questionStem: "We can ______ information, shop online, and communicate with people around the world.",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "hide" },
  //       { optionKey: "B", optionVal: "access" },
  //       { optionKey: "C", optionVal: "delete" },
  //       { optionKey: "D", optionVal: "ignore" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [{ optionKey: "B", optionVal: "access" }], // 🔧 修复：补充缺失的 optionVal
  //   },
  // },
  // // 🔧 新增题目 - 确保前三道是选择题，后续随机排列
  // {
  //   id: "q8",
  //   type: 1, // 单选题 - 第3道选择题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "化学基础",
  //   questionDifficult: 1,
  //   baseTreeId: 12,
  //   baseTreeNodeIds: [3001],
  //   provinceCode: 11,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 5,
  //   questionMd5: "8a7b9c2d3e4f5g6h7i8j9k0l1m2n3o4p",
  //   estimatedDuration: 60,
  //   questionContent: {
  //     questionOrder: 8,
  //     questionScore: 5,
  //     questionOriginName: "2024春•化学基础测试",
  //     questionStem: "下列物质中，属于化合物的是（ ）",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "氧气 O₂" },
  //       { optionKey: "B", optionVal: "水 H₂O" },
  //       { optionKey: "C", optionVal: "铁 Fe" },
  //       { optionKey: "D", optionVal: "氮气 N₂" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       {
  //         optionKey: "B",
  //         optionVal: "水 H₂O",
  //       },
  //     ],
  //   },
  //   questionExplanation: "化合物是由两种或两种以上不同元素组成的纯净物。水(H₂O)由氢元素和氧元素组成，属于化合物。而氧气、铁、氮气都是由单一元素组成的单质。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1],
  //   questionSource: 0,
  //   originPaperId: "p2024spring001",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 1,
  //   updaterId: 1,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q9",
  //   type: 3, // 填空题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "数学计算",
  //   questionDifficult: 2,
  //   baseTreeId: 8,
  //   baseTreeNodeIds: [2801, 2802],
  //   provinceCode: 31,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 1,
  //   questionMd5: "9b8c7d6e5f4g3h2i1j0k9l8m7n6o5p4",
  //   estimatedDuration: 120,
  //   questionContent: {
  //     questionOrder: 9,
  //     questionScore: 8,
  //     questionOriginName: "2024春•数学计算专项",
  //     questionStem: '<p>计算下列各题：</p><p>（1）$3x + 5 = 14$，则 $x = $ <span data-tiptype="question-blank_filling" data-index="1"></span></p><p>（2）若 $2^a = 8$，则 $a = $ <span data-tiptype="question-blank_filling" data-index="2"></span></p>',
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionVal: "3" },
  //       { optionVal: "3" },
  //     ],
  //   },
  //   questionExplanation: "（1）$3x + 5 = 14$，移项得 $3x = 9$，所以 $x = 3$<br/>（2）$2^a = 8 = 2^3$，所以 $a = 3$",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 2],
  //   questionSource: 0,
  //   originPaperId: "p2024math002",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 2,
  //   updaterId: 2,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q10",
  //   type: 2, // 多选题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "生物知识",
  //   questionDifficult: 2,
  //   baseTreeId: 15,
  //   baseTreeNodeIds: [3501, 3502, 3503],
  //   provinceCode: 44,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 6,
  //   questionMd5: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  //   estimatedDuration: 90,
  //   questionContent: {
  //     questionOrder: 10,
  //     questionScore: 6,
  //     questionOriginName: "2024春•生物综合测试",
  //     questionStem: "下列关于植物光合作用的叙述，正确的是（ ）",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "光合作用需要光照条件 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 阳光是光合作用的必要条件。" },
  //       { optionKey: "B", optionVal: "光合作用产生氧气，过程如下 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 两个步骤展示氧气产生过程。" },
  //       { optionKey: "C", optionVal: "光合作用消耗二氧化碳，反应机制 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 三步反应过程。" },
  //       { optionKey: "D", optionVal: "光合作用只在白天进行（错误观点）<img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 四图对比昼夜差异。" },
  //       { optionKey: "E", optionVal: "光合作用不需要叶绿素（错误）实际上叶绿素是关键 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 五图展示叶绿素作用。" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionKey: "A", optionVal: "光合作用需要光照条件" }, // 🔧 修复：补充缺失的 optionVal
  //       { optionKey: "B", optionVal: "光合作用产生氧气" },     // 🔧 修复：补充缺失的 optionVal
  //       { optionKey: "C", optionVal: "光合作用消耗二氧化碳" }, // 🔧 修复：补充缺失的 optionVal
  //     ],
  //   },
  //   questionExplanation: "光合作用是植物利用光能，在叶绿素的催化下，将二氧化碳和水合成有机物并释放氧气的过程。A、B、C选项正确。D选项错误，因为光合作用主要在有光照时进行，但不仅限于白天。E选项错误，叶绿素是光合作用必需的。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1],
  //   questionSource: 0,
  //   originPaperId: "p2024bio003",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 3,
  //   updaterId: 3,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q11",
  //   type: 6, // 英语填空题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "英语语法",
  //   questionDifficult: 2,
  //   baseTreeId: 20,
  //   baseTreeNodeIds: [4001, 4002],
  //   provinceCode: 50,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 3,
  //   questionMd5: "b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7",
  //   estimatedDuration: 150,
  //   questionContent: {
  //     questionOrder: 11,
  //     questionScore: 10,
  //     questionOriginName: "2024春•英语语法专项",
  //     questionStem: '<p>Complete the sentences with the correct form of the words in brackets.</p><p>1. She <span data-tiptype="question-blank_filling" data-index="1"></span> (study) English for three years.</p><p>2. The book <span data-tiptype="question-blank_filling" data-index="2"></span> (write) by a famous author.</p><p>3. If it <span data-tiptype="question-blank_filling" data-index="3"></span> (rain) tomorrow, we will stay at home.</p>',
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionVal: "has studied" },
  //       { optionVal: "was written" },
  //       { optionVal: "rains" },
  //     ],
  //   },
  //   questionExplanation: "1. 现在完成时：has studied（她学英语三年了）<br/>2. 被动语态：was written（这本书是由一位著名作者写的）<br/>3. 条件状语从句：rains（如果明天下雨，我们就待在家里）",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 2],
  //   questionSource: 0,
  //   originPaperId: "p2024eng004",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 4,
  //   updaterId: 4,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q12",
  //   type: 5, // 解答题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "物理实验",
  //   questionDifficult: 3,
  //   baseTreeId: 18,
  //   baseTreeNodeIds: [3801, 3802, 3803],
  //   provinceCode: 37,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 4,
  //   questionMd5: "c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8",
  //   estimatedDuration: 300,
  //   questionContent: {
  //     questionOrder: 12,
  //     questionScore: 15,
  //     questionOriginName: "2024春•物理实验设计",
  //     questionStem: "设计一个实验来验证欧姆定律（I = U/R）。请详细说明：<br/>（1）实验器材<br/>（2）实验步骤<br/>（3）数据处理方法<br/>（4）注意事项",
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       {
  //         optionVal: "（1）实验器材：电源、电阻器、电流表、电压表、滑动变阻器、开关、导线等<br/>（2）实验步骤：①按电路图连接电路；②调节滑动变阻器，测量不同电压下的电流值；③记录数据<br/>（3）数据处理：绘制U-I图像，验证是否为直线关系，计算斜率得到电阻值<br/>（4）注意事项：电流表串联，电压表并联；先断开开关再连接电路；注意量程选择",
  //       },
  //     ],
  //   },
  //   questionExplanation: "欧姆定律实验是验证电流、电压、电阻三者关系的经典实验。通过改变电压测量对应电流，可以验证在电阻一定时，电流与电压成正比的关系。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 3],
  //   questionSource: 0,
  //   originPaperId: "p2024phy005",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 5,
  //   updaterId: 5,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q13",
  //   type: 1, // 单选题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "历史知识",
  //   questionDifficult: 1,
  //   baseTreeId: 25,
  //   baseTreeNodeIds: [4501],
  //   provinceCode: 13,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 7,
  //   questionMd5: "d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9",
  //   estimatedDuration: 60,
  //   questionContent: {
  //     questionOrder: 13,
  //     questionScore: 4,
  //     questionOriginName: "2024春•中国古代史",
  //     questionStem: "秦始皇统一中国后，在政治上采取的重要措施是（ ）",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "分封制" },
  //       { optionKey: "B", optionVal: "郡县制" },
  //       { optionKey: "C", optionVal: "科举制" },
  //       { optionKey: "D", optionVal: "三省六部制" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       {
  //         optionKey: "B",
  //         optionVal: "郡县制",
  //       },
  //     ],
  //   },
  //   questionExplanation: "秦始皇统一中国后，废除了分封制，在全国推行郡县制。郡县制是中央集权制度的重要组成部分，郡县官员由皇帝任免，有利于加强中央集权。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1],
  //   questionSource: 0,
  //   originPaperId: "p2024his006",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 6,
  //   updaterId: 6,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q14",
  //   type: 3, // 填空题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "地理知识",
  //   questionDifficult: 2,
  //   baseTreeId: 22,
  //   baseTreeNodeIds: [4201, 4202],
  //   provinceCode: 51,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 8,
  //   questionMd5: "e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
  //   estimatedDuration: 90,
  //   questionContent: {
  //     questionOrder: 14,
  //     questionScore: 6,
  //     questionOriginName: "2024春•中国地理",
  //     questionStem: '<p>中国四大高原分别是：<span data-tiptype="question-blank_filling" data-index="1"></span>高原、<span data-tiptype="question-blank_filling" data-index="2"></span>高原、<span data-tiptype="question-blank_filling" data-index="3"></span>高原和<span data-tiptype="question-blank_filling" data-index="4"></span>高原。其中面积最大的是<span data-tiptype="question-blank_filling" data-index="5"></span>高原。</p>',
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionVal: "青藏" },
  //       { optionVal: "内蒙古" },
  //       { optionVal: "黄土" },
  //       { optionVal: "云贵" },
  //       { optionVal: "青藏" },
  //     ],
  //   },
  //   questionExplanation: "中国四大高原是青藏高原、内蒙古高原、黄土高原和云贵高原。其中青藏高原面积最大，约240万平方千米，被称为'世界屋脊'。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1],
  //   questionSource: 0,
  //   originPaperId: "p2024geo007",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 7,
  //   updaterId: 7,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q15",
  //   type: 2, // 多选题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "计算机科学",
  //   questionDifficult: 2,
  //   baseTreeId: 30,
  //   baseTreeNodeIds: [5001, 5002, 5003],
  //   provinceCode: 21,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 9,
  //   questionMd5: "f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1",
  //   estimatedDuration: 120,
  //   questionContent: {
  //     questionOrder: 15,
  //     questionScore: 8,
  //     questionOriginName: "2024春•计算机基础",
  //     questionStem: "下列关于计算机网络的叙述，正确的是（ ）",
  //     questionOptionList: [
  //       { optionKey: "A", optionVal: "TCP/IP是互联网的核心协议 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 协议栈结构图。" },
  //       { optionKey: "B", optionVal: "HTTP协议用于网页传输，请求响应过程 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 两步展示HTTP通信。" },
  //       { optionKey: "C", optionVal: "DNS用于域名解析，解析流程 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 三步解析过程。" },
  //       { optionKey: "D", optionVal: "FTP只能用于文件下载（错误）实际上可以上传下载 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 四图展示FTP功能。" },
  //       { optionKey: "E", optionVal: "IP地址是计算机在网络中的唯一标识，地址分类和结构 <img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"><img src=\"https://gil-test.oss-cn-beijing.aliyuncs.com/question/content-attach/5c32da10-5f35-4e2d-a1cd-4e2564a25f17.png\"> 六图详细展示IP地址体系。" },
  //     ],
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionKey: "A", optionVal: "TCP/IP是互联网的核心协议" },        // 🔧 修复：补充缺失的 optionVal
  //       { optionKey: "B", optionVal: "HTTP协议用于网页传输" },           // 🔧 修复：补充缺失的 optionVal
  //       { optionKey: "C", optionVal: "DNS用于域名解析" },               // 🔧 修复：补充缺失的 optionVal
  //       { optionKey: "E", optionVal: "IP地址是计算机在网络中的唯一标识" }, // 🔧 修复：补充缺失的 optionVal
  //     ],
  //   },
  //   questionExplanation: "A正确：TCP/IP确实是互联网的核心协议族。B正确：HTTP协议用于网页数据传输。C正确：DNS（域名系统）用于将域名解析为IP地址。D错误：FTP既可以上传也可以下载文件。E正确：IP地址是设备在网络中的唯一标识。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 2],
  //   questionSource: 0,
  //   originPaperId: "p2024cs008",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 8,
  //   updaterId: 8,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q16",
  //   type: 6, // 英语填空题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "英语阅读理解",
  //   questionDifficult: 3,
  //   baseTreeId: 20,
  //   baseTreeNodeIds: [4003, 4004],
  //   provinceCode: 32,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 3,
  //   questionMd5: "g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2",
  //   estimatedDuration: 180,
  //   questionContent: {
  //     questionOrder: 16,
  //     questionScore: 12,
  //     questionOriginName: "2024春•英语阅读填空",
  //     questionStem: '<p>Read the passage and fill in the blanks with appropriate words.</p><p>Climate change is one of the most <span data-tiptype="question-blank_filling" data-index="1"></span> (1) challenges facing humanity today. The Earth\'s temperature has been <span data-tiptype="question-blank_filling" data-index="2"></span> (2) steadily over the past century due to human activities. Scientists <span data-tiptype="question-blank_filling" data-index="3"></span> (3) that we must take immediate action to reduce greenhouse gas emissions. Many countries have <span data-tiptype="question-blank_filling" data-index="4"></span> (4) to achieve carbon neutrality by 2050.</p>',
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       { optionVal: "serious" },
  //       { optionVal: "rising" },
  //       { optionVal: "believe" },
  //       { optionVal: "committed" },
  //     ],
  //   },
  //   questionExplanation: "1. serious - 气候变化是最严重的挑战之一<br/>2. rising - 地球温度一直在上升<br/>3. believe - 科学家们相信/认为<br/>4. committed - 许多国家已经承诺实现碳中和",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 2],
  //   questionSource: 0,
  //   originPaperId: "p2024eng009",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 9,
  //   updaterId: 9,
  //   createTime: *************,
  //   updateTime: *************,
  // },
  // {
  //   id: "q17",
  //   type: 5, // 解答题
  //   isInWrongQuestionBank: false,
  //   questionTopic: "数学应用",
  //   questionDifficult: 3,
  //   baseTreeId: 8,
  //   baseTreeNodeIds: [2803, 2804],
  //   provinceCode: 33,
  //   cityCode: 0,
  //   areaCode: 0,
  //   phase: 3,
  //   subject: 1,
  //   questionMd5: "h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3",
  //   estimatedDuration: 240,
  //   questionContent: {
  //     questionOrder: 17,
  //     questionScore: 12,
  //     questionOriginName: "2024春•数学应用题",
  //     questionStem: "某商店销售一种商品，成本价为每件80元。<br/>（1）如果按成本价的150%定价，然后打8折销售，每件商品的利润是多少元？<br/>（2）如果要使每件商品的利润率达到25%，应该如何定价？<br/>（3）在保证利润率不低于20%的前提下，最多可以打几折？",
  //   },
  //   questionAnswer: {
  //     answerOptionList: [
  //       {
  //         optionVal: "（1）定价：80×150%=120元，售价：120×0.8=96元，利润：96-80=16元<br/>（2）利润率25%，利润：80×25%=20元，售价：80+20=100元<br/>（3）利润率20%，最低售价：80×(1+20%)=96元，最多打折：96÷120=0.8，即8折",
  //       },
  //     ],
  //   },
  //   questionExplanation: "这是一道关于商品定价和利润计算的应用题。需要掌握成本价、定价、售价、利润、利润率等概念之间的关系。利润=售价-成本价，利润率=利润÷成本价×100%。",
  //   questionExtra: "",
  //   checkInfo: null,
  //   questionSceneList: [1, 3],
  //   questionSource: 0,
  //   originPaperId: "p2024math010",
  //   attachFileList: null,
  //   subQuestionList: [],
  //   createrId: 10,
  //   updaterId: 10,
  //   createTime: 1747813400000,
  //   updateTime: 1747813400000,
  // },
];

/**
 * 模拟练习会话数据
 */
export const mockExerciseSession = {
  id: "session1",
  userId: "user123",
  courseId: "course456",
  lessonId: "lesson789",
  studyType: "in_class",
  status: "in_progress",
  startedAt: new Date(),
  currentQuestionIndex: 0,
  streakCount: 0,
  totalTimeSpent: 0,
  total: 18, // 🔧 更新：现在有18道题目（包含判断题）
  isLastQuestion: false,
};

/**
 * 模拟反馈数据配置
 */
export const mockFeedbackConfig = {
  correct: {
    type: FeedbackType.CORRECT,
    title: "正确",
    message: "做得好！",
    animationUrl: "/animations/correct.json",
    audioUrl: "/audios/correct.mp3",
    duration: 2000,
  },
  incorrect: {
    type: FeedbackType.INCORRECT,
    title: "错误",
    message: "再想想看",
    animationUrl: "/animations/incorrect.json",
    audioUrl: "/audios/incorrect.mp3",
    duration: 2000,
    questionExplanation: "查看详细解析了解更多",
    showCorrectAnswer: true,
  },
  partial: {
    type: FeedbackType.PARTIAL,
    title: "部分正确",
    message: "接近了",
    animationUrl: "/animations/partial.json",
    audioUrl: "/audios/partial.mp3",
    duration: 2000,
  },
  streak: {
    type: FeedbackType.STREAK,
    title: "连续正确",
    message: "太棒了！连续答对！",
    animationUrl: "/animations/streak.json",
    audioUrl: "/audios/streak.mp3",
    duration: 3000,
    rewards: [
      {
        type: "xp",
        value: 50,
        name: "额外经验值",
      },
    ],
  },
  completion: {
    type: FeedbackType.COMPLETION,
    title: "练习完成",
    message: "恭喜完成所有题目！",
    animationUrl: "/animations/completion.json",
    audioUrl: "/audios/completion.mp3",
    duration: 4000,
    rewards: [
      {
        type: "badge",
        name: "勤学勋章",
        iconUrl: "/images/badge.png",
      },
    ],
  },
};
