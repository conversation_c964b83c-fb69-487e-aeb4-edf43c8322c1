// 你是我大哥
import { TASK_TYPE, taskTypeEnumManager } from "@/enums";
import OutOfDate from "@/public/assign/homework/out_of_date.svg";
import {
  getClassResourceTaskReport,
  getClassTaskAvgAccuracyAndAvgProgress,
} from "@/services";
import { HomeworkReport } from "@/types";
import { cn } from "@/utils/utils";
import { useRequest } from "ahooks";
import { memo } from "react";

import ReportCardLabel from "./ReportCardLabel";

export interface ReportCardProps {
  report: HomeworkReport;

  taskInfo: {
    taskId: number;
    taskType: TASK_TYPE;
  };
  onClick?: (report: HomeworkReport) => void;
  headerSuffix?: React.ReactNode;
  className?: string;
}

const isOutOfDate = (deadline?: number) => {
  return deadline && deadline < Date.now() / 1000;
};

export function formatClassTime(time: number): string {
  const classCount = time / (1000 * 60 * 45);
  return `${Math.ceil(classCount)}节`;
}

export function formatCompleteRate(rate: number): string {
  if (rate === 0) return "";
  return Math.ceil(rate * 100) + "%";
}
export function formatCorrectRate(rate: number): string {
  if (rate === 0) return "";
  return Math.floor(rate * 100) + "%";
}

function ReportCard({
  report,
  onClick,
  className,
  headerSuffix = null,
  taskInfo,
}: ReportCardProps) {
  const { bg, lineColor } = taskTypeEnumManager.getEnumByValue(
    taskInfo.taskType
  ) || {
    bg: "",
    lineColor: "",
  };

  const { data: stats, loading: isFetchingStats } = useRequest(
    async () => {
      const res = await getClassTaskAvgAccuracyAndAvgProgress({
        taskId: taskInfo.taskId,
        assignId: report.assignId,
      });

      return res;
    },
    {
      ready: taskInfo.taskType === TASK_TYPE.TASK_TYPE_COURSE,
    }
  );

  const { data: resourceStats, loading: isFetchingResourceStats } = useRequest(
    async () => {
      const res = await getClassResourceTaskReport({
        assignIds: [report.assignId],
      });

      return res[0];
    },
    {
      ready: taskInfo.taskType === TASK_TYPE.TASK_TYPE_RESOURCE,
    }
  );

  return (
    <div
      onClick={() => {
        onClick?.(report);
      }}
      className={cn(
        `mb-4 flex flex-col items-start self-stretch rounded-[0.75rem] px-4 py-3 last:mb-0`,
        bg,
        className
      )}
    >
      {/* 班级名称和状态区域 */}
      <div className="flex w-full items-center justify-between">
        {/* 班级名称 */}
        <div className="text-gray-2 flex h-5 flex-1 flex-col justify-between text-sm font-medium leading-[150%]">
          {report.assignObject.name}
        </div>
        {/* 条件渲染：到期标记 */}
        {isOutOfDate(report.statData.deadline) && (
          // 到期标记
          <div className="flex h-[1.125rem] items-center gap-[0.125rem] rounded-[6px] bg-white/40 px-[0.25rem] text-[#64698a]">
            <p className="align-middle text-xs leading-[1]">
              <OutOfDate />
            </p>

            <span
              className="text-xxs align-middle"
              style={{ lineHeight: "normal" }}
            >
              到期
            </span>
          </div>
        )}
        {/* 后缀 */}
        {headerSuffix}
      </div>
      {/* 分割线 */}
      <div
        className={cn("my-[0.625rem] h-[1px] w-full scale-y-50", lineColor)}
      ></div>

      {/* 统计数据区域 */}
      <div
        className={
          taskInfo.taskType !== TASK_TYPE.TASK_TYPE_COURSE
            ? "flex w-full justify-between" // 两个统计项时：第一个在开始位置，第二个有间距
            : "flex w-full justify-between" // 三个或更多统计项时：均匀分布
        }
      >
        {/* 统计数据项 */}
        {/* 课程任务 */}
        {taskInfo.taskType === TASK_TYPE.TASK_TYPE_COURSE ? (
          <>
            {/* 完成率向上取整，这样0.1%也是1%，而现实数据中很少有99.x%的情况，普遍是95%上下 */}
            <ReportCardLabel
              label="完成率"
              value={formatCompleteRate(stats?.avgProgress || 0)}
              loading={isFetchingStats}
            />
            {/* 正确率向下取整，这样99.5%也不是100% */}
            <ReportCardLabel
              label="正确率"
              value={formatCorrectRate(stats?.avgAccuracy || 0)}
              loading={isFetchingStats}
            />
            <ReportCardLabel
              label="待关注"
              value={`${stats?.commonIncorrectCount || 0}题`}
              loading={false}
            />
          </>
        ) : null}
        {/* 资源任务 */}
        {taskInfo.taskType === TASK_TYPE.TASK_TYPE_RESOURCE ? (
          <>
            {/* 完成率向上取整，这样0.1%也是1%，而现实数据中很少有99.x%的情况，普遍是95%上下 */}
            <ReportCardLabel
              label="完成率"
              value={formatCompleteRate(resourceStats?.progressRate || 0)}
              loading={isFetchingResourceStats}
            />
            <ReportCardLabel
              label="课时数"
              value={formatClassTime(resourceStats?.Duration || 0)}
              loading={isFetchingResourceStats}
            />
          </>
        ) : null}
      </div>
    </div>
  );
}

export default memo(ReportCard);
