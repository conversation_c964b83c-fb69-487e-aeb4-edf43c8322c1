import { cn } from "@repo/ui/lib/utils";
import "@repo/ui/styles/font/resource-han-rounded.css";
import { useFullscreen, useSize } from "ahooks";
import { useEffect, useRef, type ComponentProps, type FC } from "react";
import { CourseProgressBar } from "./CourseProgressBar";
import { WidgetLoader } from "./WidgetLoader";
import { CourseWidgetDetail, ProgressData } from "./type";

import "./index.css";

import FullScreenCloseIcon from "@repo/core/public/assets/icons/fullscreen-close.svg";
import FullScreenOpenIcon from "@repo/core/public/assets/icons/fullscreen-open.svg";

export interface AiCoursePreviewHandler {
  refresh: () => void;
}

interface CourseViewProps {
  // 进度数据
  progressData?: ProgressData;
  isFetchingProgressData?: boolean;

  // 当前组件索引
  activeIndex: number;
  // 监听组件索引变化
  onActiveIndexChange?: (index: number) => void;

  // 组件数据
  widgetData?: CourseWidgetDetail;
  // 组件是否加载中
  isWidgetDataLoading?: boolean;

  // 进度条配置
  courseProgressBarProps?: Omit<
    ComponentProps<typeof CourseProgressBar>,
    "activeIndex" | "courseWidgetSummary" | "onChange"
  >;

  // 监听练习组件的题目索引变化
  onQuestionIndexChange?: React.ComponentProps<
    typeof WidgetLoader
  >["onQuestionIndexChange"];

  // Widget详情后缀元素
  WidgetLoaderSuffixElement?: React.ReactNode;

  // 文稿组件的props
  guideWidgetProps?: React.ComponentProps<
    typeof WidgetLoader
  >["guideWidgetProps"];

  // 组件刷新
  handlerRef?: React.RefObject<AiCoursePreviewHandler | null>;

  onEnterFullscreen?: () => void;
  onExitFullscreen?: () => void;

  widgetLoaderClassName?: string;
}

export const CourseView: FC<CourseViewProps & ComponentProps<"div">> = ({
  className,
  progressData,
  isFetchingProgressData = false,
  activeIndex,
  onActiveIndexChange,

  widgetData,
  isWidgetDataLoading = false,

  courseProgressBarProps,
  onQuestionIndexChange,

  WidgetLoaderSuffixElement,
  guideWidgetProps,

  handlerRef,
  onEnterFullscreen,
  onExitFullscreen,

  widgetLoaderClassName,
}) => {
  const widgetRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(
    widgetRef,
    {
      pageFullscreen: {
        zIndex: 1,
      },
    }
  );
  const size = useSize(widgetRef);

  const handleEnterFullscreen = () => {
    enterFullscreen();
    onEnterFullscreen?.();
  };

  const handleExitFullscreen = () => {
    exitFullscreen();
    onExitFullscreen?.();
  };

  // 组件刷新，但放一层，方便扩展
  const widgetHandlerRef = useRef<AiCoursePreviewHandler | null>(null);
  useEffect(() => {
    if (handlerRef && widgetHandlerRef.current) {
      handlerRef.current = {
        refresh: widgetHandlerRef.current.refresh,
      };
    }
  }, [handlerRef]);

  return (
    <div
      className={cn(
        "ai-course-preview flex h-full w-full flex-col gap-[10px] overflow-hidden",
        className
      )}
    >
      <div className="flex items-center justify-between">
        <p className="text-gray-1 text-sm font-medium leading-6">课程大纲</p>
      </div>
      <div className="flex h-full w-full max-w-full flex-1 flex-row gap-[10px] overflow-hidden">
        {/* 导航条 */}
        <div className="flex w-[20%] shrink-0 grow-0 flex-col">
          {isFetchingProgressData || !progressData ? (
            <div className="h-full w-full animate-pulse rounded-md bg-gray-200" />
          ) : (
            <CourseProgressBar
              {...courseProgressBarProps}
              activeIndex={activeIndex}
              courseWidgetSummary={progressData}
              onChange={onActiveIndexChange}
              className="border-purple-5 border-1 rounded-md px-2.5 py-6"
            />
          )}
        </div>
        {/* 组件内容：文稿 or 练习 or 视频 */}
        <div
          className="border-purple-5 border-1 relative flex h-full w-full flex-1 rounded-md bg-white"
          ref={widgetRef}
        >
          {isFetchingProgressData || isWidgetDataLoading || !size ? (
            <div className="h-full w-full animate-pulse rounded-md bg-gray-200" />
          ) : (
            <>
              <div className="h-full w-full flex-1 overflow-y-auto">
                <WidgetLoader
                  data={widgetData}
                  height={Math.floor((size.height / size.width) * 1000)}
                  // 目前width太小的话，会展示不全，也没法scale，会导致划线位置错误
                  width={1000}
                  guideInputProps={{
                    index: activeIndex,
                    totalGuideCount: progressData?.length,
                  }}
                  onQuestionIndexChange={onQuestionIndexChange}
                  guideStyle={{
                    width: "100%",
                    height: "100%",
                  }}
                  guideWidgetProps={guideWidgetProps}
                  handlerRef={handlerRef}
                  className={widgetLoaderClassName}
                />
              </div>
              <button
                className="border-1 top-5.5 absolute right-3.5 flex h-7 w-7 cursor-pointer select-none items-center justify-center rounded-md border-[#1F232B1F] bg-white hover:bg-slate-50 active:opacity-60"
                onClick={
                  isFullscreen ? handleExitFullscreen : handleEnterFullscreen
                }
                style={{
                  boxShadow: "0px 2.8px 11.2px 0px rgba(35, 42, 64, 0.05)",
                }}
              >
                {isFullscreen ? (
                  <FullScreenCloseIcon className="select-none" />
                ) : (
                  <FullScreenOpenIcon className="select-none" />
                )}
              </button>
              {WidgetLoaderSuffixElement}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
