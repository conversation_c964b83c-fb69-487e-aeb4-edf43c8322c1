# Loading 组件

一个美观且可定制的 Loading 组件，支持多种动画样式和配置选项。

## 特性

- 🎨 6种不同的动画样式
- 🌈 5种颜色主题
- 📏 3种尺寸选项
- 📝 支持自定义文字提示
- 🎯 完全可定制的样式
- ⚡ 基于 Tailwind CSS 构建

## 安装

```bash
# 组件已经包含在 @repo/ui 包中
import { Loading } from "@repo/ui/components/loading";
```

## 基础用法

```tsx
import { Loading } from "@repo/ui/components/loading";

// 默认样式
<Loading />

// 带文字提示
<Loading text="正在加载..." />

// 自定义样式
<Loading variant="wave" size="lg" text="请稍候..." />
```

## API

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `variant` | `"spinner" \| "dots" \| "pulse" \| "wave" \| "ripple" \| "orbit"` | `"spinner"` | 动画样式 |
| `size` | `"sm" \| "md" \| "lg"` | `"md"` | 组件尺寸 |
| `color` | `"blue" \| "green" \| "purple" \| "orange" \| "gray"` | `"blue"` | 颜色主题 |
| `text` | `string` | `undefined` | 显示的文字提示 |
| `className` | `string` | `undefined` | 自定义 CSS 类名 |

## 动画样式

### 1. Spinner (默认)
经典的旋转加载动画
```tsx
<Loading variant="spinner" />
```

### 2. Dots
三个跳动的圆点
```tsx
<Loading variant="dots" />
```

### 3. Pulse
脉冲动画
```tsx
<Loading variant="pulse" />
```

### 4. Wave
波浪动画
```tsx
<Loading variant="wave" />
```

### 5. Ripple
涟漪动画
```tsx
<Loading variant="ripple" />
```

### 6. Orbit
轨道动画
```tsx
<Loading variant="orbit" />
```

## 尺寸选项

- `sm`: 小尺寸 (16x16px)
- `md`: 中等尺寸 (32x32px) - 默认
- `lg`: 大尺寸 (48x48px)

```tsx
<Loading size="sm" />
<Loading size="md" />
<Loading size="lg" />
```

## 颜色主题

- `blue`: 蓝色 (默认)
- `green`: 绿色
- `purple`: 紫色
- `orange`: 橙色
- `gray`: 灰色

```tsx
<Loading color="green" />
<Loading color="purple" />
<Loading color="orange" />
```

## 完整示例

```tsx
import { Loading } from "@repo/ui/components/loading";

function MyComponent() {
  const [loading, setLoading] = useState(true);

  return (
    <div className="my-component">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loading 
            variant="wave" 
            size="lg" 
            text="正在加载课程内容..." 
            color="blue"
            className="my-custom-loading"
          />
        </div>
      ) : (
        <div>内容已加载完成</div>
      )}
    </div>
  );
}
```

## 自定义样式

你可以通过 `className` 属性添加自定义样式：

```tsx
<Loading 
  variant="ripple" 
  className="my-custom-loading-class"
/>
```

## 最佳实践

1. **选择合适的样式**: 根据加载内容的类型选择合适的动画样式
2. **提供文字提示**: 对于较长的加载时间，建议提供文字提示
3. **保持一致性**: 在同一个应用中保持 loading 样式的统一
4. **考虑用户体验**: 避免过于花哨的动画，保持简洁美观

## 演示

查看 `LoadingDemo` 组件来了解所有可用的样式和配置选项。 