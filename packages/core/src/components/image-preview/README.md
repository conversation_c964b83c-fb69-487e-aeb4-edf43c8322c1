# 图片预览组件

基于 `react-photo-view` 的专业图片预览组件，提供流畅的图片查看体验。

## 组件概览

### ImagePreviewModal
基础图片预览模态框，适用于单张图片预览。

### EnhancedImagePreview  
增强的图片预览组件，支持多图片预览、缩略图导航等高级功能。

## 特性

- ✅ 基于成熟的 `react-photo-view` 库
- ✅ 支持缩放、旋转、拖拽
- ✅ 键盘导航支持（ESC 关闭，方向键切换）
- ✅ 多图片预览和缩略图导航
- ✅ 自定义加载和错误状态
- ✅ 完整的 TypeScript 类型支持
- ✅ 使用 @preact-signals/safe-react 状态管理
- ✅ 响应式设计，移动端友好
- ✅ 防止背景滚动
- ✅ 流畅的动画效果

## 基础用法


### 多图片预览

```tsx
import { EnhancedImagePreview, useEnhancedImagePreview } from "@repo/core/components/image-preview";

function MultiImageExample() {
  const images = ["image1.jpg", "image2.jpg", "image3.jpg"];
  const { currentIndex, openPreview, closePreview } = useEnhancedImagePreview(images);

  return (
    <>
      <div className="grid grid-cols-3 gap-4">
        {images.map((img, index) => (
          <img 
            key={index}
            src={img} 
            onClick={() => openPreview(index)}
            className="cursor-pointer w-32 h-32 object-cover rounded"
          />
        ))}
      </div>
      <EnhancedImagePreview
        images={images}
        currentIndex={currentIndex}
        onClose={closePreview}
        onIndexChange={openPreview}
      />
    </>
  );
}
```

## API 参考

### ImagePreviewModal Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| imageSrc | `string \| null` | - | 当前预览的图片 URL |
| onClose | `() => void` | - | 关闭预览的回调函数 |
| className | `string` | - | 自定义样式类名 |
| showToolbar | `boolean` | `true` | 是否显示工具栏 |
| allowZoom | `boolean` | `true` | 是否允许缩放 |
| allowRotate | `boolean` | `true` | 是否允许旋转 |
| maxScale | `number` | `3` | 最大缩放比例 |
| minScale | `number` | `0.1` | 最小缩放比例 |

### EnhancedImagePreview Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| images | `string[]` | - | 图片列表 |
| currentIndex | `number \| null` | - | 当前预览的图片索引 |
| onClose | `() => void` | - | 关闭预览的回调函数 |
| onIndexChange | `(index: number) => void` | - | 切换图片的回调函数 |
| className | `string` | - | 自定义样式类名 |
| showToolbar | `boolean` | `true` | 是否显示工具栏 |
| showThumbnails | `boolean` | `true` | 是否显示缩略图导航 |
| loop | `boolean` | `true` | 是否循环播放 |
| speed | `number` | `300` | 动画速度（毫秒） |
| loadingElement | `React.ReactNode` | - | 自定义加载元素 |
| brokenElement | `React.ReactNode` | - | 自定义错误元素 |


## 交互方式

### 关闭预览
- `ESC` 键：关闭预览
- 点击背景区域：关闭预览
- **点击图片本身：关闭预览**（新增功能）

### 图片操作
- `←` / `→`: 切换图片（仅多图片预览）
- `+` / `-`: 缩放图片
- `R`: 旋转图片
- 鼠标滚轮：缩放图片
- **双指手势：移动设备上的双指缩放**（新增功能）

## 样式定制

组件使用语义化的 CSS 类名，方便自定义样式：

```css
/* 基础预览组件 */
.image-preview-modal-wrapper { }
.image-preview-photo-provider { }
.image-preview-loading { }
.image-preview-error { }

/* 增强预览组件 */
.enhanced-image-preview-wrapper { }
.enhanced-image-preview-photo-provider { }
.enhanced-image-preview-loading { }
.enhanced-image-preview-error { }
```

## 注意事项

1. **依赖要求**: 需要安装 `react-photo-view` 依赖
2. **样式导入**: 组件会自动导入 `react-photo-view/dist/react-photo-view.css`
3. **性能优化**: 使用 @preact-signals/safe-react 进行状态管理，避免不必要的重渲染
4. **移动端适配**: 组件已针对移动端进行优化，支持触摸手势
5. **内存管理**: 组件会自动处理事件监听器的清理和背景滚动的恢复

## 与项目集成

该组件遵循项目的设计规范：

- 使用 `@repo/ui/lib/utils` 的 `cn` 函数处理样式
- 支持 Tailwind CSS 类名
- 完整的 TypeScript 类型支持
- 遵循项目的代码风格和命名约定
- 使用 @preact-signals/safe-react 进行状态管理
