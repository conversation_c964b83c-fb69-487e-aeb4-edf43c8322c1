import { getBizTreeDetail, getBizTreeList } from "@/services/assign";
import { BizTreeDetailNode, BizTreeListItem } from "@/types/assign";
import { hasBizTreeNode } from "@/utils";
import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";

/*
    bizTreeList: 教材列表
*/

export const useBizTree = ({
    subject,
    defaultTreeId,
    defaultBizTreeNodeId,
    ready,
}: {
    subject: number;
    defaultTreeId?: number;
    defaultBizTreeNodeId?: number;
    ready: Signal<boolean>;
}) => {
    // 教材列表
    const {
        data: bizTreeList,
        loading: isFetchingBizTreeList,
    } = useRequest(async () => {
        if (subject === undefined) {
            return [] as BizTreeListItem[];
        }

        const res = await getBizTreeList(subject)

        if (!treeId.value && res.length > 0) {
            treeId.value = res[0].bizTreeId;
        }

        return res;
    }, {
        refreshDeps: [subject],
        ready: ready.value
    })

    // 当前选择的教材
    const treeId = useSignal<number | undefined>(defaultTreeId);
    const setTreeId = (id: number) => {
        treeId.value = id;
    }

    // 当前教材的章节树
    const {
        data: bizTreeDetail,
        loading: isFetchingBizTreeDetail,
    } = useRequest(async () => {
        const _treeId = treeId.value
        if (!_treeId) {
            return undefined;
        }

        const res = await getBizTreeDetail(_treeId);
        if (res.bizTreeDetail
            && !hasBizTreeNode(res.bizTreeDetail, (node) => node.bizTreeNodeId === bizTreeNodeId.value)
        ) {
            // bizTreeNode.value = res.bizTreeDetail.bizTreeNodeChildren[0];
            bizTreeNodeId.value = res.bizTreeDetail.bizTreeNodeChildren[0].bizTreeNodeId;
        }

        _calcBizTreeNodeMap(res.bizTreeDetail, undefined, undefined);
        bizTreeNodeMap.value = new Map(bizTreeNodeMap.value);

        return res;
    }, {
        refreshDeps: [treeId.value],
        ready: treeId.value !== undefined && ready.value,
    })

    // 章节树节点Map
    const bizTreeNodeMap = useSignal<Map<number, BizTreeDetailNode & { firstNodeId: number | undefined, parentNodeId: number | undefined }>>(new Map());


    // 当前选择的xxx
    // const bizTreeNode = useSignal<BizTreeDetailNode | undefined>(defaultBizTreeNode);
    // TODO: 这里其实应该是id数组，因为是选的多层节点
    const bizTreeNodeId = useSignal<number | undefined>(defaultBizTreeNodeId);

    const bizTreeNode = useComputed(() => {
        if (!bizTreeNodeId.value) {
            return undefined;
        }

        return bizTreeNodeMap.value.get(bizTreeNodeId.value);
    })


    function _calcBizTreeNodeMap(bizTreeDetail: BizTreeDetailNode, firstNodeId: number | undefined, parentNodeId: number | undefined) {
        if (!bizTreeDetail || !bizTreeDetail.bizTreeNodeChildren || bizTreeDetail.bizTreeNodeChildren.length === 0) {
            return;
        }

        bizTreeDetail.bizTreeNodeChildren.forEach((item) => {
            bizTreeNodeMap.value.set(item.bizTreeNodeId, {
                ...item,
                firstNodeId,
                parentNodeId
            });

            _calcBizTreeNodeMap(item, firstNodeId || item.bizTreeNodeId, item.bizTreeNodeId);
        });
    }

    return {
        // Done
        treeId,
        setTreeId,
        bizTreeList,
        isFetchingBizTreeList,
        bizTreeDetail,
        isFetchingBizTreeDetail,

        // 等待更名
        bizTreeNode,
        bizTreeNodeId,
        bizTreeNodeMap,
    }
}