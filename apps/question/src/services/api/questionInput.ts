import { request } from '@umijs/max';
import { UploadFile } from 'antd';

// export async function fetchUploadHistoryList(): Promise<API.UploadHistoryListResponse> {
//     return request('/api/v1/word/upload/list', {
//         method: 'GET',
//     });
// }

/**
 * 获取oss预签名url
 * @returns
 */
// export async function fetchUploadPolicy(params: API.UploadPolicyRequestParams): Promise<API.UploadPolicyResponse> {
//     return request('/api/upload/policy', {
//         method: 'GET',
//         params,
//     });
// }
export async function fetchUploadPolicy(
  params: API.UploadPolicyRequestParams,
): Promise<API.UploadPolicyResponse> {
  return request('http://question.local.xiaoluxue.cn/api/v1/api/upload/paper/presignurl', {
    method: 'GET',
    params,
  });
}
export async function fetchUploadPolicy2(
  params: API.UploadPolicyRequestParams,
): Promise<API.UploadPolicyResponse> {
  return request('/api/v1/api/upload/paper/policytoken', {
    method: 'GET',
    params,
  });
}

export async function noticUploadResult(
  params: API.NoticeUploadResultRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/resource/notice/upload/result', {
    method: 'POST',
    data: params,
  });
}
export async function uploadPaperUrl(
  params: API.UploadPaperUrlRequestParams,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/resource/import/paper', {
    method: 'POST',
    data: params,
  });
}

export async function fetchUploadHistoryList(
  params: API.Common.CommonListRequestParams,
): Promise<API.UploadWordListResponse> {
  return request('/api/v1/resource/get/upload/paper/list', {
    method: 'GET',
    params,
  });
}

export async function failedPaperRecordDelete(params: {
  paperId: string;
}): Promise<API.Common.CommonResponse> {
  return request('/api/v1/resource/delete/failed/paper', {
    method: 'POST',
    data: params,
  });
}

export async function uploadWordToOss(url: string, file: UploadFile) {
  const formData = new FormData();
  formData.append('file', file as unknown as File);
  // formData.append('x-oss-credential', 'LTAI5tPsnnDoL3Fh1uPdMDwe/20250320/cn-beijing/oss/aliyun_v4_request');
  // formData.append('x-oss-date', '20250320T103428Z');
  // formData.append('x-oss-expires', '600');
  // formData.append('x-oss-signature', 'f4992041a7a6eecc70401d9baf5d841d7433055c1b4178f700535c5410ffed27');
  // formData.append('x-oss-signature-version', 'OSS4-HMAC-SHA256');

  const res = await fetch(url, {
    method: 'PUT',
    body: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  if (!res.ok) {
    return {
      code: 1,
      data: null,
    };
  }
  return {
    code: 0,
    data: res.json(),
  };
}

export async function uploadFileToOss(
  fileName: string,
  data: API.OssPolicyTokenType,
  file: UploadFile,
) {
  const formData = new FormData();

  formData.append('success_action_status', '200');
  formData.append('policy', data.policy);
  formData.append('x-oss-signature', data.signature);
  formData.append('x-oss-signature-version', data.x_oss_signature_version);
  formData.append('x-oss-credential', data.x_oss_credential);
  formData.append('x-oss-date', data.x_oss_date);
  formData.append('key', data.dir + '/' + fileName); // 文件名
  formData.append('x-oss-security-token', data.security_token);
  formData.append('file', file as unknown as File); // file 必须为最后一个表单域

  const res = await fetch(data.host, {
    method: 'POST',
    body: formData,
  });

  if (!res.ok) {
    return {
      code: 1,
      data: null,
    };
  }
  return {
    code: 0,
    data: res,
  };
}

export async function fetchParseResult(params: any): Promise<any> {
  return request('/api/v1/resource/get/parse/paper/result', {
    method: 'GET',
    params,
  });
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const fetchParseResultForSwr = ([_, paperId]: [string, number]) => {
  return request('/api/v1/resource/get/parse/paper/result', {
    method: 'GET',
    params: {
      paperId,
    },
  });
};

export async function modifyQuestion(
  params: API.ModifyQuestionData,
): Promise<API.Common.CommonResponse> {
  return request('/api/v1/resource/modify/question', {
    method: 'POST',
    data: params,
  });
}

export async function deleteQuestion(params: {
  questionId: string;
}): Promise<API.Common.CommonResponse> {
  return request('/api/v1/resource/delete/question', {
    method: 'POST',
    data: params,
  });
}
