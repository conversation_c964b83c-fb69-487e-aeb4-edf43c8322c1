import HomepageEmptyIcon from "@/public/assign/homepage-empty.svg";
import NormalEmptyIcon from "@/public/assign/normal-empty.svg";
import TaskEmptyIcon from "@/public/assign/task-empty.svg";
import { cn } from "@/utils/utils";

import { useMemo } from "react";
interface AssignEmptyProps {
  style?: React.CSSProperties;
  className?: string;
  type: "course" | "homework" | "test" | "resource" | "homepage" | "normal";
  content?: React.ReactNode;
}

export function AssignEmpty({
  style = {},
  className = "",
  type,
  content = "请选择布置对象，再设置内容哦~",
}: AssignEmptyProps) {
  const EmptyImage = useMemo(() => {
    switch (type) {
      case "homepage":
        return HomepageEmptyIcon;
      case "normal":
        return NormalEmptyIcon;
      case "course":
      default:
        return TaskEmptyIcon;
    }
  }, [type]);
  return (
    <div
      className={cn(
        "flex select-none flex-col items-center justify-center rounded-md border border-slate-600/10 bg-white",
        className
      )}
      style={style}
    >
      <EmptyImage className="h-30 w-30" />
      <div className="mt-2 text-base font-normal leading-normal text-slate-400">
        {content}
      </div>
    </div>
  );
}
