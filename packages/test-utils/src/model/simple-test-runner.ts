/**
 * 简单测试运行器 - 统一的测试断言函数
 * 用于 Model 层的基础功能测试
 */

export interface TestResult {
  description: string;
  status: 'PASS' | 'FAIL';
  error: string | null;
}

let totalTests = 0;
let passedTests = 0;
let failedTests = 0;
const testResults: TestResult[] = [];

/**
 * 运行单个测试用例
 * @param description 测试描述
 * @param testFn 测试函数
 */
export function test(description: string, testFn: () => void) {
  totalTests++;
  try {
    testFn();
    passedTests++;
    testResults.push({ description, status: 'PASS', error: null });
    console.log(`✅ ${description}`);
  } catch (error) {
    failedTests++;
    const errorMessage = error instanceof Error ? error.message : String(error);
    testResults.push({ description, status: 'FAIL', error: errorMessage });
    console.error(`❌ ${description}: ${errorMessage}`);
  }
}

/**
 * 断言函数集合
 * @param actual 实际值
 * @returns 断言方法对象，包含正向和反向断言
 *
 * @example
 * ```typescript
 * // 正向断言
 * expect(1 + 1).toBe(2);
 * expect([1, 2, 3]).toEqual([1, 2, 3]);
 * expect(() => { throw new Error() }).toThrow();
 *
 * // 反向断言
 * expect(1 + 1).not.toBe(3);
 * expect(() => { return 'success' }).not.toThrow();
 * expect(true).not.toBeFalsy();
 * ```
 */
export function expect(actual: unknown) {
  // 正向断言函数
  const assertions = {
    /**
     * 严格相等断言
     * @param expected 期望值
     */
    toBe: (expected: unknown) => {
      if (actual !== expected) {
        throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 深度相等断言
     * @param expected 期望值
     */
    toEqual: (expected: unknown) => {
      const actualStr = JSON.stringify(actual);
      const expectedStr = JSON.stringify(expected);
      if (actualStr !== expectedStr) {
        throw new Error(`Expected ${expectedStr}, but got ${actualStr}`);
      }
    },

    /**
     * 函数抛出异常断言
     * @param expectedError 可选的期望错误信息
     */
    toThrow: (expectedError?: string | RegExp) => {
      if (typeof actual !== 'function') {
        throw new Error('Expected a function that throws, but got ' + typeof actual);
      }
      try {
        actual();
        throw new Error('Expected function to throw, but it did not');
      } catch (e) {
        // 如果指定了期望的错误信息，进行匹配
        if (expectedError) {
          const errorMessage = e instanceof Error ? e.message : String(e);
          if (typeof expectedError === 'string') {
            if (!errorMessage.includes(expectedError)) {
              throw new Error(`Expected error to contain "${expectedError}", but got "${errorMessage}"`);
            }
          } else if (expectedError instanceof RegExp) {
            if (!expectedError.test(errorMessage)) {
              throw new Error(`Expected error to match ${expectedError}, but got "${errorMessage}"`);
            }
          }
        }
        // 函数确实抛出了错误，测试通过
      }
    },

    /**
     * 真值断言
     */
    toBeTruthy: () => {
      if (!actual) {
        throw new Error(`Expected truthy value, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 假值断言
     */
    toBeFalsy: () => {
      if (actual) {
        throw new Error(`Expected falsy value, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 定义断言
     */
    toBeDefined: () => {
      if (actual === undefined) {
        throw new Error('Expected value to be defined, but got undefined');
      }
    },

    /**
     * 未定义断言
     */
    toBeUndefined: () => {
      if (actual !== undefined) {
        throw new Error(`Expected undefined, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 空值断言
     */
    toBeNull: () => {
      if (actual !== null) {
        throw new Error(`Expected null, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 数组包含断言
     * @param expected 期望包含的值
     */
    toContain: (expected: unknown) => {
      if (!Array.isArray(actual)) {
        throw new Error(`Expected array, but got ${typeof actual}`);
      }
      if (actual.indexOf(expected) === -1) {
        throw new Error(`Expected array to contain ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 数组长度断言
     * @param expected 期望的长度
     */
    toHaveLength: (expected: number) => {
      if (!actual || typeof (actual as any).length !== 'number') {
        throw new Error(`Expected object with length property, but got ${typeof actual}`);
      }
      if ((actual as any).length !== expected) {
        throw new Error(`Expected length ${expected}, but got ${(actual as any).length}`);
      }
    }
  };

  // 反向断言函数
  const notAssertions = {
    /**
     * 严格不相等断言
     * @param expected 不期望的值
     */
    toBe: (expected: unknown) => {
      if (actual === expected) {
        throw new Error(`Expected ${JSON.stringify(actual)} not to be ${JSON.stringify(expected)}`);
      }
    },

    /**
     * 深度不相等断言
     * @param expected 不期望的值
     */
    toEqual: (expected: unknown) => {
      const actualStr = JSON.stringify(actual);
      const expectedStr = JSON.stringify(expected);
      if (actualStr === expectedStr) {
        throw new Error(`Expected ${actualStr} not to equal ${expectedStr}`);
      }
    },

    /**
     * 函数不抛出异常断言
     */
    toThrow: () => {
      if (typeof actual !== 'function') {
        throw new Error('Expected a function for not.toThrow(), but got ' + typeof actual);
      }
      try {
        actual();
        // 函数没有抛出错误，测试通过
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        throw new Error(`Expected function not to throw, but it threw: ${errorMessage}`);
      }
    },

    /**
     * 非真值断言
     */
    toBeTruthy: () => {
      if (actual) {
        throw new Error(`Expected falsy value, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 非假值断言
     */
    toBeFalsy: () => {
      if (!actual) {
        throw new Error(`Expected truthy value, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 非定义断言
     */
    toBeDefined: () => {
      if (actual !== undefined) {
        throw new Error(`Expected undefined, but got ${JSON.stringify(actual)}`);
      }
    },

    /**
     * 非未定义断言
     */
    toBeUndefined: () => {
      if (actual === undefined) {
        throw new Error('Expected value to be defined, but got undefined');
      }
    },

    /**
     * 非空值断言
     */
    toBeNull: () => {
      if (actual === null) {
        throw new Error('Expected value not to be null, but got null');
      }
    },

    /**
     * 数组不包含断言
     * @param expected 不期望包含的值
     */
    toContain: (expected: unknown) => {
      if (!Array.isArray(actual)) {
        throw new Error(`Expected array, but got ${typeof actual}`);
      }
      if (actual.indexOf(expected) !== -1) {
        throw new Error(`Expected array not to contain ${JSON.stringify(expected)}, but it does`);
      }
    },

    /**
     * 数组长度不等断言
     * @param expected 不期望的长度
     */
    toHaveLength: (expected: number) => {
      if (!actual || typeof (actual as any).length !== 'number') {
        throw new Error(`Expected object with length property, but got ${typeof actual}`);
      }
      if ((actual as any).length === expected) {
        throw new Error(`Expected length not to be ${expected}, but got ${(actual as any).length}`);
      }
    }
  };

  return {
    ...assertions,
    /**
     * 反向断言操作符
     * 支持所有正向断言的反向操作
     *
     * @example
     * ```typescript
     * expect(1 + 1).not.toBe(3);
     * expect(() => { return 'success' }).not.toThrow();
     * expect([1, 2, 3]).not.toContain(4);
     * ```
     */
    not: notAssertions
  };
}

/**
 * 获取测试统计信息
 * @returns 测试统计对象
 */
export function getTestStats() {
  return {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    results: testResults,
    passRate: totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : '0'
  };
}

/**
 * 重置测试统计信息
 */
export function resetTestStats() {
  totalTests = 0;
  passedTests = 0;
  failedTests = 0;
  testResults.length = 0;
}

/**
 * 生成测试报告摘要
 * @returns 格式化的测试报告字符串
 */
export function generateTestSummary(): string {
  const stats = getTestStats();
  const status = stats.failed === 0 ? '🎉 所有测试通过！' : '⚠️ 有测试失败，需要修复';
  
  return `
📊 测试统计:
   总测试数: ${stats.total}
   通过: ${stats.passed} (${stats.passRate}%)
   失败: ${stats.failed} (${(100 - parseFloat(stats.passRate)).toFixed(1)}%)

${status}
`;
}