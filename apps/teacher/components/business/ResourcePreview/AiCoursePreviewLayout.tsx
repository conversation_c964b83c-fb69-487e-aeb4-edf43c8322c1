import { PageHeader } from "@/components/PageHeader";
import { minutesToString } from "@/utils";
import dynamic from "next/dynamic";
import { useMemo } from "react";
import { AiCoursePreviewProps } from "../AiCoursePreview";
import { PageContainer } from "./PageContainer";
import { useAiCoursePreviewModel } from "./hooks/useAiCoursePreviewModel";
import { useAiCourseReportModel } from "./hooks/useAiCourseReportModel";

const LazyAiCoursePreview = dynamic(() => import("../AiCoursePreview"));

export default function AiCoursePreviewLayout({
  id,
  onBack,
  headerSuffixNode,
  handlerRef,
  onEnterFullscreen,
  onExitFullscreen,
}: {
  id: string;
  onBack?: () => void;
  headerSuffixNode?: React.ReactNode;
  handlerRef?: AiCoursePreviewProps["handlerRef"];
  onEnterFullscreen?: () => void;
  onExitFullscreen?: () => void;
}) {
  const {
    aiCourseData,
    isFetchingAiCourse,
    widgetData,
    activeIndex,
    handleActiveIndexChange,
    isWidgetDataLoading,
  } = useAiCoursePreviewModel(id);

  const { reportWidget, handleQuestionIndexChange } = useAiCourseReportModel({
    aiCourseData,
    widgetData,
  });

  const title = useMemo(() => {
    if (!aiCourseData) return "";

    const { lessonName, lessonShowInfo, bizTreeNodeList } = aiCourseData;
    const time = minutesToString(
      lessonShowInfo.widgetList.reduce<number>(
        (acc, curr) => acc + (curr.duration || 0),
        0
      ) / 60
    );
    return `课程学习：${bizTreeNodeList[0]?.bizTreeNodeName || lessonName}${time ? `（约${time}）` : ""}`;
  }, [aiCourseData]);

  return (
    <PageContainer>
      <div className="flex-0 h-17.5 flex items-center justify-between pr-6">
        <PageHeader
          className={`h-17.5 flex items-center gap-3 truncate`}
          onBack={onBack}
          needBack={!!onBack}
        >
          <h1 className="text-gray-1 truncate whitespace-nowrap text-xl font-medium leading-[normal] tracking-wider">
            {title}
          </h1>
        </PageHeader>
        {/* 后缀 */}
        {headerSuffixNode}
      </div>

      <LazyAiCoursePreview
        data={aiCourseData}
        isFetchingData={isFetchingAiCourse}
        activeIndex={activeIndex.value}
        widgetData={widgetData}
        isWidgetDataLoading={isWidgetDataLoading}
        handleActiveIndexChange={handleActiveIndexChange}
        handleQuestionIndexChange={handleQuestionIndexChange}
        reportWidget={reportWidget}
        handlerRef={handlerRef}
        onEnterFullscreen={onEnterFullscreen}
        onExitFullscreen={onExitFullscreen}
      />
    </PageContainer>
  );
}
