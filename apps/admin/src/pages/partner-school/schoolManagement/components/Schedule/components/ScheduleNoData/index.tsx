import { NodeType } from '@/components/GradeTree';
import { ScheduleContext } from '@/pages/partner-school/schoolManagement/contexts/ScheduleContext';
import { PlusOutlined, ScheduleOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import dayjs from 'dayjs';
import React, { useContext } from 'react';

const ScheduleNoData: React.FC = () => {
  const { selectedNodeInfo, scheduleData, setEditMode, weekInfo } = useContext(ScheduleContext);
  if (!selectedNodeInfo || scheduleData) return null;

  const { nodeType } = selectedNodeInfo;
  const isGradeView = nodeType === NodeType.Grade;
  // 选择时间后，是否在今天之前
  const isBeforeToday = dayjs(weekInfo?.endDate).isBefore(dayjs());

  // 创建年级课表按钮
  const renderGradeCreateButton = () =>
    isBeforeToday ? null : (
      <Button
        className="mt-6"
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setEditMode('grade')}
      >
        创建年级课表模版
      </Button>
    );

  // 导入班级课表按钮
  const renderClassImportButton = () => (
    <div className="text-gray-400 mb-5">请先创建年级课表模板</div>
  );

  return (
    <div className="flex flex-col items-center justify-center h-full py-10 mt-10">
      <ScheduleOutlined className="text-5xl text-gray-300 mb-4" />
      <div className="text-gray-600">暂无{isGradeView ? '年级' : '班级'}课表数据</div>
      {isGradeView ? renderGradeCreateButton() : renderClassImportButton()}
    </div>
  );
};

export default ScheduleNoData;
