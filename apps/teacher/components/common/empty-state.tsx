// import Image from "next/image"
import NoResultIcon from "@/public/images/normal-empty.svg";
import { cn } from "@/utils/utils";

export function EmptyState({
  title,
  description,
  className,
}: {
  title?: string | false;
  description?: string | false;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex h-full flex-1 flex-col items-center justify-center text-center",
        className
      )}
    >
      <div className="mb-4 flex items-center justify-center">
        {/* <Image src={noResult} alt="no result" className="w-[11.75rem] h-[8rem] flex-shrink-0 aspect-[47/32]" /> */}
        <NoResultIcon className="aspect-[47/32] flex-shrink-0" />
      </div>
      <h3 className="text-gray-2 mb-1 text-center text-base font-normal leading-[150%]">
        {title ?? "没有找到相关结果哦~"}
      </h3>
      <p className="text-gray-4 mt-1 text-center text-xs font-normal leading-[150%]">
        {description ?? "搜索下别的关键词试一试"}
      </p>
    </div>
  );
}
