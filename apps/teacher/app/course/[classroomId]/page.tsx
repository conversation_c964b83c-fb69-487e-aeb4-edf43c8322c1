"use client";
import { PageHeader } from "@/components/PageHeader";
import { ClassStatus } from "@/enums";
import { useApp, useUmeng } from "@/hooks";
import { getBehaviorCategorys, getCourseInfo } from "@/services";
import { StudentInfo } from "@/types/course";
import { ScrollArea } from "@/ui/scroll-area";
import { TchSheet } from "@/ui/tch-sheet";
import {
  umeng,
  UmengCategory,
  UmengCourseAction,
  UmengCoursePageName,
} from "@/utils";
import { useCreation, useMount, useRequest } from "ahooks";
import { useParams } from "next/navigation";
import { useState } from "react";
import { match } from "ts-pattern";
import CourseScan from "./_components/course-scan";
import { DynamicRanking } from "./_components/dynamic-ranking";
import { LearningStatus } from "./_components/learning-status";
import { StudentDetail } from "./_components/student-detail";

export default function CourseDetailPage() {
  const { setOpen } = useApp();

  useMount(() => setOpen(false));

  useUmeng(UmengCategory.COURSE, UmengCoursePageName.CLASSROOM_REPORT);

  const { classroomId } = useParams<{ classroomId: string }>()!;
  // 是否在上课中
  const [isInClass, setIsInClass] = useState<boolean | null>(null);

  const { data: courseInfo } = useRequest(
    () => getCourseInfo({ classroomId }),
    {
      pollingInterval: 10000,
      ready: isInClass || isInClass === null,
      onSuccess(data) {
        setIsInClass(data.isInClass ?? false);
      },
    }
  );

  // 是不是学科老师
  const isSubjectTeacher = useCreation(() => {
    if (!courseInfo) {
      return false;
    }

    return courseInfo.subjects.some((subject) => {
      return subject.subjectName === courseInfo.subject;
    });
  }, [courseInfo]);

  // 要查看的学生ID
  const [student, _setStudent] = useState<StudentInfo | null>(null);

  const setStudent = (student: StudentInfo | null) => {
    _setStudent(student);

    if (!student) {
      return;
    }

    umeng.trackEvent(
      UmengCategory.COURSE,
      UmengCourseAction.CLASSROOM_REPORT_SINGLE_CLICK1,
      {
        single_detail_open: "对单人学生打开查看详情",
      }
    );

    umeng.trackEvent(
      UmengCategory.COURSE,
      UmengCourseAction.CLASSROOM_REPORT_SINGLE_CLICK2,
      {
        classroom_status: courseInfo?.isInClass
          ? "课中ongoing"
          : "完课finished",
      }
    );
  };

  const {
    data: studentLatestBehaviorData,
    run: getStudentLatestBehaviorData,
    loading,
  } = useRequest(
    () =>
      getBehaviorCategorys({
        classroomId,
      }),
    {
      pollingInterval: 10000,
      ready: isInClass || isInClass === null,
      // onSuccess: (data) => {
      //   if (activeTab === "good") {
      //     umeng.trackEvent(
      //       UmengCategory.COURSE,
      //       UmengCourseAction.CLASSROOM_REPORT_ACTIVE_LABEL_SHOW1,
      //       {
      //         like_suggested: data?.praiseList.length ?? 0,
      //       }
      //     );
      //     umeng.trackEvent(
      //       UmengCategory.COURSE,
      //       UmengCourseAction.CLASSROOM_REPORT_ACTIVE_LABEL_SHOW2,
      //       {
      //         classroom_status: isInClass ? "课中ongoing" : "课后after",
      //       }
      //     );

      //     return;
      //   }

      //   umeng.trackEvent(
      //     UmengCategory.COURSE,
      //     UmengCourseAction.CLASSROOM_REPORT_NEGATIVE_LABEL_SHOW1,
      //     {
      //       follow_suggested: data?.attentionList.length ?? 0,
      //     }
      //   );

      //   umeng.trackEvent(
      //     UmengCategory.COURSE,
      //     UmengCourseAction.CLASSROOM_REPORT_NEGATIVE_LABEL_SHOW2,
      //     {
      //       classroom_status: isInClass ? "课中ongoing" : "课后after",
      //     }
      //   );
      // },
    }
  );

  return (
    <div className="flex h-full flex-col">
      <PageHeader className="flex-none" needBack>
        <div className="flex translate-y-px items-center gap-2">
          <span className="text-gray-1 ml-4 text-xl/normal font-semibold">
            {courseInfo
              ? courseInfo.gradeName + courseInfo.className + courseInfo.subject
              : ""}
          </span>
          {isInClass && (
            <span className="h-4.5 bg-green-1 inline-flex items-center rounded-sm px-1 text-xs/normal font-medium text-white">
              <span className="mr-0.5 h-1 w-1 rounded-full bg-white"></span>
              上课中
            </span>
          )}
        </div>
      </PageHeader>

      {/* 主要内容区域 */}
      {match(studentLatestBehaviorData?.classStatus)
        .with(ClassStatus.Within30Seconds, () => <CourseScan />)
        .otherwise(() => (
          <ScrollArea className="flex-1 overflow-hidden">
            <main className="space-y-6 px-6 py-4">
              <DynamicRanking
                loading={loading}
                getStudentLatestBehaviorData={getStudentLatestBehaviorData}
                studentLatestBehaviorData={studentLatestBehaviorData}
                isInClass={isInClass ?? false}
                subject={courseInfo?.subject ?? ""}
                isSubjectTeacher={isSubjectTeacher}
                classroomId={classroomId}
                setStudent={setStudent}
              />

              {isInClass && (
                <LearningStatus
                  classroomId={classroomId}
                  setStudent={setStudent}
                  studentData={studentLatestBehaviorData?.allStudentsList ?? []}
                />
              )}

              {/* <CourseRanking
                isInClass={isInClass}
                classroomId={classroomId}
                setStudent={setStudent}
              /> */}
            </main>
          </ScrollArea>
        ))}

      {/* 学生详情 Sheet */}
      <TchSheet className="w-130 max-w-130! outline-0" open={Boolean(student)}>
        <StudentDetail
          classroomId={classroomId}
          isInClass={isInClass ?? false}
          student={student!}
          setStudent={setStudent}
          subject={courseInfo?.subject ?? ""}
          isSubjectTeacher={isSubjectTeacher}
        />
      </TchSheet>
    </div>
  );
}
