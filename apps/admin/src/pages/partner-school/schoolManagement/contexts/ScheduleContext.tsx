import { ScheduleData } from '@/services/schedule-management/type';
import { Semester } from '@/services/semester-management/type';
import { SchoolItem } from '@/types/school';
import { createContext } from 'react';
import { WeekInfo } from '../components/Schedule/components/SemesterWeekSelector/useSemesterWeekSelector';
import { EditModeType, ScheduleLoadingState, SelectedNodeInfo } from '../types';

interface ScheduleContextType {
  name: string;
  selectedNodeInfo: SelectedNodeInfo | null;
  scheduleData: ScheduleData | null;
  hasSemesterData: boolean;
  loading: ScheduleLoadingState;
  editMode: EditModeType;
  schoolInfo: SchoolItem | undefined;
  semesterInfo: Semester | null;
  weekInfo: WeekInfo | null;
  handleImportSuccess: () => void;
  setEditMode: React.Dispatch<React.SetStateAction<EditModeType>>;
  SemesterWeekSelector: React.ReactNode;
  handleScheduleAction: (action: 'save' | 'cancel') => void;
  setStartDate: (date: string) => void;
}

const ScheduleContext = createContext<ScheduleContextType>({
  name: 'schedule',
  selectedNodeInfo: null,
  scheduleData: null,
  hasSemesterData: false,
  loading: { tree: false, schedule: false },
  editMode: false,
  schoolInfo: undefined,
  semesterInfo: null,
  weekInfo: null,
  handleImportSuccess: () => {},
  setEditMode: () => {},
  SemesterWeekSelector: null,
  handleScheduleAction: () => {},
  setStartDate: () => {},
});

export { ScheduleContext, ScheduleContextType };
