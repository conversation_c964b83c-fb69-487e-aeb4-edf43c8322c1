import FilterPanel from '@/components/FilterPanel';
import QuestionSearch from '@/components/QuestionSearch';
import TreeNodeFilter from '@/components/TreeNodeFilter';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { ChangeEventPayload } from '@/types/common/common';
import { sortOptions } from '@/utils/constants';
import { defaultQuestionListRequestParams } from '@/utils/tree';
import { ContainerOutlined, LeftOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Affix,
  Badge,
  Button,
  Card,
  Col,
  Flex,
  Modal,
  Radio,
  RadioChangeEvent,
  Row,
  Space,
  theme,
  Typography,
} from 'antd';
import React, { FC, useContext, useEffect, useState } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';
import ExerciseProgressInfo from '../ExerciseProgressInfo';
import ExerciseSelectedQuestionList from '../ExerciseSelectedQuestionList';
import ExerciseSelectQuestionList from '../ExerciseSelectQuestionList';

export interface ExerciseSelectQuestionModalProps {
  node?: API.Common.BaseTreeNodeAntdType;
  showModal: boolean;
  onCloseModal: () => void;
}
const ExerciseSelectQuestionModal: FC<ExerciseSelectQuestionModalProps> = (props) => {
  const { node, showModal, onCloseModal } = props;
  const { data: questionFilterEnumData, run: fetchQuestionFilterEnumRun } =
    useModel('questionFilterEnumModel');
  const { selectedSubject } = useContext(ExerciseContext);
  const [questionListRequestParams, setQuestionListRequestParams] =
    useState<API.QuestionListRequestParams>(defaultQuestionListRequestParams());
  const [sort, setSort] = useState<string>('newest');
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [top] = React.useState<number>(26);
  const [showSelectedQuestionView, setShowSelectedQuestionView] = useState<boolean>(false);

  const { useToken } = theme;
  const { token } = useToken();
  const ease = {
    least: 20,
    current: 10,
  };
  const relativelyEasy = {
    least: 20,
    current: 10,
  };
  const medium = {
    least: 20,
    current: 10,
  };
  const relativelyHard = {
    least: 20,
    current: 10,
  };
  const hard = {
    least: 20,
    current: 10,
  };

  useEffect(() => {
    if (!questionFilterEnumData) {
      console.log('筛选问题：获取枚举值: ', questionFilterEnumData);
      fetchQuestionFilterEnumRun();
    }
  }, [questionFilterEnumData]);

  // useEffect(() => {
  //     console.log('获取到问题过滤条件枚举：', questionFilterEnumData);
  //     if (questionFilterEnumData) {
  //         const { filters } = questionFilterEnumData;
  //         const filterParams: QuestionListFilters = {};
  //         filters.forEach((filter) => {
  //             filterParams[filter.field] = [];
  //         });
  //         const requestParams = defaultQuestionListRequestParams();
  //         setQuestionListRequestParams({
  //             ...requestParams,
  //             filters: filterParams,
  //         });
  //     }
  // }, [questionFilterEnumData]);

  useEffect(() => {
    console.log('科目变化了2: ', selectedSubject);
  }, [selectedSubject]);
  useEffect(() => {
    if (questionListRequestParams) {
      console.log('科目变化了222: ', questionListRequestParams);
    }
  }, [questionListRequestParams]);

  const onBack = () => {
    if (showSelectedQuestionView) {
      setShowSelectedQuestionView(false);
      return;
    }
    onCloseModal();
  };

  /**
   * 显示已选题目视图
   */
  const onSelectedQuestion = () => {
    setShowSelectedQuestionView(true);
  };

  // 问题列表的过滤条件改变
  const onFilterChange = (params: ChangeEventPayload) => {
    console.log('选题-筛选条件处理: ', params, questionListRequestParams);
    const { type, field, value } = params;
    if (!questionListRequestParams) {
      return;
    }

    if (type === 'search') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        search: value,
        page: 1,
      });
      return;
    }
    questionListRequestParams.page = 1;
    if (type === 'filter') {
      const filters = { ...questionListRequestParams?.filters };
      filters[field] = value;
      setQuestionListRequestParams({
        ...questionListRequestParams,
        filters,
        page: 1,
      });
      return;
    }

    if (type === 'sort') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        sort: value,
      });
      return;
    }

    if (type === 'reset') {
      const { phaseList, subjectList, baseTreeNodeIds, bizTreeNodeIds, sort, keyword } =
        questionListRequestParams;
      const requestParams = defaultQuestionListRequestParams();
      setQuestionListRequestParams({
        ...requestParams,
        phaseList,
        subjectList,
        baseTreeNodeIds,
        bizTreeNodeIds,
        sort,
        keyword,
      });
      return;
    }

    // 发送请求
  };
  const onSortTypeChange = (e: RadioChangeEvent) => {
    const { value } = e.target;
    setSort(value);
    onFilterChange({
      type: 'sort',
      field: 'sort',
      value,
    });
  };
  const onPageChange = (page: number, pageSize: number) => {
    console.log('onPageChange: ', page, pageSize);
    if (!questionListRequestParams) {
      return;
    }
    setQuestionListRequestParams({
      ...questionListRequestParams,
      page,
      pageSize,
    });
  };

  const onKeywordSearch = (keyword: string) => {
    onFilterChange({
      type: 'search',
      field: '',
      value: keyword,
    });
  };

  return (
    <Modal
      centered
      open={showModal}
      mask={false}
      width={'100vw'}
      height={'100vh'}
      closable={false}
      footer={null}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        // padding: 0,
        borderRadius: 0,
        maxWidth: '100vw',
        maxHeight: '100vh',
      }}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 0,
          height: '100vh',
          borderRadius: 0,
          backgroundColor: token.colorBgLayout,
        },
      }}
    >
      <div style={{ height: '100vh', padding: '50px 0 0' }}>
        <Flex
          align="center"
          justify="space-between"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            height: 50,
            padding: '0 20px 0 0',
          }}
          gap={16}
        >
          <Button
            type="text"
            size="large"
            icon={<LeftOutlined />}
            style={{ gap: 4 }}
            onClick={onBack}
          >
            返回
          </Button>
          <ExerciseBaseInfo node={node} copyable={false} />
          <ExerciseProgressInfo
            ease={ease}
            relativelyEasy={relativelyEasy}
            medium={medium}
            relativelyHard={relativelyHard}
            hard={hard}
          />
          <Typography.Text>已选题目</Typography.Text>

          <Badge count={1} size="small">
            <Button
              type="text"
              icon={<ContainerOutlined style={{ fontSize: 20 }} />}
              size="small"
              onClick={onSelectedQuestion}
            />
          </Badge>
        </Flex>
        {!showSelectedQuestionView ? (
          <Card style={{ height: '100%', overflow: 'auto', borderRadius: 0 }} ref={setContainer}>
            <Row gutter={16}>
              <Col span={6}>
                <Affix offsetTop={top} target={() => container}>
                  <TreeNodeFilter selectedSubject={selectedSubject} onChange={() => {}} />
                </Affix>
              </Col>
              <Col span={18}>
                <Space direction="vertical" size={16}>
                  <Card>
                    <FilterPanel
                      filters={questionListRequestParams?.filters}
                      onChange={onFilterChange}
                    />
                  </Card>
                  <Flex align="center" justify="space-between">
                    <Radio.Group
                      value={sort}
                      options={sortOptions}
                      defaultValue="Apple"
                      optionType="button"
                      buttonStyle="solid"
                      onChange={onSortTypeChange}
                    />
                    {/* <Input.Search placeholder="搜索" /> */}
                    <QuestionSearch
                      onSearch={onKeywordSearch}
                      value={questionListRequestParams?.search}
                    />
                  </Flex>
                  <ExerciseSelectQuestionList
                    requestParams={questionListRequestParams}
                    onPageChange={onPageChange}
                  />
                </Space>
              </Col>
            </Row>
          </Card>
        ) : (
          <Card style={{ height: '100%', overflow: 'auto', borderRadius: 0 }}>
            <ExerciseSelectedQuestionList questions={[]} />
          </Card>
        )}
      </div>
    </Modal>
  );
};
export default ExerciseSelectQuestionModal;
