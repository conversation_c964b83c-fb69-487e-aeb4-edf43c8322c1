"use client";

import fetcher from "@/app/utils/fetcher";
import { useEffect } from "react";
import { SWRConfig } from "swr";

export default function Layout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  useEffect(() => {
    document.fonts.forEach((font) => {
      if ([400, 500, 700].includes(Number(font.weight))) {
        font.load();
      }
    });
  }, []);

  return (
    <SWRConfig value={{ fetcher, refreshInterval: 0 }}>
      <div className="font-resource-han-rounded h-screen w-full overflow-hidden">
        {children}
      </div>
    </SWRConfig>
  );
}
