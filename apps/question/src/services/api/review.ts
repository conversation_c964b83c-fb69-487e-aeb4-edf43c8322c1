import { request } from '@umijs/max';

/**
 * 获取审核任务列表
 */
export const getAuditTaskList = (params: {
  auditTaskId?: number;
  auditTaskType?: number;
  auditStatus?: number;
  auditTaskName?: string;
  submiter?: string;
  phase?: number;
  subject?: number;
  page: number;
  pageSize: number;
}) => {
  return request<API.Response<API.PageData<API.Review.AuditTaskItem>>>(
    '/api/v1/audit/task/get/list',
    {
      method: 'GET',
      params,
    },
  );
};

/**
 * 录入题目类型审核单题
 */
export const fetchAuditTaskList = (data: API.Review.AuditTaskListRequestParams) => {
  return request<API.Response<API.PageData<API.Review.AuditTaskItem>>>(
    '/api/v1/audit/get/task/list',
    {
      method: 'POST',
      data,
    },
  );
};

/**
 * 获取审核任务详情
 */
export const getAuditTaskDetail = (params: { auditTaskId: number }) => {
  return request<API.Response<API.Review.AuditTaskDetail>>('/api/v1/audit/get/audit/task/detail', {
    method: 'GET',
    params,
  });
};

/**
 * 审核任务撤回
 */
export const withdrawAuditTask = (params: { auditTaskId: number }) => {
  return request<API.Response<void>>('/api/v1/audit/task/withdraw', {
    method: 'POST',
    data: params,
  });
};

/**
 * 录入题目类型审核单题
 */
export const submitImportQuestionAudit = (data: {
  answerWrong: string;
  auditStatus: number;
  auditTaskId: number;
  baseTreeNodeIds: number[];
  explanationWrong: string;
  questionId: string;
  questionWrong: string;
}) => {
  return request<API.Response<void>>('/api/v1/audit/proc/import/question', {
    method: 'POST',
    data,
  });
};

/**
 * 审核任务提交
 */
export const submitAuditTask = (data: {
  auditTaskId: number;
  auditTaskStatus?: number;
  reason?: string;
}) => {
  return request<API.Response<void>>('/api/v1/audit/submit', {
    method: 'POST',
    data,
  });
};

/**
 * 选题类型审核单题
 */
export const submitChooseQuestionAudit = (data: {
  /**
   * 审核状态
   */
  auditStatus: number;
  /**
   * 审核任务id
   */
  auditTaskId: number;
  /**
   * 题目id
   */
  questionId: string;
  // 其他参数根据实际需求添加
}) => {
  return request<API.Response<void>>('/api/v1/audit/proc/choose/question', {
    method: 'POST',
    data,
  });
};

/**
 * 创建审核任务
 */
export const createAuditTask = (data: {
  auditTaskType: number;
  auditTaskName?: string;
  phase?: number;
  subject?: number;
  chooseQuestion?: {
    questionSetId: number;
  };
  importQuestion?: {
    paperId: string;
    list: {
      questionId: string;
    }[];
  };
  fixQuestion?: {
    questionId: string;
  };
  // 其他参数根据实际需求添加
}) => {
  return request<
    API.Response<any> & {
      detail?: {
        questionId: string;
        questionIdx: number;
        questionFieldPath: string;
        questionErrorMsg: string;
      }[];
    }
  >('/api/v1/audit/task/create', {
    method: 'POST',
    data,
  });
};
