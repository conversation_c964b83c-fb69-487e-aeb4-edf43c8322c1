"use client";
import { Loading } from "@/app/components/common/loading";
import { setStatusBar } from "@/app/utils/device";
import { CourseView } from "@/app/views/course/course-view";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect } from "react";

function CoursePageContent() {
  // knowledgeId=231900&knowledgeId=105772&knowledgeName=集合的概念&lessonId=78&lessonName=集合的概念&phaseId=3&studySessionId=1852&studyType=1&subjectId=2
  // http://stu.local.xiaoluxue.cn/course?knowledgeId=231900&referenceId=引用ID&widgetIndex=组件索引&commentId=评论ID&commentRootId=根评论ID
  const searchParams = useSearchParams();
  const knowledgeId = searchParams.get("knowledgeId");
  const subjectId = searchParams.get("subjectId") ?? 0;
  const knowledgeName = searchParams.get("knowledgeName") ?? "";
  const lessonName = searchParams.get("lessonName") ?? "";
  const widgetIndex = searchParams.get("redirectWidgetIndex") ?? undefined;
  const commentId = searchParams.get("redirectCommentId") ?? undefined;
  const commentRootId = searchParams.get("redirectCommentRootId") ?? undefined;
  const referenceId = searchParams.get("redirectReferenceId") ?? undefined;
  const studySessionId = searchParams.get("studySessionId") ?? 0;
  const studyType = searchParams.get("studyType") ?? 0;

  return (
    <CourseView
      knowledgeId={Number(knowledgeId)}
      subjectId={Number(subjectId)}
      knowledgeName={knowledgeName}
      lessonName={lessonName}
      className="select-none"
      widgetIndex={widgetIndex}
      commentId={commentId}
      commentRootId={commentRootId}
      referenceId={referenceId}
      studySessionId={Number(studySessionId)}
      studyType={Number(studyType)}
    />
  );
}

export default function Page() {
  useEffect(() => {
    setStatusBar({
      eventType: "setStatusBarVisibility",
      isVisible: false,
    });
  }, []);
  return (
    <Suspense
      fallback={
        <div className="flex h-screen items-center justify-center">
          <Loading />
        </div>
      }
    >
      <CoursePageContent />
    </Suspense>
  );
}
