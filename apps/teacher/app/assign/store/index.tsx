"use client";
import { useCurrentSubjectLocalStorage } from "@/hooks/useCurrentSubjectLocalStorage";
import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import { UserSubjectItem } from "@/types/assign";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { createContext, useMemo } from "react";

export interface AssignContextType {
  currentSubject: Signal<UserSubjectItem>;
  subjectTaskTypes: Signal<UserSubjectItem[]>;
  setCurrentSubject: (subject: UserSubjectItem) => void;
}

export function useAssignState(): AssignContextType {
  const { setCurrentSubjectToLocalStorage, getCurrentSubjectFromLocalStorage } =
    useCurrentSubjectLocalStorage();

  const { subjectInfos: subjectTaskTypes } = useSubjectInfosByUserInfo();

  const defaultSubject = useMemo(() => {
    const cached = getCurrentSubjectFromLocalStorage();
    if (
      cached &&
      subjectTaskTypes.value.find(
        (item) => item.subjectKey === cached.subjectKey
      )
    ) {
      return {
        subjectKey: cached.subjectKey,
        subjectName:
          subjectTaskTypes.value.find(
            (item) => item.subjectKey === cached.subjectKey
          )?.subjectName || "",
        taskTypes: [10, 20, 30, 40],
      };
    }
    return subjectTaskTypes.value.length
      ? subjectTaskTypes.value[0]
      : {
          subjectKey: 0,
          subjectName: "",
          // NOTE: 这个目前是写死的，只开布置课程
          taskTypes: [10, 20, 30, 40],
        };
  }, [subjectTaskTypes.value, getCurrentSubjectFromLocalStorage]);

  // 布置页选择的科目
  const currentSubject = useSignal<UserSubjectItem>(defaultSubject);

  const setCurrentSubject = (subject: UserSubjectItem) => {
    currentSubject.value = subject;
    setCurrentSubjectToLocalStorage({
      subjectKey: subject.subjectKey,
    });
  };

  return {
    currentSubject,
    subjectTaskTypes,
    setCurrentSubject,
  };
}

export const AssignContext = createContext<AssignContextType>(
  {} as AssignContextType
);

export function AssignProvider({ children }: { children: React.ReactNode }) {
  const state = useAssignState();

  return (
    <AssignContext.Provider value={state}>{children}</AssignContext.Provider>
  );
}
