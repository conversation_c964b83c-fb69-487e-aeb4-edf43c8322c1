"use client";

import BaseTabNav from "@/components/common/tab-nav";
import { getTeacherTaskClassList, PanelItem } from "@/services/homework";
import type { StudentBase } from "@/types/homeWork";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { InputSearch } from "@/ui/searchInput";
import { toast } from "@/ui/toast";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { cn } from "@/utils/utils";
import { batch, useSignal } from "@preact-signals/safe-react";
import { useMount, useRequest, useUpdateEffect } from "ahooks";
import { ChevronDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTaskContext } from "../../_context/task-context";
import {
  useAnswerResults,
  useAnswerResultsPolling,
} from "../tabs/Results/store/answers";
import { DrawerContainer } from "./Header/course-select-drawer";

// 标签页类型
export type TabValue = "report" | "results" | "ask";

// 标签项定义
export interface TabItem<T extends string = string> {
  id: T;
  label: string;
  disabled?: boolean;
  hidden?: boolean;
}

// 辅助函数：比较URLSearchParams对象的内容是否相同 (忽略顺序)
const areUrlParamsEqual = (
  params1: URLSearchParams,
  params2: URLSearchParams
): boolean => {
  if (params1.size !== params2.size) return false;

  const keys1 = Array.from(params1.keys()).sort();
  const keys2 = Array.from(params2.keys()).sort();

  if (keys1.join(",") !== keys2.join(",")) return false;

  for (const key of keys1) {
    if (params1.getAll(key).join(",") !== params2.getAll(key).join(","))
      return false; // getAll for multi-value params
  }

  return true;
};

const tabWidth = {
  class: {
    report: {
      width: 63,
      left: 0,
    },
    results: {
      width: 63,
      left: 87,
    },
    ask: {
      width: 63,
      left: 174,
    },
  },
  student: {
    results: {
      width: 63,
      left: 0,
    },
    ask: {
      width: 63,
      left: 87,
    },
  },
};

// 作业特定的TabNav组件
export default function TabNav() {
  const {
    taskType,
    activeTab,
    assignId,
    setActiveTab,
    viewMode,
    currentCourse,
    currentClass,
    setCurrentClass,
    taskData,
    studentData,
    useFetchHomeworkDetailRequest,
    classData,
    homeworkData,
    setStudentListMap,
    taskId,
    viewModeNew,
    classList,
  } = useTaskContext();

  const router = useRouter();
  const searchParams = useSearchParams();
  const isDrawerOpen = useSignal(false);

  // 使用 ref 跟踪前一个 URL 查询字符串
  const prevUrlQueryStringRef = useRef(searchParams?.toString());

  const { filter, setFilter, fetchParams, answerResultsState, pagination } =
    useAnswerResults();

  const [classDataList, setClassDataList] = useState<
    Array<{
      taskId: number;
      assignId: number;
      classId: number;
      className: string;
    }>
  >([]);

  // 定义标签配置
  const tabs: TabItem<TabValue>[] = [
    { id: "report", label: "学习报告", hidden: viewMode.value !== "class" },
    { id: "results", label: "答题结果", hidden: taskType.value === 40 },
    { id: "ask", label: "学生提问", hidden: taskType.value === 40 },
  ];

  // 获取班级列表
  // const classList =
  //   taskData.value?.homeworkData?.reports.map((report) => ({
  //     id: report.assignObject.id,
  //     name: report.assignObject.name,
  //     gradeName: report.assignObject.gradeName,
  //   })) || [];

  const { data, run } = useFetchHomeworkDetailRequest();
  const { data: dataAnswer, run: runAnswer } = useAnswerResultsPolling();

  const { run: teacherClassRun } = useRequest(
    async () => {
      const params = {
        taskId: taskId.value,
        classes: classList.value.map((cls) => {
          return {
            classId: cls.classId,
            className: cls.className,
          };
        }),
      };
      return getTeacherTaskClassList(params);
    },
    {
      manual: true,
      debounceWait: 500,
      onError: (err) => {
        toast.error("获取班级失败");
        console.log("获取班级数据失败", err);
      },
      onSuccess: (data) => {
        // console.log("teacher class data", data);
        setClassDataList(data.data || []);
      },
    }
  );

  useMount(() => {
    if (viewMode.value === "class" && classList.value.length > 0) {
      // teacherClassRun();
    }
  });

  useUpdateEffect(() => {
    if (viewMode.value === "class" && classList.value.length > 0) {
      // teacherClassRun();
    }
  }, [viewMode.value, classList.value]);

  useUpdateEffect(() => {
    if (data) {
      homeworkData.value = data;
      setStudentListMap(
        (data.detail.studentReports as unknown as StudentBase[]) || []
      );
    }
  }, [data]);

  useUpdateEffect(() => {
    let response;
    let panelResponse: { panel: PanelItem[] } = {
      panel: [],
    };
    if (dataAnswer && dataAnswer[0]) {
      response = dataAnswer[0];
      panelResponse = dataAnswer[1];
    } else {
      response = dataAnswer;
    }
    batch(() => {
      answerResultsState.value = {
        data: response,
        panel: panelResponse?.panel,
        loading: false,
        selectedQuestionId: null,
      };
      // 如果页码超过总页数，重置到第一页
      if (
        fetchParams.value.page >
        Math.ceil(response.pageInfo.total / fetchParams.value.pageSize)
      ) {
        pagination.value.current = 1;
      }
    });
  }, [dataAnswer]);

  // 班级选择处理函数
  const handleSelectClass = useCallback(
    (classItem: { id: number; name: string }) => {
      // 只有当选择不同的班级时才更新
      if (currentClass.peek()?.id !== classItem.id) {
        console.log("[TabNav] Class selection changed to:", classItem.name);
        setCurrentClass(classItem);

        // DONE: 埋点12 => `homework_list_report_class_click` 作业报告页中点切换班级
        umeng.trackEvent(
          UmengCategory.HOMEWORK,
          UmengHomeworkAction.REPORT_CLASS_SELECT_CLICK,
          {
            eventName: "作业报告页中点切换班级",
          }
        );
      }
    },
    [currentClass, setCurrentClass]
  );

  const isClassOpen = useSignal(false);

  // URL 同步函数 - 优化性能
  const updateUrlIfNeeded = useCallback(() => {
    // 构建目标URL参数
    const buildTargetParams = () => {
      // 保留现有的所有参数
      const params = new URLSearchParams(searchParams?.toString());

      // 更新或添加必要的参数
      params.set("view", viewMode.peek());

      // 学生视图下，添加学生ID
      if (viewMode.peek() === "student" && studentData.peek().studentId) {
        params.set("studentId", studentData.peek().studentId);
        // 可选：保存学生名称到URL
        if (studentData.peek().studentName) {
          params.set("studentName", studentData.peek().studentName);
        }
      }

      // 如果有课程ID，确保它被保留
      if (viewMode.value === "class") {
        if (currentCourse.peek()?.id) {
          params.set("courseId", String(currentCourse.peek()?.id));
        } else {
          params.delete("courseId");
        }
      }

      return params;
    };

    // 获取当前URL参数和目标URL参数
    const currentParams = new URLSearchParams(searchParams?.toString());
    const targetParams = buildTargetParams();

    // 使用规范化函数比较是否需要更新URL
    if (!areUrlParamsEqual(currentParams, targetParams)) {
      const targetQueryString = targetParams.toString();

      // 更新前一次的URL查询字符串，然后更新URL
      prevUrlQueryStringRef.current = targetQueryString;

      // 判断是否是从班级视图切换到学生视图，这种情况需要添加历史记录
      const isClassToStudent =
        currentParams.get("view") !== "student" &&
        targetParams.get("view") === "student";

      if (isClassToStudent) {
        // 班级视图 → 学生视图：使用push添加历史记录
        router.push(`?${targetQueryString}`, { scroll: false });
      } else {
        // 其他情况：使用replace避免创建新的历史记录
        router.replace(`?${targetQueryString}`, { scroll: false });
      }
    }
  }, [viewMode, studentData, router, searchParams, currentCourse]);

  // 使用防抖机制来更新URL - 延长延迟时间，确保不干扰UI响应
  useEffect(() => {
    // 延长延迟时间，确保tab切换动画完成后再更新URL
    const timer = setTimeout(() => {
      if (viewModeNew.value === "student") return;
      updateUrlIfNeeded();
    }, 300); // 增加到300ms，确保不干扰UI响应

    return () => {
      clearTimeout(timer);
    };
  }, [
    activeTab.value,
    viewMode.value,
    studentData.value.studentId,
    studentData.value.studentName,
    updateUrlIfNeeded,
    currentCourse.value,
  ]);
  // console.log("currentCourse", currentCourse.value);
  // 标签页切换处理函数
  const handleTabChange = useCallback(
    (tab: string) => {
      // 确保tab是TabValue类型
      const tabValue = tab as TabValue;
      const tabLabels: Record<TabValue, string> = {
        report: "student_tab",
        results: "question_tab",
        ask: "ask_tab",
      };

      // 只有当选择不同的标签页时才更新
      if (activeTab.peek() !== tabValue) {
        // 立即更新UI状态，提高响应速度
        setActiveTab(tabValue);

        // 延迟执行非关键操作，确保UI响应优先
        setTimeout(() => {
          console.log("[TabNav] Tab changed to:", tabValue);

          // 埋点操作延迟执行，不阻塞UI
          if (viewMode.peek() === "class") {
            umeng.trackEvent(
              UmengCategory.HOMEWORK,
              UmengHomeworkAction.REPORT_TAB_CLICK,
              {
                tab: tabLabels[tabValue],
              }
            );
          } else {
            umeng.trackEvent(
              UmengCategory.HOMEWORK,
              UmengHomeworkAction.REPORT_STUDENT_TAB_CLICK,
              {
                tab: tabLabels[tabValue],
              }
            );
          }
        }, 50);
      }
    },
    [activeTab, viewMode, setActiveTab]
  );

  // 创建班级/课程选择器
  const classSelector = (
    <div className="flex items-center gap-3 text-sm">
      {/*  知识点搜索 */}
      {activeTab.value === "results" && (
        <InputSearch
          debounce
          wait={300}
          classNames={{
            input: " rounded-[1.125rem] text-sm",
          }}
          className="lg:w-50 xs:w-20 sm:w-30 h-6 md:w-40"
          placeholder="请输入题目关键词"
          value={filter.value.keyword}
          onChange={(value) => {
            setFilter({ keyword: value.target.value });
          }}
        />
      )}
      {/* 班级选择器 - 在学生视图下隐藏 */}
      {viewMode.value !== "student" && classList.value.length > 0 && (
        <DropdownMenu
          onOpenChange={(open) => {
            isClassOpen.value = open;
          }}
        >
          <DropdownMenuTrigger asChild>
            <div
              className="flex cursor-pointer items-center"
              onClick={() => {
                // DONE: 埋点12 => `homework_list_report_class_click` 作业报告页中点切换班级
                umeng.trackEvent(
                  UmengCategory.HOMEWORK,
                  UmengHomeworkAction.REPORT_CLASS_SELECT_CLICK,
                  {
                    eventName: "作业报告页中点切换班级",
                  }
                );
              }}
            >
              <span className={cn("text-gray-2 mr-1")}>班级：</span>
              <span className={cn("text-gray-2 mr-1")}>
                {currentClass.value?.name}
              </span>
              <ChevronDown
                className={cn(
                  "text-gray-2 h-4 w-4",
                  isClassOpen.value && "rotate-180"
                )}
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="max-h-80 w-24 overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]"
          >
            {/* {classList.map((classItem) => (
              <DropdownMenuItem
                key={classItem.id}
                className={`flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm leading-[150%] ${currentClass.value?.id === classItem.id ? "bg-primary-6 text-primary-1" : "text-gray-2 font-normal"}`}
                onClick={() => handleSelectClass(classItem)}
              >
                {classItem.name}
              </DropdownMenuItem>
            ))} */}
            {classList.value.map((classItem) => (
              <DropdownMenuItem
                key={classItem.classId}
                className={`flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm leading-[150%] ${currentClass.value?.id === classItem.classId ? "bg-primary-6 text-primary-1" : "text-gray-2 font-normal"}`}
                onClick={() => {
                  handleSelectClass({
                    id: classItem.classId,
                    name: classItem.className,
                  });
                  assignId.value = classItem.assignId.toString();
                }}
              >
                {classItem.className}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* 课程选择器 - 无论在哪种视图下都显示（如果有的话） */}
      {classData.value.taskType === 10 && (
        <div
          className="flex cursor-pointer items-center"
          onClick={() => {
            isDrawerOpen.value = true;
          }}
        >
          <span className="mr-1">课程：</span>
          <span className="mr-1 font-medium">
            {currentCourse.value?.name || "全部"}
          </span>
          <ChevronDown className="h-4 w-4" />
        </div>
      )}
    </div>
  );

  // 使用 useMemo 优化渲染性能，避免不必要的重新渲染
  const memoizedTabNav = React.useMemo(
    () => (
      <BaseTabNav
        activeTab={activeTab.value}
        onTabChange={handleTabChange}
        tabs={tabs}
        rightContent={classSelector}
        className="border-line-2 sticky top-0 z-50 h-[42px] border-b"
        tabClassName="font-semibold"
        tabWidth={tabWidth[viewMode.value]}
      />
    ),
    [activeTab.value, handleTabChange, tabs, classSelector, viewMode.value]
  );

  return (
    <>
      {memoizedTabNav}

      {/* 课程选择抽屉 */}
      <DrawerContainer
        open={isDrawerOpen.value}
        onOpenChange={(open) => (isDrawerOpen.value = open)}
        onConfirm={() => {
          if (activeTab.value === "report") {
            run();
          } else if (activeTab.value === "results") {
            runAnswer();
          }
        }}
      />
    </>
  );
}
