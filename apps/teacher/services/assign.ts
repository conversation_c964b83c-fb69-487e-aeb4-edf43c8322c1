import { BizTreeDetailData, ClassStudentListItem } from "@/types/assign";
import {
  AssignTaskDetailData,
  AssignTaskParams,
  CoursePracticeItem,
  CoursePracticeListParams,
} from "@/types/assign/course";
import { AssignSubjectListData } from "@/types/assign/home";
import { BizTreeListData, TreeType } from "@/types/assign/tree";
import { HomeWorkData } from "@/types/homeWork";
import {
  initTreeFormatInfo,
  traverseBizTree,
  traverseKnowledgeTree,
} from "@/utils";
import to from "await-to-js";
import { r } from "../libs/axios";

/**
 * 获取老师的学科列表
 */
export const getAssignSubjectList = () => {
  return r.get<AssignSubjectListData>("/task/type");
};

export const getStudentsByClassIds = (classIDs: string) => {
  return r.get<ClassStudentListItem[]>("/teacher/class/students", {
    params: {
      classIDs,
    },
  });
};

/**
 * 获取业务树列表
 */
export const getBizTreeList = (subject: number) => {
  return r.get<BizTreeListData>("/task/chapter-tree/list", {
    params: {
      subject,
    },
  });
};

/**
 * 获取业务树详情
 */
export const getBizTreeDetail = (bizTreeId: number) => {
  return r.get<BizTreeDetailData>("/task/chapter-tree/detail", {
    params: {
      bizTreeId,
    },
  });
};
/**
 * 获取知识树列表
 */
export const getKnowledgeTreeList = (subject: number) => {
  return r.get<BizTreeListData>("/task/knowledge-tree/list", {
    params: {
      subject,
    },
  });
};

/**
 * 获取知识树详情
 */
export const getKnowledgeTreeDetail = (treeId: number) => {
  return r.get<BizTreeDetailData>("/task/knowledge-tree/detail", {
    params: {
      bizTreeId: treeId,
    },
  });
};

/**
 * 获取最近布置的作业
 */
export const getTaskLatestAssign = (subjectId: number) => {
  return r.get<HomeWorkData>("/task/report/latest", {
    params: {
      subjectId,
    },
  });
};

/**
 * 获取课程和巩固练习列表
 */
export const getCoursePracticeList = (params: CoursePracticeListParams) => {
  return r.get<CoursePracticeItem[]>("/task/course-practice/list", {
    params,
  });
};

/**
 * 布置课程任务
 */
export const assignTask = (params: AssignTaskParams) => {
  return r.post<null>("/task/management/create", params);
};

export async function fetchTreeList(
  subject: number,
  type: TreeType = "bizTree"
) {
  const api = type === "bizTree" ? getBizTreeList : getKnowledgeTreeList;
  const [err, res] = await to(api(subject));
  if (err) {
    return [];
  }
  return Array.isArray(res) ? res : [];
}

export async function fetchBizTreeDetail(bizTreeId: number) {
  const [err, res] = await to(getBizTreeDetail(bizTreeId));
  if (err) {
    return initTreeFormatInfo();
  }
  return traverseBizTree(res ? res : undefined);
}
export async function fetchKnowledgeTreeDetail(treeId: number) {
  const [err, res] = await to(getKnowledgeTreeDetail(treeId));
  if (err) {
    return initTreeFormatInfo();
  }
  return traverseKnowledgeTree(res ? res : undefined);
}

export async function getAssignTaskDetail(taskId: number) {
  return r.get<AssignTaskDetailData>("/task/management/detail", {
    params: {
      taskId,
    },
  });
}

export async function fetchTaskDetail(taskId: number) {
  const [err, res] = await to(getAssignTaskDetail(taskId));
  if (err) {
    return null;
  }
  return res;
}
