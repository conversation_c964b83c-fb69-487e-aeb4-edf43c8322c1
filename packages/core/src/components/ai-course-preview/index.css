.ai-course-preview  {
    /* @doc https://developer.mozilla.org/en-US/docs/Web/CSS/text-spacing-trim */
    text-spacing-trim: space-all;
    
    /* 橘色 */
    --color-orange-1: #FF902E;
    --color-orange-2: #FFEDDF;
    --color-orange-text: #CC6204;
  
    /* 分隔线颜色 */
    --Divider-Divider1: rgba(51, 46, 41, 0.12);
    --Divider-Divider2: rgba(51, 46, 41, 0.06);
  
    /* 文稿组件内使用的颜色 */
    --aspect-stu: 10/6;
    --color-stone-600:#5C5757;
    --color-stone-700: #4A292A;
    --color-stone-900: #221407;
    --color-amber-200: #FFD080;
    --color-yellow-950: #4D2F13;
    --color-dim-orange: #CC6204;
    --color-main-orange: #FFA666;
    --color-pink-100: #F7EBF0;
  
  
    --width-guide: 804px;
    --width-section-h3: 772px; /* 文稿的H3固定宽度: 722px */
    --text-h3: 28px;
    --text-h4: 22px;
    --tracking-guide: 0.08em;
    --leading-guide: 2;
  
    /* widget loader */
    --font-resource-han-rounded: "ResourceHanRounded";


    /* 背景 */
    --background: var(--color-bg-white);
    --foreground: var(--color-text-3);

    /* 卡片 */
    --card: var(--color-bg-white);
    --card-foreground: var(--color-text-3);

    /* 弹出框 */
    --popover: var(--color-bg-white);
    --popover-foreground: var(--color-text-3);

    /* 主色 */
    --primary: var(--color-orange-1);
    --primary-foreground: white;

    /* 次要色 */
    --secondary: white;
    --secondary-foreground: var(--color-orange-1);

    .bg-orange-1 {
        background-color: var(--color-orange-1);
    }

    .text-orange-1 {
        color: var(--color-orange-1);
    }
    .border-orange-1 {
        border-color: var(--color-orange-1);
    }

    .bg-orange-2 {
        background-color: var(--color-orange-2);
    }

    .text-orange-2 {
        color: var(--color-orange-2);
    }

    .border-orange-2 {
        border-color: var(--color-orange-2);
    }

    .text-orange-text {
        color: var(--color-orange-text);
    }

    .text-stone-700 {
        color: var(--color-stone-700);
    }

    .text-stone-900 {
        color: var(--color-stone-900);
    }

    .border-stone-500 {
        border-color: var(--color-stone-500);
    }

    .bg-amber-200 {
        background-color: var(--color-amber-200);
    }
    
    .text-yellow-950 {
        color: var(--color-yellow-950);
    }

    .bg-yellow-50 {
        background-color: var(--color-yellow-50);
    }

    .bg-yellow-100 {
        background-color: var(--color-yellow-100);
    }

    .bg-yellow-200 {
        background-color: var(--color-yellow-200);
    }

    .bg-yellow-300 {
        background-color: var(--color-yellow-300);
    }
    .border-yellow-200 {
        border-color: var(--color-yellow-200);
    }

    .bg-yellow-700 {
        background-color: var(--color-yellow-700);
    }

    .bg-yellow-800 {
        background-color: var(--color-yellow-800);
    }

    .text-yellow-800 {
        color: var(--color-yellow-800);
    }

    .text-yellow-900 {
        color: var(--color-yellow-900);
    }
    
    .text-dim-orange {
        color: var(--color-dim-orange);
    }

    .bg-main-orange {
        background-color: var(--color-main-orange);
    }

    .text-main-orange {
        color: var(--color-main-orange);
    }

    .w-section-h3 {
        width: var(--width-section-h3);
    }

    .text-h3 {
        font-size: var(--text-h3);
    }

    .text-h4 {
        font-size: var(--text-h4);
    }

    .tracking-guide {
        letter-spacing: var(--tracking-guide);
    }

    .leading-guide {
        line-height: var(--leading-guide);
    }
    
    .-top-1 {
        top: -0.25rem;
    }

    .-left-1 {
        left: -0.25rem;
    }
    
    
}

