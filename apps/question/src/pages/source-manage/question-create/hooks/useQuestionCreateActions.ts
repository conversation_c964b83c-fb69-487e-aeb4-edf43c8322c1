import { showSubmitModal } from '@/components/SubmitModal';
import { NumberSelectorOption } from '@/pages/source-manage/components/NumberSelector';
import { deleteQuestion, modifyQuestion } from '@/services/api';
import { createAuditTask, submitAuditTask } from '@/services/api/review';
import { getShortQuestionStem } from '@/utils';
import { message } from 'antd';
import { to } from 'await-to-js';
import { useCallback } from 'react';
import { useQuestionCreate } from '../contexts/QuestionCreateContext';
import { QuestionCreateProps } from '../types';
import { transformFormDataToBackend } from '../utils/formToBackend';

/**
 * 题目创建相关的业务逻辑 hooks
 */
export const useQuestionCreateActions = ({ way, data, onSubmit }: QuestionCreateProps) => {
  const {
    form,
    questionList,
    activeQuestion,
    activeIndex,
    setQuestionList,
    setActiveQuestion,
    setIsLoading,
    attachFileList,
    // numberSelectorRef,
    saveQueue,
    questionSelectorRef,
    paperId,
    baseInfo,
  } = useQuestionCreate();

  // 表单失焦保存 - 使用 useCallback 稳定引用
  const handleBlurSave = useCallback(async () => {
    // saveDraftVisited();
    const values = form.getFieldsValue();
    console.log('原始数据:', values);
    // 转换为后端格式用于验证
    const backendData = transformFormDataToBackend({
      ...values,
      questionStatus: way === 'audit' ? 4 : 2,
    });
    const params = {
      ...backendData,
      attachFileList: attachFileList,
    };
    console.log('后端接口需要的格式', params);
    // 创建保存的Promise，如果保存失败，则不执行后续的保存操作
    const newSavePromise = (async () => {
      const [err, res] = await to(modifyQuestion(params));
      if (err || res.code) {
        message.error(err?.message || res?.message);
        return;
      }
      if (res.code === 0) {
        // 这里将res.data赋值给questionList对应questionId的数组项
        setQuestionList(
          questionList.map((item) => {
            if (item.questionId === res.data.questionId) {
              setActiveQuestion(res.data);
              return res.data;
            }
            return item;
          }),
        );
      }
    })();

    // 更新队列
    saveQueue.current = saveQueue.current.then(() => newSavePromise);

    // 等待当前保存操作完成
    await newSavePromise;
  }, [form, questionList, setQuestionList, saveQueue, attachFileList, way]);

  // 纠错提交接口
  const correctionSubmit = async (values: any) => {
    const backendData = transformFormDataToBackend({
      ...values,
      questionStatus: 8,
    });
    const params = {
      ...backendData,
      attachFileList: attachFileList,
    };
    const [err, res] = await to(modifyQuestion(params));
    if (err || res.code) {
      message.error(err?.message || res?.message);
      return;
    }
    onSubmit?.();
  };
  // 纠错模式下，提交表单
  const correctionHandleSubmit = async () => {
    const values = await form?.validateFields();
    // 等待所有保存操作完成
    showSubmitModal({
      title: '确认提交题目纠错？',
      content:
        '提交题目纠错后所有使用该题目的练习与课程内容将同步更新。为避免不必要的操作，请在确认题目存在问题后再进行编辑。',
      onOk: () => correctionSubmit(values),
    });
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    const auditTaskName =
      baseInfo.generateChannel === 2
        ? getShortQuestionStem(values.questionStem)
        : baseInfo.paperName;
    const params = {
      auditTaskType: 1,
      auditTaskName: auditTaskName,
      phase: baseInfo.phase,
      subject: baseInfo.subject,
      importQuestion: {
        paperId,
        list: questionList.map((item: any) => ({
          questionId: item.questionId,
        })),
      },
    };
    const [err, res] = await to(createAuditTask(params));
    if (res?.code === 300300) {
      const errors =
        res?.detail?.map((item: any) => ({
          id: item.questionId,
          idx: item.questionIdx,
          fieldPath: item.questionFieldPath,
          errorMsg: item.questionErrorMsg,
        })) || [];
      questionSelectorRef.current?.setErrors(errors);
    }
    if (err || res.code) {
      message.error(err?.message || res?.message);
      return;
    }
    onSubmit?.();
  };
  /**
   * 找到未访问的题目索引
   * @param data 题目选项数据
   * @returns 未访问的题目索引数组
   */
  const findUnvisitedIndices = (data: NumberSelectorOption[]) => {
    return data
      .map((item, index) => (item.status === 'unvisited' ? index + 1 : -1)) // 找到 'unvisited' 的下标
      .filter((index) => index !== -1); // 过滤掉 -1
  };
  /**
   * 表单提交处理
   */
  const handleFormSubmit = async () => {
    handleBlurSave();
    await saveQueue.current;
    try {
      const values = await form?.validateFields();
      const unvisitedItems = findUnvisitedIndices(questionSelectorRef.current?.getOptions() || []);
      if (unvisitedItems.length > 0) {
        const unvisitedNumbers = unvisitedItems.join('、');
        message.warning(`${unvisitedNumbers} 题尚未查看，请查看后再提交审核`, 3);
        return;
      }
      // 等待所有保存操作完成
      showSubmitModal({
        title: '确认提交',
        content: '是否确认提交问题？',
        onOk: () => handleSubmit(values),
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const auditTaskFunc = async (status: number, reason?: string) => {
    try {
      setIsLoading(true);
      const res = await submitAuditTask({
        auditTaskId: data.auditTaskId,
        auditTaskStatus: status,
        ...(reason && { reason }),
      });

      if (res.code === 0) {
        message.success('提交成功');
        onSubmit?.();
      } else {
        message.error(res.message || '提交失败');
      }
    } catch (error) {
      console.error('提交审核结果失败:', error);
      message.error('提交失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 审核通过
  const handleAuditPassSubmit = async () => {
    handleBlurSave();
    try {
      // TODO: 审核通过时，暂时不进行表单验证
      // await form?.validateFields();
      // const unvisitedItems = findUnvisitedIndices(questionSelectorRef.current?.getOptions() || []);
      // if (unvisitedItems.length > 0) {
      //   const unvisitedNumbers = unvisitedItems.join('、');
      //   message.warning(`${unvisitedNumbers} 题尚未查看，请查看后再提交审核`, 3);
      //   return;
      // }
      // 等待所有保存操作完成
      await saveQueue.current;

      showSubmitModal({
        title: '确认题目审核通过？',
        content: '题目审核通过后，将对线上所有用户可见，请确认完成审核后提交通过。',
        onOk: () => auditTaskFunc(2),
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除题目
  const handleDeleteQuestion = () => {
    showSubmitModal({
      title: '是否确认是删除此题？',
      content: '',
      okText: '删除',
      onOk: async () => {
        const [err, res] = await to(deleteQuestion({ questionId: activeQuestion.questionId }));
        if (err || res.code) {
          message.error(err?.message || res?.message);
          return;
        }
        setQuestionList(questionList.filter((item, index) => index !== activeIndex));
        questionSelectorRef.current?.removeQuestion(activeQuestion.questionId);
        message.success('删除成功');
      },
    });
  };

  return {
    handleBlurSave,
    correctionHandleSubmit,
    handleFormSubmit,
    handleAuditPassSubmit,
    auditTaskFunc,
    handleDeleteQuestion,
  };
};
