/**
 * SWR Hook 测试辅助工具
 * 提供标准化的 SWR Hook 测试环境和 Mock 工具
 */

import React, { ReactNode } from 'react';
import { renderHook, RenderHookResult } from '@testing-library/react';
import { SWRConfig } from 'swr';

/**
 * Fetcher Mock 配置接口
 */
export interface FetcherMocks {
  get?: any;
  post?: any;
}

/**
 * SWR Hook 渲染配置
 */
export interface SWRHookRenderOptions {
  /** Fetcher Mock 配置 */
  mocks?: FetcherMocks;
  /** 自定义 SWR 配置 */
  swrConfig?: Parameters<typeof SWRConfig>[0]['value'];
}

/**
 * 渲染 SWR Hook 的标准化函数
 * 提供统一的 SWR Hook 测试环境，支持 Mock 数据和自定义配置
 *
 * @example
 * ```typescript
 * // 基础用法
 * const { result } = renderSWRHook(() => useUsers());
 *
 * // 带 Mock 数据
 * const { result } = renderSWRHook(
 *   () => useUsers(),
 *   {
 *     mocks: {
 *       get: createMockResponse([{ id: 1, name: 'John' }])
 *     }
 *   }
 * );
 *
 * // 自定义 SWR 配置
 * const { result } = renderSWRHook(
 *   () => useUsers(),
 *   {
 *     swrConfig: {
 *       refreshInterval: 1000,
 *       revalidateOnFocus: false
 *     }
 *   }
 * );
 *
 * // 复合配置
 * const { result } = renderSWRHook(
 *   () => useUserDetail(userId),
 *   {
 *     mocks: {
 *       get: vi.fn().mockResolvedValue({ id: 1, name: 'John' }),
 *       post: vi.fn().mockResolvedValue({ success: true })
 *     },
 *     swrConfig: {
 *       dedupingInterval: 0,
 *       shouldRetryOnError: false
 *     }
 *   }
 * );
 * ```
 *
 * @param hook 要测试的 Hook 函数
 * @param options 渲染选项
 * @returns renderHook 的结果
 */
export function renderSWRHook<T>(
  hook: () => T,
  options: SWRHookRenderOptions = {}
): RenderHookResult<T, unknown> {
  const { mocks, swrConfig } = options;

  // 应用 Fetcher Mock（如果提供）
  if (mocks) {
    try {
      // 检测测试环境并应用相应的 Mock
      if (typeof globalThis !== 'undefined' && 'vi' in globalThis) {
        // Vitest 环境 - 使用动态require避免类型检查
        const fetcher = (globalThis as any).require?.('@repo/lib/utils/fetcher') || {};
        const { get, post } = fetcher;
        
        if (mocks.get && get?.mockImplementation) {
          get.mockImplementation(mocks.get);
        }
        
        if (mocks.post && post?.mockImplementation) {
          post.mockImplementation(mocks.post);
        }
      }
      else if (typeof globalThis !== 'undefined' && 'jest' in globalThis) {
        // Jest 环境 - 使用动态require避免类型检查
        const fetcher = (globalThis as any).require?.('@repo/lib/utils/fetcher') || {};
        const { get, post } = fetcher;
        
        if (mocks.get && get?.mockImplementation) {
          get.mockImplementation(mocks.get);
        }
        
        if (mocks.post && post?.mockImplementation) {
          post.mockImplementation(mocks.post);
        }
      }
    } catch (error) {
      // 忽略Mock应用错误，让测试继续运行
      console.warn('Failed to apply fetcher mocks:', error);
    }
  }

  // 默认 SWR 配置 - 针对测试环境优化
  const defaultSWRConfig = {
    dedupingInterval: 0,           // 禁用去重，确保每次都能触发请求
    provider: () => new Map(),     // 使用独立的缓存实例
    shouldRetryOnError: false,     // 禁用错误重试，避免测试超时
    errorRetryCount: 0,            // 设置重试次数为 0
    revalidateOnFocus: false,      // 禁用焦点重新验证
    revalidateOnReconnect: false,  // 禁用重连重新验证
    refreshWhenOffline: false,     // 禁用离线刷新
    refreshWhenHidden: false,      // 禁用隐藏时刷新
    ...swrConfig                   // 用户自定义配置覆盖默认配置
  };

  return renderHook(hook, {
    wrapper: ({ children }: { children: ReactNode }) =>
      React.createElement(SWRConfig as any, { value: defaultSWRConfig }, children),
  });
}

/**
 * 创建成功响应的 Mock 函数
 * @param data 返回的数据
 * @param delay 延迟时间（毫秒）
 * @returns Mock 函数
 */
export function createMockResponse<T>(data: T, delay = 0) {
  return () => new Promise<T>(resolve => 
    setTimeout(() => resolve(data), delay)
  );
}

/**
 * 创建错误响应的 Mock 函数
 * @param message 错误消息
 * @param delay 延迟时间（毫秒）
 * @returns Mock 函数
 */
export function createMockError(message: string, delay = 0) {
  return () => new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error(message)), delay)
  );
}

/**
 * 创建网络错误的 Mock 函数
 * @param delay 延迟时间（毫秒）
 * @returns Mock 函数
 */
export function createNetworkError(delay = 0) {
  return () => new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error('Network Error')), delay)
  );
}

/**
 * 创建超时错误的 Mock 函数
 * @param delay 延迟时间（毫秒）
 * @returns Mock 函数
 */
export function createTimeoutError(delay = 5000) {
  return () => new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error('Request Timeout')), delay)
  );
}

/**
 * 创建分页数据的 Mock 函数
 * @param items 数据项数组
 * @param page 页码
 * @param pageSize 每页大小
 * @param delay 延迟时间（毫秒）
 * @returns Mock 函数
 */
export function createPaginatedMockResponse<T>(
  items: T[],
  page = 1,
  pageSize = 10,
  delay = 0
) {
  return () => new Promise(resolve => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = items.slice(startIndex, endIndex);
    
    setTimeout(() => resolve({
      data: paginatedItems,
      pagination: {
        page,
        pageSize,
        total: items.length,
        totalPages: Math.ceil(items.length / pageSize)
      }
    }), delay);
  });
}

/**
 * 等待 SWR Hook 状态变化的辅助函数
 * 提供详细的调试信息和错误提示
 *
 * @example
 * ```typescript
 * // 等待数据加载完成
 * await waitForSWRState(result, state => !state.isLoading);
 *
 * // 等待数据存在
 * await waitForSWRState(result, state => state.data !== undefined);
 *
 * // 等待错误状态
 * await waitForSWRState(result, state => state.error !== undefined);
 *
 * // 自定义超时时间
 * await waitForSWRState(result, state => !state.isLoading, 10000);
 *
 * // 使用预定义的状态检查器
 * await waitForSWRState(result, swrStateCheckers.isLoaded);
 * await waitForSWRState(result, swrStateCheckers.hasData);
 * ```
 *
 * @param result renderHook 的结果
 * @param predicate 状态判断函数
 * @param timeout 超时时间（毫秒），默认 5000ms
 * @returns Promise
 */
export async function waitForSWRState<T>(
  result: RenderHookResult<T, unknown>,
  predicate: (current: T) => boolean,
  timeout = 5000
): Promise<void> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let lastState: T;
    
    const checkState = () => {
      const currentState = (result as any).current;
      lastState = currentState;
      
      try {
        if (predicate(currentState)) {
          resolve();
          return;
        }
      } catch (error) {
        reject(new Error(
          `Predicate function threw an error: ${error instanceof Error ? error.message : String(error)}\n` +
          `Current state: ${JSON.stringify(currentState, null, 2)}`
        ));
        return;
      }
      
      const elapsed = Date.now() - startTime;
      if (elapsed > timeout) {
        // 生成详细的错误信息
        const stateInfo = typeof currentState === 'object' && currentState !== null
          ? Object.keys(currentState).map(key => {
              const value = (currentState as any)[key];
              return `  ${key}: ${JSON.stringify(value)}`;
            }).join('\n')
          : `  value: ${JSON.stringify(currentState)}`;
        
        reject(new Error(
          `Timeout waiting for SWR state change after ${timeout}ms\n\n` +
          `最后的状态信息：\n${stateInfo}\n\n` +
          `调试建议：\n` +
          `1. 检查 Mock 函数是否正确返回数据\n` +
          `2. 确认 SWR Hook 是否正确调用 fetcher\n` +
          `3. 验证 predicate 函数的逻辑是否正确\n` +
          `4. 考虑增加超时时间或使用 console.log 调试状态变化\n\n` +
          `示例调试代码：\n` +
          `console.log('Current state:', result.current);`
        ));
        return;
      }
      
      setTimeout(checkState, 10);
    };
    
    checkState();
  });
}

/**
 * 常用的 SWR 状态检查函数
 */
export const swrStateCheckers = {
  /** 检查是否加载完成 */
  isLoaded: <T extends { isLoading?: boolean }>(state: T) => !state.isLoading,
  
  /** 检查是否有数据 */
  hasData: <T extends { data?: any }>(state: T) => state.data !== undefined,
  
  /** 检查是否有错误 */
  hasError: <T extends { error?: any }>(state: T) => state.error !== undefined,
  
  /** 检查是否正在变更 */
  isMutating: <T extends { isMutating?: boolean }>(state: T) => state.isMutating === true,
};