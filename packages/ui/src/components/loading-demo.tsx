import { FC } from "react";
import { Loading } from "./loading";

const LoadingDemo: FC = () => {
    const variants = ["spinner", "dots", "pulse", "wave", "ripple", "orbit"] as const;
    const colors = ["blue", "green", "purple", "orange", "gray"] as const;
    const sizes = ["sm", "md", "lg"] as const;

    return (
        <div className="loading_demo_container p-8 space-y-8">
            <h1 className="loading_demo_title text-3xl font-bold text-gray-800 mb-8">
                Loading 组件演示
            </h1>

            {/* 不同样式的展示 */}
            <section className="loading_demo_section">
                <h2 className="loading_demo_section_title text-xl font-semibold text-gray-700 mb-4">
                    不同样式 (Variants)
                </h2>
                <div className="loading_demo_grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                    {variants.map((variant) => (
                        <div key={variant} className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                            <Loading variant={variant} size="md" />
                            <span className="loading_demo_label text-sm text-gray-600 capitalize">
                                {variant}
                            </span>
                        </div>
                    ))}
                </div>
            </section>

            {/* 不同颜色的展示 */}
            <section className="loading_demo_section">
                <h2 className="loading_demo_section_title text-xl font-semibold text-gray-700 mb-4">
                    不同颜色 (Colors)
                </h2>
                <div className="loading_demo_grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                    {colors.map((color) => (
                        <div key={color} className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                            <Loading variant="spinner" color={color} size="md" />
                            <span className="loading_demo_label text-sm text-gray-600 capitalize">
                                {color}
                            </span>
                        </div>
                    ))}
                </div>
            </section>

            {/* 不同尺寸的展示 */}
            <section className="loading_demo_section">
                <h2 className="loading_demo_section_title text-xl font-semibold text-gray-700 mb-4">
                    不同尺寸 (Sizes)
                </h2>
                <div className="loading_demo_grid grid grid-cols-1 md:grid-cols-3 gap-6">
                    {sizes.map((size) => (
                        <div key={size} className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                            <Loading variant="dots" size={size} />
                            <span className="loading_demo_label text-sm text-gray-600 capitalize">
                                {size}
                            </span>
                        </div>
                    ))}
                </div>
            </section>

            {/* 带文字的展示 */}
            <section className="loading_demo_section">
                <h2 className="loading_demo_section_title text-xl font-semibold text-gray-700 mb-4">
                    带文字提示
                </h2>
                <div className="loading_demo_grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                        <Loading
                            variant="wave"
                            size="lg"
                            text="正在加载课程内容..."
                        />
                    </div>
                    <div className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                        <Loading
                            variant="ripple"
                            size="md"
                            text="请稍候..."
                            color="green"
                        />
                    </div>
                    <div className="loading_demo_item flex flex-col items-center space-y-2 p-4 border rounded-lg">
                        <Loading
                            variant="orbit"
                            size="lg"
                            text="处理中..."
                            color="purple"
                        />
                    </div>
                </div>
            </section>

            {/* 使用示例 */}
            <section className="loading_demo_section">
                <h2 className="loading_demo_section_title text-xl font-semibold text-gray-700 mb-4">
                    使用示例
                </h2>
                <div className="loading_demo_code bg-gray-100 p-4 rounded-lg">
                    <pre className="loading_demo_pre text-sm text-gray-800 overflow-x-auto">
                        {`// 基础用法
<Loading />

// 自定义样式
<Loading variant="wave" size="lg" text="正在加载..." />

// 自定义颜色
<Loading variant="spinner" color="green" />

// 完整配置
<Loading 
  variant="ripple" 
  size="md" 
  text="请稍候..." 
  color="purple"
  className="my-custom-class"
/>`}
                    </pre>
                </div>
            </section>
        </div>
    );
};

export { LoadingDemo };
