"use client";

import { FEEDBACK_MAX_IMAGES } from "@/configs";
import CloseIconBg from "@/public/icons/ic_close_bg.svg";
import UploadIcon from "@/public/icons/icon_upload_plus.svg";
import { useSignal } from "@preact-signals/safe-react";
import { getAndroidBridgeAvailable, pickImages } from "@repo/lib/utils/device";
import { produce } from "immer";
import React, { memo } from "react";
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";

interface FeedbackImageUploadProps {
  images: File[];
  onChange: (newImages: File[]) => void;
}

const FeedbackImageUpload: React.FC<FeedbackImageUploadProps> = ({
  images,
  onChange,
}) => {
  const isAndroidBridgeAvailable = useSignal(getAndroidBridgeAvailable());

  // 删除图片
  const handleRemoveImg = (idx: number) => {
    onChange(
      produce(images, (draft) => {
        draft.splice(idx, 1);
      })
    );
  };

  // PC 端上传图片
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    const remain = FEEDBACK_MAX_IMAGES - images.length;
    const addFiles = Array.from(files).slice(0, remain);
    onChange([...images, ...addFiles]);
    e.target.value = "";
  };

  const handleAndroidImageChange = async () => {
    if (!isAndroidBridgeAvailable.value) return;
    const result = await pickImages(FEEDBACK_MAX_IMAGES);

    if (!result) return;

    const remain = FEEDBACK_MAX_IMAGES - images.length;
    const addFiles = Array.from(result.imageUriList).slice(0, remain);

    const realAddFiles: File[] = [];

    for await (const item of addFiles) {
      const res = await fetch(item.imageUri);
      const blob = await res.blob();
      const file = new File(
        [blob],
        `image-${Date.now()}.${blob.type.split("/")[1]}`,
        {
          type: blob.type,
        }
      );
      realAddFiles.push(file);
    }

    onChange([...images, ...realAddFiles]);
  };

  return (
    <div className="feedback_upload flex flex-wrap gap-3">
      <PhotoProvider className="pointer-events-auto">
        {images.map((img, idx) => {
          const imgUrl = URL.createObjectURL(img);

          return (
            <div
              key={idx}
              className="h-22.5 w-22.5 border-line-2 relative flex items-center justify-center overflow-hidden rounded-md border"
            >
              <PhotoView src={imgUrl}>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img src={imgUrl} className="h-full w-full object-cover" />
              </PhotoView>

              <CloseIconBg
                onClick={() => {
                  handleRemoveImg(idx);
                  URL.revokeObjectURL(imgUrl);
                }}
                className="size-7.75 z-999 absolute -right-px -top-px cursor-pointer active:opacity-40"
                width={20}
                height={20}
              />
            </div>
          );
        })}
      </PhotoProvider>

      {images.length < FEEDBACK_MAX_IMAGES && (
        <label
          className="h-22.5 w-22.5 border-line-2 gap-3.75 flex cursor-pointer flex-col items-center justify-center rounded-md border"
          onClick={handleAndroidImageChange}
        >
          <UploadIcon className="size-4.75" />

          <span className="text-gray-4 text-sm/normal">点击传截图</span>
          {!isAndroidBridgeAvailable.value && (
            <input
              type="file"
              accept="image/*"
              max={FEEDBACK_MAX_IMAGES}
              multiple
              className="hidden"
              onChange={handleImageChange}
            />
          )}
        </label>
      )}
    </div>
  );
};

export default memo(FeedbackImageUpload);
