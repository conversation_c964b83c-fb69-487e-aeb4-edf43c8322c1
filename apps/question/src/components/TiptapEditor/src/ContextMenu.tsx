import type { Editor } from '@tiptap/react';
import React, { useEffect, useRef, useState } from 'react';

interface ContextMenuProps {
  editor: Editor | null;
  children: React.ReactNode;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ editor, children }) => {
  const [visible, setVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [menuType, setMenuType] = useState<'formula' | 'text' | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // 判断选区内容类型
  const getSelectionType = () => {
    if (!editor) return null;
    const { from, to } = editor.state.selection;
    if (from === to) return null;
    const text = editor.state.doc.textBetween(from, to, '\n').trim();
    // 纯公式：全为 \( ... \) 或 $$...$$
    const isFormula = /^((\$\$[\s\S]*\$\$)|(\\\([\s\S]+\\\)))$/.test(text);
    // 纯文本：不含公式分隔符
    const isText = !text.includes('$$') && !/^\\\(.+\\\)$/.test(text);
    // 混合内容判定：只要有公式分隔符且去掉所有分隔符后还有非空内容
    const formulaDelims = [/\$\$[\s\S]*?\$\$/g, /\\\([\s\S]+?\\\)/g];
    let textWithoutFormula = text;
    formulaDelims.forEach((re) => {
      textWithoutFormula = textWithoutFormula.replace(re, '');
    });
    if (
      (text.includes('$$') || /\\\(.+\\\)/.test(text)) &&
      textWithoutFormula.replace(/\s+/g, '') !== ''
    ) {
      return 'mixed';
    }
    if (isFormula) return 'formula';
    if (isText) return 'text';
    return 'mixed';
  };

  // 右键菜单触发
  const handleContextMenu = (e: React.MouseEvent) => {
    if (!editor) return;
    const type = getSelectionType();
    if (!type || type === 'mixed') {
      setVisible(false);
      return;
    }
    e.preventDefault();
    setMenuType(type);
    setPosition({ x: e.clientX, y: e.clientY });
    setVisible(true);
  };

  // 菜单项点击
  const handleMenuClick = () => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const text = editor.state.doc.textBetween(from, to, '\n');

    // 使用 chain 命令确保操作的原子性，避免中间状态导致的焦点丢失
    let newContent = '';
    if (menuType === 'formula') {
      // 公式转文本
      newContent = text.replace(/^\\\(|\\\)$/g, '').replace(/^\$\$|\$\$$/g, '');
    } else {
      // 文本转公式
      newContent = `\\(${text}\\)`;
    }

    // 执行替换操作，使用 chain 确保原子性
    editor
      .chain()
      .focus() // 先确保焦点在编辑器上
      .insertContentAt({ from, to }, newContent)
      .run();

    setVisible(false);
  };

  // 点击其他区域关闭菜单
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setVisible(false);
      }
    };
    if (visible) document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [visible]);

  return (
    <div className="relative" onContextMenu={handleContextMenu}>
      {children}
      {visible && (
        <div
          ref={menuRef}
          className="fixed z-50 bg-white rounded-lg shadow-sm text-xs p-1"
          style={{ left: position.x, top: position.y, minWidth: 100 }}
        >
          <div
            className="px-2 py-1 cursor-pointer hover:bg-[rgba(0,0,0,0.04)] text-[rgba(0,0,0,0.88)] rounded"
            onMouseDown={(e) => {
              // 阻止 mousedown 事件冒泡，避免触发编辑器失焦
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={handleMenuClick}
          >
            {menuType === 'formula' ? '转为文本' : '转为公式'}
          </div>
        </div>
      )}
    </div>
  );
};

export default ContextMenu;
