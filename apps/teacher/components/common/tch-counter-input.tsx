import { Input } from "@/ui/input";
import { cn } from "@/utils/utils";
import { useControllableValue } from "ahooks";
import { forwardRef } from "react";

interface TchCounterInputProps extends React.ComponentProps<"input"> {
  maxLength: number;
}

const placeholderClassName = "placeholder:text-gray-4";
const hoverClassName = "hover:border-primary-2";
const focusClassName = "focus-visible:ring-0";

export const TchCounterInput = forwardRef<
  HTMLInputElement,
  TchCounterInputProps
>(({ maxLength, onChange, className, style, ...props }, ref) => {
  const [innerValue, setInnerValue] = useControllableValue(props, {
    defaultValue: "",
  });

  return (
    <div className="relative">
      <Input
        ref={ref}
        style={style}
        value={innerValue}
        onChange={(e) => {
          setInnerValue(e.target.value);
          onChange?.(e);
        }}
        maxLength={maxLength}
        className={cn(
          `border-line-3 h-12 rounded-md border px-3 py-2 pr-12 text-sm leading-normal`,
          placeholderClassName,
          hoverClassName,
          focusClassName,
          className
        )}
      />
      <span className="text-gray-4 absolute right-4 top-1/2 -translate-y-1/2 text-xs font-normal">
        {innerValue.length}/{maxLength}
      </span>
    </div>
  );
});

TchCounterInput.displayName = "TchCounterInput";
