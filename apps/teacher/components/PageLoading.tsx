"use client"
import loadingData from "@/public/lottie/page-loading.json";
import Lottie from "lottie-react";

export default function PageLoading({
  text = "网络不通畅，页面努力加载中...",
}: {
  text?: string;
}) {
  return (
    <div className="flex size-full flex-col items-center justify-center gap-1">
      <Lottie className="size-17.5" animationData={loadingData} loop={true} />

      <div className="text-gray-4 text-sm/normal">{text}</div>
    </div>
  );
}
