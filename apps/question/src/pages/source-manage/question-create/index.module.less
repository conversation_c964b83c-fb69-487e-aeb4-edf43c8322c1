.createQuestionModal {
  position: relative;
  padding: 32px 40px;
}
.createQuestion {
  height: calc(100vh - 64px);
  margin: -32px -40px;
  overflow-y: scroll;
  background-color: transparent;
  border-radius: 0px;
  .createQuestionContent {
    position: relative;
    padding: 32px 40px;
  }
}

.questionCreateMainContainer {
  position: relative;
  height: 100%;

  :global(.ant-form-item .ant-form-item-label > label) {
    width: 100%;
  }
  .questionCreateMainNavbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 28%, #f5f5f5 100%);
  }
  // 题干头部样式
  .questionTitleBar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  // 层级标识样式
  .level0 {
    color: #1890ff;
    font-weight: 600;
    font-size: 16px;
    &::before {
      content: '🔹 ';
    }
  }

  .level1 {
    color: #52c41a;
    font-weight: 500;
    font-size: 15px;
    &::before {
      content: '📋 ';
    }
  }

  .level2 {
    color: #fa8c16;
    font-weight: 500;
    font-size: 14px;
    &::before {
      content: '📝 ';
    }
  }

  // 添加按钮样式
  .addButton {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
  }

  // 第一层级容器
  .level1Container {
    max-height: 100%;
    margin: 0 0 16px 24px;
    padding: 14px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(246, 255, 237, 0.3) 0%, rgba(240, 249, 255, 0.3) 100%);
    border: 1px solid rgba(183, 235, 143, 0.6);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    // 进入动画 - 从左到右
    animation: slideInFromLeft 0.3s ease-out;

    :global(.ant-form-item) {
      margin-bottom: 16px;
    }

    // 第一层级的题干输入框
    :global(.ant-form-item-control-input) {
      border-radius: 6px;
    }

    // 删除动画 - 从右到左滑出
    &.deleting {
      max-height: 0;
      margin: 0;
      padding: 0;
      border-width: 0;
      box-shadow: none;
      transform: translateX(20px);
      opacity: 0;
    }
  }

  // 第二层级容器
  .level2Container {
    position: relative;
    max-height: 100%;
    margin: 0 0 16px 24px;
    padding: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255, 247, 230, 0.3) 0%, rgba(254, 242, 230, 0.3) 100%);
    border: 1px solid rgba(255, 213, 145, 0.8);
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    // 进入动画 - 从左到右
    animation: slideInFromLeft 0.3s ease-out;

    // 添加左侧连接线
    &::before {
      position: absolute;
      top: 20px;
      left: -12px;
      width: 8px;
      height: 1px;
      background-color: #ffd591;
      transition: opacity 0.2s ease;
      content: '';
    }

    :global(.ant-form-item) {
      margin-bottom: 12px;
    }

    // 第二层级的题干输入框
    :global(.ant-form-item-control-input) {
      border-radius: 4px;
    }

    // 删除动画 - 从右到左滑出
    &.deleting {
      max-height: 0;
      margin: 0 0 0 24px;
      padding: 0;
      border-width: 0;
      box-shadow: none;
      transform: translateX(20px);
      opacity: 0;

      &::before {
        opacity: 0;
      }
    }
  }

  // 主要内容区域样式调整
  :global(.ant-form-item-label) {
    padding-bottom: 4px;
  }

  // 响应式调整
  @media (max-width: 768px) {
    .level1Container {
      margin: 12px 0;
      padding: 12px;
    }

    .level2Container {
      margin: 8px 0 8px 16px;
      padding: 8px;
    }

    .questionTitleBar {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }
  }

  // 统一的进入动画 - 从左到右
  @keyframes slideInFromLeft {
    from {
      max-height: 0;
      transform: translateX(-30px);
      opacity: 0;
    }
    to {
      max-height: 100%;
      transform: translateX(0);
      opacity: 1;
    }
  }
}

.stableAnswerField {
  position: relative;
  overflow: hidden;

  /* 确保内容不会突然跳跃 */
  > div {
    transition: opacity 0.2s ease-in-out;
  }

  .ant-form-item {
    margin-bottom: 24px;

    /* 防止表单项高度变化 */
    .ant-form-item-control {
      min-height: 32px;
    }

    /* 确保输入框过渡平滑 */
    .ant-form-item-control-input {
      transition: all 0.2s ease-in-out;

      .ant-input,
      .ant-select {
        transition: all 0.2s ease-in-out;
      }
    }
  }
}
