import { Markdown } from "@/app/components/common/markdown";
import TypedMessage from "@/app/components/common/type-message";
import { Conversation } from "@/app/models/chat-model";
import { memo, useCallback } from "react";
import RoleAvatar from "./role-avatar-view";

interface Message {
  type: "dolli" | "user" | "system"; // 消息类型
  message: string; // 消息内容
  isTyping?: boolean; // 是否正在打字
  time?: string; // 时间戳
  isComplete?: boolean;
}

interface MessageItemProps {
  type: Message["type"];
  message: string;
  isTyping?: boolean;
  index: number;
  setMessageList: React.Dispatch<React.SetStateAction<Message[]>>;
  onTextUpdate?: () => void; // 添加文本更新回调
  isComplete?: boolean;
  placeholder?: boolean;
}

interface MessageListProps {
  conversations: Conversation[];
  setMessageList: (list: Conversation[]) => void;
  onTextUpdate?: () => void; // 添加文本更新回调
}

// 提取消息组件
const ChatMessage = memo(
  ({
    type,
    message,
    isTyping,
    isComplete, // ← 加上
    index,
    setMessageList,
    onTextUpdate,
    placeholder,
  }: MessageItemProps) => {
    // ← 类型加上
    const handleComplete = useCallback(() => {
      // setIsComplete(true);
      // 更新父组件中的消息状态
      setMessageList((prev) => {
        const newList = [...prev];
        if (newList[index]) {
          newList[index] = {
            ...newList[index],
            isTyping: false,
            isComplete: true,
          };
        }
        return newList;
      });
    }, [index, setMessageList]);

    if (type !== "user") {
      return (
        <li className={`mr-20 flex gap-3 self-start px-8 pb-2 pl-7`}>
          <RoleAvatar />
          <div className="font-['DFP King Gothic GB'] wrap-break-word min-h-6 pt-6 text-[17px] leading-[26px] text-[#1F232B]">
            {placeholder ? (
              <Loading />
            ) : (
              <TypedMessage
                onComplete={handleComplete}
                message={message}
                onTextUpdate={onTextUpdate}
                isTyping={isTyping}
                isComplete={isComplete} // ← 传递
              />
            )}
          </div>
        </li>
      );
    }

    return (
      <li className={`ml-20 mt-1 flex flex-col gap-3 self-end px-8`}>
        <div className="rounded-xl border border-[rgba(51,46,41,0.06)] bg-[#FFFFFF] px-5 py-4 text-[17px] leading-[22px]">
          <Markdown content={message} />
        </div>
      </li>
    );
  }
);

// 添加displayName
ChatMessage.displayName = "ChatMessage";

// 主组件
const MessageList = ({
  conversations,
  setMessageList,
  onTextUpdate,
}: MessageListProps) => {
  return (
    <ul className={`-ml-3 mb-3 flex flex-col gap-3 text-[rgba(51,48,45,0.95)]`}>
      {conversations.map((conversation, index) => (
        <ChatMessage
          key={index}
          type={conversation.type as Message["type"]}
          message={conversation.message}
          isTyping={conversation.isTyping}
          isComplete={conversation.isComplete} // ← 传递
          index={index}
          setMessageList={
            setMessageList as React.Dispatch<React.SetStateAction<Message[]>>
          }
          onTextUpdate={onTextUpdate}
          placeholder={conversation.placeholder}
        />
      ))}
    </ul>
  );
};

export default memo(MessageList);

function Loading() {
  return (
    <div className="flex gap-1">
      <span className="animate-blink bg-text-5 size-3 rounded-full"></span>
      <span className="animate-blink bg-text-5 size-3 rounded-full delay-200"></span>
      <span className="animate-blink delay-400 bg-text-5 size-3 rounded-full"></span>
    </div>
  );
}
