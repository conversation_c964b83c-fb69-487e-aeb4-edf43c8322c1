import CloseIcon from "@/public/icons/ic_close.svg";
import type { Organization } from "@/types/login";
import { Button } from "@/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/ui/tch-dialog";
import { cn } from "@/utils/utils";
import { useState } from "react";

export interface SchoolSwitchModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onOpenChange: (open: boolean) => void;
  schools: Organization[];
  currentSchoolId?: number;
  onConfirm: (school: Organization) => void;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
}

export function SchoolSwitchModal({
  open,
  setOpen,
  onOpenChange,
  schools,
  currentSchoolId,
  onConfirm,
  title = "选择学校",
  description = "您的账号关联了多个学校的职务，请选择其中一个学校进行登录",
  confirmText = "确认登录",
  cancelText = "取 消",
}: SchoolSwitchModalProps) {
  const [selectedSchoolId, setSelectedSchoolId] = useState(currentSchoolId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="w-130 !max-w-[unset] gap-0 rounded-[1.25rem] p-0"
        close={false}
      >
        <DialogHeader className="p-6">
          <DialogTitle className="text-gray-1 flex items-start justify-between text-xl font-semibold not-italic leading-[150%]">
            {title}
            <CloseIcon
              className="size-5 cursor-pointer"
              onClick={() => setOpen(false)}
            />
          </DialogTitle>
          <DialogDescription className="text-gray-2 leading-normal">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-3 px-6">
          {schools.map((school) => (
            <div
              key={school.organizationId}
              className={cn(
                "border-line-2 text-gray-2 hover:bg-fill-gray-2 flex cursor-pointer items-center gap-2 rounded-[0.75rem] border bg-white p-4 text-base leading-normal transition-colors",
                selectedSchoolId === school.organizationId &&
                  "!bg-primary-6 text-primary-1 font-medium"
              )}
              onClick={() => setSelectedSchoolId(school.organizationId)}
            >
              <span>{school.organizationName}</span>
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-3 p-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-line-3 text-gray-2 cursor-pointer rounded-[1.125rem] hover:bg-[unset]"
          >
            {cancelText}
          </Button>
          <Button
            disabled={!selectedSchoolId}
            onClick={() => {
              const selectedSchool = schools.find(
                (s) => s.organizationId === selectedSchoolId
              );
              if (selectedSchool) {
                onConfirm(selectedSchool);
              }
            }}
            className="bg-primary-2 hover:bg-primary-2/90 cursor-pointer rounded-[1.125rem] text-white"
          >
            {confirmText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
