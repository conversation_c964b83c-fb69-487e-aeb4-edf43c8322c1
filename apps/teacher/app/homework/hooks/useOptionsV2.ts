import { getSubjectClassList } from "@/services";
import { GradeClass, Subject } from "@/types/subjectClass";
import { signal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";

export const subjectList = signal<Subject[]>([]);
export const classTreeList = signal<GradeClass[]>([]);
export const classFlatList = signal<
  {
    gradeId: number;
    gradeName: string;
    classID: number;
    className: string;
  }[]
>([]);
export const loading = signal(false);
export const subjectClassTreeList = signal<
  Array<{
    gradeClasses: GradeClass[];
    subjectKey: number;
    subjectName: string;
  }>
>([]);
export const subjectClassFlatList = signal<
  {
    gradeId: number;
    gradeName: string;
    classID: number;
    className: string;
    subjectId: number;
    subjectName: string;
    // classList: Array<{ classID: number; className: string }>;
  }[]
>([]);

export function useFetchOptionData() {
  return useRequest(getSubjectClassList, {
    manual: true,
    debounceWait: 1000,
    onError: (error) => {
      console.error("获取学科班级列表失败", error);
    },
    onSuccess: (res) => {
      subjectList.value = res.subjects || [];
      classTreeList.value = res.gradeClasses || [];
      classFlatList.value =
        res.gradeClasses?.flatMap((item) =>
          item.class?.map((child) => ({
            ...child,
            gradeId: item.gradeID,
            gradeName: item.gradeName,
          }))
        ) || [];
      subjectClassTreeList.value = res.subjectToGradeClasses || [];
      subjectClassFlatList.value =
        res.subjectToGradeClasses?.flatMap(
          (subject) =>
            subject.gradeClasses?.flatMap((item) =>
              item.class.map((child) => ({
                ...child,
                gradeId: item.gradeID,
                gradeName: item.gradeName,
                subjectId: subject.subjectKey,
                subjectName: subject.subjectName,
              }))
            ) || []
        ) || [];
    },
  });
}

export function useOptions() {
  return {
    subjectList: subjectList.value,
    classTreeList: classTreeList.value,
    classFlatList: classFlatList.value,
    loading: loading.value,
    refreshOptions: useFetchOptionData,
    useFetchOptionData,
    subjectClassTreeList: subjectClassTreeList.value,
    subjectClassFlatList: subjectClassFlatList.value,
  };
}
