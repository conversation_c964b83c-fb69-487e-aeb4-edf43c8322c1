import { Flex, Form } from 'antd';
import React, { memo, useMemo } from 'react';
import { useQuestionCreate } from '../../../contexts/QuestionCreateContext';
import { getQuestionStemFillPointUuids } from '../../../utils/questionBlank';
import AnswerFieldSelector from '../../AnswerFieldSelector';
import QuestionAnswerModeSelector from '../../QuestionAnswerModeSelector';
import QuestionMatchLine from '../../QuestionMatchLine';

interface SubQuestionAnswerSectionProps {
  name: number;
  restField: any;
  currentFillIdx: number;
}

/**
 * 子题目答案区域组件
 * 负责管理一级题目的作答方式和答案字段
 */
const SubQuestionAnswerSection: React.FC<SubQuestionAnswerSectionProps> = ({
  name,
  restField,
  currentFillIdx,
}) => {
  const { form } = useQuestionCreate();

  const subQuestionList = Form.useWatch(['subQuestionList', name, 'subQuestionList'], form) || [];
  const questionAnswerMode = Form.useWatch(['subQuestionList', name, 'questionAnswerMode'], form);
  const questionStem = Form.useWatch(['subQuestionList', name, 'questionStem'], form);
  const isMatchLineName = ['subQuestionList', name, 'isMatchLine'];
  const isMatchLine = Form.useWatch(isMatchLineName, form);
  const questionOptionMatrixName = ['subQuestionList', name, 'questionOptionMatrix'];
  const hasSubQuestions = subQuestionList.length > 0;
  const count = useMemo(() => {
    return getQuestionStemFillPointUuids(questionStem || '').length;
  }, [questionStem]);

  // 当有二级题目时，不显示一级题目的作答字段
  if (hasSubQuestions) {
    return null;
  }

  return (
    <>
      <Flex justify="space-between">
        <QuestionAnswerModeSelector
          name={[name, 'questionAnswerMode']}
          count={count}
          restField={restField}
        />
        <QuestionMatchLine
          name={[name, 'isMatchLine']}
          count={count}
          isMatchLineName={isMatchLineName}
          questionAnswerMode={questionAnswerMode}
        />
      </Flex>
      <AnswerFieldSelector
        questionAnswerMode={questionAnswerMode}
        isMatchLine={isMatchLine}
        fieldName={[name, 'questionOptionMatrix']}
        questionOptionMatrixName={questionOptionMatrixName}
        restField={restField}
        questionStem={questionStem}
        currentFillIdx={currentFillIdx}
      />
    </>
  );
};

export default memo(SubQuestionAnswerSection);
