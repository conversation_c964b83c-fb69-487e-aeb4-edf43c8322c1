declare namespace API {
  declare namespace Common {
    interface TreeListItem {
      label: string;
      value: number;
      isMaterial?: boolean;
      children?: TreeListItem[];
      [property: string]: any;
    }

    interface ResponsePaginationType {
      page: number;
      pageSize: number;
      total: number;
      [property: string]: any;
    }

    interface BaseTreeNodeAntdType {
      key: number;
      title: string;
      children?: BaseTreeNodeAntdType[];
      parentsKey?: number[];
      shelfStatus?: number;
      shelfOnLeafNodeStat?: string;
      [property: string]: any;
    }

    interface TreeNodeFilterType {
      key: string;
      value: number | string;
      isEqual: boolean;
    }

    interface CommonListRequestParams {
      page?: number;
      pageSize?: number;
      [property: string]: any;
    }

    interface CommonResponse {
      code: number;
      message: string;
      data: any;
      [property: string]: any;
    }
  }
}
