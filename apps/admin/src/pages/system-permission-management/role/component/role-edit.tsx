import { post } from '@/services/fetcher';
import { Role } from '@/types/role';
import { Button, Form, Input, Spin } from 'antd';
import { useCallback, useEffect } from 'react';
import useSWRMutation from 'swr/mutation';
import SubmitButton from '../../component/submit-button';
import { useRoleManagementContext } from '../context';
import EditableMenuTree from './editable-menu-tree';

const RoleEdit: React.FC<{ onSuccess: () => void; isChangeStatus?: boolean }> = ({
  onSuccess,
  isChangeStatus = false,
}) => {
  const { selectedRole, refreshRoleList } = useRoleManagementContext();
  const [form] = Form.useForm<Role>();

  const { trigger, isMutating } = useSWRMutation('/api/v1/role/update', post);

  const submit = useCallback(async () => {
    console.log(`编辑角色 submit`, form.getFieldsValue(), selectedRole);
    trigger(
      {
        ...selectedRole,
        roleName: form.getFieldsValue().roleName,
        menuIds: form.getFieldsValue().menuIds,
        roleStatus: isChangeStatus ? (selectedRole?.roleStatus === 1 ? 0 : 1) : undefined,
      },
      {
        onSuccess: () => {
          onSuccess();
          refreshRoleList();
        },
      },
    );
  }, [form, onSuccess, refreshRoleList, trigger, selectedRole]);

  useEffect(() => {
    if (selectedRole) {
      console.log('编辑角色 submit2： ', selectedRole);
      form.setFieldsValue({
        roleName: selectedRole.roleName,
        menuIds: selectedRole.menuIds.reduce(
          (acc, curr) => [...acc, ...curr.menuIds],
          [] as number[],
        ),
      });
    }
  }, [selectedRole, form]);

  if (!selectedRole) {
    return null;
  }

  return (
    <Spin spinning={isMutating} tip="保存中...">
      <Form layout="vertical" form={form} initialValues={{ ...selectedRole }} onFinish={submit}>
        <Form.Item<Role>
          label="角色名称"
          name="roleName"
          rules={[{ required: true, message: '请输入角色名称' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item<Role>
          label="菜单权限"
          name="menuIds"
          rules={[{ required: true, message: '请选择菜单权限' }]}
        >
          <EditableMenuTree platformCheckedKeys={selectedRole?.menuIds} />
        </Form.Item>

        <Form.Item>
          {isChangeStatus ? (
            <div className="flex w-full items-center justify-center">
              <Button
                loading={isMutating}
                type="primary"
                className="mt-6 w-[120px]"
                danger={selectedRole.roleStatus === 1}
                onClick={submit}
              >
                {selectedRole.roleStatus === 1 ? '禁用' : '启用'}
              </Button>
            </div>
          ) : (
            <SubmitButton form={form}>保存</SubmitButton>
          )}
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default RoleEdit;
