"use client";
import {
  AiCourse,
  AssignCourseParams,
  CourseResourceItem,
  Practice,
} from "@/types/assign/course";
import { BizTreeDetailNode } from "@/types/assign/tree";
import { createContext, useContext } from "react";

import { useParams } from "next/navigation";
import useCheckAssignTarget from "../../store/useCheckAssignTarget";
import { useAssignInitialStateWithCache } from "./useAssignInitialState";
import { useAssignStep } from "./useAssignStep";
import { useBizTree } from "./useBizTree";
import { useBizTreeResourceList } from "./useBizTreeResourceList";

// 获取总时长
export function getTotalMinutes(list: AiCourse[] | Practice[] = []) {
  return list.reduce(
    (acc, item) =>
      acc +
      ("totalDuration" in item
        ? (item.totalDuration || 0) / 60
        : item.estimatedTime || 0),
    0
  );
}

export interface CourseTreeInfo {
  treeId: number | undefined;
  bizTreeNodeId: number | undefined;
  bizTreeNode: BizTreeDetailNode | undefined;
  courseResourceList: CourseResourceItem[];
}

export type AssignCourseContextType = ReturnType<typeof useAssignCourseState>;

export function useAssignCourseState() {
  const { subjectKey } = useParams();
  const subject = Number(subjectKey);

  // TODO: 差一个根据当前情况初始化的逻辑
  const {
    isInitialed,
    mode,
    defaultState,

    setCachedAssignInitialState,
  } = useAssignInitialStateWithCache();

  const {
    bizTreeList,
    isFetchingBizTreeList,
    treeId,
    setTreeId,
    bizTreeNodeMap,
    bizTreeDetail,
    isFetchingBizTreeDetail,
    bizTreeNodeId,
    bizTreeNode,
  } = useBizTree({
    subject: subject,
    ready: isInitialed,
    defaultTreeId: defaultState.value?.treeId,
    defaultBizTreeNodeId: defaultState.value?.bizTreeNodeId,
  });

  // 1. 布置对象
  const { classList, checkedClasses, checkedClassStudentList, toggleClass } =
    useCheckAssignTarget(subject);

  // 2. 布置资源
  const {
    bizTreeResourceList,
    isFetchingBizTreeResourceList,
    selectedAiCourses,
    selectedPractices,
    selectAiCourse,
    selectPractice,
    unselectAiCourse,
    unselectPractice,
    clearSelectedResources,
  } = useBizTreeResourceList({
    subject,
    bizTreeId: treeId.value,
    bizTreeNodeId: bizTreeNodeId.value,
    ready: isInitialed,
    mode,
    bizTreeDetail,
    defaultAiCourseIds: defaultState.value?.selectedAiCourseIds,
    defaultPracticeIds: defaultState.value?.selectedPracticeIds,
  });

  // 最后在做这个，他是顶层Store，应用其他的Store
  const {
    currentAssignStep,
    aiCoursePreviewData,
    practicePreviewData,

    // 这几个聚拢下吧，丑不垃圾的
    goToSelectTarget,
    goToSetTime,
    goToAiCoursePreview,
    goToPracticePreview,

    assignTimeRanges,
    hasInvalidTimeRanges,
    assignName,
    updateAssignTimeRange,
    totalClassTimes,
    invalidTimeRanges,
    confirmAssign,
    teacherComment,
    updateTeacherComment,

    totalAiCourseTime,
    totalPracticeTime,
  } = useAssignStep({
    selectedAiCourses,
    selectedPractices,
    checkedTargetClassList: checkedClasses,
    currentSubject: subject,
    treeId: treeId,
    bizTreeNodeMap,
    onConfirmed: (params: AssignCourseParams) => {
      setCachedAssignInitialState({
        selectedAiCourseIds: [],
        selectedPracticeIds: [],
        treeId: params.bizTreeId || 0,
        bizTreeNodeId:
          params.resources?.[0]?.resourceExtra?.lastLevelBizTreeNodeId || 0,
      });
    },
  });

  return {
    mode,

    currentAssignStep,
    goToSelectTarget,

    classList,
    checkedClasses,
    checkedClassStudentList,
    toggleClass,

    bizTreeList,
    isFetchingBizTreeList,
    isFetchingBizTreeDetail,
    treeId,
    setTreeId,
    bizTreeDetail,
    bizTreeNodeId,
    bizTreeNode,
    bizTreeNodeMap,

    bizTreeResourceList,
    courseResourceList: bizTreeResourceList || [],
    isFetchingBizTreeResourceList,

    selectedAiCourses,
    selectedPractices,
    selectAiCourse,
    selectPractice,
    unselectAiCourse,
    unselectPractice,
    clearSelectedResources,

    aiCoursePreviewData,
    practicePreviewData,
    goToAiCoursePreview,
    goToPracticePreview,
    goToSetTime,
    assignTimeRanges,
    hasInvalidTimeRanges,

    assignName,
    updateAssignTimeRange,
    totalClassTimes,
    invalidTimeRanges,
    confirmAssign,

    teacherComment,
    updateTeacherComment,

    subjectKey,

    totalAiCourseTime,
    totalPracticeTime,
  };
}

export const AssignCourseContext =
  createContext<AssignCourseContextType | null>(null);

export const AssignCourseProvider =
  AssignCourseContext.Provider as React.Provider<AssignCourseContextType>;

export function useAssignCourseContext() {
  const state = useContext(AssignCourseContext);

  if (!state) {
    throw new Error("Please use under AssignCourseProvider");
  }

  return state;
}
