import { FEEDBACK_TYPE } from "@/enums";
import { useFeedback } from "./useFeedback";

export enum FeedbackSource {
  COURSE_PREVIEW = "课程预览页",
  HOMEWORK_ASSIGN = "作业布置（资源中心）",
  REPORT = "学情报告页",
}

interface FeedbackQuestionPayload {
  feedbackSource: FeedbackSource;
  // 问题ID
  questionId: string | number;

  // 学科ID
  subjectId: string | number;
  courseId?: string | number;
  // 学段id
  feedbackPhaseId?: number;
  // 问题版本ID
  feedbackQuestionVersionId: number;
  // 报告详情tab
  tab?: string;
  // 报告详情view
  view?: string;
}

interface FeedbackQuestionPayloadInWidget {
  feedbackSource: FeedbackSource.COURSE_PREVIEW;
  questionId: string | number;
  // 问题版本ID
  feedbackQuestionVersionId: number;
  subjectId: string | number;
  // 学段id
  feedbackPhaseId?: number;

  courseId: string | number;
  version: string;
  widgetIndex: number;
  feedbackWidgetName: string;
}

interface FeedbackCourcePayload {
  feedbackSource: FeedbackSource;
  // 学段id
  feedbackPhaseId?: number;
  // 学科ID，e.g: 2 - 表示数学
  subjectId: number;
  // 课程 - 根据id和版本号确定具体内容
  // 课程ID
  courseId: string | number;
  // 课程版本号
  version: string;

  /*
    这两个看情况使用：
      视频讲解里点反馈：
        传入widgetIndex
        如果对应widgetType为video，则需要传入url
        如果对应widgetType为exercise，则需要传入当时预览的questionId
        如果对应widgetType为guide，则没有额外参数

      练习列表里点某个题目的反馈问题按钮：
        传入questionId
  */
  // 组件索引，e.g: 0 - 表示第一个组件，同接口的widgetIndex
  widgetIndex: number;
  feedbackWidgetName: string;
  // 问题ID
  questionId?: string;
  // 问题版本ID
  feedbackQuestionVersionId?: number;
}

interface FeedbackVideoParamsInWidget {
  feedbackSource: FeedbackSource;
  // 学段id
  feedbackPhaseId?: number;
  // 学科ID，e.g: 2 - 表示数学
  subjectId: number;
  // 课程 - 根据id和版本号确定具体内容
  // 课程ID
  courseId: string | number;
  // 课程版本号
  version: string;
  // 目前只会在视频讲解里点反馈
  widgetIndex: number;
  feedbackWidgetName: string;
  // url: string;
}

type FeedbackQuestionParams = [
  FEEDBACK_TYPE.QUESTION,
  FeedbackQuestionPayload | FeedbackQuestionPayloadInWidget,
];
type FeedbackCourceParams = [FEEDBACK_TYPE.COURSE, FeedbackCourcePayload];
type FeedbackVideoParams = [FEEDBACK_TYPE.VIDEO, FeedbackVideoParamsInWidget];

type FeedbackParams =
  | FeedbackQuestionParams
  | FeedbackCourceParams
  | FeedbackVideoParams;

export function useFeedbackByType() {
  const feedback = useFeedback();

  const routeToFeedback = (...[type, payload]: FeedbackParams) => {
    feedback.openFeedback({
      feedbackType: type,
      ...payload,
    });
  };

  return {
    isOpen: feedback.isOpen,
    routeToFeedback,
    closeFeedback: feedback.closeFeedback,
  };
}
