"use client";

import { signal, computed } from "@preact-signals/safe-react";

export interface QuestionStats {
  answerCount: number;
  wrongCount: number;
  correctRate: number;
}

export interface QuestionOption {
  key: string;
  content: string;
  count: number;
  percentage: number;
}

export interface Question {
  id: string;
  orderNumber: number;
  content: string;
  options: QuestionOption[];
  answer: string;
  analysis: string;
  difficulty: 'easy' | 'medium' | 'hard';
  stats: QuestionStats;
  tags: string[];
}

interface QuestionState {
  questions: Question[];
  filter: {
    keyword: string;
    showCommonWrong: boolean;
    sortBy?: 'answerCount' | 'wrongCount' | 'orderNumber';
  };
  pagination: {
    current: number;
    pageSize: number;
  };
  loading: boolean;
  selectedQuestionId: string | null;
}

export const questionState = signal<QuestionState>({
  questions: [],
  filter: {
    keyword: '',
    showCommonWrong: false,
    sortBy: 'answerCount'
  },
  pagination: {
    current: 1,
    pageSize: 10
  },
  loading: false,
  selectedQuestionId: null
});

// 计算属性：过滤后的题目列表
export const filteredQuestions = computed(() => {
  let result = [...questionState.value.questions];
  const { keyword, showCommonWrong } = questionState.value.filter;
  
  // 关键词过滤
  if (keyword) {
    result = result.filter(q => 
      q.content.toLowerCase().includes(keyword.toLowerCase()) ||
      q.options.some(opt => opt.content.toLowerCase().includes(keyword.toLowerCase())) ||
      q.analysis.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  // 共性错题过滤
  if (showCommonWrong) {
    result = result.filter(q => 
      q.stats.answerCount > 10 && q.stats.correctRate < 60
    );
  }

  // 排序逻辑
  return result
});

// 计算属性：分页后的题目列表
export const paginatedQuestions = computed(() => {
  const { current, pageSize } = questionState.value.pagination;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  
  return filteredQuestions.value.slice(start, end);
});

// 计算属性：总页数
export const totalPages = computed(() => {
  return Math.ceil(filteredQuestions.value.length / questionState.value.pagination.pageSize);
});

// 更新过滤条件
export function updateFilter(newFilter: Partial<typeof questionState.value.filter>) {
  questionState.value = {
    ...questionState.value,
    filter: {
      ...questionState.value.filter,
      ...newFilter
    },
    // 重置分页
    pagination: {
      ...questionState.value.pagination,
      current: 1
    }
  };
}

// 更新分页
export function updatePagination(newPagination: Partial<typeof questionState.value.pagination>) {
  questionState.value = {
    ...questionState.value,
    pagination: {
      ...questionState.value.pagination,
      ...newPagination
    }
  };
}

// 选择题目
export function selectQuestion(id: string | null) {
  questionState.value = {
    ...questionState.value,
    selectedQuestionId: id
  };
}

// 重置状态
export function resetQuestionState() {
  questionState.value = {
    questions: [],
    filter: {
      keyword: '',
      showCommonWrong: false,
      sortBy: 'answerCount'
    },
    pagination: {
      current: 1,
      pageSize: 10
    },
    loading: false,
    selectedQuestionId: null
  };
}
