import React, { useState, useRef } from 'react';
import NumberSelector, { NumberSelectorRef } from './index';
import { message } from 'antd';

# NumberSelector 组件

数字选择器组件，支持纯展示模式和带状态管理的模式。

## 特性

- 🔢 数字选择器
- 📖 浏览历史记录（可选）
- ❌ 错误状态显示（可选）
- 🎨 可定制样式
- 🚀 简洁的 API 设计
- 📱 纯展示模式支持

## 使用场景

### 1. 纯展示模式

使用 `count` 参数，适用于简单的数字选择器，无需状态管理：

```tsx
<NumberSelector count={10} value={0} onChange={handleChange} />
```

### 2. 状态管理模式

使用 `options` 参数，支持浏览历史和错误状态：

```tsx
<NumberSelector
  options={questionStates}
  value={activeIndex}
  onChange={handleChange}
  browsingHistory={true}
/>
```

## Props

```typescript
interface NumberSelectorProps {
  options?: NumberSelectorOption[]; // 选项数组（状态管理模式）
  count?: number; // 项目数量（纯展示模式）
  value: number; // 当前选中索引
  browsingHistory?: boolean; // 是否启用浏览历史记录显示
  disabled?: boolean; // 是否禁用
  onChange: (params: {
    // 选择变化回调
    currentVal: string;
    currentIdx: number;
    prevVal: string;
    prevIdx: number;
    options?: NumberSelectorOption[]; // 仅在状态管理模式下提供
  }) => void;
}

// 选项数据结构（状态管理模式）
interface NumberSelectorOption {
  id: string; // 问题ID
  status: ItemStatus; // 状态
  errorMessage?: string; // 错误信息（可选）
}
```

## Ref 方法

```typescript
interface NumberSelectorRef {
  getCurrentValue: () => number; // 获取当前选中值
  getOptions: () => NumberSelectorOption[] | undefined; // 获取选项数组（如果有）
  getCount: () => number; // 获取项目总数
}
```

## 基本使用

### 纯展示模式

```tsx
import NumberSelector from '@/components/NumberSelector';

function SimpleExample() {
  const [value, setValue] = useState(0);

  const handleChange = ({ currentIdx }) => {
    setValue(currentIdx);
    console.log('选择了第', currentIdx + 1, '项');
  };

  return <NumberSelector count={5} value={value} onChange={handleChange} />;
}
```

### 状态管理模式

```tsx
import NumberSelector, { ItemStatus } from '@/components/NumberSelector';

function StatefulExample() {
  const [value, setValue] = useState(0);
  const [options, setOptions] = useState([
    { id: 'q1', status: ItemStatus.UNVISITED },
    { id: 'q2', status: ItemStatus.VISITED },
    { id: 'q3', status: ItemStatus.ERROR, errorMessage: '表单验证失败' },
  ]);

  const handleChange = ({ currentIdx, options: newOptions }) => {
    setValue(currentIdx);
    // 在这里处理状态变化逻辑
    console.log('当前索引:', currentIdx);
    console.log('选项状态:', newOptions);
  };

  return (
    <NumberSelector
      options={options}
      value={value}
      onChange={handleChange}
      browsingHistory={true}
    />
  );
}
```

## 高级使用

### 动态切换模式

```tsx
function DynamicExample() {
  const [mode, setMode] = useState<'simple' | 'advanced'>('simple');
  const [value, setValue] = useState(0);
  const [options, setOptions] = useState([
    { id: 'q1', status: ItemStatus.UNVISITED },
    { id: 'q2', status: ItemStatus.VISITED },
    { id: 'q3', status: ItemStatus.ACTIVE },
    { id: 'q4', status: ItemStatus.ERROR, errorMessage: '有错误' },
  ]);

  const handleChange = ({ currentIdx }) => {
    setValue(currentIdx);
  };

  return (
    <div>
      <button onClick={() => setMode(mode === 'simple' ? 'advanced' : 'simple')}>
        切换模式: {mode}
      </button>

      {mode === 'simple' ? (
        <NumberSelector count={3} value={value} onChange={handleChange} />
      ) : (
        <NumberSelector
          options={options}
          value={value}
          onChange={handleChange}
          browsingHistory={true}
        />
      )}
    </div>
  );
}
```

### 状态更新示例

```tsx
function StateUpdateExample() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [questionStates, setQuestionStates] = useState([
    { id: 'q1', status: ItemStatus.UNVISITED },
    { id: 'q2', status: ItemStatus.UNVISITED },
    { id: 'q3', status: ItemStatus.UNVISITED },
  ]);

  // 更新问题状态
  const updateQuestionStatus = (id, status, errorMessage) => {
    setQuestionStates((prev) =>
      prev.map((item) => (item.id === id ? { ...item, status, errorMessage } : item)),
    );
  };

  const handleChange = ({ currentIdx, prevIdx }) => {
    // 标记当前题目为已访问
    const currentQuestion = questionStates[currentIdx];
    if (currentQuestion && currentQuestion.status === ItemStatus.UNVISITED) {
      updateQuestionStatus(currentQuestion.id, ItemStatus.VISITED);
    }

    setActiveIndex(currentIdx);
  };

  return (
    <NumberSelector
      options={questionStates}
      value={activeIndex}
      onChange={handleChange}
      browsingHistory={true}
    />
  );
}
```

## 参数优先级

组件会根据传入的参数自动选择模式：

1. **如果传入 `options`**: 使用状态管理模式，忽略 `count` 参数
2. **如果未传入 `options` 但传入 `count`**: 使用纯展示模式
3. **如果都未传入**: 默认使用 `count = 0`

```tsx
// 状态管理模式 - options 优先
<NumberSelector options={[...]} count={10} /> // 实际使用 options.length

// 纯展示模式
<NumberSelector count={10} /> // 使用 count

// 默认情况
<NumberSelector /> // count = 0，显示空选择器
```

## 样式定制

组件支持以下 CSS 类名：

- `.selectorContainer`: 容器样式
- `.selectorItem`: 项目样式
- `.selected`: 选中状态
- `.visited`: 已访问状态（仅状态管理模式）
- `.error`: 错误状态（仅状态管理模式）
- `.disabled`: 禁用状态

```css
.selectorItem.visited {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.selectorItem.error {
  background-color: #fff2f0;
  border-color: #ff4d4f;
}
```

## 类型定义

```typescript
// 项目状态枚举
export enum ItemStatus {
  UNVISITED = 'unvisited',
  VISITED = 'visited',
  ACTIVE = 'active',
  ERROR = 'error',
}

// 选项数据结构
export interface NumberSelectorOption {
  id: string;
  status: ItemStatus;
  errorMessage?: string;
}
```

## 设计理念

该组件采用了灵活的设计理念：

1. **双模式支持**: 既支持简单的纯展示，也支持复杂的状态管理
2. **参数优先级**: 智能选择使用模式，避免配置冲突
3. **向后兼容**: 保持原有 API 的兼容性
4. **类型安全**: 完整的 TypeScript 类型定义

这种设计使得组件既能满足简单的展示需求，也能处理复杂的状态管理场景。
