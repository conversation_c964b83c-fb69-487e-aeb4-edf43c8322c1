'use client';

import { EyeOutlined } from '@ant-design/icons';
import { Button, Table, TableColumnsType, Tag } from 'antd';
import dayjs from 'dayjs';
import type React from 'react';

interface HistoryListItem {
  id: number;
  name: string;
  uploadTime: number;
  status: number;
}

const getStatusConfig = (status: number) => {
  switch (status) {
    case 1:
      return { text: '后台自动识别中', color: 'processing' };
    case 2:
      return { text: '识别失败重新上传', color: 'error' };
    case 3:
      return { text: '审核中', color: 'warning' };
    case 4:
      return { text: '审核不通过', color: 'error', showLink: true };
    case 5:
      return { text: '数据已入库', color: 'success' };
    default:
      return { text: '未知状态', color: 'default' };
  }
};

const data: HistoryListItem[] = [
  {
    id: 1,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741594833749,
    status: 1,
  },
  {
    id: 2,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷2017 - 2018年河北省邢台市桥东区月考试卷2017 - 2018年河北省邢台市桥东区月考试卷2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741594843749,
    status: 2,
  },
  {
    id: 3,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741597833749,
    status: 3,
  },
  {
    id: 4,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741597833749,
    status: 4,
  },
  {
    id: 5,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741597833749,
    status: 5,
  },
  {
    id: 6,
    name: '2017 - 2018年河北省邢台市桥东区月考试卷',
    uploadTime: 1741597833749,
    status: 5,
  },
];

const HistoryUploadList: React.FC = () => {
  function handleView(record: HistoryListItem) {
    console.log(record);
  }
  const columns: TableColumnsType<HistoryListItem> = [
    {
      title: '已上传Word文件',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      width: '200px',
      render: (text: number) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '进度',
      dataIndex: 'status',
      width: '180px',
      render: (status: number, record: HistoryListItem) => {
        const { text, color, showLink } = getStatusConfig(status);
        return (
          <span>
            <Tag color={color}>{text}</Tag>
            {showLink && (
              <Button
                type="link"
                size="small"
                style={{ padding: 0, marginLeft: 8, gap: 2 }}
                icon={<EyeOutlined />}
                onClick={() => handleView(record)}
              >
                查看
              </Button>
            )}
          </span>
        );
      },
    },
  ];

  return (
    <div>
      <Table columns={columns} dataSource={data} size="middle" rowKey="id" />
    </div>
  );
};

export default HistoryUploadList;
