"use client";

import { cn } from "@repo/ui/lib/utils";
import * as React from "react";
import { createRoot } from "react-dom/client";

const info_danger = (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.6667 10C19.6667 15.0626 15.5626 19.1667 10.5 19.1667C5.43739 19.1667 1.33333 15.0626 1.33333 10C1.33333 4.93739 5.43739 0.833336 10.5 0.833336C15.5626 0.833336 19.6667 4.93739 19.6667 10Z"
      fill="#FA5A57"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.13896 6.58977C7.8492 6.3 7.3794 6.3 7.08964 6.58977C6.79988 6.87953 6.79988 7.34933 7.08964 7.63909L9.45066 10.0001L7.08975 12.361C6.79999 12.6508 6.79999 13.1206 7.08975 13.4103C7.37951 13.7001 7.84931 13.7001 8.13907 13.4103L10.5 11.0494L12.8609 13.4104C13.1507 13.7001 13.6205 13.7001 13.9102 13.4104C14.2 13.1206 14.2 12.6508 13.9102 12.361L11.5493 10.0001L13.9103 7.63907C14.2001 7.34931 14.2001 6.87951 13.9103 6.58975C13.6206 6.29999 13.1508 6.29999 12.861 6.58975L10.5 8.95079L8.13896 6.58977Z"
      fill="white"
    />
  </svg>
);

const info_notice = (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="10.5" cy="9.99999" r="9.16667" fill="#6574FC" />
    <path
      d="M10.5 10L10.5 14.1667"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M10.5 6.66667L10.5 7.08334"
      stroke="white"
      strokeWidth="2.6"
      strokeLinecap="round"
    />
  </svg>
);

const info_success = (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.6667 10C19.6667 15.0626 15.5626 19.1667 10.5 19.1667C5.43739 19.1667 1.33333 15.0626 1.33333 10C1.33333 4.93739 5.43739 0.833332 10.5 0.833332C15.5626 0.833332 19.6667 4.93739 19.6667 10Z"
      fill="#4DE03F"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.9258 7.09962C15.3114 7.44316 15.3455 8.03426 15.002 8.41989L10.5335 13.5345C10.1455 13.9786 9.45938 13.9923 9.05405 13.5639L6.52715 10.8933C6.16995 10.5202 6.18277 9.9283 6.55578 9.5711C6.92879 9.2139 7.52074 9.22672 7.87794 9.59973L9.76029 11.6068L13.6055 7.17584C13.949 6.79021 14.5401 6.75608 14.9258 7.09962Z"
      fill="white"
    />
  </svg>
);

const info_warning = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_363_10395)">
      <circle cx="10" cy="9.99999" r="9.16667" fill="#FA9524" />
      <path
        d="M10 5.83333V9.99999"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M10 13.3333V13.75"
        stroke="white"
        strokeWidth="3"
        strokeLinecap="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_363_10395">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

interface ToastProps {
  message: string;
  description?: string;
  type?: "success" | "error" | "info" | "warning" | "default";
  duration?: number;
  onClose?: () => void;
  id: string;
  position: number;
}

const icons = {
  success: info_success,
  error: info_danger,
  info: info_notice,
  warning: info_warning,
};

const TOAST_GAP = 2; // %
const TOAST_HEIGHT = 4; // %
const INITIAL_BOTTOM = 20; // %

const ToastContainer = ({
  message,
  // description,
  type = "default",
  duration = 2000,
  onClose,
  position,
  id,
}: ToastProps) => {
  const [isExiting, setIsExiting] = React.useState(false);
  const [isEntering, setIsEntering] = React.useState(true);

  React.useEffect(() => {
    // 进入动画
    const enterTimer = setTimeout(() => {
      setIsEntering(false);
    }, 10);

    // 退出动画
    const exitTimer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => {
        onClose?.();
      }, 200);
    }, duration);

    return () => {
      clearTimeout(enterTimer);
      clearTimeout(exitTimer);
    };
  }, [duration, onClose]);

  const bottom = `${INITIAL_BOTTOM + position * (TOAST_HEIGHT + TOAST_GAP)}%`;

  return (
    <div
      data-id={id}
      className={cn(
        "toast-item z-999 fixed left-1/2 -translate-x-1/2 transform",
        "flex items-start gap-1 rounded-full px-2.5 py-2 shadow-lg",
        "transition-all duration-200 ease-in-out will-change-transform",
        isEntering ? "translate-y-2 opacity-0" : "translate-y-0 opacity-100",
        isExiting ? "translate-y-2 opacity-0" : "",
        "bg-[rgba(16,16,25,0.7)] text-white"
      )}
      style={{ bottom }}
    >
      {type !== "default" && icons[type]}
      <div className="flex flex-col">
        <span className="whitespace-nowrap text-sm font-normal">{message}</span>
      </div>
    </div>
  );
};

let toastContainer: HTMLDivElement | null = null;
let toastCount = 0;
const activeToasts: string[] = [];

const createToastContainer = () => {
  if (typeof document === "undefined") return null;

  toastContainer = document.createElement("div");
  toastContainer.id = "toast-container";
  document.body.appendChild(toastContainer);
  return toastContainer;
};

const updateToastPositions = () => {
  if (!toastContainer) return;

  const toastElements = toastContainer.getElementsByClassName("toast-item");
  Array.from(toastElements).forEach((el, index) => {
    const bottom = `${INITIAL_BOTTOM + index * (TOAST_HEIGHT + TOAST_GAP)}%`;
    (el as HTMLElement).style.bottom = bottom;
  });
};

const show = (
  message: string,
  options?: Omit<ToastProps, "message" | "id" | "position">
) => {
  if (typeof document === "undefined") return;

  const container = toastContainer || createToastContainer();
  if (!container) return;

  const toastId = `toast-${Date.now()}-${toastCount++}`;

  const toastElement = document.createElement("div");
  container.appendChild(toastElement);

  const root = createRoot(toastElement);

  const handleClose = () => {
    const index = activeToasts.indexOf(toastId);
    if (index > -1) {
      activeToasts.splice(index, 1);
    }

    // 先移除元素
    root.unmount();
    container.removeChild(toastElement);

    // 再更新其他 toast 的位置
    updateToastPositions();
  };

  // 先添加到活动列表
  activeToasts.push(toastId);
  const position = activeToasts.length - 1;

  // 渲染新的 toast
  root.render(
    <ToastContainer
      message={message}
      {...options}
      id={toastId}
      position={position}
      onClose={handleClose}
    />
  );

  // 更新所有已存在的 toast 的位置
  updateToastPositions();
};

export const toast = {
  show,
  success: (
    message: string,
    options?: Omit<ToastProps, "message" | "type" | "id" | "position">
  ) => show(message, { ...options, type: "success" }),
  error: (
    message: string,
    options?: Omit<ToastProps, "message" | "type" | "id" | "position">
  ) => show(message, { ...options, type: "error" }),
  info: (
    message: string,
    options?: Omit<ToastProps, "message" | "type" | "id" | "position">
  ) => show(message, { ...options, type: "info" }),
  warning: (
    message: string,
    options?: Omit<ToastProps, "message" | "type" | "id" | "position">
  ) => show(message, { ...options, type: "warning" }),
};
