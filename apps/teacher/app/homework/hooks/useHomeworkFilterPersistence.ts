import { LOCALSTORAGE_HOMEWORK_FILTER_KEY_PREFIX } from "@/configs";
import { TASK_TYPE } from "@/enums";
import { useApp } from "@/hooks";
import { useCurrentSubjectLocalStorage } from "@/hooks/useCurrentSubjectLocalStorage";
import { GetHomeworkListParams } from "@/services/homework";
import { format, subDays } from "date-fns";
import { useCallback } from "react";

export interface HomeworkFilterData {
  taskType: TASK_TYPE;
  subject: number;
  groupId: number;
  startDate: string;
  endDate: string;
}

export interface HomeworkFilterPersistence {
  getFilterLocalStorageKey: () => string;
  getFilterFromLocalStorage: () => HomeworkFilterData | null;
  setFilterToLocalStorage: (filter: Partial<HomeworkFilterData>) => void;
  clearFilterFromLocalStorage: () => void;
}

/**
 * 作业报告筛选条件持久化 Hook
 * 结合用户ID安全地保存和恢复筛选条件
 */
export function useHomeworkFilterPersistence(): HomeworkFilterPersistence {
  const { userInfo } = useApp();
  const { setCurrentSubjectToLocalStorage } =
    useCurrentSubjectLocalStorage();

  const getFilterLocalStorageKey = useCallback(() => {
    return `${LOCALSTORAGE_HOMEWORK_FILTER_KEY_PREFIX}-${userInfo?.userID}`;
  }, [userInfo?.userID]);

  const getFilterFromLocalStorage = useCallback((): HomeworkFilterData | null => {
    try {
      const key = getFilterLocalStorageKey();
      const filterData = localStorage.getItem(key);
      const filterDataJSON = filterData ? JSON.parse(filterData) : null;

      return filterDataJSON;
    } catch (error) {
      console.error("获取作业筛选条件失败:", error);
      return null;
    }
  }, [getFilterLocalStorageKey]);

  const setFilterToLocalStorage = useCallback((filter: Partial<HomeworkFilterData>) => {
    try {
      const key = getFilterLocalStorageKey();
      const existingFilter = getFilterFromLocalStorage();

      // 合并现有数据和新数据
      const updatedFilter: HomeworkFilterData = {
        ...getDefaultFilterData(),
        ...existingFilter,
        ...filter
      };

      localStorage.setItem(key, JSON.stringify(updatedFilter));
      setCurrentSubjectToLocalStorage({
        subjectKey: updatedFilter.subject,
      });
    } catch (error) {
      console.error("保存作业筛选条件失败:", error);
    }
  }, [getFilterLocalStorageKey, getFilterFromLocalStorage, setCurrentSubjectToLocalStorage]);

  const clearFilterFromLocalStorage = useCallback(() => {
    try {
      const key = getFilterLocalStorageKey();
      localStorage.removeItem(key);
    } catch (error) {
      console.error("清除作业筛选条件失败:", error);
    }
  }, [getFilterLocalStorageKey]);

  return {
    getFilterLocalStorageKey,
    getFilterFromLocalStorage,
    setFilterToLocalStorage,
    clearFilterFromLocalStorage,
  };
}

/**
 * 获取默认筛选条件
 */
export const getDefaultFilterData = (): HomeworkFilterData => ({
  taskType: 0 as TASK_TYPE,
  subject: 0,
  groupId: 0,
  startDate: format(subDays(new Date(), 6), 'yyyy-MM-dd'),
  endDate: format(new Date(), 'yyyy-MM-dd'),
});

/**
 * 将筛选数据转换为查询参数
 */
export const convertFilterToQuery = (filter: HomeworkFilterData): Partial<GetHomeworkListParams> => ({
  taskType: filter.taskType,
  subject: filter.subject,
  groupId: filter.groupId,
  startDate: filter.startDate,
  endDate: filter.endDate,
});