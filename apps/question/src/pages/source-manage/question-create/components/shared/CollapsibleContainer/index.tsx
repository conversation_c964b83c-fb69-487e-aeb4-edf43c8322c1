import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import cn from 'classnames';
import React, { ReactNode, useState } from 'react';
import styles from './index.module.less';

interface CollapsibleContainerProps {
  /** 头部内容 */
  header: ReactNode;
  /** 子内容 */
  children: ReactNode;
  /** 初始折叠状态，默认展开(false) */
  defaultCollapsed?: boolean;
  /** 容器自定义className */
  className?: string;
  /** 头部自定义className */
  headerClassName?: string;
}

const CollapsibleContainer: React.FC<CollapsibleContainerProps> = ({
  header,
  children,
  defaultCollapsed = false,
  className,
  headerClassName,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className={cn(styles.collapsibleContainer, className)}>
      <div className={cn(styles.header, headerClassName)}>
        <div className={styles.headerContent}>{header}</div>
        <Button
          type="text"
          size="small"
          icon={isCollapsed ? <DownOutlined /> : <UpOutlined />}
          onClick={toggleCollapse}
          className={styles.collapseButton}
        />
      </div>
      {!isCollapsed && <div className={styles.content}>{children}</div>}
    </div>
  );
};

export default CollapsibleContainer;
