import RegionSelector from '@/components/RegionSelector';
import useEnumManagerMap from '@/hooks/useEnumManager';
import KnowledgePointSelector from '@/pages/source-manage/components/KnowledgePointSelector';
import { Col, Form, FormProps, Radio, Row, Select } from 'antd';
import { useMemo } from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';

const BaseForm = (props: FormProps) => {
  const { recentYearList, subjectLabelQuestionTypeRelationList, questionDifficultList } =
    useEnumManagerMap();
  const { form, activeQuestion, setDefaultBaseTreeNodeIds } = useQuestionCreate();

  const labelQuestionTypeList = useMemo(() => {
    return (
      subjectLabelQuestionTypeRelationList?.enums.find(
        (item) => item.value === activeQuestion.subject,
      )?.labelQuestionTypeList || []
    );
  }, [activeQuestion.subject, subjectLabelQuestionTypeRelationList]);

  return (
    <Form form={form} {...props}>
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item
            name="labelQuestionType"
            label="题型"
            rules={[{ required: true, message: '请选择题型' }]}
          >
            <Select allowClear placeholder="请选择题型">
              {labelQuestionTypeList.map((item: { value: string | number; nameZh: string }) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.nameZh}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="questionYear" label="年份">
            <Select allowClear placeholder="请选择年份">
              {recentYearList?.enums.map((item: { value: string | number; label: string }) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="region" label="地区">
            <RegionSelector
              placeholder="请选择地区"
              allowClear
              showSearch
              treeNodeFilterProp="title"
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={8}>
          {/* 知识点选择 */}
          <Form.Item
            name="baseTreeNodeIds"
            label="所属基础树"
            rules={[{ required: true, message: '请选择所属基础树' }]}
          >
            <KnowledgePointSelector
              onChange={(value) => {
                setDefaultBaseTreeNodeIds(value.baseTreeNodeIds);
                form.setFieldValue('baseTreeNodeIds', value.baseTreeNodeIds);
              }}
              baseTreeId={activeQuestion?.baseTreeId}
              onlyLeaf={true}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="questionDifficult"
            label="难度"
            initialValue={1}
            rules={[{ required: true, message: '请选择难度' }]}
          >
            <Radio.Group>
              {questionDifficultList?.enums.map(
                (item: { value: string | number; label: string }) => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ),
              )}
            </Radio.Group>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default BaseForm;
