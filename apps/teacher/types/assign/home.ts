import { AssignTaskTypeEnum } from "@/configs/assign";
import { CommonResponseType } from ".";
import { HomeWorkData } from "../homeWork";

// export type UserTaskType = 10 | 20 | 30 | 40;
export interface UserSubjectItem {
  subjectKey: number;
  subjectName: string;
  taskTypes: AssignTaskTypeEnum[];
}
export interface AssignSubjectListData {
  subjectTaskTypes: UserSubjectItem[];
}
export type AssignSubjectListResponse = CommonResponseType<AssignSubjectListData>;

export type TaskLatestAssignResponse = CommonResponseType<HomeWorkData>;