import { cn } from "@/utils";
import { Dialog, Dialog<PERSON>lose, <PERSON>alog<PERSON>ontent, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger } from "@radix-ui/react-dialog";
import { Maximize2, X } from "lucide-react";
import Image from "next/image";
import { TransformComponent, TransformWrapper } from "react-zoom-pan-pinch";

interface ImageViewerProps {
  className?: string;
  classNameImageViewer?: string;
  classNameThumbnailViewer?: string;
  imageTitle?: string;
  imageUrl: string;
  width?: number;
  height?: number;
  thumbnailUrl?: string;
}

const ImageViewer = ({
  className,
  classNameImageViewer,
  classNameThumbnailViewer,
  imageTitle,
  imageUrl,
  width = 100,
  height = 100,
  thumbnailUrl,
}: ImageViewerProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className={cn("cursor-pointer relative", className)}>
          <Image src={thumbnailUrl || imageUrl} alt={`${imageTitle}-Preview`} width={width} height={height} className={cn("rounded-lg object-cover transition-opacity hover:opacity-90", classNameThumbnailViewer)} />
          <span className="bg-[rgba(0,0,0,0.3)] size-5 rounded-full absolute top-1 right-1 flex justify-center items-center">
          <Maximize2 className="text-white rotate-90 size-4" />
          </span>
        </div>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="motion-preset-fade motion-duration-150 fixed inset-0 z-50 bg" />
        <DialogContent className="bg-background motion-scale-in-[0.5] motion-rotate-in-[-10deg] motion-blur-in-[5px] motion-duration-150 motion-duration-150/translate motion-duration-[0.00s]/rotate fixed inset-0 z-50 flex flex-col items-center justify-center p-0 !bg-[rgba(0,0,0,0)]">
          <DialogTitle className="sr-only">{imageTitle}</DialogTitle>
          <div className="relative flex h-screen w-screen items-center justify-center bg-[rgba(0,0,0,0.3)]">
            <TransformWrapper initialScale={1} initialPositionX={0} initialPositionY={0}>
              {() => (
                <>
                  <TransformComponent>
                    <Image src={imageUrl} alt={imageTitle || ''} className={classNameImageViewer} width={500} height={500} />
                  </TransformComponent>
                </>
              )}
            </TransformWrapper>
            <DialogClose asChild>
              <button className="absolute top-4 right-4 z-10 cursor-pointer rounded-full bg-black/50 p-2 text-white transition-colors hover:bg-black/70" aria-label="Close">
                <X className="size-6" />
              </button>
            </DialogClose>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

export default ImageViewer;