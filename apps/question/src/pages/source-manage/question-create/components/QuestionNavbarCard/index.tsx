import { ArrowLeftOutlined } from '@ant-design/icons';
import { Card, Col, Row, Space } from 'antd';
import React from 'react';
import { useQuestionCreateActions } from '../../hooks/useQuestionCreateActions';
import { QuestionCreateProps } from '../../types';
import styles from './index.module.less';
import QuestionActionButtons from './QuestionActionButtons';

// 题目标题组件的属性接口
interface IProps extends QuestionCreateProps {
  auditTaskId: string; // 审核任务ID
  auditTaskName?: string; // 审核任务名称
}

/**
 * 题目标题组件
 * 显示返回按钮和题目ID信息
 */
const QuestionNavbar: React.FC<IProps> = ({
  onBack,
  auditTaskId,
  auditTaskName,
  way,
  data,
  onSubmit,
}) => {
  const { handleBlurSave } = useQuestionCreateActions({ way, data, onBack, onSubmit });
  return (
    <Card hoverable={true} className={styles.questionNavbarCard}>
      <Row justify="space-between" align="middle" className={styles.questionNavbar}>
        <Col>
          <Space>
            {/* 返回按钮 */}
            {onBack && (
              <span
                className={styles.questionTitle}
                onClick={() => {
                  // 非纠错模式下，保存数据
                  if (way !== 'correction') {
                    handleBlurSave();
                  }
                  onBack?.();
                }}
              >
                <ArrowLeftOutlined />
                返回
              </span>
            )}

            {/* 题目ID信息 */}
            {auditTaskId ? (
              <span className={styles.questionId}>
                <span className={styles.questionIdBold}>{auditTaskName}</span>
                【ID: {auditTaskId}】
              </span>
            ) : (
              <span>上传题目</span>
            )}
          </Space>
        </Col>
        <Col>
          <QuestionActionButtons way={way} data={data} onBack={onBack} onSubmit={onSubmit} />
        </Col>
      </Row>
    </Card>
  );
};

export default QuestionNavbar;
