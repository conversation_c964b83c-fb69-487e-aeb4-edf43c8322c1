"use client";

import { Slot } from "@radix-ui/react-slot";
import { cn } from "@repo/ui/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { LoaderCircle } from "lucide-react";
import * as React from "react";
import { FC } from "react";

//TODO: 定制的按钮组件需要移动到apps/aipt
const buttonVariants = cva(
  "inline-flex items-center justify-center cursor-pointer whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none",
  {
    variants: {
      type: {
        primary:
          "bg-indigo-500 text-white hover:bg-indigo-400 active:bg-indigo-600",
        outline:
          "border border-indigo-500 bg-transparent text-indigo-500 hover:bg-indigo-50 active:bg-indigo-100",
        default:
          "border border-slate-200 bg-white text-slate-600 hover:bg-slate-50 active:bg-slate-200",
        text: "bg-white text-slate-600 hover:bg-slate-50 active:bg-slate-200",
        error:
          "border border-red-200 bg-white text-red-600 hover:bg-rose-50 active:bg-red-100",
      },
      radius: {
        default: "rounded",
        full: "rounded-full",
      },
      state: {
        default: "",
        hover: "",
        pressed: "",
        disabled: "cursor-not-allowed",
        loading: "cursor-wait",
      },
      size: {
        sm: "h-7 px-3 text-xs",
        md: "h-8 px-4 text-sm",
        lg: "h-9 px-5 text-sm",
      },
      isIconOnly: {
        true: "p-0",
        false: "",
      },
    },
    defaultVariants: {
      type: "default",
      radius: "default",
      size: "md",
      state: "default",
      isIconOnly: false,
    },
    compoundVariants: [
      // 中大尺寸圆角
      {
        size: ["md", "lg"],
        radius: "default",
        className: "rounded-md",
      },
      // 图标按钮尺寸
      {
        isIconOnly: true,
        size: "sm",
        className: "h-7 w-7",
      },
      {
        isIconOnly: true,
        size: "md",
        className: "h-8 w-8",
      },
      {
        isIconOnly: true,
        size: "lg",
        className: "h-9 w-9",
      },
      // 主要按钮禁用状态
      {
        type: "primary",
        state: "disabled",
        className: "bg-indigo-200",
      },
      {
        type: "primary",
        state: "loading",
        className: "bg-indigo-300 text-white",
      },
      // 描边按钮禁用状态
      {
        type: "outline",
        state: "disabled",
        className: "border-indigo-200 bg-indigo-50 text-indigo-300",
      },
      {
        type: "outline",
        state: "loading",
        className: "border-indigo-300 text-indigo-400 bg-transparent",
      },
      // 默认按钮禁用状态
      {
        type: "default",
        state: "disabled",
        className: "border-slate-200 bg-slate-100 text-slate-400",
      },
      {
        type: "default",
        state: "loading",
        className: "border-slate-200 bg-slate-50 text-slate-500",
      },
      // error按钮禁用状态
      {
        type: "error",
        state: "disabled",
        className: "border-red-100 bg-rose-50 text-red-300",
      },
      {
        type: "error",
        state: "loading",
        className: "border-red-200 bg-rose-50 text-red-400",
      },
      // 无边框按钮禁用状态
      {
        type: "text",
        state: "disabled",
        className: "bg-slate-100 text-slate-400",
      },
      {
        type: "text",
        state: "loading",
        className: "bg-slate-50 text-slate-500",
      },
    ],
  }
);

type ButtonBaseProps = {
  asChild?: boolean;
  loading?: boolean | { icon?: React.ReactNode };
  icon?: React.ReactNode;
  iconPlacement?: "start" | "end";
  isIconOnly?: boolean;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
} & VariantProps<typeof buttonVariants>;

type ButtonAsButtonProps = ButtonBaseProps &
  Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, keyof ButtonBaseProps> & {
    href?: undefined;
  };

type ButtonAsAnchorProps = ButtonBaseProps &
  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof ButtonBaseProps> & {
    href: string;
  };

type ButtonProps = ButtonAsButtonProps | ButtonAsAnchorProps;

const Button: FC<ButtonProps> = (props) => {
  const {
    className,
    radius,
    size,
    type,
    asChild = false,
    loading = false,
    icon,
    href,
    iconPlacement = "start",
    isIconOnly = false,
    disabled,
    children,
    ...rest
  } = props;

  const renderIcon = () => {
    if (loading) {
      const isLoadingObject = typeof loading === "object";
      const loadingIcon =
        isLoadingObject && loading.icon ? (
          loading.icon
        ) : (
          <LoaderCircle
            className={cn(
              "animate-spin",
              size === "sm" ? "size-4" : size === "lg" ? "size-6" : "size-5"
            )}
          />
        );

      return (
        <span
          className={cn(
            "-ml-1.5 mr-1 inline-flex",
            isIconOnly
              ? "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
              : ""
          )}
        >
          {loadingIcon}
        </span>
      );
    }
    return icon ? (
      <span
        className={cn(
          !isIconOnly && children && iconPlacement === "start"
            ? "-ml-1 mr-1 flex-shrink-0"
            : "",
          !isIconOnly && children && iconPlacement === "end"
            ? "-mr-1 ml-1 flex-shrink-0"
            : "",
          size === "sm" ? "text-base" : size === "lg" ? "text-xl" : "text-lg"
        )}
      >
        {icon}
      </span>
    ) : null;
  };

  const classes = cn(
    buttonVariants({
      radius,
      type,
      size,
      state: disabled ? "disabled" : loading ? "loading" : "default",
      isIconOnly,
      className,
    })
  );

  if (href) {
    return (
      <a
        className={classes}
        href={href}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {!isIconOnly && iconPlacement === "start" && renderIcon()}
        {children}
        {!isIconOnly && iconPlacement === "end" && renderIcon()}
        {isIconOnly && renderIcon()}
      </a>
    );
  }

  const Comp = asChild ? Slot : "button";
  return (
    <Comp
      className={classes}
      disabled={
        disabled ||
        (typeof loading === "boolean" ? loading : loading?.icon !== undefined)
      }
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {!isIconOnly && iconPlacement === "start" && renderIcon()}
      {children}
      {!isIconOnly && iconPlacement === "end" && renderIcon()}
      {isIconOnly && renderIcon()}
    </Comp>
  );
};

Button.displayName = "Button";

export { Button, buttonVariants };
