import { AiCourse, Practice } from "@/types/assign/course";
import { calculateClassTimeStr, minutesToString } from "@/utils/date";

export function getTotalMinutes(list: AiCourse[] | Practice[] = []) {
  return list.reduce(
    (acc, item) =>
      acc +
      ("totalDuration" in item
        ? (item.totalDuration || 0) / 60
        : item.estimatedTime || 0),
    0
  );
}

export function getEstimatedTime(list: AiCourse[] | Practice[]) {
  const total = getTotalMinutes(list);
  if (total <= 0) {
    return "";
  }
  const time = minutesToString(total);
  const classTime = calculateClassTimeStr(total);
  return `${time}（${classTime}）`;
}

export function formatTime(totalMinutes: number) {
  return minutesToString(totalMinutes);
}
