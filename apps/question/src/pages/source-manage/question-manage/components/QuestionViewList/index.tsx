/**
 * 题目管理列表
 */
import QuestionList from '@/components/QuestionList';
import useEnumManagerMap from '@/hooks/useEnumManager';
import { fetchQuestionList } from '@/services/api';
import {
  checkQuestionListRequestParams,
  compareNumberArrayEqual,
  handleMultiQuestionSearch,
} from '@/utils';
import { useRequest } from '@umijs/max';
import { cloneDeep } from 'lodash';
import { FC, useEffect } from 'react';

export interface QuestionViewListProps {
  requestParams?: API.QuestionListRequestParams;
  onPageChange?: (page: number, pageSize: number) => void;
}
const QuestionViewList: FC<QuestionViewListProps> = ({ requestParams, onPageChange }) => {
  const { provinceList } = useEnumManagerMap();
  const {
    data: questionListData,
    run: fetchQuestionListRun,
    loading: fetchQuestionListLoading,
  } = useRequest(
    () => {
      const validate = checkQuestionListRequestParams(requestParams);
      if (!requestParams || !validate) {
        return Promise.resolve({
          data: {
            total: 0,
            page: 1,
            pageSize: 10,
            list: [],
          },
        });
      }
      // 省份如果全选，则置空
      const payload = cloneDeep(requestParams);
      const { questionProvince } = payload;
      const provinces = provinceList?.enums.map((item) => item.value) ?? [];
      if (questionProvince && compareNumberArrayEqual(questionProvince, provinces)) {
        payload.questionProvince = [];
      }
      // 处理关键字搜索时，是我个问题Id的情况
      handleMultiQuestionSearch(payload);
      return fetchQuestionList(payload);
    },
    {
      manual: true,
      // throttleInterval: 1000,
      debounceInterval: 300,
    },
  );

  useEffect(() => {
    fetchQuestionListRun();
  }, [requestParams]);

  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    console.log('onQuestionEventHandler: ', question, type);
  };

  return (
    <div>
      <QuestionList
        questions={questionListData?.list ?? []}
        loading={fetchQuestionListLoading}
        pagination={{
          pageSize: questionListData?.pageSize ?? 10,
          total: questionListData?.total ?? 0,
          page: questionListData?.page ?? 1,
        }}
        onPageChange={(page, pageSize) => onPageChange?.(page, pageSize)}
        onQuestionEventHandler={onQuestionEventHandler}
        refresh={fetchQuestionListRun}
      />
    </div>
  );
};
export default QuestionViewList;
