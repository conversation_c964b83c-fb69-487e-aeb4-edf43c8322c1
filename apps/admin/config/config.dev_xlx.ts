// config/config.test.ts test环境对应的配置文件
import type { IConfigFromPlugins } from '@@/core/pluginConfig';
import { defineConfig } from '@umijs/max';

/**
 * 导出的多环境变量命名约定：一律大写且采用下划线分割单词
 * 注意：在添加变量后，需要在src/typing.d.ts内添加该变量的声明，否则在使用变量时IDE会报错。
 */

// 输出当前配置文件被加载的信息
console.log('======== 加载本地环境配置 config.local.ts ========');

export default defineConfig({
  define: {
    REACT_APP_ENV: 'dev_xlx',
    API_URL: 'https://admin-api.dev.xiaoluxue.cn', // API地址
    // API_URL: 'http://172.16.28.47:8080', // API地址
    API_SECRET_KEY: 'XXXXXXXXXXXXXXXX', // API调用密钥
  },
}) as IConfigFromPlugins;
