import { umeng, UmengCategory, UmengCommonAction } from "@/utils";
import { useEffect } from "react";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const formatDuration = (duration: number) => {
  // 计算总秒数
  const totalSeconds = Math.floor(duration / 1000);
  // 计算小时数
  const hours = Math.floor(totalSeconds / 3600)
    .toString()
    .padStart(2, "0");
  // 计算剩余的秒数
  const remainingSecondsAfterHours = totalSeconds % 3600;
  // 计算分钟数
  const minutes = Math.floor(remainingSecondsAfterHours / 60)
    .toString()
    .padStart(2, "0");
  // 计算最终剩余的秒数
  const seconds = (remainingSecondsAfterHours % 60).toString().padStart(2, "0");

  return `${hours}:${minutes}:${seconds}`;
};

/**
 * 友盟统计
 * 自动触发页面浏览，自动统计页面停留时间 (注意只在需要统计页面停留时间时使用，无需统计直接用 `import { umeng } from "@/utils";`)
 */
export const useUmeng = (category: UmengCategory, pageName: string) => {
  useEffect(() => {
    umeng.trackEvent(category, UmengCommonAction.PAGE_VIEW, {
      page_name: pageName,
    });

    const duration = 60 * 1000;

    const timer = setInterval(() => {
      umeng.trackEvent(category, UmengCommonAction.PAGE_TIME, {
        page_name: pageName,
      });
    }, duration);

    return () => {
      clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return umeng;
};
