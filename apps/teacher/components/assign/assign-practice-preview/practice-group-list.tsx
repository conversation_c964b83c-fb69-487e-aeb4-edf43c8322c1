import { QuestionSetGroupItem } from "@/types/assign";
import { cn } from "@/utils/utils";

interface PracticeGroupListProps {
  style?: React.CSSProperties;
  className?: string;
  practiceGroupList: QuestionSetGroupItem[];
  activeGroup: number;
  onChange: (group: QuestionSetGroupItem) => void;
}

export function PracticeGroupList({
  style = {},
  className = "",
  practiceGroupList = [],
  activeGroup,
  onChange,
}: PracticeGroupListProps) {
  return (
    <div className={cn("flex w-full flex-col gap-1", className)} style={style}>
      {practiceGroupList.map((item) => (
        <div
          key={item.questionGroupId}
          className={cn(
            "hover:bg-primary-6 hover:text-primary-1 cursor-pointer rounded-md p-2 text-sm leading-normal",
            activeGroup === item.questionGroupId && "bg-primary-6 text-primary-1 font-medium"
          )}
          onClick={() => onChange(item)}
        >
          {item.questionGroupName}
        </div>
      ))}
    </div>
  );
}
