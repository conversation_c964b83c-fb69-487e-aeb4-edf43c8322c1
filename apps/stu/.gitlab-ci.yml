image: node:20

variables:
  # 阿里云OSS配置，这些变量应该在GitLab CI/CD设置中配置为安全变量
  # Settings -> CI/CD -> Variables
  OSS_ACCESS_KEY_ID: ${OSS_ACCESS_KEY_ID}
  OSS_ACCESS_KEY_SECRET: ${OSS_ACCESS_KEY_SECRET}
  OSS_BUCKET: ${OSS_BUCKET}
  OSS_REGION: ${OSS_REGION}
  # 默认使用out目录作为构建输出目录，如果您的项目使用的是.next/static，请相应调整
  DIST_DIR: "out"

stages:
  - build
  - deploy

# 缓存node_modules以加快构建速度
cache:
  paths:
    - node_modules/
    - .pnpm-store/

# 只在master分支执行
workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

build:
  stage: build
  script:
    - npm install -g pnpm
    - pnpm install
    - pnpm build
    # 如果使用的是Next.js的静态导出，添加导出命令
    - pnpm next export || echo "Next.js export not needed"
  artifacts:
    paths:
      - ${DIST_DIR}
    expire_in: 1 hour

deploy:
  stage: deploy
  image: python:3.10-slim
  dependencies:
    - build
  script:
    # 安装aliyun oss命令行工具
    - pip install aliyun-oss2
    - echo "开始上传文件到阿里云OSS..."
    # 创建上传脚本
    - |
      cat > upload_to_oss.py << 'EOF'
      import os
      import oss2
      
      # 获取OSS配置
      access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
      access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
      bucket_name = os.environ.get('OSS_BUCKET')
      region = os.environ.get('OSS_REGION')
      dist_dir = os.environ.get('DIST_DIR')
      
      # 连接OSS
      auth = oss2.Auth(access_key_id, access_key_secret)
      endpoint = f'https://oss-{region}.aliyuncs.com'
      bucket = oss2.Bucket(auth, endpoint, bucket_name)
      
      # 上传文件
      for root, dirs, files in os.walk(dist_dir):
          for file in files:
              local_path = os.path.join(root, file)
              # 计算OSS中的路径
              oss_path = local_path.replace(dist_dir + '/', '')
              print(f'上传: {local_path} -> {oss_path}')
              bucket.put_object_from_file(oss_path, local_path)
      
      print('上传完成!')
      EOF
    - python upload_to_oss.py
    - echo "部署完成!" 