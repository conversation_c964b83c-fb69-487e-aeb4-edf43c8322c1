<svg width="108" height="108" viewBox="0 0 108 108" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_194_1221)">
<g clip-path="url(#clip0_194_1221)">
<rect x="26" y="22" width="56" height="56" rx="18" fill="#FFE7CF"/>
<path d="M26.0313 106.052C17.4862 91.7798 24.4368 69.6733 29.3502 60.172C33.641 51.8746 36.0844 40.2674 39.9905 32.5465C43.2804 26.0433 45.5546 38.2083 53.4615 37.8178C60.8804 37.4514 66.806 25.7505 68.9826 31.668C71.7466 39.1827 73.9611 44.7486 80.2085 60.172C86.456 75.5954 90.5138 96.6529 81.9656 108.98C72.6921 122.354 35.2072 121.378 26.0313 106.052Z" fill="url(#paint0_linear_194_1221)"/>
<path d="M64.0041 64.0765C62.0518 67.1027 55.5115 68.1707 49.4593 67.1027C41.1618 65.6384 43.6657 50.6625 46.7261 47.8722C50.0451 44.8462 58.0495 44.4556 60.7828 46.5055C65.0956 49.7402 66.2274 60.6304 64.0041 64.0765Z" fill="#AE7365"/>
<ellipse cx="42.4309" cy="34.9869" rx="1.46425" ry="2.04995" transform="rotate(-10.5089 42.4309 34.9869)" fill="#B2786A"/>
<ellipse cx="66.9066" cy="34.0781" rx="1.53853" ry="1.9819" transform="rotate(15 66.9066 34.0781)" fill="#B2786A"/>
<path d="M42.3333 46.6033C42.919 46.1152 43.7976 45.8223 44.5785 45.8223" stroke="#4A292A" stroke-opacity="0.71" stroke-width="2" stroke-linecap="round"/>
<path d="M49.9474 49.9364C50.7937 50.5458 52.4423 51.243 54.3401 51.0446M58.3424 49.1267C57.0078 50.3717 55.6175 50.911 54.3401 51.0446M54.3401 51.0446C54.633 54.3474 55.1796 61.0895 55.0234 61.6362M55.0234 61.6362C54.8672 62.1829 51 64 48.5 60.5M55.0234 61.6362C55.0234 61.6362 58.3424 64 60 61" stroke="#5D2D2F" stroke-width="0.976166" stroke-linecap="round"/>
<path d="M67.3231 46.2128C67.0302 46.0175 66.2688 45.6271 65.566 45.6271" stroke="#4A292A" stroke-opacity="0.71" stroke-width="2" stroke-linecap="round"/>
<g filter="url(#filter1_f_194_1221)">
<ellipse cx="39.5711" cy="53.9617" rx="2.9285" ry="1.95233" fill="#FF9999"/>
</g>
<g filter="url(#filter2_f_194_1221)">
<ellipse cx="69.8322" cy="52.8879" rx="2.9285" ry="2.04995" fill="#FF9999"/>
</g>
<path d="M28.0523 62.4543C27.792 62.6821 27.5252 63.3915 27.7595 64.0162C30.8344 72.216 68.0752 106.382 84.4748 104.039C85.1163 103.947 85.0605 103.356 85.1581 102.672" stroke="#FFC290" stroke-width="1.95233" stroke-linecap="round"/>
</g>
<rect x="25" y="21" width="58" height="58" rx="19" stroke="white" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_194_1221" x="0" y="0" width="108" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.18 0 0 0 0 0.22 0 0 0 0 0.3 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_194_1221"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_194_1221" result="shape"/>
</filter>
<filter id="filter1_f_194_1221" x="32.7379" y="48.1047" width="13.6663" height="11.714" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.95233" result="effect1_foregroundBlur_194_1221"/>
</filter>
<filter id="filter2_f_194_1221" x="62.999" y="46.9333" width="13.6663" height="11.9092" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.95233" result="effect1_foregroundBlur_194_1221"/>
</filter>
<linearGradient id="paint0_linear_194_1221" x1="54.4204" y1="30" x2="54.4204" y2="118.328" gradientUnits="userSpaceOnUse">
<stop stop-color="#D79A71"/>
<stop offset="1" stop-color="#EDAA7D"/>
</linearGradient>
<clipPath id="clip0_194_1221">
<rect x="26" y="22" width="56" height="56" rx="18" fill="white"/>
</clipPath>
</defs>
</svg>
