# Question Management System - 项目特定规则

## 项目概要
这是内容管理平台项目，基于 Ant Design Pro 构建。

**全局架构规范请参考：**
- 📋 **MVVM 架构规范**：`/.cursor/rules/application-architecture.mdc`
- 📋 **前端代码规范**：`/.cursor/rules/global-rules/rules-frontend-code.mdc`
- 📋 **工作流程规范**：`/.cursor/rules/workflow.mdc`

## 题库系统特定规范

### 1. 技术栈特定要求
- **框架**: React 18+ + TypeScript + Ant Design Pro
- **公式渲染**: MathJax (数学公式必须支持)
- **构建工具**: UmiJS 4.x
- **UI 组件库**: Antd, ProComponents 优先
- **富文本编辑器**: 使用 Tiptap 编辑器

### 2. 业务组件规范

#### 题目组件开发
- **题目内容渲染**: 必须支持富文本和 MathJax 公式
- **答案处理**: 支持单选、多选、填空、简答等题型
- **进度追踪**: 答题进度和时间记录

#### 题库管理组件
- **题目录入**: 支持批量导入和单个录入
- **题目审核**: 包含审核状态和审核流程
- **分类管理**: 支持多级分类和标签系统

### 3. 数学公式处理规范

```typescript
// MathJax 公式渲染示例
import { MathJax } from 'better-react-mathjax';

// 行内公式
<MathJax inline dynamic>
  {`$${formula}$`}
</MathJax>

// 块级公式
<MathJax dynamic>
  {`$$${formula}$$`}
</MathJax>
```

### 4. Pro Components 使用规范

#### 表格场景 - 题目列表
```typescript
<ProTable
  columns={columns}
  request={async (params) => {
    const result = await queryQuestions(params);
    return {
      data: result.data,
      success: true,
      total: result.total, // 必须显示总数
    };
  }}
  pagination={{
    showTotal: (total) => `共 ${total} 道题目`, // 题库特定文案
  }}
  toolBarRender={() => [
    <Button key="import" type="Primary">批量导入</Button>,
    <Button key="export">导出题目</Button>,
  ]}
/>
```

#### 表单场景 - 题目编辑
```typescript
<ProForm
  layout="vertical" // 题目编辑使用垂直布局
  onFinish={async (values) => {
    // 题目提交前验证公式格式
    const validatedValues = validateMathFormulas(values);
    return submitQuestion(validatedValues);
  }}
>
  <ProFormText
    name="title"
    label="题目标题"
    rules={[{ required: true, message: '请输入题目标题' }]}
  />
  <ProFormTextArea
    name="content"
    label="题目内容"
    placeholder="支持 LaTeX 公式，如：$x^2 + y^2 = z^2$"
  />
</ProForm>
```

### 5. 文件上传处理

```typescript
// 题目附件上传
import { uploadQuestionAttachHelper } from '@/services/api/question';

const uploadProps = {
  customRequest: uploadQuestionAttachHelper,
  accept: '.jpg,.png,.pdf,.doc,.docx',
  maxCount: 5,
};
```

### 6. 业务状态管理

#### 题目状态
```typescript
type QuestionStatus = 'draft' | 'pending' | 'approved' | 'rejected';
type QuestionType = 'single' | 'multiple' | 'fill' | 'essay';

interface Question {
  id: string;
  title: string;
  content: string;
  type: QuestionType;
  status: QuestionStatus;
  difficulty: 1 | 2 | 3 | 4 | 5;
  tags: string[];
  formulas?: string[]; // MathJax 公式列表
}
```

### 7. 错误处理特定要求

```typescript
// 公式解析错误处理
const handleFormulaError = (error: any) => {
  if (error.type === 'MATHJAX_PARSE_ERROR') {
    message.error('数学公式格式错误，请检查 LaTeX 语法');
  } else {
    message.error('题目处理失败');
  }
};
```

### 8. 性能优化要求

- **大量题目渲染**: 使用虚拟滚动
- **公式缓存**: MathJax 渲染结果缓存
- **图片懒加载**: 题目附件图片懒加载

### 9. 可访问性要求

- **键盘导航**: 支持答题的键盘操作
- **屏幕阅读器**: 数学公式提供 alt text
- **高对比度**: 支持高对比度模式

### 10. 测试要求

```typescript
// 题目组件测试示例
describe('QuestionComponent', () => {
  it('should render math formula correctly', () => {
    const formula = 'x^2 + y^2 = z^2';
    render(<QuestionComponent formula={formula} />);
    expect(screen.getByText(/x\^2/)).toBeInTheDocument();
  });
});
```

---

## 开发检查清单

- [ ] 是否支持 MathJax 公式渲染
- [ ] 是否处理了各种题型的答案验证
- [ ] 是否添加了适当的错误处理
- [ ] 是否考虑了性能优化
- [ ] 是否遵循了 ProComponents 使用规范
- [ ] 是否添加了必要的可访问性支持

**注意**: 通用的 React、TypeScript、架构规范请参考根目录的全局规则文件。 