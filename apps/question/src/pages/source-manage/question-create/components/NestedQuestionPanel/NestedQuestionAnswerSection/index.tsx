import { Flex, Form } from 'antd';
import React, { memo, useMemo } from 'react';
import { useQuestionCreate } from '../../../contexts/QuestionCreateContext';
import { getQuestionStemFillPointUuids } from '../../../utils/questionBlank';
import AnswerFieldSelector from '../../AnswerFieldSelector';
import QuestionAnswerModeSelector from '../../QuestionAnswerModeSelector';
import QuestionMatchLine from '../../QuestionMatchLine';

interface NestedQuestionAnswerSectionProps {
  parentName: number;
  subName: number;
  restField: any;
  currentFillIdx: number;
}

/**
 * 嵌套题目答案区域组件
 * 负责管理二级题目的作答方式和答案字段
 */
const NestedQuestionAnswerSection: React.FC<NestedQuestionAnswerSectionProps> = ({
  parentName,
  subName,
  restField,
  currentFillIdx,
}) => {
  const { form } = useQuestionCreate();

  const questionAnswerMode = Form.useWatch(
    ['subQuestionList', parentName, 'subQuestionList', subName, 'questionAnswerMode'],
    form,
  );
  const questionStem = Form.useWatch(
    ['subQuestionList', parentName, 'subQuestionList', subName, 'questionStem'],
    form,
  );

  const isMatchLineName = [
    'subQuestionList',
    parentName,
    'subQuestionList',
    subName,
    'isMatchLine',
  ];
  const isMatchLine = Form.useWatch(isMatchLineName, form);

  const questionOptionMatrixName = [
    'subQuestionList',
    parentName,
    'subQuestionList',
    subName,
    'questionOptionMatrix',
  ];

  const count = useMemo(() => {
    return getQuestionStemFillPointUuids(questionStem || '').length;
  }, [questionStem]);

  return (
    <>
      <Flex justify="space-between">
        <QuestionAnswerModeSelector
          name={[subName, 'questionAnswerMode']}
          count={count}
          restField={restField}
        />
        <QuestionMatchLine
          name={[subName, 'isMatchLine']}
          count={count}
          isMatchLineName={isMatchLineName}
          questionAnswerMode={questionAnswerMode}
        />
      </Flex>
      <AnswerFieldSelector
        questionAnswerMode={questionAnswerMode}
        isMatchLine={isMatchLine}
        questionStem={questionStem}
        fieldName={[subName, 'questionOptionMatrix']}
        questionOptionMatrixName={questionOptionMatrixName}
        restField={restField}
        currentFillIdx={currentFillIdx}
      />
    </>
  );
};

export default memo(NestedQuestionAnswerSection);
