import Link from "@/components/TchLink";
import { cn } from "@/utils/utils";
import { FC, ReactNode, SVGProps } from "react";

export default function MenuLink({
  href,
  icon: Icon,
  activeIcon: ActiveIcon,
  children,
  active,
}: {
  active: boolean;
  href: string;
  icon: FC<SVGProps<SVGElement>>;
  activeIcon: FC<SVGProps<SVGElement>>;
  children: ReactNode;
}) {
  return (
    <Link
      href={href}
      className={cn(
        "text-gray-3 pl-6.75 flex h-12 items-center gap-2 rounded-xl border-[0.5px] border-transparent text-base transition-colors",
        active && "border-line-2 text-primary-1 bg-white font-medium",
        "hover:border-line-2 hover:bg-white"
      )}
      prefetch
    >
      {active ? <ActiveIcon /> : <Icon />}
      <span>{children}</span>
    </Link>
  );
}
