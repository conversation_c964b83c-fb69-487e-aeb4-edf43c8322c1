"use client";
import { Sheet, SheetContent, SheetTitle } from "@/ui/sheet";
import { cn } from "@/utils/utils";
import { ChevronLeft } from "lucide-react";
import { DrawerCard } from "./drawer-card";
import { EmptyState } from "@/components/common/empty-state";

interface CommunicationTemplate {
  id: string;
  content: string;
}

interface OfflineCommunicationDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTemplate?: (template: string) => void;
  className?: string;
}

export function OfflineCommunicationDrawer({
  open,
  onOpenChange,
  onSelectTemplate,
  className,
}: OfflineCommunicationDrawerProps) {
  // 预设的沟通模板
  const communicationTemplates: CommunicationTemplate[] = [
    {
      id: "1",
      content:
        "今天答题状态不是很好，是不是课程学习有点难，是否有需要我帮忙的吗？",
    },
    {
      id: "2",
      content:
        '"我理解有些知识点确实不容易掌握，老师以前学习时也遇到过类似问题。你觉得我们可以一起做些什么来改善呢？"',
    },
    {
      id: "3",
      content:
        '"老师很在意你的学习状态，也希望能帮到你。如果有什么需要支持的，可以随时告诉我。"',
    },
    {
      id: "4",
      content:
        '"最近课堂上，老师注意到你偶尔会走神/没有参与讨论（描述具体现象）。是不是最近遇到了什么困难？"',
    },
  ];

  const handleSelectTemplate = (template: CommunicationTemplate) => {
    onSelectTemplate?.(template.content);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className={cn(
          "bg-fill-gray-2 w-full gap-0 overflow-y-auto px-6 sm:max-w-md",
          className
        )}
        closeable={false}
      >
        <SheetTitle className="hidden" />
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-[0.625rem]">
              <div
                className="flex cursor-pointer items-center justify-center"
                onClick={() => onOpenChange(false)}
              >
                <ChevronLeft className="h-5" />
              </div>
              <span className="text-lg font-medium">线下沟通</span>
            </div>
          </div>

          <DrawerCard title="沟通参考：" className="flex-1 overflow-y-auto">
            <div className="space-y-4">
              {communicationTemplates.map((template) => (
                <div
                  key={template.id}
                  className="border-line-1 text-gray-3 flex cursor-pointer flex-col items-end gap-[0.6875rem] self-stretch rounded-xl border p-3 text-sm leading-[150%] transition-colors hover:bg-gray-50"
                  onClick={() => handleSelectTemplate(template)}
                  style={{
                    background:
                      "linear-gradient(180deg, #F5FAFF 13.1%, #FFF 80.77%)",
                  }}
                >
                  {template.content}
                </div>
              ))}
            </div>
            <EmptyState
              title={false}
              description={false}
              className="mt-6 items-end"
            />
          </DrawerCard>
        </div>
      </SheetContent>
    </Sheet>
  );
}
