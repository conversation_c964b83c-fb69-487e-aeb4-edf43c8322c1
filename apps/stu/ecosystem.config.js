// ecosystem.config.js
const defaultConfig = {
  script: "dist/standalone/server.js",
  name: "default-app", // 默认应用名称
  env: {
    // 默认环境变量
    PORT: process.env.PORT || 3000,
    NODE_ENV: "test", // 默认环境
    SERVICE_NAME: process.env.name || "default-service",
  }
};

// 环境特定配置
const envConfigs = {
  test: {
    PORT: process.env.PORT || 3000,
    NODE_ENV: "test",
  },
  prod: {
    PORT: process.env.PORT || 3000,
    NODE_ENV: "prod",
  },
};

module.exports = {
  apps: [
    {
      ...defaultConfig,
      // env 是默认环境配置
      env: defaultConfig.env,
      // 特定环境配置
      env_test: {
        ...defaultConfig.env,
        ...envConfigs.test,
      },
      env_prod: {
        ...defaultConfig.env,
        ...envConfigs.prod,
      },
    },
  ],
};