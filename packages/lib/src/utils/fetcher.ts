interface Response<T> {
  code: number;
  data: T;
  message: string;
}

class ErrorWithTraceIdAndHttpStatus extends Error {
  constructor(
    message: string,
    public traceId?: string | null,
    public httpCode = "没有httpStatus",
    public isFromCdn = false
  ) {
    super(message);
  }
}

function abortSignalAny(signals: AbortSignal[]) {
  const controller = new AbortController();

  const onAbort = (signal: AbortSignal) => {
    controller.abort(signal.reason);
  };

  for (const signal of signals) {
    if (signal.aborted) {
      controller.abort(signal.reason);
      break;
    }
    signal.addEventListener("abort", () => onAbort(signal), { once: true });
  }

  return controller.signal;
}

async function* streamIterator(stream: ReadableStream) {
  const decoder = new TextDecoderStream();
  // const splitter = new TransformStream({
  //   transform(chunk, controller) {
  //     for (let i = 0; i < chunk.length; i++) {
  //       controller.enqueue(chunk.slice(i, i + 1));
  //     }
  //   },
  // });
  const textStream = stream.pipeThrough(decoder);
  // .pipeThrough(splitter);
  const reader = textStream.getReader();
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      yield value;
    }
  } finally {
    reader.releaseLock();
  }
}

export const fetchStream = async (
  url: string | URL | globalThis.Request,
  init?: RequestInit,
  timeOut = 30000
) => {
  const abortController = new AbortController();
  let errMsg: string | undefined = undefined;
  const timer = setTimeout(() => {
    errMsg = `接口超过${timeOut}ms未响应`;
    abortController.abort();
  }, timeOut);
  const signal = init?.signal
    ? abortSignalAny([abortController.signal, init.signal])
    : abortController.signal;
  try {
    const response = await fetch(url, {
      ...init,
      signal,
      headers: {
        ...init?.headers,
      },
    });
    clearTimeout(timer);
    const traceId = response.headers.get("trace_id");
    const contentType = response.headers.get("Content-Type");
    if (!response.ok) {
      const error = new ErrorWithTraceIdAndHttpStatus(
        "网络请求失败",
        traceId,
        String(response.status)
      );
      // 将额外的信息附加到错误对象上。
      error.message = await response.text();
      throw error;
    }
    if (!contentType?.includes("text/plain")) {
      if (contentType?.includes("application/json")) {
        const res = (await response.json()) as Response<any>;
        const { code, message } = res;
        throw new ErrorWithTraceIdAndHttpStatus(
          `[${code}] ${message}`,
          traceId,
          String(response.status)
        );
      }
      throw new ErrorWithTraceIdAndHttpStatus(
        "不支持的Content-Type",
        traceId,
        String(response.status)
      );
    }
    if (!response.body) {
      throw new ErrorWithTraceIdAndHttpStatus(
        "请求失败，没有Body",
        traceId,
        String(response.status)
      );
    }
    return streamIterator(response.body);
  } catch (err) {
    if (err instanceof Error) {
      err.message = errMsg || err.message;
      throw err;
    }
    throw new Error("网络请求失败2");
  }
};

const fetcher = async <T>(
  url: string | URL | globalThis.Request,
  init?: RequestInit,
  timeOut = 30000
) => {
  const abortController = new AbortController();
  let errMsg: string | undefined = undefined;
  const timer = setTimeout(() => {
    errMsg = `接口超过${timeOut}ms未响应`;
    abortController.abort();
  }, timeOut);
  const signal = init?.signal
    ? abortSignalAny([abortController.signal, init.signal])
    : abortController.signal;
  try {
    async function retry(left = 3): Promise<globalThis.Response> {
      try {
        return await fetch(url, {
          ...init,
          signal,
          headers: {
            ...init?.headers,
          },
        });
      } catch (e) {
        if (e instanceof TypeError && e.message.includes("Failed to fetch")) {
          console.log("跨域或网络问题", {
            请求路径: url,
            剩余重试次数: left - 1,
          });
          if (left > 0) return retry(left - 1);
        }
        throw e;
      }
    }
    const response = await retry();
    clearTimeout(timer);
    const traceId = response.headers.get("trace_id");
    if (!response.ok) {
      const error = new ErrorWithTraceIdAndHttpStatus(
        "网络请求失败",
        traceId,
        String(response.status)
      );
      // 将额外的信息附加到错误对象上。
      error.message = await response.text();
      throw error;
    }
    const res = (await response.json()) as Response<T>;
    const { code, message, data } = res;

    if (code !== 0) {
      const error = new ErrorWithTraceIdAndHttpStatus(
        "网络请求失败",
        traceId,
        String(response.status)
      );
      error.message = `[${code}] ${message}`;
      throw error;
    }
    return data as T;
  } catch (err) {
    if (err instanceof Error) {
      err.message = errMsg || err.message;
      throw err;
    }
    throw new Error("网络请求失败2");
  }
};

export const fetchFile = async <T>(url: string, timeOut = 30000) => {
  const abortController = new AbortController();
  let errMsg: string | undefined = undefined;
  const timer = setTimeout(() => {
    errMsg = `文件请求超过${timeOut}ms`;
    abortController.abort();
  }, timeOut);
  const signal = abortController.signal;
  let isFromCdn = false;
  let httpCode: string | undefined = undefined;
  try {
    const response = await fetch(url, { signal });
    clearTimeout(timer);
    isFromCdn = response.headers.get("x-cdn") === "1";
    httpCode = String(response.status);
    if (!response.ok) {
      throw new ErrorWithTraceIdAndHttpStatus(
        "获取文件失败",
        undefined,
        httpCode,
        isFromCdn
      );
    }
    const blob = await response.blob();
    const text = await blob.text();
    return JSON.parse(text) as T;
  } catch (err) {
    throw new ErrorWithTraceIdAndHttpStatus(
      errMsg || (err as Error)?.message || "获取文件失败",
      undefined,
      httpCode,
      isFromCdn
    );
  }
};

/**
 * TODO: 移动到各个apps内实现
 */
export async function get<T>(
  url: string,
  { query }: { query?: Record<string, string> }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  return await fetcher<T>(`${url}?${params?.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export default fetcher;
