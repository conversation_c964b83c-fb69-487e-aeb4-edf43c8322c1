import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC } from "react";
import { CourseProgressBar } from "./course-progress-bar";
import { CourseViewProvider } from "./course-view-context";
import { CourseWidgetsLoaderView } from "./course-widgets-loader";

export const CourseView: FC<
  {
    courseProgressBarProps?: ComponentProps<typeof CourseProgressBar>;
    knowledgeId: number;
  } & ComponentProps<"div">
> = ({ className, knowledgeId, courseProgressBarProps }) => {
  return (
    <CourseViewProvider knowledgeId={knowledgeId}>
      <div className={cn("flex flex-row h-full", className)}>
        {/* 导航条 */}
        <CourseProgressBar {...courseProgressBarProps} />
        {/* 组件内容：文稿 or 练习 or 视频 */}
        <CourseWidgetsLoaderView />
      </div>
    </CourseViewProvider>
  );
};
