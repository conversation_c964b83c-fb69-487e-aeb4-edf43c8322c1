import { ArrowLeftOutlined } from '@ant-design/icons';
import { Outlet, useLocation, useModel, useNavigate, useSearchParams } from '@umijs/max';
import { Button, ConfigProvider, message, Spin, Tabs, Typography } from 'antd';
import React, { useEffect } from 'react';
import { useSchoolTabNavigation } from './hooks/useSchoolTabNavigation';
import styles from './index.less';

interface TabItem {
  key: string;
  label: string;
}

const { Title } = Typography;

const SchoolManagement: React.FC = () => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const schoolId = searchParams.get('id');
  const { schoolInfo, loading, fetchSchoolInfo } = useModel('schoolModel');
  const navigate = useNavigate();

  // 使用导航 hook
  const { schoolPermissions, routePrefix, hasPermission, navigateBack, navigateToMainTab } =
    useSchoolTabNavigation();

  useEffect(() => {
    if (schoolId) {
      // 确保 schoolId 是数字
      const numericSchoolId = parseInt(schoolId, 10);
      if (!isNaN(numericSchoolId)) {
        fetchSchoolInfo(numericSchoolId);
      }
    }

    // 如果直接访问主路由，默认导航到学期管理页面
    if (location.pathname === `${routePrefix}/management`) {
      navigate(`${routePrefix}/management/academic/semester?id=${schoolId}`);
    }
  }, [schoolId, routePrefix]);

  const handleBack = () => {
    navigateBack();
  };

  // 构建学术管理标签项
  const getAcademicTabItems = (): TabItem[] => {
    return [
      { key: 'semester', label: '学期管理' },
      { key: 'subject', label: '学科教材管理' },
      { key: 'schedule', label: '课表管理' },
      { key: 'teacherSchedule', label: '老师课表' },
      { key: 'examinationManagement', label: '考情管理' },
    ];
  };

  // 构建账号管理标签项
  const getAccountTabItems = (): TabItem[] => {
    return [
      { key: 'student', label: '学生管理' },
      { key: 'teacher', label: '教师管理' },
    ];
  };

  // 处理主标签切换
  const handleMainTabChange = async (tab: string) => {
    // console.log(` schoolPermissions`,schoolPermissions)
    // 检查主标签的权限
    const mainTabPath = `${routePrefix}/management/${tab}`;
    const hasMainTabPermission = await hasPermission(mainTabPath, schoolPermissions);

    if (!hasMainTabPermission) {
      message.error('您没有权限访问此页面');
      return;
    }

    // 如果是权限管理标签，直接导航
    if (tab === 'permission') {
      navigate(`${routePrefix}/management/permission?id=${schoolId}`);
      return;
    }

    // 否则，导航到第一个有权限的子标签
    await navigateToMainTab(tab);
  };

  // 确定当前激活的主标签
  const getActiveMainTab = () => {
    const pathname = location.pathname;
    if (pathname.includes('/academic/')) return 'academic';
    if (pathname.includes('/account/')) return 'account';
    if (pathname.includes('/permission')) return 'permission';
    return 'academic'; // 默认
  };

  // 确定当前学术管理激活的子标签
  const getAcademicActiveTab = () => {
    const pathname = location.pathname;
    if (pathname.includes('/academic/semester')) return 'semester';
    if (pathname.includes('/academic/subject')) return 'subject';
    if (pathname.includes('/academic/schedule')) return 'schedule';
    if (pathname.includes('/academic/teacherSchedule')) return 'teacherSchedule';
    if (pathname.includes('/academic/examinationManagement')) return 'examinationManagement';
    return 'semester'; // 默认
  };

  // 确定当前账号管理激活的子标签
  const getAccountActiveTab = () => {
    const pathname = location.pathname;
    if (pathname.includes('/account/student')) return 'student';
    if (pathname.includes('/account/teacher')) return 'teacher';
    return 'student'; // 默认
  };

  // 处理教务管理标签切换
  const handleAcademicTabChange = async (tab: string) => {
    console.log(`routePrefix + tab`, routePrefix, tab);
    const targetPath = `${routePrefix}/management/academic/${tab}`;
    const hasTabPermission = await hasPermission(targetPath, schoolPermissions);

    if (!hasTabPermission) {
      message.error('您没有权限访问此页面');
      return;
    }

    navigate(`${targetPath}?id=${schoolId}`);
  };

  // 处理账号管理标签切换
  const handleAccountTabChange = async (tab: string) => {
    const targetPath = `${routePrefix}/management/account/${tab}`;
    const hasTabPermission = await hasPermission(targetPath, schoolPermissions);

    if (!hasTabPermission) {
      message.error('您没有权限访问此页面');
      return;
    }

    navigate(`${targetPath}?id=${schoolId}`);
  };

  return (
    <div className={styles.container}>
      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div className={styles.header}>
            <Button
              type="link"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className={styles.backButton}
            >
              返回
            </Button>
            <Title level={4} className={styles.schoolName}>
              {schoolInfo?.schoolName || '学校详情'}
            </Title>
            <div className={styles.placeholder} />
          </div>

          <Tabs
            type="card"
            size="small"
            activeKey={getActiveMainTab()}
            onChange={handleMainTabChange}
            className={styles.tabs}
            items={[
              { key: 'academic', label: '教务管理' },
              { key: 'account', label: '账号管理' },
              { key: 'permission', label: '权限管理' },
            ]}
          />

          {getActiveMainTab() === 'academic' && (
            <div className={styles.academicContainer}>
              <ConfigProvider
                theme={{
                  components: {
                    Tabs: {
                      titleFontSize: 12,
                    },
                  },
                }}
              >
                <Tabs
                  tabBarGutter={10}
                  tabBarStyle={{
                    padding: '0px 10px',
                  }}
                  activeKey={getAcademicActiveTab()}
                  onChange={handleAcademicTabChange}
                  items={getAcademicTabItems()}
                />
              </ConfigProvider>
            </div>
          )}

          {getActiveMainTab() === 'account' && (
            <div className={styles.academicContainer}>
              <Tabs
                size="small"
                activeKey={getAccountActiveTab()}
                onChange={handleAccountTabChange}
                className={styles.subTabs}
                items={getAccountTabItems()}
              />
            </div>
          )}

          <div className={styles.secondTabContent}>
            <Outlet />
          </div>
        </>
      )}
    </div>
  );
};

export default SchoolManagement;
