import { Flex } from 'antd';
import { FC } from 'react';
import useStyles from './index.style';

interface ProgressInfoItemType {
  least: number;
  current: number;
}

export interface ExerciseProgressInfoProps {
  ease: ProgressInfoItemType;
  relativelyEasy: ProgressInfoItemType;
  medium: ProgressInfoItemType;
  relativelyHard: ProgressInfoItemType;
  hard: ProgressInfoItemType;
}
const ExerciseProgressInfo: FC<ExerciseProgressInfoProps> = ({
  ease,
  relativelyEasy,
  medium,
  relativelyHard,
  hard,
}) => {
  const { styles } = useStyles();

  return (
    <Flex gap="small" wrap style={{ flex: 1 }} align="center" justify="center">
      <div className={styles.progressInfoItem + ' green'}>
        简单{ease.current}/{ease.least}
      </div>
      <div className={styles.progressInfoItem + ' cyan'}>
        较易{relativelyEasy.current}/{relativelyEasy.least}
      </div>
      <div className={styles.progressInfoItem + ' blue'}>
        中等{medium.current}/{medium.least}
      </div>
      <div className={styles.progressInfoItem + ' orange'}>
        较难{relativelyHard.current}/{relativelyHard.least}
      </div>
      <div className={styles.progressInfoItem + ' red'}>
        困难{hard.current}/{hard.least}
      </div>
    </Flex>
  );
};
export default ExerciseProgressInfo;
