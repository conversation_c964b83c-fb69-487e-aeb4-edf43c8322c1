interface license {
  url: string;
  appId: number;
}

const licenseMap = {
  "xiaoluxue.com": {
    url: "https://vod-license-m.volccdn.com/vod-license/l-**********-ch-vod-a-797888.lic?lk3s=0d9f7a7d&x-expires=**********&x-signature=NGp4uOGZiF0RNeqAzwX9Lyu2h18%3D",
    appId: 797888,
  },
  "xiaoluxue.cn": {
    url: "https://vod-license-m.volccdn.com/vod-license/l-**********-ch-vod-a-797887.lic?lk3s=0d9f7a7d&x-expires=**********&x-signature=QmZXD04gaVkO%2FZizaL6GRa9IZjY%3D",
    appId: 797887,
  },
};

export const getLicense = () => {
  const hostName = window.location.hostname;
  let license: license;
  if (hostName.indexOf("xiaoluxue.com") > -1) {
    license = licenseMap["xiaoluxue.com"];
  } else {
    license = licenseMap["xiaoluxue.cn"];
  }
  return license;
};
