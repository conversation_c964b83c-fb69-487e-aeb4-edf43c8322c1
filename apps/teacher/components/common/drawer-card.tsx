"use client"

import React, { ReactNode } from "react";
import { cn } from "@/utils/utils";

interface DrawerCardProps {
  /** 卡片标题 */
  title?: string;
  /** 标题右侧操作区域 */
  titleAction?: ReactNode;
  /** 卡片内容 */
  children: ReactNode;
  /** 底部操作区域 */
  footer?: ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 标题类名 */
  titleClassName?: string;
  /** 内容类名 */
  contentClassName?: string;
  /** 底部类名 */
  footerClassName?: string;
}

export function DrawerCard({
  title,
  titleAction,
  children,
  footer,
  className,
  titleClassName,
  contentClassName,
  footerClassName,
}: DrawerCardProps) {

  return (
    <div 
      className={cn(
        "bg-white rounded-lg mb-4 p-4",
        className
      )}
    >
      {/* 标题区域 */}
      {(title || titleAction) && (
        <div className={cn(
          "flex justify-between items-center", 
          children && "mb-2",
          titleClassName
        )}>
          {title && (
            <h3 className="text-gray-1 font-medium leading-[150%] text-base">
              {title}
            </h3>
          )}
          {titleAction}
        </div>
      )}
      
      {/* 内容区域 */}
      <div className={contentClassName}>
        {children}
      </div>
      
      {/* 底部区域 */}
      {footer && (
        <div className={cn("mt-4", footerClassName)}>
          {footer}
        </div>
      )}
    </div>
  );
} 