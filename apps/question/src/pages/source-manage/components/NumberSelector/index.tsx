import cn from 'classnames';
import { forwardRef, memo, useCallback, useMemo, useRef } from 'react';
import styles from './NumberSelector.module.less';

// 项目状态枚举
export enum ItemStatus {
  UNVISITED = 'unvisited', // 未访问
  VISITED = 'visited', // 已访问
  ACTIVE = 'active', // 当前查看
  ERROR = 'error', // 有错误
}

// 选项数据结构
export interface NumberSelectorOption {
  id: string;
  status?: ItemStatus;
  errorMessage?: string;
}

interface NumberSelectorProps {
  options?: NumberSelectorOption[];
  count?: number;
  value: number;
  browsingHistory?: boolean;
  disabled?: boolean;
  onChange: ({
    currentVal,
    currentIdx,
    prevVal,
    prevIdx,
    options,
  }: {
    currentVal: string;
    currentIdx: number;
    prevVal: string;
    prevIdx: number;
    options?: NumberSelectorOption[];
  }) => void;
}

export interface NumberSelectorRef {
  // 基础方法
  getCurrentValue: () => number;
  getOptions: () => NumberSelectorOption[] | undefined;
  getCount: () => number;
}

const NumberSelector = forwardRef<NumberSelectorRef, NumberSelectorProps>(
  ({ options, count = 0, value = 0, browsingHistory = false, disabled = false, onChange }, ref) => {
    // 计算实际数量：优先使用 options 长度，否则使用 count
    const actualCount = options?.length ?? count;

    // 使用 useRef 来存储上一次的值，避免触发重新渲染
    const prevValueRef = useRef({
      value: value,
      key: options?.[value]?.id || String(value),
    });

    // 处理点击事件
    const handleClick = useCallback(
      (idx: number) => {
        if (disabled || idx >= actualCount) return;

        const currentKey = options?.[idx]?.id || String(idx);
        const currentIdx = idx;

        // 获取改变前的值
        const prevKey = prevValueRef.current.key;
        const prevIdx = prevValueRef.current.value;

        // 更新 ref 中的当前值（下次就变成 prev 值）
        prevValueRef.current = {
          value: currentIdx,
          key: currentKey,
        };

        // 使用 requestAnimationFrame 延迟执行，避免阻塞 UI
        requestAnimationFrame(() => {
          onChange({
            currentVal: currentKey,
            currentIdx: currentIdx,
            prevVal: prevKey,
            prevIdx: prevIdx,
            options: options ? [...options] : undefined, // 只有在有 options 时才传递
          });
        });
      },
      [disabled, actualCount, options, onChange],
    );

    // // 暴露方法给父组件
    // useImperativeHandle(ref, () => ({
    //   getCurrentValue: () => value,
    //   getOptions: () => (options ? [...options] : undefined),
    //   getCount: () => actualCount,
    // }));

    // 渲染项目
    const selectorItems = useMemo(() => {
      return Array.from({ length: actualCount }).map((_, idx) => {
        const option = options?.[idx];

        // 判断各种状态
        const isVisitedItem = browsingHistory && option?.status === ItemStatus.VISITED;
        const isErrorItem = browsingHistory && option?.status === ItemStatus.ERROR;

        // 选中状态：要么是当前 value 索引，要么是 ACTIVE 状态
        const isSelected = value === idx;

        return (
          <div
            key={option?.id || idx}
            className={cn(styles.selectorItem, {
              [styles.selected]: isSelected,
              [styles.visited]: isVisitedItem,
              [styles.error]: isErrorItem,
              [styles.disabled]: disabled,
            })}
            onClick={() => handleClick(idx)}
            title={isErrorItem ? option?.errorMessage : undefined}
          >
            {idx + 1}
          </div>
        );
      });
    }, [actualCount, value, options, browsingHistory, disabled, handleClick]);

    return <div className={styles.selectorContainer}>{selectorItems}</div>;
  },
);

export default memo(NumberSelector);
