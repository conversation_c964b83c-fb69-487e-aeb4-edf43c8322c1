import LoadingIcon from "@/public/icons/animate_spin.svg";

export default function ReportCardLabel({
  label,
  value,
  loading,
}: {
  label: string;
  value: string;
  loading: boolean;
}) {
  return (
    <div className="flex flex-1 flex-col">
      {/* 统计项标签 */}
      <div className="text-gray-4 text-[0.625rem] font-normal leading-[150%]">
        {label}
      </div>
      {/* 统计项数值 */}
      <div className="text-gray-2 flex h-5 items-center text-sm font-medium lining-nums proportional-nums leading-[150%]">
        {loading ? (
          <>
            {" "}
            <LoadingIcon className="h-4 w-4 animate-spin" />
          </>
        ) : value ? (
          value
        ) : (
          "—"
        )}
      </div>
    </div>
  );
}
