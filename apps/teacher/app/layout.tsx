import LayoutIndex from "@/app/_layout";
import FaviconIcon from "@/public/favicon.ico";
import { cn } from "@/utils";
import type { Metadata, Viewport } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "小鹿爱学",
  description: "小鹿爱学-教师端",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export default function RootLayout(props: {
  children: React.ReactNode;
  login: boolean;
}) {
  return (
    <html lang="zh-CN" className={cn("h-full")}>
      <head>
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        <link rel="icon" href={FaviconIcon.src} sizes="32*32" />
      </head>

      <body className="h-full">
        <LayoutIndex {...props} />
      </body>
    </html>
  );
}
