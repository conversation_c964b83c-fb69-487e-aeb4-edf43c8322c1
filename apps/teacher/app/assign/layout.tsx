"use client";
import { ConfigProvider } from "antd";
import { AssignProvider } from "./store";

export default function AssignLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AssignProvider>
      {/* AntD字体设置——待移除所有AntD组件后删除 */}
      <ConfigProvider
        theme={{
          token: {
            fontFamily:
              "Roboto, AlibabaPuHuiTi-3-55-Regular, AlibabaPuHuiTi-3-55-RegularL3",
          },
        }}
      >
        {children}
      </ConfigProvider>
    </AssignProvider>
  );
}
