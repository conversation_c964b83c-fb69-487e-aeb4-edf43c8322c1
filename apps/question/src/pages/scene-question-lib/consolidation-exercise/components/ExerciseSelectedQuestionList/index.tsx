import QuestionList from '@/components/QuestionList';
import { FC } from 'react';

export interface ExerciseSelectedQuestionListProps {
  questions: API.QuestionItemType[];
  onPageChange?: (page: number, pageSize: number) => void;
}
/**
 * 已选题目列表
 */
const ExerciseSelectedQuestionList: FC<ExerciseSelectedQuestionListProps> = ({
  questions = [],
}) => {
  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    console.log('onQuestionEventHandler: ', question, type);
  };

  return (
    <div>
      <QuestionList
        questions={questions}
        type="exercise-selected"
        onQuestionEventHandler={onQuestionEventHandler}
      />
    </div>
  );
};
export default ExerciseSelectedQuestionList;
