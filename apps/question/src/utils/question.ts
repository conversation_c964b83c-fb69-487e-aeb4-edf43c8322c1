import { cloneDeep } from 'lodash';
export const formatQuestionNo = (
  question: API.QuestionItemType,
  idx: number,
  perfix: string = '',
) => {
  question.questionNo = `${perfix}${idx + 1}`;
  if (question.subQuestionList && question.subQuestionList.length > 0) {
    question.subQuestionList.forEach((item, idx) => {
      formatQuestionNo(item, idx, `${question.questionNo}.`);
    });
  }
};

export const formatTrueFalseQuestion = (question: API.QuestionItemType) => {
  if (!question) return question;

  const formatOption = (item: any) => ({
    ...item,
    optionVal: item.optionKey === 'true' ? '正确' : '错误',
  });

  const formatAnswer = (item: any) => ({
    ...item,
    optionVal: item.optionKey === 'true' ? '正确' : '错误',
    optionKey: '', // 清空答案key，仅保留“正确/错误”
  });

  // 处理选项内容
  if (question.questionContentPreview) {
    const { questionOptionList, questionOptionMatrix } = question.questionContentPreview;

    if (Array.isArray(questionOptionList) && questionOptionList.length > 0) {
      question.questionContentPreview.questionOptionList = questionOptionList.map(formatOption);
    }

    if (Array.isArray(questionOptionMatrix) && questionOptionMatrix.length > 0) {
      question.questionContentPreview.questionOptionMatrix = questionOptionMatrix.map(
        (row: any[]) => row.map(formatOption),
      );
    }
  }

  // 处理答案内容
  if (question.questionAnswerPreview) {
    const { answerOptionList, answerOptionMatrix } = question.questionAnswerPreview;

    if (Array.isArray(answerOptionList) && answerOptionList.length > 0) {
      question.questionAnswerPreview.answerOptionList = answerOptionList.map(formatAnswer);
    }

    if (Array.isArray(answerOptionMatrix) && answerOptionMatrix.length > 0) {
      question.questionAnswerPreview.answerOptionMatrix = answerOptionMatrix.map((row: any[]) =>
        row.map(formatAnswer),
      );
    }
  }

  return question;
};

export const formatQuestion = (question: API.QuestionItemType, idx: number = 0) => {
  const questionArr: API.QuestionItemType[] = [];
  const extractQuestionAnswers: API.QuestionAnswerType[] = [];

  const travel = (
    q: API.QuestionItemType,
    idx: number = 0,
    questionNoPrefix: string | null = null,
  ) => {
    const {
      questionAnswerPreview = {},
      subQuestionList,
      questionId,
      questionAnswerMode,
      questionContentPreview = {},
    } = q;
    const hasSubQuestion = Array.isArray(subQuestionList) && subQuestionList.length > 0;
    // 处理子问题列表，防止后端返回的是 null
    q.subQuestionList = hasSubQuestion ? subQuestionList : [];

    if (questionAnswerMode === 4) {
      formatTrueFalseQuestion(q);
    }

    const index = String(idx + 1);
    let questionNo: string | null = null;

    // 处理问题编号
    {
      if (questionNoPrefix === null) {
        // 主问题
        q.questionNo = index;
        questionNo = '';
        q.level = 1;
      }
      if (questionNoPrefix === '') {
        // 子问题
        questionNo = index;
        q.questionNo = index;
        q.level = 2;
      }
      if (questionNoPrefix) {
        // 孙级问题
        questionNo = index;
        q.questionNo = questionNo;
        q.level = 3;
      }
    }

    // 处理问题答案
    {
      const { answerOptionList, answerOptionMatrix } = questionAnswerPreview;
      const isChild = subQuestionList && subQuestionList.length > 0;
      questionAnswerPreview.answerOptionList = Array.isArray(answerOptionList)
        ? answerOptionList
        : [];
      if (!answerOptionMatrix && questionAnswerPreview.answerOptionList.length > 0) {
        questionAnswerPreview.answerOptionMatrix = [questionAnswerPreview.answerOptionList];
      }
      questionAnswerPreview.answerOptionMatrix = Array.isArray(
        questionAnswerPreview.answerOptionMatrix,
      )
        ? questionAnswerPreview.answerOptionMatrix
        : [];
      q.questionAnswerPreview = {
        ...questionAnswerPreview,
        questionId: questionId,
        questionNo: questionNo ?? '',
        level: q.level,
        questionAnswerMode: questionAnswerMode,
        isChild: isChild,
      };
    }

    // 提取出有效的问题答案
    {
      const { answerOptionMatrix } = q.questionAnswerPreview;
      if (answerOptionMatrix && answerOptionMatrix.length > 0) {
        extractQuestionAnswers.push(q.questionAnswerPreview);
      }
    }

    // 格式化选项
    {
      const { questionOptionMatrix, questionOptionList } = questionContentPreview;
      questionContentPreview.questionOptionList = Array.isArray(questionOptionList)
        ? questionOptionList
        : [];

      if (!questionOptionMatrix && questionContentPreview.questionOptionList.length > 0) {
        questionContentPreview.questionOptionMatrix = [questionContentPreview.questionOptionList];
      }
      questionContentPreview.questionOptionMatrix = Array.isArray(
        questionContentPreview.questionOptionMatrix,
      )
        ? questionContentPreview.questionOptionMatrix
        : [];
    }

    // 把问题添加到问题数组中
    questionArr.push(q);

    if (hasSubQuestion) {
      q.subQuestionList = subQuestionList.map((item, i) => {
        const newItem = {
          ...item,
          questionContentPreview: cloneDeep(item.questionContent),
          questionAnswerPreview: cloneDeep(item.questionAnswer),
        };
        travel(newItem, i, questionNo);
        return newItem;
      });
    }
  };

  travel(question, idx, null);

  question.extractQuestionAnswers = extractQuestionAnswers;

  return question;
};

export const formatMainQuestions = (questions: API.QuestionItemType[]) => {
  return questions.map((item, idx) => {
    // formatQuestionNo(item, idx, '');
    // const questionAnswer = formatQuestionAnswer(item);

    const q = formatQuestion(
      {
        ...item,
        questionContentPreview: cloneDeep(item.questionContent),
        questionAnswerPreview: cloneDeep(item.questionAnswer),
      },
      idx,
    );

    return {
      ...q,
      isMainQuestion: true,
    };
  });
};

// 获取题目题干前20个字符
export const getShortQuestionStem = (questionStem: string, length = 20) => {
  const div = document.createElement('div');
  div.innerHTML = questionStem;
  const text = div.textContent || div.innerText;
  return text.slice(0, length);
};

/**
 * 检查是否有必要的问题列表请求参数
 * @param requestParams 请求参数
 * @returns
 */
export const checkQuestionListRequestParams = (
  requestParams: API.QuestionListRequestParams | undefined,
) => {
  if (!requestParams) {
    return false;
  }
  const { phaseList, subjectList, baseTreeNodeIds, bizTreeNodeIds } = requestParams;
  if (!phaseList?.length && !subjectList?.length) {
    // 学段学科都没有值
    return false;
  }
  if (!baseTreeNodeIds?.length && !bizTreeNodeIds?.length) {
    // 树节点都没有值
    return false;
  }
  return true;
};

export const splitByMultiSeparators = (str: string, separators: string[]) => {
  if (!str || !separators || separators.length === 0) {
    return [];
  }
  const regex = new RegExp(
    `[${separators
      .map((sep) =>
        // 对特殊字符进行转义
        sep.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
      )
      .join('')}]`,
  );
  return str
    .split(regex)
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

/**
 * 处理多个题目id搜索的情况, 如果搜索的是多个题目id，则把题目id列表设置到请求参数questionIds中，并清空搜索关键字
 * @param requestParams 请求参数
 * @returns
 */
export const handleMultiQuestionSearch = (
  requestParams: API.QuestionListRequestParams | undefined,
) => {
  if (!requestParams || !requestParams.keyword) {
    return;
  }
  const { keyword } = requestParams;
  const items = splitByMultiSeparators(keyword, [',', '、', '；', '，', ';', ' ', '|']);
  const isAllQuestionIds = items.every((item) => item.startsWith('q'));
  if (isAllQuestionIds && items.length > 1) {
    requestParams.questionIds = items;
    requestParams.keyword = '';
    return;
  }
  requestParams.questionIds = [];
  return;
};
