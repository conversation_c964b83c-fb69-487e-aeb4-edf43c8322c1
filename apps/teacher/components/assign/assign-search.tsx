"use client";
import { cn } from "@/utils/utils";
import { Search, X } from "lucide-react";
import { useEffect, useState } from "react";

interface AssignSearchProps extends React.ComponentProps<"input"> {
  style?: React.CSSProperties;
  className?: string;
  clearable?: boolean;
  searchable?: boolean;
  classNames?: {
    input?: string;
    search?: string;
    clear?: string;
  };
  value: string;
}

export function AssignSearch({
  style = {},
  className = "",
  value = "",
  onChange,
  disabled = false,
  clearable = true,
  searchable = true,
  classNames,
  ...props
}: AssignSearchProps) {
  const isShowClear = clearable && value && !disabled;
  const [innerValue, setInnerValue] = useState<string>(value);
  const onValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInnerValue(value);
    onChange?.({ target: { value } } as React.ChangeEvent<HTMLInputElement>);
  };
  const onHandleClear = () => {
    setInnerValue("");
    onChange?.({
      target: { value: "" },
    } as React.ChangeEvent<HTMLInputElement>);
  };

  const onSearch = () => {
    onChange?.({
      target: { value: innerValue },
    } as React.ChangeEvent<HTMLInputElement>);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  useEffect(() => {
    setInnerValue(value);
  }, [value]);

  return (
    <div className={cn("relative w-full", className)} style={style}>
      <input
        disabled={disabled}
        type="text"
        value={innerValue}
        onChange={onValueChange}
        onKeyDown={handleKeyDown}
        data-slot="input"
        className={cn(
          "file:text-foreground placeholder:text-gray-4 selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input shadow-xs flex h-full w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-[0.875rem] outline-none transition-all duration-300 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
          "font-normal leading-[150%] text-[#838BAB]",
          searchable && "pr-12",
          isShowClear && "pr-17",
          "text-gray-2 border-line-3 rounded-[1.125rem] border bg-white outline-none",
          "focus:border-primary-2 focus-visible:border-primary-2 caret-primary-2 focus:shadow-[0px_4px_8px_0px_rgba(16,18,25,0.04)] focus-visible:bg-white focus-visible:shadow-[0px_4px_8px_0px_rgba(16,18,25,0.04)]",
          classNames?.input
        )}
        {...props}
      />
      {searchable && (
        <Search
          onClick={onSearch}
          className={cn(
            "absolute right-3 top-1/2 h-full -translate-y-1/2 transform py-[2.5%]",
            disabled ? "text-gray-5" : "text-gray-4",
            classNames?.search
          )}
        />
      )}

      {isShowClear && (
        <div
          onClick={onHandleClear}
          className={cn(
            "absolute right-6 top-1/2 h-full -translate-y-1/2 transform cursor-pointer py-[3%]",
            searchable && "right-10",
            classNames?.clear
          )}
        >
          <X className="bg-gray-5 h-full w-full rounded-full p-[0.125rem] text-white" />
        </div>
      )}
    </div>
  );
}
