import NumberSelector from '@/pages/source-manage/components/NumberSelector';
import cn from 'classnames';
import lodash from 'lodash';
import React, { memo, useEffect, useState } from 'react';
import AnswerFormList from './AnswerFormList';
import { AnswerFieldSyncProvider, useAnswerFieldSyncContext } from './contexts/answerContext';
import { useAnswerFieldManager } from './hooks';
import styles from './index.module.less';
import type { AnswerFieldSelectorProps } from './types';

/**
 * 答案字段选择器组件
 * 支持多种题型的答案输入和 m选n 模式的实时同步
 */
const AnswerFieldSelector: React.FC<AnswerFieldSelectorProps> = memo(
  ({
    questionAnswerMode,
    isMatchLine,
    fieldName,
    questionOptionMatrixName,
    questionStem,
    className,
    currentFillIdx = 0,
  }) => {
    const [activeFillIdx, setActiveFillIdx] = useState(0);

    // 响应外部传入的currentFillIdx
    useEffect(() => {
      if (currentFillIdx !== undefined) {
        setActiveFillIdx(currentFillIdx);
      }
    }, [currentFillIdx]);

    // 使用答案字段管理 hook
    const fieldManager = useAnswerFieldManager({
      isMatchLine,
      activeFillIdx,
      questionStem,
      fieldName,
      questionOptionMatrixName,
    });

    const { isSyncing } = useAnswerFieldSyncContext();

    // 删除插入点时默认选中第一个
    useEffect(() => {
      const expectedCount = fieldManager.uuids.length;
      if (activeFillIdx >= expectedCount) {
        setActiveFillIdx(0);
      }
    }, [activeFillIdx, fieldManager.uuids.length]);

    return (
      <div className={cn(className, styles.answerFieldSelector)}>
        {/* 插入点选择器 */}
        {fieldManager.uuids.length > 0 && (
          <div className={styles.topicSelector}>
            <NumberSelector
              options={fieldManager.uuids.map((uuid) => ({
                id: uuid,
              }))}
              disabled={isSyncing}
              value={activeFillIdx}
              onChange={({ currentIdx }) => setActiveFillIdx(currentIdx)}
            />
          </div>
        )}

        {/* m选n模式提示信息 */}
        {isMatchLine && fieldManager.uuids.length > 1 && questionAnswerMode === 1 && (
          <div className={styles.matchLineTip}>
            已开启 m 选 n 模式，修改任意作答区选项时，其他作答区选项会自动同步
          </div>
        )}

        {/* 答案表单列表 */}
        <AnswerFormList
          fieldName={fieldName}
          questionAnswerMode={questionAnswerMode}
          fieldManager={fieldManager}
          isMatchLine={isMatchLine}
          activeFillIdx={activeFillIdx}
          questionOptionMatrixName={questionOptionMatrixName}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有关键属性变化时才重新渲染
    return (
      lodash.isEqual(prevProps.questionAnswerMode, nextProps.questionAnswerMode) &&
      lodash.isEqual(prevProps.isMatchLine, nextProps.isMatchLine) &&
      lodash.isEqual(prevProps.fieldName, nextProps.fieldName) &&
      lodash.isEqual(prevProps.questionOptionMatrixName, nextProps.questionOptionMatrixName) &&
      lodash.isEqual(prevProps.questionStem, nextProps.questionStem) &&
      lodash.isEqual(prevProps.currentFillIdx, nextProps.currentFillIdx)
    );
  },
);

AnswerFieldSelector.displayName = 'AnswerFieldSelector';

const AnswerFieldSelectorContent: React.FC<AnswerFieldSelectorProps> = memo((props) => {
  return (
    <AnswerFieldSyncProvider>
      <AnswerFieldSelector {...props} />
    </AnswerFieldSyncProvider>
  );
});

AnswerFieldSelectorContent.displayName = 'AnswerFieldSelectorContent';

export default AnswerFieldSelectorContent;
