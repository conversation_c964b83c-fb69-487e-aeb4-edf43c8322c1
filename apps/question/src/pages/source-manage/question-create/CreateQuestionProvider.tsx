import { Spin } from 'antd';
import React, { useContext } from 'react';

import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useNavigate, useSearchParams } from '@umijs/max';
import QuestionFormCard from './components/QuestionFormCard';
import QuestionNavbar from './components/QuestionNavbarCard';
import { QuestionCreateProvider, useQuestionCreate } from './contexts/QuestionCreateContext';
import { useFormInitialization } from './hooks/useFormInitialization';
import { useQuestionInitActions } from './hooks/useQuestionInitActions';
import { QuestionCreateProps } from './types';

import styles from './index.module.less';

interface QuestionData {
  phase: number; // 学段
  subject: number; // 学科
}

// 内部组件，使用 Context
const QuestionCreateContent: React.FC<QuestionCreateProps> = ({
  data,
  onBack,
  onSubmit,
  way = 'create',
}) => {
  const { isLoading, loadingText } = useQuestionCreate();

  // 初始化题目列表的hook（用于组件初始化）
  useQuestionInitActions({
    way,
    data,
  });

  // 初始化表单
  useFormInitialization();

  const navigate = useNavigate();
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit?.();
      return;
    }
    navigate('/question-input');
  };

  return (
    <div className={styles.questionCreateMainContainer}>
      <Spin spinning={isLoading} tip={loadingText}>
        <div className={styles.questionCreateMainNavbar}>
          <QuestionNavbar
            onBack={onBack}
            auditTaskId={data?.auditTaskId}
            auditTaskName={data?.auditTaskName}
            way={way}
            data={data}
            onSubmit={handleSubmit}
          />
        </div>
        <QuestionFormCard way={way} data={data} onBack={onBack} onSubmit={onSubmit} />
      </Spin>
    </div>
  );
};

// 主组件，提供 Context
const QuestionCreate: React.FC<QuestionCreateProps> = (props) => {
  const [searchParams] = useSearchParams();
  const { selectedSubject } = useContext(ExerciseContext);
  const [phaseId, subjectId] = selectedSubject;

  // 从URL参数获取phaseId和subjectId
  const urlPhaseId =
    searchParams.get('phaseId') !== null ? +searchParams.get('phaseId')! : undefined;
  const urlSubjectId =
    searchParams.get('subjectId') !== null ? +searchParams.get('subjectId')! : undefined;

  const initialBaseInfo: QuestionData = {
    phase: urlPhaseId || props.data?.phase || phaseId,
    subject: urlSubjectId || props.data?.subject || subjectId,
  };

  return (
    <QuestionCreateProvider initialBaseInfo={initialBaseInfo}>
      <QuestionCreateContent {...props} />
    </QuestionCreateProvider>
  );
};

export default QuestionCreate;
