import { convertQuestion, QuestionSetGroupItem } from "@/types/assign";
import { <PERSON><PERSON> as Tch<PERSON><PERSON><PERSON> } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
import { Signal } from "@preact-signals/safe-react";
import {
  QaContentType,
  QuestionItem,
} from "@repo/core/views/tch-question-view";
import { useEffect, useMemo } from "react";
import { AssignHeading } from "../assign-heading";
import useQuestionFilterEnumMap from "./useQuestionFilterEnumMap";

interface PracticeGroupItemProps {
  style?: React.CSSProperties;
  className?: string;
  group: QuestionSetGroupItem;
  activeGroup: Signal<number>;
  onReport?: (question: QaContentType) => void;
}

export function PracticeGroupItem({
  style = {},
  className = "",
  group,
  activeGroup,
  onReport,
}: PracticeGroupItemProps) {
  const { questionFilterEnumMap } = useQuestionFilterEnumMap();
  const questionList = useMemo(() => {
    const list = group.questionGroupQuestionList || [];

    if (!questionFilterEnumMap) {
      return [];
    }

    // TODO: 第二个参数只是为了转换Tag，那可以不在这里转换
    return list.map((item) => {
      return convertQuestion(item.questionInfo, questionFilterEnumMap || {});
    });
  }, [group, questionFilterEnumMap]);

  const domId = useMemo(() => {
    return `practice-group-${group.questionGroupId}`;
  }, [group.questionGroupId]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 元素进入视口
            activeGroup.value = group.questionGroupId || 0;
          }
        });
      },
      {
        threshold: 0.1, // 当元素10%可见时触发（默认0）
        rootMargin: "0px", // 扩展或缩小视口判定范围
      }
    );
    const dom = document.getElementById(domId) as Element;
    if (dom) {
      observer.observe(dom);
    }
    return () => {
      observer.disconnect();
    };
  }, [domId, activeGroup, group.questionGroupId]);

  return (
    <div className={cn("space-y-2", className)} style={style} id={domId}>
      <AssignHeading content={group.questionGroupName || ""} className="h-6" />
      <div className="space-y-3">
        {questionList.map((item, index) => (
          <QuestionItem
            key={item.questionId}
            qaContent={item}
            index={index}
            className="border! border-solid! border-line-1"
            showViewButton={false}
            footerButton={
              onReport ? (
                <TchButton
                  type="default"
                  size="md"
                  radius="full"
                  className="h-8.5"
                  onClick={() => {
                    onReport(item);
                  }}
                >
                  问题反馈
                </TchButton>
              ) : null
            }
          />
        ))}
      </div>
    </div>
  );
}
