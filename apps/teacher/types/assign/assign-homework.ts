import { QuestionFilterSort, QuestionFilterSource } from "@/enums";
import {
  QaContentType,
  QuestionAnswer,
} from "@repo/core/views/tch-question-view";
import { CommonResponseType } from ".";

export interface QuestionFilterOption<T = number> {
  nameEn: string;
  nameZh: string;
  value: T;
}

export interface QuestionFilterEnumData {
  provinceList: QuestionFilterOption[];
  questionDifficultList: QuestionFilterOption[];
  questionTypeList: QuestionFilterOption[];
  yearList: QuestionFilterOption[];
}

export interface QuestionFilterEnumDatas extends QuestionFilterEnumData {
  sortList: QuestionFilterOption<QuestionFilterSort>[];
  sourceList: QuestionFilterOption<QuestionFilterSource>[];
}

export type QuestionFilterEnumValueType =
  | number
  | QuestionFilterSort
  | QuestionFilterSource;
export type QuestionFilterEnumMapKey = keyof QuestionFilterEnumData;

export type QuestionFilterEnumDatasMap = {
  [key in QuestionFilterEnumMapKey]: Map<
    QuestionFilterEnumValueType,
    QuestionFilterOption<QuestionFilterEnumValueType>
  >;
};

export type QuestionFilterEnumResponse =
  CommonResponseType<QuestionFilterEnumData>;

export interface QuestionListParams {
  baseTreeNodeIds?: number[];
  bizTreeNodeIds?: number[];
  keyword?: string;
  page?: number;
  pageSize?: number;
  questionDifficult?: number[];
  questionType?: number[];
  questionYears?: number[];
  sort?: string;
  subject: number;
  questionProvince?: number[];
}

export interface QuestionListResponseData {
  list: QuestionType[];
  page: number;
  pageSize: number;
  total: number;
}

export interface QuestionType {
  aiScene: number;
  areaCode: number;
  baseTreeId: number;
  baseTreeNodeIds: number[];
  cityCode: number;
  consolidateScene: number;
  phase: number;
  provinceCode: number;
  questionAnswer: QuestionAnswer;
  questionContent: QuestionContentType;
  questionDifficult: number;
  questionExplanation: string;
  questionExtra: string;
  questionId: string;
  questionSource: number;
  questionTopic: string;
  questionType: number;
  questionVersionId: number;
  questionYear: number;
  subject: number;
  questionTags: string[];
  subQuestionList?: QuestionType[];
  questionAnswerMode: number;
}

export interface QuestionContentType {
  questionOptionList: QuestionOptionItem[];
  questionOrder: number;
  questionOriginName: string;
  questionScore: number;
  questionStem: string;
}

export interface QuestionOptionItem {
  optionKey: string;
  optionVal: string;
}

export type QuestionListResponse = CommonResponseType<QuestionListResponseData>;

export interface QuestionFiltersType {
  provinceList: number[];
  questionDifficultList: number[];
  questionTypeList: number[];
  yearList: number[];
  sort: QuestionFilterSort;
  sourceList: QuestionFilterSource[];
}

export interface QuestionTypeGroup {
  questionType: QuestionFilterEnumValueType;
  typeName: string;
  questions: QaContentType[];
}

export interface QuestionDetailListParams {
  questionIds: string[];
}
