import { Button } from "@/ui/tch-button";
import { TchDrawer } from "@/ui/tch-drawer";
import { cn } from "@/utils/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";

import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { CoursePracticeItem } from "@/types/assign/course";
import { AssignCourseItem } from "./assign-course-item";

interface AssignJoinedResourceProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignJoinedResource({
  style = {},
  className = "",
}: AssignJoinedResourceProps) {
  const open = useSignal(false);
  const { selectedAiCourses, selectedPractices } = useAssignCourseContext();

  const totalCounter = useComputed(() => {
    return selectedAiCourses.value.length + selectedPractices.value.length;
  });

  // 这里需要computed，应为也可能通过已加入列表变更
  const courseResourceList = useComputed(() => {
    const combinedNodes: CoursePracticeItem[] = [];

    for (const e of selectedAiCourses.value) {
      combinedNodes.push({
        ...e,
        practice: { id: 0 },
      });
    }

    for (const e of selectedPractices.value) {
      const exist = combinedNodes.find(
        (item) => item.bizTreeNodeId === e.bizTreeNodeId
      );
      if (exist) {
        exist.practice = e.practice;
        continue;
      }
      combinedNodes.push({
        ...e,
        aiCourse: { id: 0 },
      });
    }

    return combinedNodes;
  });

  const Title = () => {
    const courseLen = selectedAiCourses.value.length;
    const practiceLen = selectedPractices.value.length;

    return (
      <div className="text-gray-2 text-base">
        {courseLen > 0 && (
          <>
            <span className="font-extrabold">{courseLen}</span>
            <span>个课程学习</span>
          </>
        )}
        {courseLen > 0 && practiceLen > 0 && <span>, </span>}
        {practiceLen > 0 && (
          <>
            <span className="font-extrabold">{practiceLen}</span>
            <span>个巩固练习</span>
          </>
        )}
      </div>
    );
  };

  return (
    <>
      <Button
        type={totalCounter.value > 0 ? "outline" : "default"}
        size="lg"
        radius="full"
        disabled={totalCounter.value === 0}
        onClick={() => {
          open.value = true;
        }}
        className={cn("", className)}
        style={style}
      >
        已加入 ({totalCounter.value})
      </Button>
      <TchDrawer
        open={open.value}
        onOpenChange={(v) => (open.value = v)}
        title={`已加入 (${totalCounter.value})`}
      >
        {totalCounter.value > 0 ? (
          <div className="flex flex-col space-y-4">
            <Title />
            {courseResourceList.value.map((item) => (
              <AssignCourseItem
                key={item.bizTreeNodeId}
                data={item}
                showPreview={false}
              />
            ))}
          </div>
        ) : (
          <AssignEmpty
            type="course"
            className="flex-1 rounded-2xl border-none"
            content="还没有加入任何资源哦~"
          />
        )}
      </TchDrawer>
    </>
  );
}
