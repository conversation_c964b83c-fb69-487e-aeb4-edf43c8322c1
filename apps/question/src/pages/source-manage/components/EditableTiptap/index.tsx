import TiptapEditor from '@/components/TiptapEditor';
import { Feature } from '@/components/TiptapEditor/src/utils/enum';
import cs from 'classnames';
import React, { memo, useEffect, useState } from 'react';
import { useQuestionCreate } from '../../question-create/contexts/QuestionCreateContext';
import styles from './style.module.less';

export interface FileInfo {
  base64: string;
  md5FileName: string;
  url: string;
}

interface EditableTiptapProps {
  value?: string;
  onChange?: (val: string) => void;
  textarea?: boolean;
  placeholder?: string;
  onBlur?: () => void;
  includeFeatures?: Feature[];
  excludeFeatures?: Feature[];
  stripOuterNode?: boolean;
  onQuestionFillAreaClick?: (e: { index: number }) => void;
  style?: React.CSSProperties;
}

const EditableTiptap: React.FC<EditableTiptapProps> = ({
  value = '',
  onChange,
  textarea = false,
  placeholder = '请输入',
  onBlur,
  includeFeatures,
  excludeFeatures,
  stripOuterNode,
  onQuestionFillAreaClick,
  style,
}) => {
  const { setAttachFileList } = useQuestionCreate();
  const [innerValue, setInnerValue] = useState(value);

  const handleFileUpload = (file: FileInfo) => {
    const fileInfo: API.Review.AttachFileList = {
      fileName: file.md5FileName,
      fileType: 1,
      fileData: file.base64,
      ossPath: file.url,
    };
    setAttachFileList((prev) => [...prev, fileInfo]);
  };

  useEffect(() => {
    setInnerValue(value);
  }, [value]);

  const handleChange = (val: string) => {
    setInnerValue(val);
    onChange?.(val);
  };

  const [isFocus, setIsFocus] = useState(false);
  return (
    <div
      className={cs(styles.editableTiptap, textarea ? styles.isTextarea : styles.isInput, {
        [styles.isFocus]: isFocus,
      })}
      style={style}
    >
      <TiptapEditor
        isTextarea={textarea}
        editable={false}
        content={innerValue}
        onChange={handleChange}
        placeholder={placeholder}
        onImageUpload={handleFileUpload}
        onFocus={() => setIsFocus(true)}
        onBlur={() => {
          setIsFocus(false);
          onBlur?.();
        }}
        includeFeatures={includeFeatures}
        excludeFeatures={[Feature.save, ...(excludeFeatures || [])]}
        stripOuterNode={stripOuterNode}
        onQuestionFillAreaClick={onQuestionFillAreaClick}
      />
    </div>
  );
};

export default memo(EditableTiptap);
