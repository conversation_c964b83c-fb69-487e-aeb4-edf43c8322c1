export default class EnumManager<T extends readonly Enum[]> {
  constructor(public enums: T) {}

  getEnumByValue(value: EnumValue<this>) {
    return this.enums.find((item) => item.value === value);
  }

  getLabelByValue(value: EnumValue<this>) {
    return this.enums.find((item) => item.value === value)?.label as
      | EnumLabel<this>
      | undefined;
  }

  getValueByLabel(label: EnumLabel<this>) {
    return this.enums.find((item) => item.label === label)?.value as
      | EnumValue<this>
      | undefined;
  }
}

export interface Enum {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  label: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [p: string]: any;
}

/**
 * Enum中value的类型
 */
export type EnumValue<T extends EnumManager<readonly Enum[]>> =
  T["enums"][number]["value"];
export type EnumLabel<T extends EnumManager<readonly Enum[]>> =
  T["enums"][number]["label"];
