"use client";

import Interactive from "@repo/core/components/interactive-component";
import { StudyType } from "@repo/core/enums";
import { CourseWidgetDetail } from "../type";

import { ExerciseView } from "@repo/core/exercise/preview/view/exercise-view";
import { COMPLETE_EXERCISE_VARIABLES } from "@repo/core/exercise/theme/exercise-theme-variables";
import { cn } from "@repo/ui/lib/utils";
import { useEffect, useState } from "react";
import { GuidePlayer } from "../GuidePlayer";
import "./index.css";

export const WidgetLoader = ({
  data,
  width,
  height,
  guideStyle,
  guideInputProps,
  onQuestionIndexChange,
  guideWidgetProps,
  handlerRef,
  className,
}: {
  data?: CourseWidgetDetail;
  width: number;
  height: number;
  guideStyle?: React.CSSProperties;
  guideInputProps?: React.ComponentProps<typeof GuidePlayer>["inputProps"];
  onQuestionIndexChange?: React.ComponentProps<
    typeof ExerciseView
  >["onQuestionIndexChange"];
  guideWidgetProps?: Partial<
    Omit<
      React.ComponentProps<typeof GuidePlayer>,
      "data" | "width" | "height" | "className"
    >
  >;
  handlerRef?: React.RefObject<{
    refresh: () => void;
  } | null>;
  className?: string;
}) => {
  const [mainKey, setMainKey] = useState(0);

  useEffect(() => {
    if (handlerRef) {
      handlerRef.current = {
        refresh: () => {
          setMainKey((prev) => prev + 1);
        },
      };
    }
  }, [handlerRef, setMainKey]);

  if (!data) return <div className="h-full w-full bg-red-300">NO DATA</div>;

  return (
    <>
      {/* 文稿组件 */}
      {data.type === "guide" && (
        <div className="ai-course-preview-guide-view h-full w-full flex-1">
          <GuidePlayer
            key={`${data.type}-${data.name}-${mainKey}`}
            data={data.data}
            className={cn("h-full w-full", className)}
            width={width}
            height={height}
            {...guideWidgetProps}
            inputProps={guideInputProps}
            style={guideStyle}
          />
        </div>
      )}

      {/* 练习组件 */}
      {data.type === "exercise" && (
        <div
          className="ai-course-preview-exercise-view h-full w-full"
          style={{ zoom: 0.73 }}
        >
          {(data.data.length || 0) > 0 ? (
            <ExerciseView
              key={`${data.type}-${data.name}-${mainKey}`}
              className="h-full w-full"
              questionList={data.data}
              showExplanations={true}
              studyType={StudyType.REINFORCEMENT_EXERCISE}
              showContinueButton={() => false}
              onQuestionIndexChange={onQuestionIndexChange}
              customVariables={COMPLETE_EXERCISE_VARIABLES} // 🔥 样式隔离
              onBack={() => false}
            />
          ) : (
            <div className="mt-6 flex h-full w-full items-center justify-center">
              暂无数据
            </div>
          )}
        </div>
      )}

      {/* 视频组件 */}
      {data.type === "video" && (
        <div className="h-full">
          <video src={data.data.url} controls className="h-full w-full"></video>
        </div>
      )}

      {data.type === "interactive" && (
        <div className="h-full w-full overflow-y-auto">
          <Interactive
            key={`${data.type}-${data.data.typeName}-${mainKey}`}
            url={data.data.url}
            type={data.data.typeName}
            onReport={(e) => {
              console.log(e);
            }}
          >
            <div>loading...</div>
          </Interactive>
        </div>
      )}
    </>
  );
};
