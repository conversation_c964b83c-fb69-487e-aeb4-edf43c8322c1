import background from "@/public/images/interactive-bg.jpg";
import { FC } from "react";
import { InteractiveContentView } from "./interactive-content-view";
import { InteractiveMenuView } from "./interactive-menu-view";
import { InteractiveViewProvider } from "./interactive-view-context";

export const InteractiveView: FC<{
  active: boolean;
  index: number;
  url: string;
  type: string;
  onReport: (e: CustomEvent<unknown>) => void;
}> = ({ active, index, url, type, onReport }) => {
  return (
    <InteractiveViewProvider active={active} index={index}>
      <div
        className="h-full w-full bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${background.src})` }}
      >
        {active && <InteractiveMenuView />}
        {active && (
          <InteractiveContentView url={url} type={type} onReport={onReport} />
        )}
      </div>
    </InteractiveViewProvider>
  );
};
