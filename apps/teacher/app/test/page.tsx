"use client";

import { r } from "@/libs/axios";
import { But<PERSON> } from "@/ui/button";
import { batch, useSignal } from "@preact-signals/safe-react";
import { useEffect } from "@preact-signals/safe-react/react";
import { VolcenginePlayer } from "@repo/core/components/volcengine-video/volcengine-video";

import ResourcePreviewer from "@/components/resource/ResourcePreviewer";
import { useRequest } from "ahooks";

type TestResource = {
  resourceId: string;
  userFilename: string;
  originalFileName: string;
  fileType: string;
  resourceName: string;
};

// TODO: remove this
export default function Page() {
  // TEST
  const { data: list } = useRequest(async () => {
    const res = await r.post<{
      total: number;
      resources: Array<TestResource>;
    }>("/resource/teacher/resource/list");
    console.log("res", res);
    return res.resources || [];
  });

  const resourceId = useSignal("");
  const currentResource = useSignal<TestResource | null>(null);

  useEffect(() => {
    const int = setInterval(() => {
      refSignal.value?.play();
    }, 1000);
    return () => clearInterval(int);
  });

  const refSignal = useSignal<VolcenginePlayer | null>(null);

  const handleGalleryClose = function () {
    currentResource.value = null;
    resourceId.value = "";
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div>
        {list?.map((e, i) => {
          return (
            <Button
              key={i}
              onClick={() => {
                batch(() => {
                  resourceId.value = e.resourceId;
                  currentResource.value = e;
                });
              }}
            >
              {e.userFilename || e.originalFileName}
              {" | " + e.fileType}
            </Button>
          );
        })}
      </div>
      <div className="flex-1">
        <ResourcePreviewer
          resourceIds={resourceId.value ? [resourceId.value] : []}
          visible
          onClose={handleGalleryClose}
        />
      </div>
    </div>
  );
}
