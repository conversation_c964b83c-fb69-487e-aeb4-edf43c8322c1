import { useRef, useState } from 'react';

/**
 * 题目表单操作相关的hooks
 * 管理表单的增删操作、删除动画、提交等功能
 */
export const useQuestionFormActions = () => {
  // 删除状态管理 - 用于控制删除动画
  const [deletingItems, setDeletingItems] = useState<Set<string>>(new Set());

  // 使用 useRef 保存 Form.List 的 add 方法
  const addFieldRef = useRef<((defaultValue?: any, insertIndex?: number) => void) | null>(null);
  // 使用 useRef 保存 Form.List 的 remove 方法
  const removeFieldRef = useRef<((index: number | number[]) => void) | null>(null);
  // 保存嵌套Form.List的add/remove方法，key为外层的name
  const nestedAddRefs = useRef<Map<number, (defaultValue?: any, insertIndex?: number) => void>>(
    new Map(),
  );
  const nestedRemoveRefs = useRef<Map<number, (index: number | number[]) => void>>(new Map());

  /**
   * 添加子题目
   */
  const handleAddSubQuestion = () => {
    // 调用 Form.List 的 add 方法
    if (addFieldRef.current) {
      addFieldRef.current();
    }
  };

  /**
   * 带动画的删除函数
   * @param key 删除项的唯一标识
   * @param deleteCallback 实际删除操作的回调函数
   */
  const handleDeleteWithAnimation = (key: string, deleteCallback: () => void) => {
    // 添加到删除状态
    setDeletingItems((prev) => new Set([...prev, key]));

    // 延迟执行实际删除操作
    setTimeout(() => {
      deleteCallback();
      // 删除完成后从删除状态中移除
      setDeletingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(key);
        return newSet;
      });
    }, 300); // 与CSS动画时间保持一致（0.3s）
  };

  return {
    deletingItems,
    addFieldRef,
    removeFieldRef,
    nestedAddRefs,
    nestedRemoveRefs,
    handleAddSubQuestion,
    handleDeleteWithAnimation,
  };
};
