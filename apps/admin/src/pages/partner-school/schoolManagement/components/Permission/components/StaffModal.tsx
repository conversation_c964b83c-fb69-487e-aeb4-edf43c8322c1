import fetcher from '@/services/fetcher';
import { Menu } from '@/types/menu';
import { UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { AutoComplete, Form, Input, message, Modal, Spin, Tree, TreeProps } from 'antd';
import React, { Key, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import type { StaffInfo } from '../../../types';
import usePermissionData, { separateMenuNodes } from '../hooks/usePermissionData';

interface StaffModalProps {
  open: boolean;
  schoolId?: string | number;
  editingStaff?: StaffInfo;
  onCancel: () => void;
  onSuccess: () => void;
  existUserIds?: number[];
}

interface CheckedMenuIds {
  checked: Key[];
  halfChecked: Key[];
}

interface SchoolMenusData {
  list: Menu[];
}

const StaffModal: React.FC<StaffModalProps> = ({
  open,
  schoolId,
  editingStaff,
  onCancel,
  onSuccess,
  existUserIds = [],
}) => {
  const [form] = Form.useForm();

  // 使用自定义 hook 获取权限数据
  const {
    users,
    availableRoles,
    platformMenus,
    userMenus,
    isLoading,
    isMutating,
    loadBaseData,
    loadUserMenus,
    saveStaffPermission,
    getMenuTree,
  } = usePermissionData();
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const { data: schoolMenusData } = useSWR(
    `/api/v1/user_school_menu/allMenus?userId=${Number(currentUser?.userId || 0)}&schoolId=${Number(schoolId)}`,
    fetcher<SchoolMenusData>,
    {
      revalidateOnFocus: false,
    },
  );

  // console.log('platformMenus: ', platformMenus, editingStaff, availableRoles);

  const [checkedMenuIds, setCheckedMenuIds] = useState<CheckedMenuIds>({
    checked: [],
    halfChecked: [],
  });

  // 生成菜单树
  const menuTree = useMemo(() => {
    if (!schoolMenusData || !Array.isArray(schoolMenusData.list)) return [];
    console.log('xxx: ', schoolMenusData);
    const treeIds = schoolMenusData.list.map((menu) => menu.menuId);
    return getMenuTree(treeIds);
  }, [getMenuTree, schoolMenusData]);

  // 初始数据加载
  useEffect(() => {
    if (open) {
      // 加载基础数据
      loadBaseData();
      console.log('编辑1：', editingStaff);

      // 编辑模式下加载用户菜单权限
      if (editingStaff && schoolId) {
        console.log('编辑：', editingStaff);
        loadUserMenus(editingStaff.userId, schoolId);
      } else {
        // 新建模式下清空数据
        // setCheckedRoleIds([]);
        setCheckedMenuIds({
          checked: [],
          halfChecked: [],
        });
        form.resetFields();
      }
    }
    // 仅在弹窗状态改变、编辑对象改变或学校ID改变时执行
  }, [open, editingStaff, schoolId]);

  // 编辑模式数据加载
  useEffect(() => {
    if (!open || !editingStaff) return;
    console.log('编辑2：', schoolMenusData, users, userMenus);
    if (!schoolMenusData || !Array.isArray(schoolMenusData.list)) return;

    // 只有当数据都加载完成后才设置表单值
    if (userMenus.length > 0 && schoolMenusData.list.length > 0) {
      const menuIds = userMenus.map((menu) => menu.menuId);

      form.setFieldsValue({
        uName: editingStaff.userName,
        phoneNumber: editingStaff.userPhone,
        menuIds,
      });

      // setCheckedRoleIds(editingStaff.roleIds);

      // 获取叶子节点和父节点
      const { leafNodes, parentNodes } = separateMenuNodes(schoolMenusData.list, menuIds);

      setCheckedMenuIds({
        checked: leafNodes,
        halfChecked: parentNodes,
      });
    }
  }, [open, editingStaff, userMenus, schoolMenusData, form]);

  // 表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单值: ', values);

      if (!schoolId) {
        message.error('学校ID不能为空');
        return;
      }

      // 合并 checked 和 halfChecked 的菜单ID
      const allMenuIds = [...new Set([...checkedMenuIds.checked, ...checkedMenuIds.halfChecked])];

      await saveStaffPermission({
        userName: values.uName,
        userPhone: values.phoneNumber,
        schoolId: Number(schoolId),
        menuIds: allMenuIds,
        userId: editingStaff?.userId,
      });

      message.success(editingStaff ? '编辑成功' : '添加成功');
      onSuccess();
    } catch (error: any) {
      if (error.errorFields) {
        return;
      }
      if (error.message) {
        return;
      }
      message.error('操作失败');
    }
  };

  // 树形控件勾选处理
  const onCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
    const checkedKeysArray = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    const { halfCheckedKeys } = info;

    setCheckedMenuIds({
      checked: checkedKeysArray,
      halfChecked: halfCheckedKeys || [],
    });
    form.setFieldValue('menuIds', checkedKeysArray);
  };

  const handleSelect = (value: string, option) => {
    const { key } = option;
    console.log('users ===> ', users);
    const user = users.find((user) => String(user.userId) === key);
    console.log('handleSelect: ', value, option, user);
    if (!user) return;

    form.setFieldValue('phoneNumber', user.userPhoneNumber ?? '');
  };

  return (
    <Modal
      title={editingStaff ? '编辑学校管理员' : '添加学校管理员'}
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={isMutating}
      okButtonProps={{
        disabled: isMutating || isLoading || menuTree.length === 0,
      }}
      width={600}
    >
      <Spin spinning={isLoading} tip="数据加载中...">
        <Form
          form={form}
          layout="vertical"
          initialValues={{ roleIds: [], menuIds: [] }}
          autoComplete="off"
          validateTrigger={['onSubmit']}
        >
          <Form.Item
            label="姓名"
            name="uName"
            rules={[{ required: true, message: '请选择或输入姓名' }]}
          >
            <AutoComplete
              onSelect={handleSelect}
              placeholder="输入姓名"
              defaultActiveFirstOption={false}
              filterOption
            >
              {users.map((user) => {
                const isExist = existUserIds?.includes(user.userId);
                return (
                  <AutoComplete.Option key={user.userId} value={user.userName} disabled={isExist}>
                    {isExist ? <UserOutlined /> : ''}{' '}
                    {`${user.userName} (${user.userPhoneNumber || '无手机号'}) `}
                    <span className="text-xs text-gray-500">
                      {user.roles?.map((role) => role.roleName).join(',') || '无角色'}
                    </span>
                  </AutoComplete.Option>
                );
              })}
            </AutoComplete>
          </Form.Item>
          <Form.Item
            name="phoneNumber"
            label="手机号"
            rules={[
              {
                required: true,
                message: '请输入手机号！',
              },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: '手机号格式错误！',
              },
            ]}
          >
            <Input size="large" placeholder="手机号" />
          </Form.Item>

          <Form.Item
            name="menuIds"
            label="权限范围"
            rules={[
              {
                required: true,
                message: '请选择权限范围',
                validator: (rule, value) => {
                  console.log('value: ', value);
                  if (value?.length === 0) {
                    // callback('请先选择角色');
                    return Promise.reject('请选择权限范围');
                  } else {
                    return Promise.resolve();
                    // callback();
                  }
                  // callback();
                },
              },
            ]}
          >
            {menuTree.length > 0 ? (
              <Tree
                defaultExpandAll
                showLine
                checkable
                selectable={false}
                checkedKeys={{
                  checked: checkedMenuIds.checked,
                  halfChecked: checkedMenuIds.halfChecked,
                }}
                treeData={menuTree}
                onCheck={onCheck}
              />
            ) : (
              <div className="text-gray-400 p-4 text-center border border-dashed rounded-md">
                没有可分配的权限
              </div>
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default StaffModal;
