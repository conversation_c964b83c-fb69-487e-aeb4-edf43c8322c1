import { cn } from "@repo/ui/lib/utils";
import { FC } from "react";

interface LayerProps {
  className?: string;
  children?: React.ReactNode;
}

export const Layer: FC<LayerProps> = ({ className, children }) => {
  return (
    <div className={cn("relative h-full w-full", className)}>{children}</div>
  );
};

interface LayerItemProps extends LayerProps {
  index: number;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export const LayerItem: FC<LayerItemProps> = ({
  index,
  children,
  className,
  onClick,
  style,
}) => {
  return (
    <div
      data-name={`layer-item-${index}`}
      className={cn("absolute h-full w-full", className)}
      style={{ zIndex: index, ...style }}
      onClick={onClick}
    >
      {children}
    </div>
  );
};
