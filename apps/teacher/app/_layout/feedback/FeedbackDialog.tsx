"use client";
import { FEEDBACK_TYPE } from "@/enums";
import { useFeedback } from "@/hooks";
import CloseIcon from "@/public/icons/ic_close.svg";
import { ScrollArea } from "@/ui/scroll-area";
import { Button } from "@/ui/tch-button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/ui/tch-dialog";
import { Textarea } from "@/ui/textarea";
import { toast } from "@/ui/toast";
import { useState } from "react";
import FeedbackImageUpload from "./_components/FeedbackImageUpload";

export default function FeedbackDialog({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const { submitRequest, closeFeedback } = useFeedback();
  const [description, setDescription] = useState("");
  const [images, setImages] = useState<File[]>([]);

  return (
    <Dialog open={open}>
      <DialogContent
        className="w-130 max-h-full !max-w-[unset] grid-rows-[auto_1fr] gap-0 rounded-2xl p-0 outline-0"
        close={false}
        onAnimationEnd={() => {
          if (!open) {
            closeFeedback();
          }
        }}
      >
        <DialogHeader className="p-6">
          <DialogTitle className="text-gray-1 flex items-start justify-between text-sm font-normal not-italic">
            <div className="flex-1 text-center">用户反馈</div>
            <CloseIcon
              className="size-5 cursor-pointer active:opacity-40"
              onClick={() => {
                setOpen(false);
              }}
            />
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="overflow-hidden">
          {/* 反馈内容 */}
          <div className="feedback_content flex flex-col px-6">
            <div className="flex flex-col gap-2">
              <label className="feedback_label text-gray-2 text-sm font-normal">
                <span className="text-danger-2">*</span> 描述反馈问题
              </label>
              <Textarea
                value={description}
                onChange={(e) => {
                  setDescription(e.target.value);
                }}
                placeholder="请输入10个字以上的问题描述，以便我们为您提供更好的帮助"
                maxLength={300}
              />
            </div>
            <div className="mb-4.5 mt-7 flex items-center">
              <span className="feedback_label text-gray-2 text-sm/normal font-normal">
                上传截图（{images.length}/4）
              </span>
            </div>

            <FeedbackImageUpload images={images} onChange={setImages} />
          </div>

          {/* 底部按钮 */}
          <div className="feedback_footer pt-13.5 flex justify-end gap-3 px-6 pb-5">
            <Button
              type="default"
              size="lg"
              radius="full"
              className="feedback_btn_cancel text-gray-2 min-w-24"
              onClick={() => setOpen(false)}
            >
              取 消
            </Button>
            <Button
              type="primary"
              size="lg"
              radius="full"
              className="feedback_btn_submit min-w-24"
              onClick={async () => {
                if (description.trim().length < 10) {
                  toast.error("请输入10个字以上的反馈问题");
                  return;
                }

                if (submitRequest.loading) return;

                await submitRequest.runAsync({
                  description,
                  images,
                  feedbackType: FEEDBACK_TYPE.OTHER,
                });

                toast.success("反馈提交成功");
                setOpen(false);
              }}
            >
              提 交
            </Button>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
