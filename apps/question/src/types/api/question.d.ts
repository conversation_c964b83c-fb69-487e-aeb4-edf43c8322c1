declare namespace API {
  type QuestionListSubjectPhase = {
    phaseEnum: number;
    subjectEnum: number;
    [property: string]: any;
  };
  type QuestionListSearch = {
    /**
     * 题目ID：questionId, 题干内容：questionStem
     */
    field: string;
    /**
     * 搜索内容
     */
    keyword: string;
    [property: string]: any;
  };
  type QuestionListSort = {
    /**
     * 创建时间
     */
    field: string;
    /**
     * asc, desc
     */
    order: string;
    [property: string]: any;
  };

  type QuestionListFilters = Record<string, number[]>;
  type QuestionListRequestParams = {
    /**
     * 学科学段
     */
    phaseList?: number[];
    /**
     * 学科学段
     */
    subjectList?: number[];
    /**
     * 基础树
     */
    baseTreeNodeIds?: number[];
    /**
     * 业务树
     */
    bizTreeNodeIds?: number[];
    /**
     * 搜索框
     */
    keyword?: string;
    questionType?: number[];
    questionDifficult?: number[];
    questionYears?: number[];
    questionSource?: number[];
    questionCategory?: number[];
    questionProvince?: number[];

    /**
     * 题目id列表
     */
    questionIds?: string[];

    /**
     * 排序
     */
    sort?: string;

    /**
     * 页码
     */
    page?: number;
    /**
     * 每页条数
     */
    pageSize?: number;
    /**
     * 前端区分是否立即发送请求 默认发送
     */
    [property: string]: any;
  };

  interface QuestionListResponse {
    code: number;
    message: string;
    data: {
      total: number;
      page: number;
      pageSize: number;
      list: QuestionItemType[];
    };
  }

  interface QuestionItemType {
    /**
     * 问题id
     */
    questionId: string;
    /**
     * 问题年份
     */
    questionYear: number;
    /**
     * 题干
     */
    questionContent: QuestionContentInfo;
    /**
     * 问题类型
     */
    questionType: number;
    /**
     * 问题类型标签名称
     */
    labelQuestionTypeNameZh: string;
    /**
     * 问题类型标签
     */
    labelQuestionType: number;
    /**
     * 问题答案
     */
    questionAnswer: QuestionAnswerType;

    /**
     * 提取子问题答案汇聚到主问题
     */
    extractQuestionAnswers?: QuestionAnswerType[];
    /**
     * 问题难度
     */
    questionDifficult: number;
    /**
     * 问题解析
     */
    questionExplanation: string;
    /**
     * 问题选项？
     */
    /**
     * 问题排列？
     */
    /**
     * 1 表示ai课场景 0表示没有ai课场景
     */
    aiScene: number;
    /**
     * 地区编码
     */
    areaCode: number;
    /**
     * 基础树id
     */
    baseTreeId: number;
    /**
     * 知识点id列表
     */
    baseTreeNodeIds: number[];
    /**
     * 城市
     */
    cityCode: number;
    /**
     * 1 表示巩固练习场景 0 表示没有巩固练习场景
     */
    consolidateScene: number;
    /**
     * 学段
     */
    phase: number;
    /**
     * 省份
     */
    provinceCode: number;

    /**
     * 问题额外信息
     */
    questionExtra: string;

    /**
     * 问题来源
     */
    questionSource: number;
    /**
     * 题目专题
     */
    questionTopic: string;

    /**
     * 学科
     */
    subject: number;

    optionsLayout: number;
    options: QuestionOption[];
    subQuestionList: QuestionItemType[];
    [property: string]: any;
  }

  interface QuestionAnswerType {
    answerOptionList: QuestionContentOptionItem[];
    questionId?: string;
    subQuestionAnswerList?: QuestionAnswerType[];
    questionNo?: string;
    questionAnswerMode?: number;
    answerOptionMatrix?: QuestionContentOptionItem[][];
    level?: number;
    isChild?: boolean;
  }

  interface QuestionOption {
    key: string;
    content: string;
  }

  // interface QuestionContentInfo {
  //     question_order: number;
  //     question_score: number;
  //     question_origin_name: string;
  //     question_stem: string;
  //     question_option_list: QuestionContentOptionItem[];
  // }
  interface QuestionContentInfo {
    questionOrder: number;
    questionScore: number;
    questionOriginName: string;
    questionStem: string;
    questionOptionList: QuestionContentOptionItem[];
    questionOptionMatrix?: QuestionContentOptionItem[][];
  }

  interface QuestionContentOptionItem {
    optionKey: string;
    optionVal: string;
  }

  interface QuestionResourceRequestParams {
    questionId: string;
    resourceFileName: string;
    [property: string]: any;
  }

  interface QuestionResourceResponse {
    code: number;
    message: string;
    data: {
      resourceData: string;
      resourceType: number;
      [property: string]: any;
    };
  }
}
