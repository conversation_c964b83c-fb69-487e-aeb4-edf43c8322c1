import { history } from '@umijs/max';
import routes from '../../config/routes';

const getFirstPermissionRoute = (menus: API.UserMenu[], routesConfig: any[]) => {
  const routesArr: string[] = [];
  routesConfig.forEach((route) => {
    if (route.component) {
      routesArr.push(route.path);
    }
    if (route.routes) {
      route.routes.forEach((item: any) => {
        if (item.component) {
          routesArr.push(item.path);
        }
      });
    }
  });
  const first = menus.find((item) => routesArr.includes(item.menuPath as string));
  return first?.menuPath || '/';
};

// 检查当前页面是否有权限访问
export const checkCurrentLocationPermission = (menus: API.UserMenu[]) => {
  const pathname = location.pathname;
  // 如果菜单为空，则跳转到首页
  if (menus.length === 0) {
    history.push('/');
    return false;
  }
  // 如果当前页面有权限访问，则返回true
  if (menus.some((menu) => menu.menuPath === pathname)) {
    return true;
  }
  // 如果当前页面没有权限访问，则跳转到第一个菜单
  history.push(getFirstPermissionRoute(menus, routes));
  return false;
};

export const userInfo = {
  code: 0,
  message: 'success',
  response_time: new Date().getTime(),
  status: 200,
  data: {
    createTime: '2024-01-01 00:00:00',
    menus: [
      {
        menuId: 1,
        menuName: '题目管理',
        menuUrl: '/question-manage',
        parentId: 0,
        menuType: 1,
        menuIcon: 'SettingOutlined',
        menuSort: 1,
        menuStatus: 1,
      },
      {
        menuId: 2,
        menuName: '题目录入',
        menuUrl: '/question-input',
        parentId: 1,
        menuType: 2,
        menuIcon: 'UserOutlined',
        menuSort: 2,
        menuStatus: 1,
      },
      {
        menuId: 3,
        menuName: '场景题库',
        menuUrl: '/scene-question-lib',
        parentId: 0,
        menuType: 1,
        menuIcon: 'UserOutlined',
        menuSort: 3,
        menuStatus: 1,
      },
      {
        menuId: 4,
        menuName: '巩固练习',
        menuUrl: '/scene-question-lib/consolidation-exercise',
        parentId: 0,
        menuType: 1,
      },
      {
        menuId: 5,
        menuName: '拓展练习',
        menuUrl: '/scene-question-lib/extended-exercise',
        parentId: 0,
      },
      {
        menuId: 6,
        menuName: '审核',
        menuUrl: '/review',
        parentId: 0,
      },
      {
        menuId: 7,
        menuName: '基础数据',
        menuUrl: '/base-data',
        parentId: 0,
      },
      {
        menuId: 8,
        menuName: '基础树管理',
        menuUrl: '/base-data/base-tree',
        parentId: 0,
      },
      {
        menuId: 9,
        menuName: '业务树管理',
        menuUrl: '/base-data/business-tree',
        parentId: 0,
      },
    ],
    remark: '系统管理员',
    roles: [
      {
        roleId: 1,
        roleName: '系统管理员',
        roleKey: 'admin',
        roleSort: 1,
        roleStatus: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
        remark: '系统管理员',
      },
    ],
    schools: null,
    updateTime: '2024-01-01 00:00:00',
    userEmploymentStatus: 1,
    userId: 10001,
    userName: 'admin',
    userPhoneNumber: '***********',
    userStatus: 1,
  },
};
