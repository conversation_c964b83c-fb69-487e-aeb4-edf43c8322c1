.collapsibleContainer {
  // 头部容器样式
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
  }

  // 头部内容容器
  .headerContent {
    flex: 1;
  }

  // 折叠按钮样式
  .collapseButton {
    flex-shrink: 0;
    min-width: 20px;
    height: 20px;
    padding: 0 4px;
    color: #666;

    &:hover {
      color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  // 内容容器
  .content {
    width: 100%;
  }

  // 响应式调整
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }

    .collapseButton {
      align-self: flex-end;
    }
  }
}
