import {
  CloudUploadOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Button, Progress, Upload } from 'antd';
import React from 'react';
import styles from '../index.less';
import type { UseUploadReturn } from '../types';

interface UploadSectionProps {
  fileList: UseUploadReturn['fileList'];
  uploading: boolean;
  uploadProgress: number;
  uploadError: boolean;
  beforeUpload: UseUploadReturn['beforeUpload'];
  handleRemoveFile: UseUploadReturn['handleRemoveFile'];
  handleReupload: UseUploadReturn['handleReupload'];
  importing: boolean;
  acceptFileTypes: string[];
  showTitle?: boolean;
}

const UploadSection: React.FC<UploadSectionProps> = ({
  fileList,
  uploading,
  uploadProgress,
  uploadError,
  beforeUpload,
  handleRemoveFile,
  handleReupload,
  importing,
  acceptFileTypes,
  showTitle = true,
}) => {
  // 计算禁用状态
  const isDisabled = uploading || importing;

  return (
    <div className={`${styles.section} ${importing ? styles.disabledSection : ''}`}>
      {showTitle && <div className={styles.sectionTitle}>2. 上传表格</div>}
      <Upload.Dragger
        name="file"
        fileList={fileList}
        beforeUpload={beforeUpload}
        onRemove={handleRemoveFile}
        disabled={isDisabled}
        showUploadList={false}
        accept={acceptFileTypes.join(',')}
        className={styles.uploadArea}
      >
        <p className={styles.uploadIcon}>
          <CloudUploadOutlined />
        </p>
        <p className={styles.uploadText}>点击、拖拽/复制文件到这里上传</p>
        <p className={styles.uploadText}>上传后将覆盖原来的表格内容</p>
        <p className={styles.uploadHint}>(支持 {acceptFileTypes.join(', ')} 格式的文件)</p>
      </Upload.Dragger>

      {fileList.length > 0 && (
        <div className={`${styles.fileItem} ${uploadError ? styles.error : ''}`}>
          <FileExcelOutlined className={styles.fileIcon} />
          <div className={styles.fileInfo}>
            <div className={styles.fileName}>{fileList[0].name}</div>
            <div className={styles.fileSize}>{(fileList[0].size! / 1024).toFixed(0)} KB</div>
          </div>
          <div className={styles.fileActions}>
            {uploadError ? (
              <Button
                type="primary"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleReupload}
                className={styles.actionButton}
                disabled={isDisabled}
              >
                重新上传
              </Button>
            ) : (
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={handleRemoveFile}
                className={styles.actionButton}
                disabled={isDisabled}
              />
            )}
          </div>
        </div>
      )}

      {(uploading || uploadProgress > 0) && (
        <Progress
          percent={uploadProgress}
          status={uploadError ? 'exception' : undefined}
          className={styles.progressBar}
        />
      )}
    </div>
  );
};

export default UploadSection;
