import { Button } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Textarea } from "@/app/components/ui/textarea";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import { useAutoAnimate } from '@formkit/auto-animate/react';
import {
  RiAddLine,
  RiArrowDownSLine,
  RiArrowUpSLine,
  RiDeleteBinLine,
} from "@remixicon/react";
import { useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
interface EditExcercideProps {
  onSubmit?: () => void;
  setOpen: (open: boolean) => void;
  data: { exercise?: string; index: number };
  open?: boolean;
}

const EditExcercide: React.FC<EditExcercideProps> = (props) => {
  const [parent] = useAutoAnimate();
  const { setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const genId = () => Math.random().toString(36).slice(2) + Date.now();
  const [fromVal, setFromVal] = useState<{ id: string; value: string }[]>(
    (props.data.exercise || "")
      .split("\n")
      .filter((v) => typeof v === "string" && v !== undefined && v !== null)
      .map((v) => ({ id: genId(), value: v }))
  );

  const groupRefs = useRef<Array<HTMLDivElement | null>>([]);
  const lastInsertedIdx = useRef<number | null>(null);

  const handleInputChange = (id: string, value: string) => {
    setFromVal((prev: { id: string; value: string }[]) => {
      const arr: { id: string; value: string }[] = [...prev];
      const index = arr.findIndex((item) => item.id === id);
      if (index !== -1) {
        // @ts-expect-error
        arr[index] = { ...arr[index], value };
      }
      return arr;
    });
  };

  const handleMove = (id: string, direction: "up" | "down") => {
    setFromVal((prev: { id: string; value: string }[]) => {
      const arr = [...prev];
      const index = arr.findIndex((item) => item.id === id);
      if (index !== -1) {
        if (direction === "up" && index > 0) {
          const prevItem = arr[index - 1];
          const currItem = arr[index];
          if (prevItem && currItem) {
            arr[index - 1] = { ...currItem };
            arr[index] = { ...prevItem };
          }
        } else if (direction === "down" && index < arr.length - 1) {
          const nextItem = arr[index + 1];
          const currItem = arr[index];
          if (nextItem && currItem) {
            arr[index + 1] = { ...currItem };
            arr[index] = { ...nextItem };
          }
        }
      }
      return arr;
    });
  };

  const handleDelete = (id: string) => {
    // 直接删除，不使用动画
    setFromVal((prev) => prev.filter((v) => v.id !== id));
  };

  const handleAdd = () => {
    setFromVal((prev: { id: string; value: string }[]) => [
      ...prev,
      { id: genId(), value: "" },
    ]);
  };

  const handleInsert = (id: string) => {
    setFromVal((prev: { id: string; value: string }[]) => {
      const arr = prev.filter(
        (v: { id: string; value: string }) => typeof v.value === "string"
      );
      const index = arr.findIndex((item) => item.id === id);
      if (index !== -1) {
        arr.splice(index + 1, 0, { id: genId(), value: "" });
        lastInsertedIdx.current = index + 1;
      }
      return arr;
    });
  };

  useEffect(() => {
    if (lastInsertedIdx.current !== null) {
      const ref = groupRefs.current[lastInsertedIdx.current];
      if (ref) {
        ref.scrollIntoView({ behavior: "smooth", block: "center" });
      }
      lastInsertedIdx.current = null;
    }
  }, [fromVal]);

  const updateExercise = async () => {
    return post("/api/v1/lesson_widget/update/exercise", {
      arg: {
        lessonId,
        widgetIndex: props.data.index,
        exercise: fromVal.map((item) => item.value).join("\n"),
      },
    });
  };

  const onSubmitHandle = async () => {
    await updateExercise();
    emitter.emit("refresh");
    setOpen(false);
    emitter.emit("openSheet", props);
  };

  return (
    <div className="insert-node-modal text-sm">
      <div className="text-gray-400">请按照顺序输入题目 id，以竖线分割</div>

      <div className="w-116 h-90 mt-2 flex-col gap-4 overflow-auto" ref={parent}>
        {fromVal
          .filter(
            (v): v is { id: string; value: string } =>
              typeof v.value === "string"
          )
          .map((item: { id: string; value: string }, idx: number) => (
            <div
              key={item.id}
              className="mb-2 overflow-hidden bg-white"
              ref={(el) => {
                groupRefs.current[idx] = el;
              }}
            >
              <div className="flex flex-row items-center gap-0.5">
                <div>第{idx + 1}组：</div>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => handleMove(item.id, "up")}
                  disabled={idx === 0}
                  aria-label="上移"
                >
                  <RiArrowUpSLine size={18} />
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => handleMove(item.id, "down")}
                  disabled={idx === fromVal.length - 1}
                  aria-label="下移"
                >
                  <RiArrowDownSLine size={18} />
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => handleInsert(item.id)}
                  aria-label="向下插入"
                >
                  <RiAddLine size={18} />
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => handleDelete(item.id)}
                  aria-label="删除"
                >
                  <RiDeleteBinLine size={18} />
                </Button>
              </div>
              <div key={item.id} className="flex items-center gap-2">
                <Textarea
                  placeholder="请输入"
                  value={item.value}
                  onInput={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    handleInputChange(item.id, e.target.value)
                  }
                  className="flex-1"
                />
              </div>
            </div>
          ))}
        {/* <Button variant="outline" onClick={handleAdd} className="w-full">
          添加新分组
        </Button> */}
      </div>
      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>
        <Button onClick={onSubmitHandle}>提交</Button>
      </div>
    </div>
  );
};

const EditExcercideModal = (props: EditExcercideProps & { open: boolean }) => {
  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        className="bg-white"
      >
        <DialogHeader>
          <DialogTitle>编辑全部题目</DialogTitle>
        </DialogHeader>
        <EditExcercide {...props} setOpen={props.setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default EditExcercideModal;
