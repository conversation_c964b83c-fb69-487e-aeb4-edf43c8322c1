import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import Button from "../press-button";
import { StudyType } from "../../../../core/src/enums/question";

describe("Button Component", () => {
  it("应该渲染基础按钮", () => {
    render(<Button>测试按钮</Button>);
    expect(screen.getByText("测试按钮")).toBeInTheDocument();
  });

  it("应该支持不同的颜色", () => {
    const { rerender } = render(<Button color="orange">橙色按钮</Button>);
    expect(screen.getByText("橙色按钮")).toBeInTheDocument();

    rerender(<Button color="orange-light">浅橙色按钮</Button>);
    expect(screen.getByText("浅橙色按钮")).toBeInTheDocument();

    rerender(<Button color="red">红色按钮</Button>);
    expect(screen.getByText("红色按钮")).toBeInTheDocument();

    rerender(<Button color="green">绿色按钮</Button>);
    expect(screen.getByText("绿色按钮")).toBeInTheDocument();
  });

  it("应该支持study-theme配色", () => {
    render(
      <Button color="study-theme" studyType={StudyType.AI_COURSE}>
        AI课按钮
      </Button>
    );
    expect(screen.getByText("AI课按钮")).toBeInTheDocument();
  });

  it("应该支持巩固练习主题色", () => {
    render(
      <Button color="study-theme" studyType={StudyType.REINFORCEMENT_EXERCISE}>
        巩固练习按钮
      </Button>
    );
    expect(screen.getByText("巩固练习按钮")).toBeInTheDocument();
  });

  it("应该支持拓展练习主题色", () => {
    render(
      <Button color="study-theme" studyType={StudyType.EXPAND_EXERCISE}>
        拓展练习按钮
      </Button>
    );
    expect(screen.getByText("拓展练习按钮")).toBeInTheDocument();
  });

  it("应该支持次要样式", () => {
    render(
      <Button color="study-theme" studyType={StudyType.AI_COURSE} secondary>
        次要按钮
      </Button>
    );
    expect(screen.getByText("次要按钮")).toBeInTheDocument();
  });

  it("应该支持不同尺寸", () => {
    const { rerender } = render(
      <Button color="study-theme" studyType={StudyType.AI_COURSE} size="sm">
        小按钮
      </Button>
    );
    expect(screen.getByText("小按钮")).toBeInTheDocument();

    rerender(
      <Button color="study-theme" studyType={StudyType.AI_COURSE} size="lg">
        大按钮
      </Button>
    );
    expect(screen.getByText("大按钮")).toBeInTheDocument();
  });

  it("应该支持禁用状态", () => {
    render(
      <Button color="study-theme" studyType={StudyType.AI_COURSE} disabled>
        禁用按钮
      </Button>
    );
    const button = screen.getByText("禁用按钮");
    expect(button.closest("button")).toBeDisabled();
  });

  it("应该支持自定义样式", () => {
    render(
      <Button
        color="study-theme"
        studyType={StudyType.AI_COURSE}
        style={{ backgroundColor: "purple" }}
      >
        自定义样式按钮
      </Button>
    );
    expect(screen.getByText("自定义样式按钮")).toBeInTheDocument();
  });
});
