"use client";
import NavBackIcon from "@/public/icons/ic_nav_back.svg";
import { cn } from "@/utils";
import { useRouter } from "next/navigation";
import { memo } from "react";

const PageHeader = memo(function PageHeader({
  children,
  className,
  needBack = false,
  onBack,
}: {
  children?: React.ReactNode;
  className?: string;
  needBack?: boolean;
  onBack?: () => void;
}) {
  const router = useRouter();

  return (
    <div className={cn("h-17.5 flex items-center pl-6", className)}>
      {needBack && (
        <NavBackIcon
          onClick={() => {
            if (onBack) {
              onBack();
              return;
            }

            router.back();
          }}
          className="cursor-pointer active:opacity-80"
        />
      )}

      {children}
    </div>
  );
});

export { PageHeader };

