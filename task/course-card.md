# 课程卡片组件任务

## 任务描述
实现课程内容卡片，包含标题、副标题和开始按钮

## Figma设计图
- 文件链接: https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=305-1208&t=oQwfCmfb8L0r4nEJ-4
- 节点ID: 341:5071

## 验收标准
1. 卡片样式符合设计规范
2. 按钮点击效果正确
3. 文字排版正确
4. 阴影效果符合设计

## 技术实现要点
1. 使用React组件实现
2. 实现卡片悬浮效果
3. 实现按钮点击动画
4. 实现文字渐变效果
5. 实现阴影效果

## 依赖关系
- 无

## 状态
- 创建时间: 2024-04-30
- 更新时间: 2024-04-30
- 当前状态: 待开发

## 备注
- 注意卡片交互效果
- 确保文字排版美观
- 保持与设计稿的一致性 