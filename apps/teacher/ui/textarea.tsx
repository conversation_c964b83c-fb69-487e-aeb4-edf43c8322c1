import { cn } from "@/utils";
import { useControllableValue } from "ahooks";
import * as React from "react";

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea"> & {
    value?: string;
    containerClassName?: string;
  }
>(({ className, containerClassName, onChange, ...props }, ref) => {
  const [value, setValue] = useControllableValue(props, {
    defaultValue: "",
  });

  return (
    <div className={cn("relative flex", containerClassName)}>
      <textarea
        value={value}
        className={cn(
          "text-gray-2 placeholder:text-gray-4 h-25 w-full px-3 py-1.5 text-sm",
          "border-line-2 resize-none rounded-md border",
          "outline-none transition-all duration-300",
          "hover:border-indigo-500 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500/20",
          props.disabled ? "cursor-not-allowed bg-slate-50" : "bg-white",
          props.maxLength &&
            typeof value === "string" &&
            value.length >= props.maxLength
            ? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
            : "",
          className
        )}
        onChange={(e) => {
          setValue(e.target.value);
          onChange?.(e);
        }}
        ref={ref}
        {...props}
      />

      {props.maxLength && (
        <span className="text-gray-4 absolute bottom-1.5 right-2 text-xs">
          {`${value.length}/${props.maxLength}`}
        </span>
      )}
    </div>
  );
});
Textarea.displayName = "Textarea";

export { Textarea };
