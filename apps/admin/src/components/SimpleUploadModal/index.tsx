import { But<PERSON>, Modal, Popconfirm } from 'antd';
import React, { useCallback, useEffect } from 'react';
import { UploadSection } from '../UploadModal/components';
import { useImport, usePaste, useUpload } from '../UploadModal/hooks';
import styles from '../UploadModal/index.less';
import { SimpleUploadModalProps } from './types';

const defaultAcceptFileTypes = ['.xls', '.xlsx'];

const SimpleUploadModal: React.FC<SimpleUploadModalProps> = ({
  visible,
  onClose,
  onSuccess,
  title = '上传文件',
  acceptFileTypes = defaultAcceptFileTypes,
  importData,
  uploadParams,
  uploadTrigger,
}) => {
  // 上传相关状态和方法
  const {
    fileList,
    uploading,
    uploadProgress,
    uploadError,
    fileId,
    fileName,
    handleUpload,
    handleRemoveFile,
    handleReupload,
    beforeUpload,
    resetUploadState,
  } = useUpload({
    uploadParams,
    acceptFileTypes,
  });

  // 处理导入成功事件，阻止自动关闭
  const handleImportSuccess = useCallback(
    (data: any) => {
      console.log('handleImportSuccess data ***==> ', data);
      if (onSuccess) {
        // 传递数据但不关闭模态框
        onSuccess(data);
      }
    },
    [onSuccess],
  );

  // 导入相关状态和方法
  const { importing, importResult, handleImport, resetImportState } = useImport({
    fileId,
    importData: (fileId) => importData(fileId, fileName || ''),
    onSuccess: handleImportSuccess,
    needPolling: false,
  });

  // 粘贴上传功能
  usePaste({
    handleUpload,
    acceptFileTypes,
    disabled: uploading || importing,
  });

  // 重置状态
  useEffect(() => {
    if (!visible) {
      resetUploadState();
      resetImportState();
    }
  }, [visible]);

  // 计算按钮禁用状态
  const isImportButtonDisabled = !fileId || importing || uploading;

  // 显示"导入"按钮的条件
  const showImportButton = importResult.status !== 'success' && importResult.status !== 'failed';

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      footer={[
        importing ? (
          <Popconfirm key="confirm" title="确定关闭吗？" onConfirm={onClose}>
            <Button key="cancel">关闭</Button>
          </Popconfirm>
        ) : (
          <Button key="cancel" onClick={onClose}>
            关闭
          </Button>
        ),

        showImportButton && (
          <Button
            key="import"
            type="primary"
            loading={importing}
            disabled={!importing && isImportButtonDisabled}
            onClick={handleImport}
          >
            上传
          </Button>
        ),
      ]}
      maskClosable={false}
      closable={true}
      className={styles.uploadModal}
      width={600}
      destroyOnHidden={false}
    >
      <div className="relative">
        <UploadSection
          fileList={fileList}
          uploading={uploading}
          uploadProgress={uploadProgress}
          uploadError={uploadError}
          beforeUpload={beforeUpload}
          handleRemoveFile={handleRemoveFile}
          handleReupload={handleReupload}
          importing={importing}
          acceptFileTypes={acceptFileTypes}
          showTitle={false}
        />
      </div>
    </Modal>
  );
};

export default SimpleUploadModal;
