import { cn } from "@repo/ui/lib/utils";
import { FC } from "react";

interface LoadingProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "spinner" | "dots" | "pulse" | "wave" | "ripple" | "orbit";
  text?: string;
  color?: "blue" | "green" | "purple" | "orange" | "gray";
}

const Loading: FC<LoadingProps> = ({
  className,
  size = "md",
  variant = "spinner",
  text,
  color = "blue"
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };

  const colorClasses = {
    blue: "border-blue-600 bg-blue-600",
    green: "border-green-600 bg-green-600",
    purple: "border-purple-600 bg-purple-600",
    orange: "border-orange-600 bg-orange-600",
    gray: "border-gray-600 bg-gray-600"
  };

  const Spinner = () => (
    <div className={cn(
      "animate-spin rounded-full border-2 border-gray-300",
      `border-t-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600`,
      sizeClasses[size],
      className
    )} />
  );

  const Dots = () => (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            `bg-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600 rounded-full animate-bounce`,
            size === "sm" ? "w-1.5 h-1.5" : size === "md" ? "w-2 h-2" : "w-3 h-3"
          )}
          style={{ animationDelay: `${i * 0.1}s` }}
        />
      ))}
    </div>
  );

  const Pulse = () => (
    <div className={cn(
      `bg-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600 rounded-full animate-pulse`,
      sizeClasses[size],
      className
    )} />
  );

  const Wave = () => (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className={cn(
            `bg-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600 rounded-sm animate-pulse`,
            size === "sm" ? "w-1 h-3" : size === "md" ? "w-1.5 h-6" : "w-2 h-8"
          )}
          style={{ animationDelay: `${i * 0.1}s` }}
        />
      ))}
    </div>
  );

  const Ripple = () => (
    <div className={cn("relative", sizeClasses[size], className)}>
      <div className={cn(
        "absolute inset-0 rounded-full border-2 border-gray-300 animate-ping",
        `border-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600`
      )} />
      <div className={cn(
        "absolute inset-0 rounded-full border-2 border-gray-300 animate-ping",
        `border-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600`
      )} style={{ animationDelay: "0.5s" }} />
    </div>
  );

  const Orbit = () => (
    <div className={cn("relative", sizeClasses[size], className)}>
      <div className={cn(
        "absolute inset-0 rounded-full border-2 border-gray-300",
        `border-t-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600 animate-spin`
      )} />
      <div className={cn(
        "absolute inset-2 rounded-full border-2 border-gray-300",
        `border-b-${color === "blue" ? "blue" : color === "green" ? "green" : color === "purple" ? "purple" : color === "orange" ? "orange" : "gray"}-600 animate-spin`
      )} style={{ animationDirection: "reverse" }} />
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case "dots":
        return <Dots />;
      case "pulse":
        return <Pulse />;
      case "wave":
        return <Wave />;
      case "ripple":
        return <Ripple />;
      case "orbit":
        return <Orbit />;
      default:
        return <Spinner />;
    }
  };

  return (
    <div className="loading_container flex flex-col items-center justify-center space-y-3">
      {renderLoader()}
      {text && (
        <p className="loading_text text-sm text-gray-600 animate-pulse font-medium">
          {text}
        </p>
      )}
    </div>
  );
};

export { Loading };
