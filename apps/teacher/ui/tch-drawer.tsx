import { cn } from "@/utils/utils";
import { XIcon } from "lucide-react";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  type DialogProps,
} from "./sheet";

interface TchDrawerProps extends DialogProps {
  style?: React.CSSProperties;
  className?: string;
  side?: "top" | "right" | "bottom" | "left";
  title?: string;
  children?: React.ReactNode;
  trigger?: React.ReactNode;
}

export function TchDrawer({
  style = {},
  className = "",
  side = "right",
  title = "",
  children = null,
  trigger = null,
  ...sheetProps
}: TchDrawerProps) {
  return (
    // <div className="flex flex-wrap items-center gap-3">
      <Sheet key={side} {...sheetProps}>
        {trigger ? <SheetTrigger>{trigger}</SheetTrigger> : null}
        <SheetContent
          side={side}
          className={cn(
            "w-125! max-w-125! bg-fill-gray-2 gap-0 border-none shadow-[0px_8px_20px_rgba(15,17,20,0.10)]",
            className
          )}
          closeable={false}
          style={style}
          aria-modal="true"
          role="dialog"
        >
          <SheetHeader className="py-5.75 flex flex-row items-center justify-between gap-2 px-6">
            <SheetTitle className="text-xl leading-normal text-neutral-900">
              {title}
            </SheetTitle>
            <SheetClose asChild>
              <XIcon className="size-5 cursor-pointer" />
            </SheetClose>
          </SheetHeader>
          <SheetDescription></SheetDescription>
          <div className="relative flex flex-1 flex-col overflow-y-auto p-6 pt-0">
            {children}
          </div>
        </SheetContent>
      </Sheet>
    // </div>
  );
}
