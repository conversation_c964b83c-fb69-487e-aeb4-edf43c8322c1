/**
 * localStorage 工具方法
 * 提供安全的读写localStorage的方法，包括类型安全和错误处理
 */

// 检查是否在浏览器环境中
const isBrowser = typeof window !== "undefined";

/**
 * 从localStorage中获取数据
 * @param key 存储键名
 * @param defaultValue 默认值（如果没有找到数据）
 * @returns 解析后的数据或默认值
 */
export function getStorageItem<T>(key: string, defaultValue: T): T {
  if (!isBrowser) {
    return defaultValue;
  }

  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }

    return JSON.parse(item) as T;
  } catch (error) {
    console.error(`Error reading localStorage key "${key}":`, error);
    return defaultValue;
  }
}

/**
 * 将数据存储到localStorage
 * @param key 存储键名
 * @param value 要存储的数据
 * @returns 是否成功存储
 */
export function setStorageItem<T>(key: string, value: T): boolean {
  if (!isBrowser) {
    return false;
  }

  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error);
    return false;
  }
}

/**
 * 从localStorage中移除数据
 * @param key 存储键名
 * @returns 是否成功移除
 */
export function removeStorageItem(key: string): boolean {
  if (!isBrowser) {
    return false;
  }

  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error);
    return false;
  }
}

/**
 * 清空所有localStorage数据
 * @returns 是否成功清空
 */
export function clearStorage(): boolean {
  if (!isBrowser) {
    return false;
  }

  try {
    localStorage.clear();
    return true;
  } catch (error) {
    console.error("Error clearing localStorage:", error);
    return false;
  }
}
