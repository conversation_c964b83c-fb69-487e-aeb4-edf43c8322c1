"use client";

import { useState, useRef, useEffect } from "react";
import { cn } from "@/utils/utils";

interface TextAreaProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  disabled?: boolean;
  readOnly?: boolean;
  minHeight?: number;
  maxHeight?: number;
  autoSize?: boolean | { minRows?: number; maxRows?: number };
  loading?: boolean | { icon?: React.ReactNode };
  className?: string;
}

const TextArea = ({
  value = "",
  onChange = () => {},
  placeholder,
  maxLength,
  disabled,
  readOnly,
  minHeight = 32,
  maxHeight,
  autoSize = true,
  loading = false,
  className,
}: TextAreaProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const isLoading = typeof loading === "boolean" ? loading : true;
  const loadingIcon =
    typeof loading === "object" ? (
      loading.icon
    ) : (
      <svg
        className="h-4 w-4 animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

  useEffect(() => {
    if (!autoSize || !textareaRef.current) return;

    const textarea = textareaRef.current;
    const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20;
    const paddingTop = parseInt(getComputedStyle(textarea).paddingTop) || 8;
    const paddingBottom =
      parseInt(getComputedStyle(textarea).paddingBottom) || 8;

    // 重置高度以获取正确的 scrollHeight
    textarea.style.height = "auto";

    let targetHeight = textarea.scrollHeight;

    if (typeof autoSize === "object") {
      const { minRows = 1, maxRows = Infinity } = autoSize;
      const minHeight = minRows * lineHeight + paddingTop + paddingBottom;
      const maxHeight = maxRows * lineHeight + paddingTop + paddingBottom;

      targetHeight = Math.max(minHeight, Math.min(maxHeight, targetHeight));
    }

    textarea.style.height = `${targetHeight}px`;
  }, [value, autoSize]);

  const handleClear = () => {
    onChange("");
    textareaRef.current?.focus();
  };

  return (
    <div className={cn("relative flex")}>
      <textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => {
          const newValue = e.target.value;
          if (maxLength && newValue.length > maxLength) return;
          onChange(newValue);
        }}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        maxLength={maxLength}
        disabled={disabled || isLoading}
        readOnly={readOnly}
        style={{
          minHeight:
            typeof autoSize === "object" ? undefined : `${minHeight}px`,
          maxHeight:
            typeof autoSize === "object"
              ? undefined
              : maxHeight
                ? `${maxHeight}px`
                : undefined,
        }}
        className={cn(
          "w-full px-3 py-1.5 pb-6 pr-8 text-sm",
          "resize-none rounded border border-slate-200",
          "outline-none transition-all duration-300",
          "hover:border-indigo-500 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500/20",
          disabled || isLoading ? "cursor-not-allowed bg-slate-50" : "bg-white",
          !autoSize && "resize-y",
          autoSize && "resize-none",
          maxLength && value.length >= maxLength
            ? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
            : "",
          className,
        )}
      />
      {!disabled && !isLoading && value && (
        <button
          type="button"
          onClick={handleClear}
          className="absolute right-2 top-2 cursor-pointer text-slate-400 hover:text-slate-600"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M13.2266 2.65667C13.4922 2.91168 13.5007 3.3337 13.2457 3.59929L3.71595 13.5242C3.46094 13.7898 3.03892 13.7983 2.77333 13.5433C2.50775 13.2883 2.49918 12.8663 2.75419 12.6007L12.284 2.67582C12.539 2.41023 12.961 2.40166 13.2266 2.65667Z"
              fill="#444963"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M2.77332 2.65667C3.0389 2.40166 3.46092 2.41023 3.71593 2.67581L13.2457 12.6007C13.5007 12.8663 13.4921 13.2883 13.2266 13.5433C12.961 13.7983 12.539 13.7898 12.2839 13.5242L2.75417 3.59928C2.49916 3.3337 2.50774 2.91168 2.77332 2.65667Z"
              fill="#444963"
            />
          </svg>
        </button>
      )}
      {isLoading && (
        <span className="absolute right-2 top-2 text-slate-400">
          {loadingIcon}
        </span>
      )}
      {maxLength && (
        <span className="absolute bottom-1.5 right-2 text-xs text-gray-4">
          {`${value.length}/${maxLength}`}
        </span>
      )}
    </div>
  );
};

export default TextArea;
