/* eslint-disable */
import { RiTableLine } from '@remixicon/react';
import { Button, Popover, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useEditorConfig } from '../../../config-ctx';
import { Feature } from '../../../utils/enum';
import './style.scss';
import TableSelector from './table-selector';

const Table = () => {
  const config = useEditorConfig();
  const editor = config.editor!;
  if (!config.features.includes(Feature.table)) return null;

  const [open, setOpen] = useState(false);

  const ok = () => {};

  function showCustomContextMenu() {
    // 如果菜单已经存在，先移除旧菜单
    const existingMenu = document.getElementById('custom-context-menu');
    if (existingMenu) {
      document.body.removeChild(existingMenu);
    }

    // 保存当前编辑器的选择状态
    const currentSelection = editor.state.selection;

    // 降级复制方案
    const fallbackCopy = async (html: string) => {
      try {
        // 尝试使用现代 writeText API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(html);
          console.log('表格已复制到剪贴板（文本格式）');
        } else {
          // 使用传统方法
          const textarea = document.createElement('textarea');
          textarea.value = html;
          textarea.style.position = 'fixed';
          textarea.style.left = '-999999px';
          textarea.style.top = '-999999px';
          document.body.appendChild(textarea);
          textarea.focus();
          textarea.select();

          const successful = document.execCommand('copy');
          document.body.removeChild(textarea);

          if (successful) {
            console.log('表格已复制到剪贴板（降级方案）');
          } else {
            console.error('复制失败');
          }
        }
      } catch (error) {
        console.error('降级复制方案失败:', error);
      }
    };

    // 创建菜单容器
    const menu = document.createElement('div');
    menu.id = 'custom-context-menu';
    menu.className = 'no-blur';
    Object.assign(menu.style, {
      position: 'absolute',
      display: 'none',
      background: 'white',
      boxShadow:
        '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
      padding: '4px',
      borderRadius: '8px',
      zIndex: 9999,
      fontSize: '12px',
      minWidth: '100px',
      cursor: 'default',
    });

    // 菜单项配置
    const actions = [
      {
        label: '插入行',
        action: () => editor.commands.addRowAfter(),
      },
      {
        label: '删除行',
        action: () => editor.commands.deleteRow(),
      },
      {
        label: '插入列',
        action: () => editor.commands.addColumnAfter(),
      },
      {
        label: '删除列',
        action: () => editor.commands.deleteColumn(),
      },
      {
        label: '复制表格',
        action: async () => {
          try {
            // 恢复选择状态
            editor.view.dispatch(editor.state.tr.setSelection(currentSelection));

            // 获取表格元素的多种方式
            let table: HTMLTableElement | null = null;

            // 方式1: 从编辑器DOM中查找
            const editorDom = editor.view.dom;
            table = editorDom.querySelector('table');

            // 方式2: 如果方式1失败，从选择位置查找
            if (!table) {
              const { state, view } = editor;
              const { selection } = state;
              try {
                const dom = view.domAtPos(selection.from).node as HTMLElement;
                table = dom.closest ? dom.closest('table') : null;
              } catch (error) {
                console.warn('无法从选择位置获取表格:', error);
              }
            }

            if (!table) {
              console.warn('未找到表格元素');
              return;
            }

            const html = table.outerHTML;

            // 现代浏览器：同时写入 HTML 和纯文本格式
            if (navigator.clipboard && navigator.clipboard.write) {
              try {
                const htmlBlob = new Blob([html], { type: 'text/html' });
                const textBlob = new Blob([table.innerText || table.textContent || ''], {
                  type: 'text/plain',
                });

                const clipboardItem = new ClipboardItem({
                  'text/html': htmlBlob,
                  'text/plain': textBlob,
                });

                await navigator.clipboard.write([clipboardItem]);
                console.log('表格已复制到剪贴板（HTML格式）');
              } catch (clipboardError) {
                console.warn('现代剪贴板API失败，使用降级方案:', clipboardError);
                await fallbackCopy(html);
              }
            } else {
              // 降级方案
              await fallbackCopy(html);
            }

            // 保持编辑器焦点
            editor.view.focus();
          } catch (error) {
            console.error('复制表格失败:', error);
          }
        },
      },
      {
        label: '删除表格',
        action: () => editor.commands.deleteTable(),
      },
    ];

    // 创建并插入菜单项
    actions.forEach(({ label, action }) => {
      const item = document.createElement('div');
      item.textContent = label;
      Object.assign(item.style, {
        padding: '4px 8px',
        cursor: 'pointer',
        color: 'rgba(0,0,0,0.88)',
        borderRadius: '4px',
      });
      item.onmouseenter = () => (item.style.background = 'rgba(0,0,0,0.04)');
      item.onmouseleave = () => (item.style.background = 'white');

      //防止编辑器失去焦点
      item.onmousedown = (e) => {
        e.preventDefault(); // 防止默认行为
        e.stopPropagation(); // 防止事件冒泡
      };
      item.onclick = () => {
        action();
        hideMenu();
      };

      menu.appendChild(item);
    });

    // 添加到页面
    document.body.appendChild(menu);

    // 隐藏菜单
    function hideMenu() {
      if (document.body.contains(menu)) {
        document.body.removeChild(menu);
      }
    }
    document.addEventListener('click', hideMenu);
  }

  useEffect(() => {
    const handleContextMenu = (event: any) => {
      const target = event.target;
      // 判断是否在表格单元格中（<td> 或 <th>）
      if (target.closest('td') || target.closest('th')) {
        // 检查是否有文本选择
        const selection = window.getSelection();
        const hasTextSelection = selection && selection.toString().trim().length > 0;

        // 检查编辑器内部是否有选择
        const editorSelection = editor.state.selection;
        const hasEditorSelection = !editorSelection.empty;

        // 如果有文本选择，不显示表格菜单，让系统处理默认的复制菜单
        if (hasTextSelection || hasEditorSelection) {
          return; // 不阻止默认行为，让系统显示默认右键菜单
        }

        // 没有文本选择时，显示表格操作菜单
        event.preventDefault();

        // 显示自定义菜单
        showCustomContextMenu();

        // 获取菜单元素并显示在鼠标位置
        const menu = document.getElementById('custom-context-menu');
        if (menu) {
          menu.style.left = `${event.clientX}px`;
          menu.style.top = `${event.clientY}px`;
          menu.style.display = 'block';
        }
      }
    };

    editor.view.dom.addEventListener('contextmenu', handleContextMenu);

    return () => {
      editor.view.dom.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  return (
    <Tooltip title="插入表格">
      <Popover
        content={<TableSelector />}
        title=""
        open={open}
        trigger="click"
        onOpenChange={handleOpenChange}
      >
        <Button
          onClick={ok}
          color="default"
          variant="filled"
          autoInsertSpace
          onMouseDown={(e) => e.preventDefault()}
        >
          <RiTableLine style={{ width: 18 }} />
        </Button>
      </Popover>
    </Tooltip>
  );
};
Table.id = 'table';

export default Table;
