/**
 * 题目录入/审核/纠错 弹窗
 */
import React from 'react';
import FullScreenModal from './components/shared/FullScreenModal';
import CreateQuestionProvider from './CreateQuestionProvider';
import styles from './index.module.less';
import { QuestionCreateProps } from './types';

interface IProps extends QuestionCreateProps {
  open: boolean;
}
const CreateQuestionModal: React.FC<IProps> = ({ data, onBack, onSubmit, open = false, way }) => {
  return (
    <FullScreenModal open={open} onClose={onBack}>
      <div className={styles.createQuestionModal}>
        <CreateQuestionProvider data={data} onBack={onBack} onSubmit={onSubmit} way={way} />
      </div>
    </FullScreenModal>
  );
};

export default CreateQuestionModal;
