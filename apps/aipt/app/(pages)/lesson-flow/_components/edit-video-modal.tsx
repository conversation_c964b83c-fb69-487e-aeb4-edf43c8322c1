import { Button } from "@/app/components/ui/button";
import {
  <PERSON>alog,
  Di<PERSON>Close,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import { uploadFile } from "@/lib/uploader";
import DeleteImg from "@/public/delete.svg";
import { useSearchParams } from "next/navigation";
import { useRef, useState } from "react";
import { toast } from "sonner";

interface EditExcercideProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const EditExcercide: React.FC<EditExcercideProps> = (props) => {
  const { onSubmit, setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数

  const [video, setVideo] = useState(
    props.data.videoInfo ?? {
      name: "",
      url: "",
      length: 0,
      corver: "",
      duration: 0,
    }
  );

  const videoRef = useRef(
    props.data.videoInfo ?? {
      name: "",
      url: "",
      length: 0,
      corver: "",
      duration: 0,
    }
  );

  const [upStatus, setUpStatus] = useState("");
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setUpStatus("ing");
      const { url, volcengine } = await uploadFile(
        file,
        undefined,
        undefined,
        true
      );
      const volcengineRes: any = volcengine;

      setUpStatus("ed");
      // setVideo({ ...video, url, length: file.size });
      setVideo({
        ...videoRef.current,
        url,
        length: file.size,
        vid: volcengineRes.Vid,
      });
    }
  };

  const updateExercise = async () => {
    return post("/api/v1/lesson_widget/update/video", {
      arg: {
        lessonId,
        widgetIndex: props.data.index,
        videoInfo: video,
      },
    });
  };

  const onSubmitHandle = async () => {
    try {
      await updateExercise();
      emitter.emit("refresh");
      setOpen(false);
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleLoadedMetadata = (
    e: React.SyntheticEvent<HTMLVideoElement, Event>
  ) => {
    const duration = Math.ceil(e.currentTarget.duration || 0);
    setVideo({ ...video, duration });
    videoRef.current = { ...video, duration };
  };

  return (
    <div className="insert-node-modal">
      <div className="mt-4 flex flex-col gap-2">
        <div className="flex items-center gap-2">
          视频名称：
          <Input
            className="flex-1"
            placeholder="请在此输入..."
            onInput={(event: any) => {
              setVideo({ ...video, name: event.target.value });
              videoRef.current = { ...video, name: event.target.value };
            }}
            value={video.name}
          />
        </div>
        <div className="flex items-center gap-2">
          视频上传：
          <div className="flex-1">
            {upStatus === "ing" ? (
              <span className="text-sm text-gray-500">
                正在上传，请勿关闭...
              </span>
            ) : (
              <>
                {video.url ? (
                  <span className="flex items-center gap-2 text-sm text-gray-500">
                    删除视频{" "}
                    <DeleteImg
                      width={18}
                      height={18}
                      className="cursor-pointer opacity-50"
                      onClick={() => {
                        setVideo({ ...video, url: "" });
                        videoRef.current = { ...video, url: "" };
                      }}
                    />
                  </span>
                ) : (
                  <Input
                    className="flex-1"
                    type="file"
                    onChange={handleFileChange}
                    accept="video/*"
                  />
                )}
              </>
            )}
          </div>
        </div>

        {video.url && (
          <div className="flex items-center gap-2">
            <span className="opacity-0">视频预览：</span>
            <div className="flex-1">
              <div className="mt-2 overflow-hidden rounded-lg">
                <video
                  className="max-h-[300px] w-full object-contain"
                  controls
                  src={video.url}
                  // disableRemotePlayback
                  // crossOrigin="anonymous"
                  preload="auto"
                  onLoadedMetadata={handleLoadedMetadata}
                >
                  您的浏览器不支持视频播放
                </video>
              </div>
              {/* <div className="text-sm text-gray-500">
                存在上次上传的视频，点击上方既可预览
              </div> */}
            </div>
          </div>
        )}
      </div>
      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>
        <Button onClick={onSubmitHandle}>提交</Button>
      </div>
    </div>
  );
};

const EditExcercideModal = (props: any) => {
  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        className="bg-white"
      >
        <DialogHeader>
          <DialogTitle>编辑视频</DialogTitle>
        </DialogHeader>
        <EditExcercide {...props} setOpen={props.setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default EditExcercideModal;
