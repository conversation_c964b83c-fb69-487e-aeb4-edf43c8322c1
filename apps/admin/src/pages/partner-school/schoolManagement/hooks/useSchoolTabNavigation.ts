import { getSchoolPermissions } from '@/services/partner-school';
import { Menu } from '@/types/menu';
import { useAccess, useLocation, useModel, useNavigate } from '@umijs/max';
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

export function useSchoolTabNavigation() {
  const location = useLocation();
  const navigate = useNavigate();
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolInfo?.schoolId;
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const [schoolPermissions, setSchoolPermissions] = useState<Menu[]>([]);
  const [loadingPermissions, setLoadingPermissions] = useState(false);
  // 确定当前路由前缀
  const getRoutePrefix = () => {
    const pathname = location.pathname;
    return pathname.startsWith('/my-partner-school') ? '/my-partner-school' : '/partner-school';
  };

  const routePrefix = getRoutePrefix();
  const isMyPartnerSchool = routePrefix === '/my-partner-school';

  // 判断是否为学校顶级管理员
  const isSchoolSuperAdmin = useCallback(async () => {
    if (!isMyPartnerSchool) return true;

    return (
      Array.isArray(schoolInfo?.userIDs) && schoolInfo?.userIDs?.includes(currentUser?.userId || 0)
    );
  }, [schoolInfo, currentUser, isMyPartnerSchool]);

  // 检查用户是否有权限访问指定路径
  const hasPermission = async (path: string, _schoolPermissions: Menu[]) => {
    const superAdmin = await isSchoolSuperAdmin();
    if (superAdmin) {
      return access[path];
    }

    // 如果权限数据正在加载，默认不允许访问
    if (loadingPermissions || !_schoolPermissions) {
      return false;
    }

    // 检查用户是否有该路径的权限
    return _schoolPermissions.some((menu) => menu.menuPath === path);
  };

  // 获取可用的标签页列表
  const getAvailableTabs = () => {
    return {
      academic: [
        `${routePrefix}/management/academic/semester`,
        `${routePrefix}/management/academic/subject`,
        `${routePrefix}/management/academic/schedule`,
        `${routePrefix}/management/academic/teacherSchedule`,
        `${routePrefix}/management/academic/examinationManagement`,
      ],
      account: [
        `${routePrefix}/management/account/student`,
        `${routePrefix}/management/account/teacher`,
      ],
      permission: [`${routePrefix}/management/permission`],
    };
  };

  // 获取所有标签页路径的平铺列表
  const getAllTabs = () => {
    const tabs = getAvailableTabs();
    return [...tabs.academic, ...tabs.account, ...tabs.permission];
  };

  // 找到第一个用户有权限的标签页
  const findFirstAvailableTab = async (schoolPermissions: Menu[]) => {
    const allTabs = getAllTabs();

    for (const tab of allTabs) {
      if (await hasPermission(tab, schoolPermissions)) {
        return tab;
      }
    }

    return null;
  };

  // 根据主标签找到第一个有权限的子标签
  const findFirstAvailableSubTab = useCallback(
    async (mainTab: string) => {
      const tabs = getAvailableTabs();
      const subTabs = tabs[mainTab as keyof typeof tabs] || [];

      for (const tab of subTabs) {
        if (await hasPermission(tab, schoolPermissions)) {
          return tab;
        }
      }

      return null;
    },
    [schoolPermissions],
  );

  const querySchoolPermissions = async (schoolId: number) => {
    setLoadingPermissions(true);
    const {
      data: { list: _schoolPermissions },
    } = await getSchoolPermissions(schoolId, currentUser?.userId || 0);

    setSchoolPermissions(_schoolPermissions);
    setLoadingPermissions(false);

    return _schoolPermissions;
  };
  useEffect(() => {
    if (schoolId) {
      querySchoolPermissions(schoolId);
    }
  }, [schoolInfo]);

  // 导航到特定学校的管理页面
  const navigateToSchool = async (schoolId: number) => {
    try {
      const _schoolPermissions = await querySchoolPermissions(schoolId);

      // 寻找第一个可用的标签页
      const firstAvailableTab = await findFirstAvailableTab(_schoolPermissions);

      if (firstAvailableTab) {
        navigate(`${firstAvailableTab}?id=${schoolId}`);
      } else {
        // 如果没有可用标签页，导航到管理页面并显示无权限信息
        navigate(`${routePrefix}/management?id=${schoolId}`);
        message.error('您没有权限访问任何模块');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      message.error('导航失败，请重试1');
    }
  };

  // 导航到主标签对应的第一个有权限的子标签
  const navigateToMainTab = async (mainTab: string) => {
    if (!schoolId) return;

    try {
      const firstAvailableSubTab = await findFirstAvailableSubTab(mainTab);

      if (firstAvailableSubTab) {
        navigate(`${firstAvailableSubTab}?id=${schoolId}`);
      } else {
        message.error(`您没有权限访问${mainTab}模块`);
      }
    } catch (error) {
      console.error('Navigation error:', error);
      message.error('导航失败，请重试2');
    }
  };

  // 返回列表页面
  const navigateBack = () => {
    navigate(`${routePrefix}/list`);
  };

  return {
    schoolId,
    routePrefix,
    isMyPartnerSchool,
    schoolPermissions,
    loadingPermissions,
    hasPermission,
    findFirstAvailableTab,
    findFirstAvailableSubTab,
    navigateToSchool,
    navigateToMainTab,
    navigateBack,
    getAvailableTabs,
  };
}
