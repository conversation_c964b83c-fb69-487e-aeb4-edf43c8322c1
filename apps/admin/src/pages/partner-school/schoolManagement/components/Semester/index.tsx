import { EditOutlined, LoadingOutlined } from '@ant-design/icons';
import {
  But<PERSON>,
  Card,
  Checkbox,
  Col,
  DatePicker,
  message,
  Modal,
  Row,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

import {
  checkSemesterSetting,
  needShowSemesterModifyTip,
} from '@/pages/partner-school/utils/semester';
import { getSemesterList, updateSemesterList } from '@/services/semester-management';
import { GradeSetting, Semester } from '@/services/semester-management/type';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import cloneDeep from 'lodash-es/cloneDeep';
import isEqual from 'lodash-es/isEqual';
import styles from './index.less';

const SemesterManagement: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [gradeSettings, setGradeSettings] = useState<GradeSetting[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingData, setEditingData] = useState<GradeSetting[]>([]);
  const [applyModalVisible, setApplyModalVisible] = useState(false);
  const [currentGradeId, setCurrentGradeId] = useState<number>(0);
  const [selectedGrades, setSelectedGrades] = useState<number[]>([]);
  const { schoolInfo } = useModel('schoolModel');
  const [loading, setLoading] = useState(false);

  // 修改获取学期数据的函数
  const getSemesterData = async () => {
    setLoading(true);
    try {
      const res = await getSemesterList({
        school_id: schoolInfo?.schoolId || 1,
        school_year_id: schoolInfo?.schoolYear?.school_year_id || 1,
      });

      if (res.code === 0) {
        if (res.data && res.data.grade_settings.length > 0) {
          setGradeSettings(res.data.grade_settings);
          setEditingData(cloneDeep(res.data.grade_settings));
        }
      } else {
        messageApi.error('获取学期数据失败');
      }
    } catch (error) {
      console.error('获取学期数据出错:', error);
      messageApi.error('获取学期数据出错');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // console.log(`学校信息: `, schoolInfo);
    if (schoolInfo?.schoolId) {
      getSemesterData();
    }
  }, [schoolInfo]);

  // 检查年级所有日期是否已设置
  const checkGradeDatesComplete = (gradeId: number) => {
    const grade = editingData.find((g) => g.grade_id === gradeId);
    if (!grade) return false;

    return grade.semesters.every((semester) => semester.start_date && semester.end_date);
  };

  // 切换到编辑模式
  const handleEdit = () => {
    setEditingData(JSON.parse(JSON.stringify(gradeSettings))); // 深拷贝当前数据
    setIsEditing(true);
  };

  // 保存编辑
  const handleSave = async () => {
    setLoading(true);
    try {
      // 构建保存请求参数
      const params = {
        school_id: schoolInfo?.schoolId || 0,
        school_year_id: schoolInfo?.schoolYear?.school_year_id || 1,
        grade_settings: editingData,
      };

      const res = await updateSemesterList(params);

      if (res.code === 0) {
        messageApi.success('保存成功');
        setGradeSettings(editingData);
        setIsEditing(false);
      } else {
        messageApi.error('保存失败: ' + res.message);
      }
    } catch (error) {
      console.error('保存学期数据出错:', error);
      messageApi.error('保存学期数据出错');
    } finally {
      getSemesterData();
      setLoading(false);
    }
  };

  const handleCheckAndSave = () => {
    // 检查设置是否有效
    const { valid, message } = checkSemesterSetting(editingData);
    if (!valid) {
      messageApi.error(message);
      return;
    }
    if (needShowSemesterModifyTip(gradeSettings, editingData)) {
      Modal.confirm({
        title: '提示',
        content: '  修改学期会删除该学期对应的全部排课信息，请慎重操作！是否继续修改？',
        onOk: async () => {
          handleSave();
        },
      });
    } else {
      handleSave();
    }
  };

  // 取消编辑
  const handleCancel = () => {
    if (isEqual(editingData, gradeSettings)) {
      setIsEditing(false);
      return;
    }
    Modal.confirm({
      title: '确认取消',
      content: '取消编辑将丢失所有未保存的更改，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setIsEditing(false);
        setEditingData(JSON.parse(JSON.stringify(gradeSettings))); // 恢复原始数据
      },
    });
  };

  // 打开应用到其他年级的弹窗
  const handleApplyToOthers = (gradeId: number) => {
    setCurrentGradeId(gradeId);
    setSelectedGrades([]);
    setApplyModalVisible(true);
  };

  // 应用到其他年级
  const handleApply = () => {
    if (!currentGradeId) return;

    const sourceGrade = editingData.find((g) => g.grade_id === currentGradeId);
    if (!sourceGrade) return;

    const updatedData = [...editingData];

    selectedGrades.forEach((gradeId) => {
      const targetGradeIndex = updatedData.findIndex((g) => g.grade_id === gradeId);
      if (targetGradeIndex !== -1) {
        // 复制学期设置，但保留目标年级原有的 semester_id
        updatedData[targetGradeIndex].semesters = updatedData[targetGradeIndex].semesters.map(
          (targetSemester) => {
            const sourceSemester = sourceGrade.semesters.find(
              (s) => s.semester_type_id === targetSemester.semester_type_id,
            );
            if (sourceSemester) {
              return {
                ...sourceSemester,
                semester_id: targetSemester.semester_id, // 保留原有的 semester_id
              };
            }
            return targetSemester;
          },
        );
      }
    });

    setEditingData(updatedData);
    setApplyModalVisible(false);
  };

  // 处理日期变更
  const handleDateChange = (
    gradeId: number,
    semesterTypeId: number,
    field: 'start_date' | 'end_date',
    date: dayjs.Dayjs | null,
  ) => {
    const updatedData = [...editingData];
    const gradeIndex = updatedData.findIndex((g) => g.grade_id === gradeId);

    if (gradeIndex !== -1) {
      const semesterIndex = updatedData[gradeIndex].semesters.findIndex(
        (s) => s.semester_type_id === semesterTypeId,
      );

      if (semesterIndex !== -1) {
        // 更新日期
        const dateString = date ? date.format('YYYY-MM-DD') : '';
        updatedData[gradeIndex].semesters[semesterIndex][field] = dateString;

        setEditingData(updatedData);
      }
    }
  };

  const getDisplayDate = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return '暂未设置';
    return `${startDate} ~ ${endDate}`;
  };

  // 渲染学期项
  const renderSemesterItem = (gradeId: number, semester: Semester) => {
    // console.log(`渲染学期项: `, gradeId, semester);
    const data = isEditing ? editingData : gradeSettings;
    const grade = data.find((g) => g.grade_id === gradeId);
    if (!grade) return null;

    const semesterData = grade.semesters.find(
      (s) => s.semester_type_id === semester.semester_type_id,
    );
    if (!semesterData) return null;

    return (
      <Col span={6} key={semester.semester_type_id}>
        <div className={styles.semesterItem}>
          <div className={styles.semesterTitle}>{semester.semester_name}</div>
          {isEditing ? (
            <div className={styles.datePickerContainer}>
              <DatePicker
                className={styles.datePicker}
                value={semesterData.start_date ? dayjs(semesterData.start_date) : null}
                onChange={(date) =>
                  handleDateChange(gradeId, semester.semester_type_id, 'start_date', date)
                }
                placeholder="开始日期"
                allowClear
              />
              <DatePicker
                className={styles.datePicker}
                value={semesterData.end_date ? dayjs(semesterData.end_date) : null}
                onChange={(date) =>
                  handleDateChange(gradeId, semester.semester_type_id, 'end_date', date)
                }
                placeholder="结束日期"
                allowClear
              />
            </div>
          ) : (
            <div className={`${styles.semesterDate}`}>
              {getDisplayDate(semesterData.start_date, semesterData.end_date)}
            </div>
          )}
        </div>
      </Col>
    );
  };

  // 渲染学期卡片
  const renderSemesterCard = (grade: GradeSetting) => {
    // console.log(`renderSemesterCard grade`, grade);
    return (
      <Card
        key={grade.grade_id}
        title={grade.grade_name}
        className={styles.gradeCard}
        extra={
          isEditing && (
            <Button
              type="link"
              onClick={() => handleApplyToOthers(grade.grade_id)}
              disabled={!checkGradeDatesComplete(grade.grade_id)}
            >
              应用到其他年级
            </Button>
          )
        }
      >
        <Row gutter={16} className={styles.semestersRow}>
          {grade.semesters.map((semester) => renderSemesterItem(grade.grade_id, semester))}
        </Row>
      </Card>
    );
  };

  // 获取可应用的年级选项
  const getApplyOptions = () => {
    return editingData
      .filter((grade) => grade.grade_id !== currentGradeId)
      .map((grade) => ({
        label: grade.grade_name,
        value: grade.grade_id,
      }));
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Typography.Title level={5} className={styles.academicYear}>
          {schoolInfo?.schoolYear?.school_year || '学年设置'}
        </Typography.Title>
        {isEditing ? (
          <div className={styles.editButtons}>
            <Button onClick={handleCancel} className={styles.cancelButton} loading={loading}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleCheckAndSave}
              className={styles.saveButton}
              loading={loading}
            >
              保存
            </Button>
          </div>
        ) : (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEdit}
            className={styles.editButton}
            loading={loading}
          >
            编辑
          </Button>
        )}
      </div>

      <div className={styles.content}>
        <Spin spinning={loading} indicator={<LoadingOutlined spin />}>
          {(isEditing ? editingData : gradeSettings).map((grade) => renderSemesterCard(grade))}
        </Spin>
      </div>

      <Modal
        title="应用到其他年级"
        open={applyModalVisible}
        onOk={handleApply}
        onCancel={() => setApplyModalVisible(false)}
        okText="确认"
        cancelText="取消"
      >
        <p className="mb-4">请选择要应用到的年级：</p>
        <Checkbox.Group
          options={getApplyOptions()}
          value={selectedGrades}
          onChange={(values) => setSelectedGrades(values as number[])}
        />
      </Modal>
      {contextHolder}
    </div>
  );
};

export default SemesterManagement;
