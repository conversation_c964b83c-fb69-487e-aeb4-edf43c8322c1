"use client";
import { useGuideContext } from "@/app/context/guide-context";
import { useGuideSetContext } from "@/app/context/guide-set-context";
import { toH3Parts } from "@repo/core/guide/utils/h3-parts";
import { GuideWidgetData, Line } from "@repo/core/types/data/widget-guide";
import { FC, useMemo } from "react";
import { Button } from './common/button';
import { GuideDrawPlayer } from "./draw-player/guide-player";

export const GuideDrawH3List: FC = ({
}) => {
  const { guide } = useGuideContext();
  const { guideWidgetId, guideWidgetSetId } = guide;
  const { guideWidgetIndex, guideWidgetSetData } = useGuideSetContext();
  const videoData = useMemo(() => {
    if (typeof guide.videoJson === "string") {
      try {
        return JSON.parse(guide.videoJson) as GuideWidgetData;
      } catch (error) {
        return null;
      }
    }
    return guide.videoJson || ({} as GuideWidgetData);
  }, [guide.videoJson]);
  const parts = useMemo(
    () => {
      if (!videoData) return [];
      return toH3Parts(videoData.content).map((part) => ({
        ...videoData,
        content: part,
      }))
    },
    [videoData?.content]
  );

  const isPartCompleted = (part: Line[]) => {
    // 查找该部分中的h3标签，检查是否有draw数据
    const h3Line = part.find((line) => line.tag === "h3");
    return h3Line && h3Line.draw && h3Line.draw.length > 0;
  };

  return (
    <>
      {parts.map((part, index) => {
        const isCompleted = isPartCompleted(part.content);
        if (!part.content?.length) return null;
        let outFrame = part.content[part.content.length - 1]?.outFrame;
        if (!outFrame) return null;
        
        if (outFrame
          && videoData?.avatar?.durationInFrames
          && outFrame >= videoData?.avatar?.durationInFrames
        ) {
          outFrame = videoData?.avatar?.durationInFrames - 1;
        }
        const inFrame = outFrame - 1;

        return (
          <div key={index} className="flex w-full justify-between">
            <div className="w-[1000px]">
              <GuideDrawPlayer
                className="h-[600px] mb-4"
                data={part}
                width={1000}
                height={600}
                inFrame={inFrame}
                outFrame={outFrame}
                controls={false}
                numberOfSharedAudioTags={1}
                partIndex={guideWidgetIndex.value}
                totalPartsCount={guideWidgetSetData.value?.guideWidgetList?.length || 0}
                mode="edit"
                hideSubtitles={true}
                initSeekFrame={outFrame}
              />
            </div>
            <div className="flex flex-col items-center gap-2">
              <Button
                className="rounded border border-gray-200 bg-white px-4 py-1 text-sm text-gray-600 hover:bg-gray-50"
                onClick={() => {
                  // 在当前页打开
                  location.href = `/guide-draw/draw?id=${guideWidgetSetId}&guideWidgetId=${guideWidgetId}&blockIndex=${index}`;
                }}
              >
                进入圈画录制
              </Button>
              {/* 完成状态标记 */}
              <div className="flex items-center gap-1 text-xs">
                {isCompleted ? (
                  <>
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span className="text-green-600">已完成</span>
                  </>
                ) : (
                  <>
                    <div className="h-2 w-2 rounded-full bg-gray-300"></div>
                    <span className="text-gray-500">未完成</span>
                  </>
                )}
              </div>
            </div>
          </div>
        )
      })}
    </>
  );
};
