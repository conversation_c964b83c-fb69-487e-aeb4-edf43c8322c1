import useEnumManagerMap from '@/hooks/useEnumManager';
import { Form, Radio } from 'antd';
import React, { memo } from 'react';

interface QuestionAnswerModeSelectorProps {
  /**
   * 表单字段名称，支持嵌套路径
   * 例如：'questionAnswerMode' 或 [name, 'questionAnswerMode']
   */
  name: string | (string | number)[];
  /**
   * 题目主题干填空点数量
   */
  count?: number;
  /**
   * 表单字段的其他属性（来自 Form.List 的 restField）
   */
  restField?: any;

  /**
   * 是否必填
   */
  required?: boolean;

  /**
   * 初始值
   */
  initialValue?: number;

  /**
   * 标签文本
   */
  label?: string;
}

/**
 * 作答方式选择器组件
 * 统一管理所有题目的作答方式选择逻辑
 */
const QuestionAnswerModeSelector: React.FC<QuestionAnswerModeSelectorProps> = ({
  name,
  count = 0,
  restField,
  initialValue = 1,
  label = '作答方式',
}) => {
  const { questionAnswerModeList } = useEnumManagerMap();

  return (
    <Form.Item
      name={name}
      label={label}
      rules={[{ required: true, message: '请选择作答方式' }]}
      initialValue={initialValue}
      {...restField}
    >
      <Radio.Group>
        {questionAnswerModeList?.enums.map((item: any) => (
          <Radio
            key={item.value}
            value={item.value}
            disabled={(count === 0 && item.value === 3) || (count > 0 && item.value === 5)}
          >
            {item.label}
          </Radio>
        ))}
      </Radio.Group>
    </Form.Item>
  );
};

export default memo(QuestionAnswerModeSelector);
