import { Skeleton } from "@/ui/skeleton";
import { cn } from "@/utils/utils";

interface TchTreeSkeletonProps {
  style?: React.CSSProperties;
  className?: string;
}

export function TchTreeSkeleton({
  style = {},
  className = "",
}: TchTreeSkeletonProps) {
  
  return (
    <div className={cn("flex flex-col gap-2", className)} style={style}>
      <Skeleton className="h-6 rounded-md w-full" />
      <div className="pl-3.5 flex flex-col gap-1">
        <Skeleton className="h-6 rounded-md w-full" />
        <Skeleton className="h-6 rounded-md w-full" />
      </div>
      <Skeleton className="h-6 rounded-md w-full" />
      <Skeleton className="h-6 rounded-md w-full" />
    </div>
  );
}
