# 📘 学生报告模块（任务详情页 Tab）

模块路径：`/report/[taskId]` 页面的一级 Tab 之一  
默认进入时展示该模块（默认 tab）

---

## 一、模块概述

学生报告模块用于展示当前任务下 **某个班级** 和 **某份素材** 中，所有学生的答题数据与表现分析，包括：

- 表现优异学生（建议表扬）
- 表现波动学生（建议关注）
- 班级平均进度 / 平均正确率
- 所有学生详细列表（支持排序、筛选、查看详情、导出）

---

## 二、组件结构

```tsx
<StudentReportTab>
├── <TaskHeader>                 // 顶部任务信息：发布时间、完成时间、班级选择、素材选择
├── <StudentHighlights>
│   ├── <EncourageList>         // 建议鼓励学生（可点开抽屉）
│   └── <AttentionList>         // 建议关注学生（可点开抽屉）
├── <ClassStats>
│   ├── <AvgProgress>           // 班级平均进度（完成率）
│   └── <AvgAccuracy>           // 班级平均正确率
├── <StudentTable>              // 学生表格区域
│   ├── <CustomColumns>         // 支持字段自定义显示
│   └── <SortControl>           // 各字段支持点击排序
└── <ExportButton>              // 导出按钮
```

---

## 三、交互行为

### 1. 顶部任务信息区域

- **班级切换**：
  - 单班：展示名称
  - 多班：点开抽屉，切换班级后刷新所有数据（含高亮学生、表格数据）
- **素材切换**：
  - 单素材：仅展示“全部（1）”
  - 多素材：点开抽屉，展示各素材名称（按序号排序），切换刷新数据

### 2. 建议鼓励 / 关注学生

- 自动标注，基于当前素材内的完成度/正确率等指标判断
- 点击头像/姓名 ➜ 抽屉中展示该学生学习记录，可鼓励、推送消息

### 3. 班级整体数据

- 平均完成进度（完成率）
- 平均正确率
- 源数据来自当前素材、当前班级下所有学生的统计

### 4. 学生任务数据表格

- 展示字段（默认展示）：
  - 姓名、学习能量、完成进度、正确率
- 可选展示字段（自定义设置中勾选）：
  - 答题难度、错题数、答题数、用时
- 排序：
  - 点击表头字段支持升序 / 降序排序（默认按 UID 顺序）
- 特殊标记：
  - 建议鼓励学生：绿色标记
  - 建议关注学生：红色标记
  - 一键鼓励/提醒后，显示“已鼓励/已提醒”
- 数据异常自动飘色（如低于班均 20%，或全班倒数 10%）：
  - 最低：学习能量、进度、正确率
  - 最多：错题数
  - 最少：答题数、用时

### 5. 操作栏

- **查看详情**：点击后跳转至学生作业报告页 `/homework/student/[studentId]?taskId=xxx`
- **导出数据**：点击后导出当前班级当前素材下的学生列表（含所有列）

---

## 四、状态管理

| signal 名称 | 含义 |
|:------------|:-----|
| `currentTaskSignal` | 当前任务数据 |
| `currentClassIdSignal` | 当前班级 ID |
| `currentContentIdSignal` | 当前素材 ID |
| `studentListSignal` | 当前表格内学生数据 |
| `highlightedStudentsSignal` | 建议鼓励/关注学生数据 |
| `visibleColumnsSignal` | 用户自定义勾选的字段列 |
| `sortStateSignal` | 当前表格排序字段与方向 |

---

## 五、样式与动效

- 鼓励/关注学生：使用**浅绿/浅红标签**
- 低表现数据项：字体/背景自动飘色
- 鼠标 hover 行/列：淡灰背景提示
- 表格分页：使用统一分页组件（尾部固定）
- 抽屉交互：淡入淡出动效（Tailwind + Framer Motion）

---

## 六、边界处理

- 若当前素材下无任何学生数据：
  - 展示空状态组件 `<EmptyStudentTable />`
- 若某学生未完成任务：
  - 显示进度为 `0%`，其他项置灰或显示`--`

---

## 七、后端数据依赖

- 获取当前任务下所有学生的表现数据（带班级和素材范围参数）
- 获取鼓励/关注学生推荐列表
- 导出数据 API：按当前筛选返回完整表格数据 CSV

---

如你后续还需要拆出「答题结果」或「学生提问」模块说明，我也可以分别给你按这个格式整理出来～  
要不要我也顺手画一个这块的组件分布图或者跳转交互图？💡