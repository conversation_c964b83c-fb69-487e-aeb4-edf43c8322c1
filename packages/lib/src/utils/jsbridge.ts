/**
 * JSBridge - H5与Native交互的桥接库
 * 提供同步和异步API调用功能，以及接收Native回调的能力
 */
"use client";
// 定义API白名单，只允许调用这些API
export const ALLOWED_API_METHODS = [
  "getAppInfo",
  "getDeviceInfo",
  "requestPermission",
  "checkPermission",
  "startAsrWithRecord",
  "stopAsrWithRecord",
  "finishTalking",
  "reportEvent",
  "openScheme",
  "cacheVideos",
  "addEventListener",
  "removeEventListener",
  "takePhotoAndCrop",
  "getNetworkHeaderParams",
  "lessonFinish",
  "lessonClose",
  "controlStatusBar",
  "pickImages",
  "closePage",
  "getStudentUserInfo",
  "surveillanceReport",
  "taskResourceLoadedHandler",
  // 可以根据需要添加其他允许的API
];

interface BridgeNativeInterface {
  invokeSync: (apiName: string, params: string) => string;
  invokeAsync: (apiName: string, params: string, callbackId: string) => void;
  receiveCallback: (callbackId: string, resultJson: string) => void;
}
// 定义事件监听器相关的类型
interface EventListenerParams {
  eventName: string;
  callback: CallbackFunction;
}

interface RemoveEventListenerParams {
  eventName: string;
  callbackId: string;
}

// 回调函数类型定义
type CallbackFunction = (result: string) => void;

class JSBridge {
  // 存储回调ID和回调函数的映射
  private callbacks: Map<string, CallbackFunction> = new Map();

  // 回调ID计数器
  private callbackCounter: number = 0;

  // 存储监听器处理程序和回调函数的映射
  private listeners: Map<string, Array<string>> = new Map();

  /**
   * 检查API是否在允许列表中
   * @param apiName API名称
   * @returns 如果API在白名单中返回true，否则返回false
   */
  private isApiAllowed(apiName: string): boolean {
    return ALLOWED_API_METHODS.includes(apiName);
  }

  private parseResult<T>(result: string): T {
    const json = JSON.parse(result);
    if (json.code === 0) {
      return json.data as T;
    } else {
      return {} as T;
    }
  }

  /**
   * 生成唯一的回调ID
   */
  private generateCallbackId(): string {
    return `cb_${Date.now()}_${this.callbackCounter++}`;
  }

  /**
   * 同步调用Native API
   * @param apiName API名称
   * @param params 参数对象
   * @returns Native返回的结果
   */
  public invokeSync<T>(apiName: string, params?: object): T | null {
    // 检查API是否在允许列表中
    if (!this.isApiAllowed(apiName)) {
      console.error(`API不在允许列表中: ${apiName}`);
      throw new Error(`不允许调用此API: ${apiName}`);
    }

    // 检查Android桥是否可用
    if (this.isAndroidBridgeAvailable()) {
      try {
        return this.parseResult<T>(
          window.AndroidBridge.invokeSync(apiName, JSON.stringify(params))
        );
      } catch (error) {
        console.error(`JSBridge同步调用出错: ${apiName}`, error);
        // throw error;
        return null;
      }
    } else {
      // console.warn(`AndroidBridge不可用，无法调用: ${apiName}`);
      return null;
    }
  }

  /**
   * 异步调用Native API
   * @param apiName API名称
   * @param params 参数对象
   * @param persist 是否持久化回调
   * @returns Promise对象，包含Native返回的结果
   */
  public invokeAsync<T>(
    apiName: string,
    params?: object,
    persist: boolean = false
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.isApiAllowed(apiName)) {
        return reject(new Error(`不允许调用此API: ${apiName}`));
      }

      if (!this.isAndroidBridgeAvailable()) {
        return reject(new Error("AndroidBridge不可用"));
      }

      const callbackId = this.generateCallbackId();

      // 根据API类型处理不同的调用逻辑
      switch (apiName) {
        case "addEventListener":
          return this.handleAddEventListener<T>(
            callbackId,
            params as EventListenerParams,
            resolve,
            reject
          );
        case "removeEventListener":
          return this.handleRemoveEventListener<T>(
            callbackId,
            params as EventListenerParams,
            resolve,
            reject
          );
        default:
          return this.handleDefaultAsync<T>(
            callbackId,
            apiName,
            params,
            persist,
            resolve,
            reject
          );
      }
    });
  }

  /**
   * 处理添加事件监听器
   */
  private handleAddEventListener<T>(
    callbackId: string,
    params: EventListenerParams,
    resolve: (value: T) => void,
    reject: (reason: unknown) => void
  ): void {
    const { eventName, callback } = params;

    if (this.listeners.has(eventName)) {
      this.listeners.get(eventName)?.push(callbackId);
    } else {
      this.listeners.set(eventName, [callbackId]);
    }
    console.log("handleAddEventListener", { eventName, callbackId });

    try {
      // 存储事件回调
      this.callbacks.set(callbackId, (result) => {
        try {
          callback(result);
        } catch (error) {
          console.error(`事件回调执行错误 [${eventName}]:`, error);
        }
      });

      // 调用Native方法
      window.AndroidBridge.invokeAsync(
        "addEventListener",
        JSON.stringify({ eventName }),
        callbackId
      );

      resolve(callbackId as unknown as T);
    } catch (error) {
      this.callbacks.delete(callbackId);
      if (error instanceof Error) {
        reject(new Error(`添加事件监听器失败 [${eventName}] ${error.message}`));
      }
      console.log(error);
    }
  }

  /**
   * 处理移除事件监听器
   */
  private handleRemoveEventListener<T>(
    callbackId: string,
    params: EventListenerParams,
    resolve: (value: T) => void,
    reject: (reason: unknown) => void
  ): void {
    const { eventName } = params;
    // 移除事件监听对应的回调ID
    const addedCallbackId = this.listeners.get(eventName)?.pop();
    console.log("handleRemoveEventListener", {
      eventName,
      callbackId,
      addedCallbackId,
    });

    try {
      this.callbacks.set(callbackId, (result) => {
        try {
          const data = this.parseResult<T>(JSON.stringify(result));
          console.log("handleRemoveEventListener callback data", {
            callbackId,
            data,
          });
          resolve(data);
          // 清理回调
          this.callbacks.delete(callbackId);
        } catch (error) {
          reject(error);
        } finally {
          this.callbacks.delete(callbackId);
        }
      });
      window.AndroidBridge.invokeAsync(
        "removeEventListener",
        JSON.stringify({ eventName, callbackId: addedCallbackId }),
        callbackId
      );
    } catch (error) {
      if (error instanceof Error) {
        reject(new Error(`移除事件监听器失败 [${eventName}] ${error.message}`));
      } else {
        reject(new Error(`移除事件监听器失败 [${eventName}]`));
      }
    }
  }

  /**
   * 处理默认的异步调用
   */
  private handleDefaultAsync<T>(
    callbackId: string,
    apiName: string,
    params: object | undefined,
    persist: boolean,
    resolve: (value: T) => void,
    reject: (reason: unknown) => void
  ): void {
    try {
      // 注册回调函数
      this.callbacks.set(callbackId, (result) => {
        try {
          const data = this.parseResult<T>(JSON.stringify(result));
          resolve(data);
        } catch (error) {
          reject(error);
        } finally {
          if (!persist) {
            this.callbacks.delete(callbackId);
          }
        }
      });

      // 调用Native方法
      window.AndroidBridge.invokeAsync(
        apiName,
        JSON.stringify(params),
        callbackId
      );
    } catch (error) {
      if (!persist) {
        this.callbacks.delete(callbackId);
      }
      if (error instanceof Error) {
        reject(new Error(`异步调用失败 [${apiName}] ${error.message}`));
      } else {
        reject(new Error(`异步调用失败 [${apiName}]`));
      }
    }
  }
  /**
   * 接收Native回调
   * 此方法由Native调用，用于传递异步调用的结果
   * @param callbackId 回调ID
   * @param resultJson 结果JSON字符串
   */
  public receiveCallback(callbackId: string, resultJson: string): void {
    const callback = this.callbacks.get(callbackId);
    if (callback) {
      try {
        let result;
        try {
          // 尝试解析JSON
          result = JSON.parse(resultJson);
        } catch (e) {
          console.error(`解析回调结果出错: ${callbackId}`, e);
          // 如果解析失败，使用原始字符串
          result = resultJson;
        }

        // 执行回调
        callback(result);
      } catch (error) {
        console.error(`执行回调出错: ${callbackId}`, error);
      }
    }
  }

  /**
   * 检查AndroidBridge是否可用
   */
  public isAndroidBridgeAvailable(): boolean {
    return (
      typeof window !== "undefined" &&
      window.AndroidBridge !== undefined &&
      typeof window.AndroidBridge.invokeSync === "function" &&
      typeof window.AndroidBridge.invokeAsync === "function"
    );
  }

  /**
   * 初始化JSBridge
   * 将receiveCallback方法挂载到全局AndroidBridge对象上
   */
  public init(): void {
    if (typeof window !== "undefined") {
      // 初始化AndroidBridge对象（如果不存在）
      if (!window.AndroidBridge) {
        window.AndroidBridge = {} as BridgeNativeInterface;
      }
      // 挂载receiveCallback方法
      // 这样Native就可以通过window.AndroidBridge.receiveCallback调用
      // if (!window.AndroidBridge.receiveCallback) {
      window.AndroidBridge.receiveCallback = (
        callbackId: string,
        resultJson: string
      ) => {
        this.receiveCallback(callbackId, resultJson);
      };
      // }
      console.log("JSBridge初始化完成，API白名单已启用");
    }
  }
}

// 创建单例实例
const bridge = new JSBridge();

// 在生产环境自动初始化
if (typeof window !== "undefined") {
  bridge.init();
}

// 为TypeScript类型定义扩展Window接口
declare global {
  interface Window {
    AndroidBridge: BridgeNativeInterface;
  }
}

export default bridge;
