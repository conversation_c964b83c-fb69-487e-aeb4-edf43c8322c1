import {
  getStorageItem,
  removeStorageItem,
  setStorageItem,
} from "@repo/lib/utils/local-storage";

export class LocalCourseProgress {
  private storageKey: string;

  constructor(knowledgeId: number, lessonId: number) {
    this.storageKey = `course.progress:${knowledgeId}-${lessonId}`;
  }

  private getMap<T>(): Map<number, T> {
    const data = getStorageItem<Array<[number, T]>>(this.storageKey, []);
    return new Map(data);
  }

  private saveMap<T>(map: Map<number, T>): void {
    const data = Array.from(map.entries());
    setStorageItem(this.storageKey, data);
  }

  save<T>(index: number, content: T): void {
    const map = this.getMap<T>();
    map.set(index, content);
    this.saveMap(map);
  }

  load<T>(index: number, defaultValue: T): T {
    const map = this.getMap<T>();
    return map.get(index) ?? defaultValue;
  }

  clear(index: number): void {
    const map = this.getMap();
    map.delete(index);
    this.saveMap(map);
  }

  clearAll(): void {
    removeStorageItem(this.storageKey);
  }
}
