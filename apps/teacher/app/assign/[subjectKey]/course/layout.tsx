"use client";
import { AssignCourseProvider, useAssignCourseState } from "./store";
import {
  AssignInitialStateProvider,
  useAssignInitialState,
} from "./store/useAssignInitialState";

export function AssignCourseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const state = useAssignCourseState();

  if (!state) {
    return null;
  }
  return <AssignCourseProvider value={state}>{children}</AssignCourseProvider>;
}

export default function AssignCourseLayoutWithCache({
  children,
}: {
  children: React.ReactNode;
}) {
  const state = useAssignInitialState();

  if (!state.isInitialed.value) {
    return null;
  }

  return (
    <AssignInitialStateProvider value={state}>
      <AssignCourseLayout>{children}</AssignCourseLayout>
    </AssignInitialStateProvider>
  );
}
