import { NodeInfo, NodeType } from '@/components/GradeTree';
import { ScheduleContext } from '@/pages/partner-school/schoolManagement/contexts/ScheduleContext';
import { getCurrentGrade } from '@/pages/partner-school/utils/schedule';
import { Button } from 'antd';
import React, { useContext } from 'react';
import ScheduleImporter from '../ScheduleImporter';

const ScheduleButtons: React.FC = () => {
  const {
    selectedNodeInfo,
    scheduleData,
    editMode,
    schoolInfo,
    semesterInfo,
    handleImportSuccess,
    setEditMode,
  } = useContext(ScheduleContext);
  if (!selectedNodeInfo || !scheduleData) return null;

  const { nodeType, nodeInfo } = selectedNodeInfo;
  const isGradeView = nodeType === NodeType.Grade;
  const isClassView = nodeType === NodeType.Class;
  const isEditingTemplate = editMode === 'grade';
  const isEditingClass = editMode === 'class';
  const currentEditMode = isGradeView ? isEditingTemplate : isEditingClass;

  const templateName = `${schoolInfo?.schoolName}_${getCurrentGrade(nodeType, nodeInfo)}_${semesterInfo?.semester_name}_下载模板.xlsx`;
  if (!scheduleData && isClassView) return null;
  if (currentEditMode) return null;

  return (
    <div className="flex space-x-2">
      <ScheduleImporter
        templateName={templateName}
        templateId={scheduleData?.tpl_info?.template_id}
        type={isGradeView ? 'grade' : 'class'}
        nodeInfo={nodeInfo as NodeInfo<NodeType>}
        nodeType={nodeType}
        onSuccess={handleImportSuccess}
      />
      <Button type="primary" onClick={() => setEditMode(isGradeView ? 'grade' : 'class')}>
        {isGradeView ? '编辑模版' : '编辑课表'}
      </Button>
    </div>
  );
};

export default ScheduleButtons;
