"use client";
import { cn } from "@/utils/utils";
import { AssignCheckboxButton } from "./assign-checkbox-button";
// import { AssignButton } from "./assign-button";
import { TargetJobClass } from "@/types";
import { ClassStudentListItem } from "@/types/assign/student";
import { Signal, useComputed } from "@preact-signals/safe-react";
import { memo, useMemo } from "react";
import { TchDrawer } from "../../ui/tch-drawer";
import { AssignStudentCard } from "./assign-student-card";

export const AssignTargets = memo(function AssignTargets({
  classList,
  checkedClasses,
  toggleClass,
  checkedClassStudentList,

  className,
  style,
}: {
  classList: TargetJobClass[];
  checkedClasses: Signal<TargetJobClass[]>;
  toggleClass: (jobClass: TargetJobClass) => void;
  // TODO: 少个signal request
  checkedClassStudentList?: ClassStudentListItem[];

  className?: string;
  style?: React.CSSProperties;
}) {
  // 选中的班级id
  const selectedClassIdsSet = useComputed(() => {
    return new Set(
      Array.isArray(checkedClasses.value)
        ? checkedClasses.value.map((item) => item.jobClass)
        : []
    );
  });

  const totalSelectedStudents = useMemo(() => {
    return (
      checkedClassStudentList?.reduce((prev, item) => {
        return prev + (item.students?.length ?? 0);
      }, 0) ?? 0
    );
  }, [checkedClassStudentList]);

  const enabledGrade = useComputed(() => {
    return checkedClasses.value.length > 0
      ? checkedClasses.value[0].jobGrade
      : undefined;
  });

  const SelectedStudentsTrigger = () => {
    return (
      <div className="inline-block cursor-pointer select-none text-sm font-normal text-indigo-600">
        查看已选学生({totalSelectedStudents})
      </div>
    );
  };

  return (
    <div
      className={cn("flex flex-wrap items-center gap-3", className)}
      style={style}
    >
      {classList?.map((item) => (
        <AssignCheckboxButton
          key={item.jobClass}
          content={item.jobGradeName + item.name}
          checked={selectedClassIdsSet.value.has(item.jobClass)}
          disabled={
            !!enabledGrade.value && item.jobGrade !== enabledGrade.value
          }
          onClick={() => toggleClass(item)}
        />
      ))}
      {/* <AssignButton variant="outline">自定义选择</AssignButton> */}
      {checkedClasses.value.length > 0 && (
        <TchDrawer
          title={`已选择的学生(${totalSelectedStudents})`}
          trigger={<SelectedStudentsTrigger />}
        >
          <div className="rounded-2xl bg-white px-6 py-3">
            <AssignStudentCard classStudentList={checkedClassStudentList} />
          </div>
        </TchDrawer>
      )}
    </div>
  );
});
