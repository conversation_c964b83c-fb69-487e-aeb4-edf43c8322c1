
# @repo/lib 工具库

## 概述

`@repo/lib` 是一个通用工具库，提供了一系列在 React 应用中常用的工具函数、自定义 Hooks 和类型定义。该库专注于解决网络请求、设备交互、存储管理和文件处理等常见开发需求，旨在提高开发效率并确保代码质量和一致性。

## 功能特性

- JSBridge 与原生应用的双向通信
- Socket.io 客户端封装
- 文件上传下载工具
- 本地存储(localStorage)工具
- HTTP 请求封装
- 设备信息获取
- 通用类型定义

## 工具模块

| 模块类别 | 描述 |
|----------|------|
| `hooks` | 提供一系列自定义 React Hooks |
| `utils` | 工具函数集合 |
| `types` | TypeScript 类型定义 |

## 功能说明

### Hooks

#### useBridge

提供与原生应用 JSBridge 交互的 React Hook。

| 方法/属性 | 类型 | 说明 |
|-----------|------|------|
| `isSupported` | `boolean` | 当前环境是否支持 JSBridge |
| `callSync` | `<T>(apiName: string, params?: object) => T \| null` | 同步调用原生 API |
| `callAsync` | `<T>(apiName: string, params?: object, persist?: boolean) => Promise<T>` | 异步调用原生 API |
| `isApiAllowed` | `(apiName: string) => boolean` | 检查 API 是否在白名单内 |

**使用场景**：
- 获取设备信息
- 请求设备权限
- 控制原生功能（如状态栏）
- 调用原生 API

```tsx
import { useBridge } from "@repo/lib/hooks/use-bridge";

function DeviceInfoComponent() {
  const { isSupported, callSync } = useBridge();
  
  const getDeviceInfo = () => {
    if (isSupported) {
      const deviceInfo = callSync("getDeviceInfo");
      console.log(deviceInfo);
    }
  };
  
  return (
    <button onClick={getDeviceInfo}>获取设备信息</button>
  );
}
```

#### useScreen

获取当前设备屏幕尺寸的 Hook。

| 返回值 | 类型 | 说明 |
|--------|------|------|
| `screen` | `{ width: number; height: number }` | 屏幕尺寸对象 |

**使用场景**：
- 响应式布局调整
- 根据屏幕尺寸渲染不同组件
- 计算元素尺寸

```tsx
import useScreen from "@repo/lib/hooks/use-screen";

function ResponsiveComponent() {
  const { width, height } = useScreen();
  
  return (
    <div>
      <p>屏幕宽度: {width}px</p>
      <p>屏幕高度: {height}px</p>
      {width < 768 ? <MobileView /> : <DesktopView />}
    </div>
  );
}
```

#### useSocket

WebSocket 客户端的 React Hook 封装，基于 socket.io-client。

| 方法/属性 | 类型 | 说明 |
|-----------|------|------|
| `socket` | `Socket \| null` | Socket.io 实例 |
| `isConnected` | `boolean` | 连接状态 |
| `error` | `Error \| null` | 连接错误 |
| `connect` | `() => void` | 连接方法 |
| `disconnect` | `() => void` | 断开连接方法 |
| `emit` | `(event: string, data?: object) => void` | 发送事件 |
| `on` | `(event: string, callback: (data: object) => void) => void` | 监听事件 |
| `off` | `(event: string) => void` | 移除事件监听 |

**使用场景**：
- 即时通信
- 实时数据更新
- 聊天应用
- 协作编辑

```tsx
import { useSocket } from "@repo/lib/hooks/use-socket";

function ChatComponent() {
  const { socket, isConnected, emit, on } = useSocket({
    url: "https://api.example.com",
    options: { autoConnect: true }
  });
  
  useEffect(() => {
    if (isConnected) {
      on("message", (data) => {
        console.log("收到消息:", data);
      });
    }
    
    return () => {
      if (isConnected) {
        off("message");
      }
    };
  }, [isConnected, on, off]);
  
  const sendMessage = (message) => {
    emit("message", { content: message });
  };
  
  return (
    <div>
      <p>连接状态: {isConnected ? "已连接" : "未连接"}</p>
      <button onClick={() => sendMessage("你好")}>发送消息</button>
    </div>
  );
}
```

### Utils

#### jsbridge

提供与原生应用进行通信的 JSBridge 工具。

| 方法 | 类型 | 说明 |
|------|------|------|
| `invokeSync` | `<T>(apiName: string, params?: object) => T` | 同步调用原生 API |
| `invokeAsync` | `<T>(apiName: string, params?: object, persist?: boolean) => Promise<T>` | 异步调用原生 API |
| `init` | `() => void` | 初始化 JSBridge |

**使用场景**：
- 与原生应用通信
- 调用原生功能
- 接收原生应用回调

```ts
import bridge from "@repo/lib/utils/jsbridge";

// 同步调用
const deviceInfo = bridge.invokeSync("getDeviceInfo");

// 异步调用
bridge.invokeAsync("requestPermission", { permission: "CAMERA" })
  .then(result => console.log("权限结果:", result))
  .catch(error => console.error("权限请求失败:", error));
```

#### device

设备相关工具函数。

| 函数 | 类型 | 说明 |
|------|------|------|
| `getDeviceId` | `() => string` | 获取设备 ID |
| `getDeviceInfo` | `() => DeviceInfo \| null` | 获取完整设备信息 |
| `getScreenSize` | `() => { width: number; height: number } \| null` | 获取屏幕尺寸 |
| `getAppInfo` | `() => AppInfo \| null` | 获取应用信息 |
| `controlStatusBar` | `(isVisible: boolean) => void` | 控制状态栏显示/隐藏 |

**使用场景**：
- 获取设备信息进行适配
- 设备标识
- 应用版本检查
- UI适配

```ts
import { getDeviceInfo, controlStatusBar } from "@repo/lib/utils/device";

// 获取设备信息
const deviceInfo = getDeviceInfo();
console.log(`设备型号: ${deviceInfo?.deviceModel}`);

// 隐藏状态栏
controlStatusBar(false);
```

#### local-storage

提供安全的本地存储工具函数。

| 函数 | 类型 | 说明 |
|------|------|------|
| `getStorageItem` | `<T>(key: string, defaultValue: T \| null) => T \| null` | 获取存储数据 |
| `setStorageItem` | `<T>(key: string, value: T) => boolean` | 设置存储数据 |
| `removeStorageItem` | `(key: string) => boolean` | 移除存储数据 |
| `clearStorage` | `() => boolean` | 清空所有存储 |
| `getStorageKeys` | `() => string[]` | 获取所有存储键名 |
| `createStorageKey` | `(key: string, courseId: string) => string` | 创建带前缀的存储键 |

**使用场景**：
- 用户设置保存
- 应用状态持久化
- 缓存数据
- 用户偏好存储

```ts
import { 
  getStorageItem, 
  setStorageItem,
  createStorageKey
} from "@repo/lib/utils/local-storage";

// 保存用户设置
setStorageItem("user_settings", { theme: "dark", fontSize: "medium" });

// 获取带有课程 ID 前缀的数据
const courseKey = createStorageKey("progress", "course-123");
const progress = getStorageItem(courseKey, 0);
```

#### fetcher

HTTP 请求工具封装。

| 函数 | 类型 | 说明 |
|------|------|------|
| `get` | `<T>(url: string, { query }?) => Promise<T>` | GET 请求 |
| `post` | `<T>(url: string, { arg }?) => Promise<T>` | POST 请求 |

**使用场景**：
- API 请求
- 数据获取
- 表单提交
- 与后端通信

```ts
import { get, post } from "@repo/lib/utils/fetcher";

// GET 请求
async function fetchUsers() {
  const users = await get("/api/users", { 
    query: { limit: "10", offset: "0" } 
  });
  return users;
}

// POST 请求
async function createUser(userData) {
  const result = await post("/api/users", { arg: userData });
  return result;
}
```

#### download

文件下载工具。

| 函数 | 类型 | 说明 |
|------|------|------|
| `download` | `(url: string, fileName: string) => Promise<boolean>` | 下载文件并自动检测文件类型 |

**使用场景**：
- 下载文件
- 保存生成的内容
- 导出数据

```ts
import { download } from "@repo/lib/utils/download";

async function downloadImage(url) {
  const success = await download(url, "my-image");
  if (success) {
    console.log("下载成功");
  } else {
    console.error("下载失败");
  }
}
```

#### oss

阿里云 OSS 上传工具。

| 函数 | 类型 | 说明 |
|------|------|------|
| `upload` | `({ file, signature }) => Promise<{ url: string, error: string }>` | 上传文件到 OSS |

**使用场景**：
- 文件上传
- 图片上传
- 资源存储

```ts
import { upload } from "@repo/lib/utils/oss";

async function uploadFile(file) {
  const result = await upload({
    file,
    signature: {
      url: "/api/oss/signature",
      params: { type: "image" }
    }
  });
  
  if (result.url) {
    console.log("上传成功，文件地址:", result.url);
  } else {
    console.error("上传失败:", result.error);
  }
}
```

### 类型

#### JSBridgeSchema

JSBridge 相关类型定义。

| 类型 | 描述 |
|------|------|
| `DeviceInfo` | 设备信息接口 |
| `PermissionInfo` | 权限信息接口 |
| `AppInfo` | 应用信息接口 |

#### OssSignature

OSS 签名相关类型定义。

| 类型 | 描述 |
|------|------|
| `OssSignature` | OSS 签名信息接口 |
| `PolicyToken` | OSS Policy Token 接口 |

#### 通用类型工具

提供通用 TypeScript 类型工具。

| 类型 | 描述 |
|------|------|
| `GetProps<T>` | 获取组件 Props 类型 |
| `GetProp<T, P>` | 获取组件特定 Prop 类型 |
| `GetRef<T>` | 获取组件 Ref 类型 |
| `AnyObject` | 任意对象类型 |

## 最佳实践

1. **错误处理**：总是在调用 API 时使用 try/catch 处理可能的错误
2. **类型安全**：利用 TypeScript 类型定义保证代码类型安全
3. **环境检查**：在使用浏览器或设备特定 API 前检查环境支持情况
4. **按需引入**：仅导入需要的工具和函数，减少打包体积
5. **安全访问**：使用可选链和空值合并等语法确保代码健壮性

## 常见问题

### 在 SSR 环境中使用会报错

许多工具函数依赖于浏览器 API，请确保在组件挂载后使用或添加服务端检查：

```tsx
// 安全使用
import { useEffect } from "react";
import { getDeviceInfo } from "@repo/lib/utils/device";

function Component() {
  useEffect(() => {
    const deviceInfo = getDeviceInfo();
    // 使用 deviceInfo
  }, []);
  
  return <div>组件内容</div>;
}
```

### JSBridge 调用不起作用

确保在支持 JSBridge 的环境（如原生应用 WebView）中运行，并检查 API 是否在白名单中：

```tsx
import { useBridge } from "@repo/lib/hooks/use-bridge";

function Component() {
  const { isSupported, isApiAllowed, callSync } = useBridge();
  
  const callApi = () => {
    if (!isSupported) {
      console.warn("当前环境不支持 JSBridge");
      return;
    }
    
    if (!isApiAllowed("myApi")) {
      console.warn("API 不在白名单中");
      return;
    }
    
    callSync("myApi");
  };
  
  return <button onClick={callApi}>调用 API</button>;
}
```

## 相关模块

- [@repo/ui](/ui): UI 组件库
- [@repo/core](/core): 核心业务库
