import { Space, Typography } from 'antd';
import { FC } from 'react';

const { Paragraph, Text, Title } = Typography;

export interface ExerciseBaseInfoProps {
  node?: API.Common.BaseTreeNodeAntdType;
  copyable?: boolean;
}
const ExerciseBaseInfo: FC<ExerciseBaseInfoProps> = ({ node, copyable = true }) => {
  const { title, key } = node ?? {};
  return (
    <Space align="end" style={{ flex: 1 }}>
      <Title level={5} style={{ margin: 0, fontWeight: 'normal' }}>
        {title}
      </Title>
      <Space align="center">
        <Text type="secondary">ID: </Text>
        <Paragraph type="secondary" copyable={copyable} style={{ margin: 0 }}>
          {key}
        </Paragraph>
      </Space>
    </Space>
  );
};
export default ExerciseBaseInfo;
