import { TaskContext } from "@/app/homework/[id]/_context/task-context";
import { FEEDBACK_TYPE } from "@/enums";
import { FeedbackSource, useFeedbackByType } from "@/hooks/useReportFeedback";
import { But<PERSON> } from "@/ui/tch-button";
import { QaContentType } from "@repo/core/views/tch-question-view";
import { memo, useContext } from "react";

function ReportButton({ question }: { question: QaContentType }) {
  const { routeToFeedback } = useFeedbackByType();
  const { taskData, activeTab, viewMode, classData } = useContext(TaskContext);

  return (
    <Button
      type="default"
      size="md"
      radius="full"
      className="h-8.5 font-normal"
      onClick={() => {
        routeToFeedback(FEEDBACK_TYPE.QUESTION, {
          questionId: question.questionId,
          feedbackQuestionVersionId: question.questionVersionId,
          feedbackSource: FeedbackSource.REPORT,
          subjectId: classData.value?.subject ?? -1,
          feedbackPhaseId: question.phase ?? -1,
          tab: activeTab.value,
          view: viewMode.value,
        });
      }}
    >
      反 馈
    </Button>
  );
}

export default memo(ReportButton);
