# Press Button 组件

一个支持多种配色方案的按钮组件，包括练习主题色配色。

## 特性

- 🎨 **多种配色方案**：支持 orange、red、green、gray、white 等基础配色
- 🎯 **练习主题色**：支持根据 `studyType` 自动应用练习主题色
- 📏 **多种尺寸**：支持 sm、md、lg 三种尺寸
- 🎭 **主次样式**：支持 primary 和 secondary 两种样式
- ♿ **无障碍支持**：支持禁用状态和键盘导航
- ⚡ **性能优化**：支持点击延迟和防抖

## 基础用法

```tsx
import { Button } from '@repo/ui/src/components/press-button';

// 基础按钮
<Button>默认按钮</Button>

// 不同颜色
<Button color="orange">橙色按钮</Button>
<Button color="orange-light">浅橙色按钮</Button>
<Button color="red">红色按钮</Button>
<Button color="green">绿色按钮</Button>
<Button color="gray">灰色按钮</Button>
<Button color="white">白色按钮</Button>
```

## 练习主题色用法

```tsx
import { Button } from '@repo/ui/src/components/press-button';
import { StudyType } from '@repo/core/src/enums/question';

// AI课主题色
<Button color="study-theme" studyType={StudyType.AI_COURSE}>
  AI课按钮
</Button>

// 巩固练习主题色
<Button color="study-theme" studyType={StudyType.REINFORCEMENT_EXERCISE}>
  巩固练习按钮
</Button>

// 拓展练习主题色
<Button color="study-theme" studyType={StudyType.EXPAND_EXERCISE}>
  拓展练习按钮
</Button>
```

## 样式变体

### 次要样式

```tsx
// 次要样式按钮
<Button color="study-theme" studyType={StudyType.AI_COURSE} secondary>
  AI课次要按钮
</Button>
```

### 不同尺寸

```tsx
// 小尺寸
<Button color="study-theme" studyType={StudyType.AI_COURSE} size="sm">
  小按钮
</Button>

// 中等尺寸（默认）
<Button color="study-theme" studyType={StudyType.AI_COURSE} size="md">
  中等按钮
</Button>

// 大尺寸
<Button color="study-theme" studyType={StudyType.AI_COURSE} size="lg">
  大按钮
</Button>
```

## 高级功能

### 禁用状态

```tsx
<Button color="study-theme" studyType={StudyType.AI_COURSE} disabled>
  禁用按钮
</Button>
```

### 点击延迟

```tsx
<Button color="study-theme" studyType={StudyType.AI_COURSE} clickDelay={300}>
  延迟点击按钮
</Button>
```

### 自定义样式

```tsx
<Button 
  color="study-theme" 
  studyType={StudyType.AI_COURSE}
  style={{ backgroundColor: 'purple' }}
  className="custom-class"
>
  自定义样式按钮
</Button>
```

## 练习主题色配色方案

| 练习类型 | 主色调 | 背景色 | 文字色 | 说明 |
|---------|--------|--------|--------|------|
| AI课 | `#FEA026` | `#FEF8F4` | `#CC6204` | 温暖友好的学习体验 |
| 巩固练习 | `#FEA026` | `#FEF8F4` | `#CC6204` | 稳定可靠的练习环境 |
| 拓展练习 | `#84D64B` | `#E6FAF5` | `#5A9B2A` | 充满挑战的拓展学习 |

## API 参考

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `color` | `'orange' \| 'orange-light' \| 'red' \| 'green' \| 'gray' \| 'white' \| 'study-theme'` | `'orange'` | 按钮颜色 |
| `studyType` | `StudyType` | - | 练习类型（仅当 color 为 'study-theme' 时使用） |
| `secondary` | `boolean` | `false` | 是否为次要样式 |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | 按钮尺寸 |
| `showShadow` | `boolean` | `true` | 是否显示阴影 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `clickDelay` | `number` | `0` | 点击延迟时间（毫秒） |
| `className` | `string` | - | 自定义 CSS 类名 |
| `style` | `CSSProperties` | - | 自定义内联样式 |

### 事件

| 事件 | 类型 | 描述 |
|------|------|------|
| `onClick` | `(e: React.MouseEvent<HTMLButtonElement>) => void` | 点击事件 |

## 注意事项

1. **study-theme 配色**：当使用 `color="study-theme"` 时，必须提供 `studyType` 属性
2. **禁用状态**：禁用时按钮会自动使用灰色配色，忽略 `color` 和 `studyType` 设置
3. **阴影效果**：按钮支持按下时的阴影消失效果，可通过 `showShadow` 控制
4. **响应式设计**：按钮在不同屏幕尺寸下都能保持良好的显示效果

## 最佳实践

1. **一致性**：在同一个练习页面中，建议统一使用 `study-theme` 配色
2. **可访问性**：为按钮提供有意义的文本内容，避免使用图标按钮
3. **性能**：对于频繁点击的按钮，可以设置适当的 `clickDelay` 防止误触
4. **样式覆盖**：优先使用 `className` 进行样式定制，避免使用 `style` 属性