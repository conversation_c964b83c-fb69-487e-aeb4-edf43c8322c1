import { MergedReference } from "@repo/core/types/data/comment";
import { cn } from "@repo/ui/lib/utils";
import { FC, PropsWithChildren } from "react";

export const CommentUnderline: FC<
  PropsWithChildren<{
    mergedReference?: MergedReference[string][string][number];
    onClick?: (ref: MergedReference[string][string][number]) => void;
    className?: string;
    lineId?: string;
    textureId?: string;
    rootId?: string;
    rootTitle?: string;
    charId?: number;
    content?: string;
    isAllUnderlined?: boolean;
  }>
> = ({
  className,
  children,
  onClick,
  mergedReference,
  lineId,
  textureId,
  rootId,
  rootTitle,
  charId,
  content,
  isAllUnderlined,
}) => (
  <span
    className={cn("relative", className)}
    onClick={(e) => {
      if (mergedReference) {
        if (!isAllUnderlined) {
          e.stopPropagation();
          e.preventDefault();
        }
        onClick?.(mergedReference);
      }
    }}
    data-line-id={lineId}
    data-texture-id={textureId}
    data-char-id={charId}
    data-content={content}
    data-root-id={rootId}
    data-root-title={rootTitle}
    data-underline={mergedReference ? true : undefined}
    data-replyed={mergedReference?.replyed}
    data-last-count={
      (mergedReference?.isLast &&
        mergedReference.end === charId &&
        mergedReference.commentCount) ||
      undefined
    }
  >
    {children}
  </span>
);
