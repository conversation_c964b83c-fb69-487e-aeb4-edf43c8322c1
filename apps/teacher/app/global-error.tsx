"use client";

import * as Sentry from "@sentry/nextjs";
import Error from "next/error";

import forbidden from "@/public/images/403.png";
import { Button } from "@/ui/tch-button";
import Image from "next/image";
import { useEffect } from "react";

/**
 * 简化的全局错误处理器 - 捕获应用级别的错误
 * 注意：global-error.tsx 必须包含 html 和 body 标签
 */
export default function GlobalError({
  error,
  reset,
}: {
  /** 错误对象 */
  error: Error & { digest?: string };
  /** 重置函数 */
  reset: () => void;
}) {
  useEffect(() => {
    Sentry.captureException(error);

    if (process.env.NODE_ENV === "development") {
      console.error(error);
    }
  }, [error]);

  const handleGoHome = () => {
    window.location.href = "/course";
  };

  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>小鹿爱学</title>
        <link rel="icon" href="/favicon.ico" sizes="32*32" />
      </head>
      <body>
        <div className="bg-fill-light flex h-screen flex-col items-center justify-center">
          {/* 错误图片 */}
          <Image src={forbidden} alt="系统错误" className="size-30" />

          {/* 错误消息 */}
          <div
            className="text-gray-4 mt-2 text-sm/normal"
            style={{ textAlign: "center", marginBottom: "1rem" }}
          >
            系统出错，请尝试刷新页面或联系管理员
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button onClick={reset} type="default">
              重试
            </Button>
            <Button onClick={handleGoHome} type="primary">
              返回首页
            </Button>
          </div>

          {/* 页脚信息 */}
          <div className="footer hidden">
            <div>
              错误时间: {new Date().toLocaleString() + "   "}
              {error.digest && ` | 摘要: ${error.digest}`}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
