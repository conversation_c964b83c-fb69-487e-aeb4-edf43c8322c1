"use client";

import { ChevronLeft } from "lucide-react";
import { Sheet, SheetContent } from "@/ui/sheet";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { useState } from "react";
import { cn } from "@/utils/utils";
import { DrawerCard } from "../../../../../../../components/common/drawer-card";
// import { useStudentRemind } from "../hooks/useStudentRemind";
import { toast } from "@/ui/toast";

interface EvaluationDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentId?: number;
}

export function EvaluationDrawer({ open, onOpenChange }: EvaluationDrawerProps) {
  const [evaluationContent, setEvaluationContent] = useState("我回顾了：今天课堂整体表现很好，学习专注度和正确率都有明显提升，但对于xxx需要注意一下。");

  const handleSendEvaluation = () => {
    // 这里应该调用发送评价的API，目前模拟成功
    toast.success("评价发送成功", {
      description: "已成功发送学习评价"
    });
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className={cn(
          "gap-0 w-full sm:max-w-md overflow-y-auto px-6 bg-[#F4F7FE]",
          "flex flex-col"
        )}
        closeable={false}
      >
        {/* 抽屉头部 */}
        <div className="flex items-center justify-between py-5 sticky top-0 bg-[#F4F7FE] z-10">
          <div className="flex items-center gap-[0.625rem]">
            <div
              className="flex items-center justify-center cursor-pointer bg-white rounded-full w-9 h-9 shadow-sm"
              onClick={() => onOpenChange(false)}
            >
              <ChevronLeft className="h-5 w-5 text-[#64698a]" />
            </div>
            <span className="text-lg font-medium text-[#444963]">
              课堂学习评价
            </span>
          </div>
        </div>

        {/* 内容区域 */}
        <DrawerCard
          className="h-full"
          title="描述评价内容"
          footer={
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="h-9 rounded-[1.125rem]"
              >
                取 消
              </Button>
              <Button
                className="h-9 rounded-[1.125rem] bg-primary-2 hover:bg-primary-2/90"
                onClick={handleSendEvaluation}
                disabled={!evaluationContent.trim()}
              >
                发送评价
              </Button>
            </div>
          }
        >
          <div className="relative">
            <Textarea
              placeholder="我回顾了：今天课堂整体表现很好，学习专注度和正确率都有明显提升，但对于xxx需要注意一下。"
              className="min-h-[16rem] resize-none placeholder:text-gray-4 placeholder:text-sm"
              value={evaluationContent}
              onChange={(e) => setEvaluationContent(e.target.value)}
              maxLength={500}
            />
            <div className="absolute bottom-2 right-2 text-xs text-gray-400">{evaluationContent.length}/500</div>
          </div>
        </DrawerCard>
      </SheetContent>
    </Sheet>
  );
}
