"use client";
import { LOCALSTORAGE_SCHOOL_ID_KEY, LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { AuthContext } from "@/hooks";
import { useAppAuthEmitter } from "@/libs";
import { useLocalStorageState, useMemoizedFn, useMount } from "ahooks";
import to from "await-to-js";
import { useRouter } from "next/navigation";
import React from "react";

function AuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();

  const [token, setToken] = useLocalStorageState<string | null>(
    LOCALSTORAGE_TOKEN_KEY,
    {
      defaultValue: null,
    }
  );

  const [schoolId, setSchoolId] = useLocalStorageState<number | null>(
    LOCALSTORAGE_SCHOOL_ID_KEY,
    {
      defaultValue: null,
    }
  );

  const login = useMemoizedFn(
    ({ token, schoolId }: { token: string; schoolId: number }) => {
      setToken(token);
      setSchoolId(schoolId);
      router.replace("/course");
    }
  );

  const logout = useMemoizedFn(() => {
    setToken(null);
    setSchoolId(null);
    router.replace("/login");
  });

  /**
   * 统一处理认证失败 / 权限不足的重定向 （目前用于监听、处理在 axios 响应拦截器中发送的事件）
   */
  useAppAuthEmitter(logout);

  /**
   * 是否正在检查登录状态
   */
  const [isChecking, setIsChecking] = React.useState(true);

  /**
   * 检查登录状态，进行重定向
   */
  useMount(() => {
    const checkAuthAndRedirect = async () => {
      const pathname = window.location.pathname;
      const isLoginPage = pathname.startsWith("/login");

      if (token && isLoginPage) {
        router.replace("/course");
        return;
      }

      if (!token && !isLoginPage) {
        router.replace("/login");
        return;
      }
    };

    to(checkAuthAndRedirect());
    setIsChecking(false);
  });

  return (
    <AuthContext value={{ login, logout, token, schoolId }}>
      {isChecking ? null : children}
    </AuthContext>
  );
}

export default AuthProvider;
