export const validateFormListAnswer = (questionAnswerMode: number) => {
  const rules = [
    {
      validator: (_: any, value: any) => {
        if (!value || value.length === 0) {
          return Promise.reject('请输入答案');
        }
        if (value.length === 1) {
          return Promise.resolve();
        }
        // 检查每个填空点的答案
        for (const [index, item] of value.entries()) {
          switch (questionAnswerMode) {
            case 1: {
              // 单选题
              const singleChoiceItems = item.singleChoice?.items;
              if (!singleChoiceItems || singleChoiceItems.some((option: any) => !option.value)) {
                return Promise.reject(`请输入第${index + 1}个选项内容`);
              }
              break;
            }
            case 2: {
              // 多选题
              const multipleChoiceItems = item.multipleChoice?.items;
              if (
                !multipleChoiceItems ||
                multipleChoiceItems.some((option: any) => !option.value)
              ) {
                return Promise.reject(`请输入第${index + 1}个选项内容`);
              }
              break;
            }
            case 3: // 填空题
              if (!item.fillBlank) {
                return Promise.reject(`请输入第${index + 1}个填空内容`);
              }
              break;
            case 4: // 判断题
              if (!item.judge) {
                return Promise.reject(`请输入第${index + 1}个判断答案`);
              }
              break;
            case 5: // 简答题
              if (!item.shortAnswer) {
                return Promise.reject(`请输入第${index + 1}个解答答案`);
              }
              break;
          }
        }
        return Promise.resolve();
      },
    },
  ];
  return rules;
};

export const validateFormListAnswerItem = (type: number, questionAnswerMode: number) => {
  switch (type) {
    case 1:
      return [
        {
          required: true,
          validator: (_: any, value: any) => {
            if (questionAnswerMode !== 1) {
              return Promise.resolve();
            }
            if (!value?.items?.length) {
              return Promise.reject('请填写选项内容');
            }
            const hasEmptyValue = value.items.some(
              (item: { value: string }) => !item.value?.trim(),
            );
            if (hasEmptyValue) {
              return Promise.reject('请填写所有选项内容');
            }
            return Promise.resolve();
          },
        },
      ];
    case 2:
      return [
        {
          required: true,
          validator: (_: any, value: any) => {
            if (questionAnswerMode !== 2) {
              return Promise.resolve();
            }
            if (!value?.items?.length) {
              return Promise.reject('请填写选项内容');
            }
            const hasEmptyValue = value.items.some(
              (item: { value: string }) => !item.value?.trim(),
            );
            if (hasEmptyValue) {
              return Promise.reject('请填写所有选项内容');
            }
            return Promise.resolve();
          },
        },
      ];
    case 3:
      return questionAnswerMode === 3 ? [{ required: true, message: '请输入填空答案' }] : [];
    case 4:
      return questionAnswerMode === 4 ? [{ required: true, message: '请输入判断答案' }] : [];
    case 5:
      return questionAnswerMode === 5 ? [{ required: true, message: '请输入解答答案' }] : [];
    default:
      return [];
  }
};
