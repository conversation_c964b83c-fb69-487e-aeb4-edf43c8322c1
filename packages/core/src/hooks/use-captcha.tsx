"use client";
import { useCallback } from "@preact-signals/safe-react/react";
import React, { useEffect, useSyncExternalStore } from "react";

export const CaptchaHolder = React.memo<{ children: React.ReactNode }>(
  function CaptchaHolder({ children }) {
    return (
      <>
        <span id="captcha-button">{children}</span>
        <div id="captcha-element"></div>
      </>
    );
  }
);

const subscribe = (callback: () => void) => {
  const checkCaptcha = () => {
    if (typeof window.initAliyunCaptcha === "function") {
      callback();
    }
  };

  // 初始检查
  checkCaptcha();

  // 监听 load 事件
  document.addEventListener("load", checkCaptcha);

  return () => {
    document.removeEventListener("load", checkCaptcha);
  };
};

const getSnapshot = () => {
  return !!window.initAliyunCaptcha;
};

export const useCaptcha = function <T>({
  captchaVerifyCallback,
  onBizResultCallback,
  SceneId,
  prefix,
}: CaptchaProps<T>) {
  const isCaptchaReady = useSyncExternalStore(
    subscribe,
    getSnapshot,
    () => false
  );
  const captchaVerifyCallbackRef = React.useRef(captchaVerifyCallback);
  const onBizResultCallbackRef = React.useRef(onBizResultCallback);
  captchaVerifyCallbackRef.current = captchaVerifyCallback;
  onBizResultCallbackRef.current = onBizResultCallback;

  const realCaptchaVerifyCallback = useCallback(
    (captchaVerifyParam: string) =>
      captchaVerifyCallbackRef.current(captchaVerifyParam),
    []
  );

  const realOnBizResultCallback = useCallback(
    (bizResult?: T) => onBizResultCallbackRef.current(bizResult),
    []
  );

  useEffect(() => {
    if (!isCaptchaReady) {
      return;
    }

    window.initAliyunCaptcha({
      SceneId, // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
      prefix, // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
      mode: "popup", // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
      element: "#captcha-element", // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
      button: "#captcha-button", // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
      captchaVerifyCallback: realCaptchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
      onBizResultCallback: realOnBizResultCallback, // 业务请求结果回调函数，无需修改
      getInstance: (instance: unknown) => instance, // 绑定验证码实例函数，无需修改
      slideStyle: {
        width: 360, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
        height: 40,
      },
      language: "cn", // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
    });

    return () => {
      // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
      document.getElementById("aliyunCaptcha-mask")?.remove();
      document.getElementById("aliyunCaptcha-window-popup")?.remove();
    };
  }, [
    SceneId,
    isCaptchaReady,
    prefix,
    realCaptchaVerifyCallback,
    realOnBizResultCallback,
  ]);
};

export type CaptchaProps<T> = {
  onBizResultCallback: (bizResult?: T) => void;
  captchaVerifyCallback: (captchaVerifyParam: string) => Promise<{
    captchaResult: boolean;
    bizResult?: boolean;
  }>;
  SceneId: string;
  prefix: string;
};

declare global {
  interface Window {
    initAliyunCaptcha: (options: unknown) => void;
  }
}
