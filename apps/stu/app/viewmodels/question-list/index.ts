import { useQuestionList } from "@/app/models/question-list-model";
import { StudyType } from "@repo/core/enums";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useState } from "react";

export function useQuestionListViewModel() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const studySessionIdParam = searchParams.get("studySessionId");

  // 确保 studySessionId 是有效的数字
  const studySessionId = studySessionIdParam
    ? Number(studySessionIdParam)
    : undefined;

  // 当前查看解析的题目ID
  const [analysisId, setAnalysisId] = useState<number | null>(null);
  const [onlyWrong, setOnlyWrong] = useState(searchParams.get("onlyWrong") === "true");

  // 获取题目数据 - 将 onlyWrong 状态传递给 Model 层
  const { questions, isLoading, error, refresh, lessonId = "", studyType = StudyType.AI_COURSE } = useQuestionList({
    onlyWrong,
    studySessionId:
      studySessionId && !isNaN(studySessionId) ? studySessionId : undefined,
  });

  // 处理"只看错题"切换
  const handleOnlyWrongChange = useCallback((checked: boolean) => {
    setOnlyWrong(checked);
    // 不需要手动调用 refresh，因为 onlyWrong 变化会触发 useSWR 重新请求
  }, []);

  // 处理"查看解析"点击
  const handleViewAnalysis = useCallback((id: string) => {
    const params = new URLSearchParams();
    params.set("currentQuestionId", id);
    params.set("onlyWrong", onlyWrong.toString());
    params.set("source", "question-list");
    params.set("studySessionId", studySessionId?.toString() || "");
    params.set("lessonId", lessonId.toString());
    params.set("studyType", studyType.toString());
    router.push(`/exercise-preview?${params.toString()}`);
    // 这里可以触发弹窗、侧边栏等
  }, [onlyWrong, studySessionId, lessonId, studyType, router]);

  // 关闭解析
  const handleCloseAnalysis = useCallback(() => {
    setAnalysisId(null);
  }, []);

  return {
    lessonId,
    questions: questions || [],
    isLoading,
    error,
    analysisId,
    handleViewAnalysis,
    handleCloseAnalysis,
    refresh,
    onlyWrong,
    handleOnlyWrongChange,
  };
}
