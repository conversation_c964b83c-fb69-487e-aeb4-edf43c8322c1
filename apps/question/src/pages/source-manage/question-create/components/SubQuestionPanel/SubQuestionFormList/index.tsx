import styles from '@/pages/source-manage/question-create/index.module.less';
import { Form, Input } from 'antd';
import cn from 'classnames';
import React, { memo, useCallback, useState } from 'react';
import { useQuestionCreate } from '../../../contexts/QuestionCreateContext';
import NestedQuestionFormList from '../../NestedQuestionPanel/NestedQuestionFormList';
import QuestionStem from '../../QuestionStem';
import QuestionTitleBar from '../../QuestionTitleBar';
import CollapsibleContainer from '../../shared/CollapsibleContainer';
import SubQuestionAnswerSection from '../SubQuestionAnswerSection';

/**
 * 二级题目Form.List组件
 * 负责管理二级子题目的增删改查逻辑
 */
const SubQuestionFormList: React.FC = () => {
  const {
    form,
    formActions: {
      deletingItems,
      addFieldRef,
      removeFieldRef,
      nestedAddRefs,
      handleDeleteWithAnimation,
    },
  } = useQuestionCreate();

  // 获取二级题目列表
  const subQuestionList = Form.useWatch('subQuestionList', form) || [];

  // 为每个小题独立管理activeFillIdx
  const [activeFillIndices, setActiveFillIndices] = useState<Record<number, number>>({});

  const onQuestionFillAreaClick = useCallback((e: { index: number }, questionIndex: number) => {
    setActiveFillIndices((prev) => ({
      ...prev,
      [questionIndex]: e.index,
    }));
  }, []);

  return (
    <Form.List name="subQuestionList">
      {(fields, { add, remove }) => {
        // 保存 add 方法到 ref 中
        addFieldRef.current = add;
        // 保存 remove 方法到 ref 中
        removeFieldRef.current = remove;

        return (
          <>
            {fields.map(({ key, name, ...restField }) => {
              // 获取二级题目列表的作答方式
              const questionAnswerMode = subQuestionList[name]?.questionAnswerMode;
              const currentFillIdx = activeFillIndices[name] || 0;

              return (
                <CollapsibleContainer
                  key={key}
                  header={
                    <QuestionTitleBar
                      level="level1"
                      title={`小题 ${name + 1}`}
                      onDelete={() =>
                        handleDeleteWithAnimation(`level1-${key}`, () => remove(name))
                      }
                      onAddSub={() => nestedAddRefs.current.get(name)?.()}
                      required={true}
                    />
                  }
                  className={cn(
                    styles.level1Container,
                    deletingItems.has(`level1-${key}`) && styles.deleting,
                  )}
                >
                  {/* 题目id，隐藏，供form提交时使用 */}
                  <Form.Item name={[name, 'questionId']} label="questionId" hidden>
                    <Input />
                  </Form.Item>
                  {/* 二级题目主题干 */}
                  <QuestionStem
                    name={[name, 'questionStem']}
                    questionAnswerMode={questionAnswerMode}
                    onQuestionFillAreaClick={(e) => onQuestionFillAreaClick(e, name)}
                  />

                  {/* 三级题目列表组件 */}
                  <NestedQuestionFormList parentName={name} />

                  {/* 二级题目组件，监听三级子题目列表，当没有三级子题目时才显示二级的作答方式和答案 */}
                  <SubQuestionAnswerSection
                    name={name}
                    restField={restField}
                    currentFillIdx={currentFillIdx}
                  />
                </CollapsibleContainer>
              );
            })}
          </>
        );
      }}
    </Form.List>
  );
};

export default memo(SubQuestionFormList);
