<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#175;&#190;&#231;&#168;&#139;&#229;&#155;&#190;&#230;&#160;&#135;-3">
<g id="&#228;&#185;&#166;&#230;&#156;&#172;" filter="url(#filter0_i_916_3918)">
<g id="Rectangle 34624304" filter="url(#filter1_di_916_3918)">
<rect y="2" width="40" height="50" rx="9" fill="url(#paint0_linear_916_3918)"/>
</g>
<g id="Rectangle 34624307" filter="url(#filter2_di_916_3918)">
<path d="M8 2H20V17.0613C20 18.698 18.1414 19.6418 16.82 18.6761L14 16.6154L11.18 18.6761C9.8586 19.6418 8 18.698 8 17.0613V2Z" fill="url(#paint1_linear_916_3918)" fill-opacity="0.9" shape-rendering="crispEdges"/>
</g>
</g>
<g id="&#232;&#167;&#134;&#233;&#162;&#145;" filter="url(#filter3_d_916_3918)">
<foreignObject x="25.2552" y="13" width="33.7448" height="43"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4px);clip-path:url(#bgblur_0_916_3918_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624305" filter="url(#filter4_i_916_3918)" data-figma-bg-blur-radius="8">
<path d="M33.72 24.2773C32.6383 22.9739 33.5652 21 35.259 21H42C46.9706 21 51 25.0294 51 30V36.1875V40.3731C51 44.5853 47.5853 48 43.3731 48C42.3618 48 41.5755 47.12 41.689 46.115L42.666 37.4595C42.8185 36.1077 42.4138 34.7522 41.545 33.7054L33.72 24.2773Z" fill="url(#paint2_linear_916_3918)" fill-opacity="0.6"/>
</g>
<foreignObject x="5" y="10" width="57" height="49"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_1_916_3918_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624306" filter="url(#filter5_i_916_3918)" data-figma-bg-blur-radius="10">
<rect x="15" y="20" width="37" height="29" rx="8" fill="url(#paint3_linear_916_3918)" fill-opacity="0.4"/>
<rect x="15.5" y="20.5" width="36" height="28" rx="7.5" stroke="url(#paint4_linear_916_3918)" stroke-opacity="0.4"/>
</g>
<path id="Vector 5559" d="M24 29.5H40" stroke="url(#paint5_linear_916_3918)" stroke-opacity="0.9" stroke-width="4" stroke-linecap="round"/>
<path id="Vector 5560" d="M24 37H34" stroke="url(#paint6_linear_916_3918)" stroke-opacity="0.9" stroke-width="4" stroke-linecap="round"/>
</g>
<path id="Vector 5561" d="M52.5661 40.6023L40.4763 35.9524C39.3733 35.5282 38.2237 36.4543 38.4034 37.6223L40.3086 50.0056C40.5396 51.5077 42.5769 51.8008 43.2221 50.4248L45.6504 45.2455C45.8688 44.7798 46.3055 44.454 46.8142 44.3773L52.243 43.5596C53.805 43.3242 54.0404 41.1694 52.5661 40.6023Z" fill="url(#paint7_linear_916_3918)"/>
</g>
<defs>
<filter id="filter0_i_916_3918" x="0" y="2" width="40" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_916_3918"/>
</filter>
<filter id="filter1_di_916_3918" x="-4" y="-2" width="48" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.176471 0 0 0 0 0.564706 0 0 0 0 0.968627 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_3918"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_3918" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.284893 0 0 0 0 0.635366 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_3918"/>
</filter>
<filter id="filter2_di_916_3918" x="7" y="-2" width="14" height="23.0649" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.490099 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_3918"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_3918" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.284893 0 0 0 0 0.635366 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_3918"/>
</filter>
<filter id="filter3_d_916_3918" x="13" y="20" width="41" height="33" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.203922 0 0 0 0 0.580392 0 0 0 0 0.964706 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_3918"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_3918" result="shape"/>
</filter>
<filter id="filter4_i_916_3918" x="25.2552" y="13" width="33.7448" height="43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.455573 0 0 0 0 1 0 0 0 0 0.992692 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_916_3918"/>
</filter>
<clipPath id="bgblur_0_916_3918_clip_path" transform="translate(-25.2552 -13)"><path d="M33.72 24.2773C32.6383 22.9739 33.5652 21 35.259 21H42C46.9706 21 51 25.0294 51 30V36.1875V40.3731C51 44.5853 47.5853 48 43.3731 48C42.3618 48 41.5755 47.12 41.689 46.115L42.666 37.4595C42.8185 36.1077 42.4138 34.7522 41.545 33.7054L33.72 24.2773Z"/>
</clipPath><filter id="filter5_i_916_3918" x="5" y="10" width="57" height="49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.322749 0 0 0 0 1 0 0 0 0 0.990909 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_916_3918"/>
</filter>
<clipPath id="bgblur_1_916_3918_clip_path" transform="translate(-5 -10)"><rect x="15" y="20" width="37" height="29" rx="8"/>
</clipPath><linearGradient id="paint0_linear_916_3918" x1="6" y1="2" x2="28" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="#5DD4FF"/>
<stop offset="1" stop-color="#2B8EF4"/>
</linearGradient>
<linearGradient id="paint1_linear_916_3918" x1="14" y1="2" x2="15.5816" y2="21.1584" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#C7E5FF"/>
</linearGradient>
<linearGradient id="paint2_linear_916_3918" x1="41" y1="21" x2="33.9662" y2="35.77" gradientUnits="userSpaceOnUse">
<stop stop-color="#52E7E3"/>
<stop offset="1" stop-color="#52E7E3" stop-opacity="0.8"/>
</linearGradient>
<linearGradient id="paint3_linear_916_3918" x1="33.5" y1="20" x2="19.803" y2="47.8663" gradientUnits="userSpaceOnUse">
<stop stop-color="#52E7E3"/>
<stop offset="1" stop-color="#52E7E3" stop-opacity="0.8"/>
</linearGradient>
<linearGradient id="paint4_linear_916_3918" x1="45" y1="20.9062" x2="21.3093" y2="49.5139" gradientUnits="userSpaceOnUse">
<stop stop-color="#A8EDFF" stop-opacity="0.8"/>
<stop offset="1" stop-color="#C4F3FF" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint5_linear_916_3918" x1="24" y1="29.5" x2="39" y2="29.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#CCF9FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_916_3918" x1="24" y1="37" x2="33.375" y2="37" gradientUnits="userSpaceOnUse">
<stop stop-color="#CCF9FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint7_linear_916_3918" x1="38.7692" y1="34.2308" x2="48" y2="55.7692" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFFBFD"/>
<stop offset="1" stop-color="#C0F3FE"/>
</linearGradient>
</defs>
</svg>
