"use client";

import { ClientProvider } from "./client-provider";
import { ThemeProvider } from "./theme-provider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <ClientProvider>{children}</ClientProvider>
    </ThemeProvider>
  );
}
