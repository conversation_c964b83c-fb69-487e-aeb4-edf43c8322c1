"use client";
import React, { createElement, Fragment, memo, useState, useMemo } from "react";
import Script from "next/script";

/**
 * 互动组件
 * @param url 互动组件 js 脚本
 * @param type 互动组件的类型
 * @param children 可传递在组件加载过程中的 loading UI
 * @param onReport 互动组件的上报事件
 */
function InteractiveComponent<
  P extends React.HtmlHTMLAttributes<T>,
  T extends Element,
>({
  url,
  type,
  children = null,
  onLoad,
  onError,
  ...props
}: React.ClassAttributes<T> &
  P & {
    url: string;
    type: string;
    onReport?: (e: CustomEvent) => void;
    onLoad?: () => void;
    onError?: (e: unknown) => void;
    children?: React.ReactNode;
  }) {
  const dom = useMemo(() => {
    const result = createElement(
      type,
      {
        ...props,
        ref: (element: Element | null) => {
          if (element && element.shadowRoot) {
            const style = document.createElement("style");
            style.textContent = `
            :host{height: 100%;overflow-y: auto;}
            .main-container{min-height: 100%;}
            `;
            element.shadowRoot.appendChild(style);
          }
        },
      },
      ...(Array.isArray(children) ? children : [children])
    );
    return result;
  }, [type, props, children]);
  return (
    <>
      <Script src={url} onLoad={onLoad} onError={onError} />
      {dom}
    </>
  );
}

export default memo(InteractiveComponent);

// 使用示例：
// function Demo() {
//   return (
//     <InteractiveComponent
//       url="https://www.baidu.com/a/b/c"
//       type="aab-ddc"
//       onClick={(e) => {}}
//       onAAA={(e) => {}}
//     >
//       {/* 这里的内容会在组件加载期间显示 */}
//       <div className="loading-spinner">加载互动组件中...</div>
//     </InteractiveComponent>
//   );
// }
