import { Checkbox } from "@/ui/checkbox";
import { computed, effect, signal } from "@preact-signals/safe-react";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useMemo } from "react";

// ============= 类型定义 =============
interface ReminderInfo {
  key: string;
  url: string;
  expireTime: number | null; // null 表示永久不提醒
  [key: string]: unknown;
}

interface ReminderStore {
  [key: string]: ReminderInfo;
}

export interface NolongerTipCheckBoxProps {
  /** 提醒的唯一键，必须传入 */
  reminderKey?: string;
  /**
   * 用于决定显示文本的天数设置
   * - `true`: 文本为 "不再提醒" (永久)
   * - `number`: 文本为 "N天内不再提醒"
   */
  showReminder?: true | number;
  /** 是否启用提醒检查 */
  enabled?: boolean;
}

// ============= 全局状态 =============
const storageKey = 'app_dialog_reminders_store';
let isInitialized = false;

// 全局信号
const reminderStoreSignal = signal<ReminderStore>({});
const currentReminderKeySignal = signal<string>('');
const isCheckedSignal = signal<boolean>(false);

// 计算信号 - 检查当前提醒是否应该显示
const shouldShowSignal = computed(() => {
  const reminderKey = currentReminderKeySignal.value;
  if (!reminderKey) return true;

  const reminders = reminderStoreSignal.value;
  const reminderInfo = reminders[reminderKey];

  if (!reminderInfo) return true;
  if (reminderInfo.expireTime === null) return false;

  const now = Date.now();
  if (now > reminderInfo.expireTime) {
    // 已过期，计划清理
    setTimeout(() => clearReminderSignal(reminderKey), 0);
    return true;
  }
  return false;
});

// ============= 存储辅助函数 =============
/**
 * 从localStorage加载提醒设置
 */
function loadReminders(): ReminderStore {
  if (typeof window === 'undefined') return {};

  try {
    const storedValue = localStorage.getItem(storageKey);
    if (!storedValue) return {};

    const parsedReminders = JSON.parse(storedValue) as ReminderStore;
    const now = Date.now();

    // 清理过期提醒
    Object.keys(parsedReminders).forEach(key => {
      const reminder = parsedReminders[key];
      if (reminder.expireTime !== null && now > reminder.expireTime) {
        delete parsedReminders[key];
      }
    });

    return parsedReminders;
  } catch (error) {
    console.error('加载提醒记录失败:', error);
    return {};
  }
}

/**
 * 保存提醒设置到localStorage
 */
function saveReminders(store: ReminderStore): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(storageKey, JSON.stringify(store));
  } catch (error) {
    console.error('保存提醒记录失败:', error);
  }
}

/**
 * 初始化提醒存储
 */
function initializeReminderStore() {
  if (typeof window !== 'undefined' && !isInitialized) {
    reminderStoreSignal.value = loadReminders();
    isInitialized = true;

    // 自动保存效果
    effect(() => {
      if (isInitialized) {
        saveReminders(reminderStoreSignal.value);
      }
    });
  } else if (typeof window === 'undefined' && !isInitialized) {
    isInitialized = true;
  }
}

// 初始化运行
initializeReminderStore();

// ============= 公共API函数 =============
/**
 * 保存或更新提醒首选项
 */
export function saveReminderPreferenceSignal(
  key: string,
  options: Partial<ReminderInfo>,
  days?: number | boolean
): void {
  if (!key) {
    console.error('保存提醒设置失败: reminderKey 不能为空');
    return;
  }

  const expireTime = days === true
    ? null
    : typeof days === 'number' ? Date.now() + days * 24 * 60 * 60 * 1000 : null;

  // 创建新的提醒信息
  const newReminder: ReminderInfo = {
    url: options.url || '',
    key,
    ...options,
    expireTime
  };

  // 更新存储
  reminderStoreSignal.value = {
    ...reminderStoreSignal.value,
    [key]: newReminder
  };
  saveReminders(reminderStoreSignal.value);
}

/**
 * 清除特定的提醒首选项
 */
export function clearReminderSignal(key: string): void {
  if (!key) {
    console.error('清除提醒设置失败: reminderKey 不能为空');
    return;
  }

  if (reminderStoreSignal.peek()[key]) {
    const currentStore = { ...reminderStoreSignal.value };
    delete currentStore[key];
    reminderStoreSignal.value = currentStore;
  }
}

/**
 * 清除所有提醒首选项
 */
export function clearAllRemindersSignal(): void {
  reminderStoreSignal.value = {};
}

/**
 * 根据天数设置获取对应的显示文本
 */
export function getRemindDaysText(showReminder?: true | number): string {
  if (typeof showReminder === 'number' && showReminder > 0) {
    return `${showReminder}天内不再提醒`;
  }
  return '不再提醒';
}

// 添加一个用于跟踪活动reminderKey的集合
const activeReminderKeys = new Set<string>();

/**
 * 全局钩子: 用于在组件间直接使用提醒状态和方法
 */
export function useReminderHook(
  params: { reminderKey: string; showReminder?: boolean | number }
) {
  const url = usePathname()!;
  const { reminderKey } = params;
  
  // 在开发环境下检查重复键
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') {
      if (reminderKey && activeReminderKeys.has(reminderKey)) {
        console.warn(
          `警告: 检测到重复的reminderKey "${reminderKey}"。` +
          `多个组件使用相同的reminderKey可能导致意外行为。` +
          `请确保每个"不再提醒"功能使用唯一的reminderKey。`
        );
      }
      
      if (reminderKey) {
        activeReminderKeys.add(reminderKey);
        return () => {
          activeReminderKeys.delete(reminderKey);
        };
      }
    }
  }, [reminderKey]);

  // 设置当前查询的提醒键
  useEffect(() => {
    if (reminderKey) {
      currentReminderKeySignal.value = reminderKey;

      return () => {
        // 在组件卸载时清除当前键（如果它仍然是当前键）
        if (currentReminderKeySignal.peek() === reminderKey) {
          currentReminderKeySignal.value = '';
        }
      };
    }
  }, [reminderKey]);

  // 提醒设置函数
  const saveReminderSetting = useCallback((
    options: Record<string, unknown> = {},
    days?: number | true,
    forceSet: boolean = false
  ) => {
    const key = reminderKey || currentReminderKeySignal.value;

    if (!key) {
      console.error('保存提醒设置失败: reminderKey 不能为空');
      return;
    }

    // 使用从参数中传入的days，如果没有，则使用函数参数中的days
    const finalDays = days !== undefined ? days : params.showReminder;

    if (isCheckedSignal.value || forceSet) {
      // 默认添加当前URL
      const finalOptions = { url, ...options };
      saveReminderPreferenceSignal(key, finalOptions, finalDays);
    }
  }, [reminderKey, url, params.showReminder]);

  // 提醒状态重置函数
  const clearReminder = useCallback(() => {
    const key = reminderKey || currentReminderKeySignal.value;

    if (!key) {
      console.error('清除提醒设置失败: reminderKey 不能为空');
      return;
    }

    clearReminderSignal(key);
    isCheckedSignal.value = false;
  }, [reminderKey]);

  const setReminderChecked = useCallback((checked: boolean) => {
    isCheckedSignal.value = checked;
  }, []);

  return {
    shouldShow: shouldShowSignal.value,
    saveReminderSetting,
    clearReminder,
    setReminderChecked
  };
}

// ============= 组件实现 =============
export function NolongerTipCheckBox({
  reminderKey,
  showReminder = true,
  enabled = true,
}: NolongerTipCheckBoxProps) {


  // 检查 reminderKey 是否存在
  useEffect(() => {
    if (!reminderKey && enabled) {
      console.error('警告: 必须为 NolongerTipCheckBox 组件提供 reminderKey 属性');
    }
  }, [reminderKey, enabled]);

  // 优化文本计算，仅在 showReminder 变化时重新计算
  const labelText = useMemo(() => getRemindDaysText(showReminder), [showReminder]);

  // 如果禁用了检查，则返回空
  if (!enabled) return null;

  return (
    <div className="flex select-none items-center gap-2">
      <Checkbox
        id={`${reminderKey}-checkbox`}
        checked={isCheckedSignal.value}
        onCheckedChange={(checked) => {
          if (typeof checked === 'boolean') {
            isCheckedSignal.value = checked;
          }
        }}
        className="rounded-0.75 border border-slate-300 data-[state=checked]:bg-primary-2 data-[state=checked]:border-primary-2"
      />
      <label
        htmlFor={`${reminderKey}-checkbox`}
        className="text-gray-2 text-xs font-normal leading-normal cursor-pointer"
      >
        {labelText}
      </label>
    </div>
  );
}