import { useSignal } from "@preact-signals/safe-react";
import { cn } from "@/utils/utils";
import { useEffect } from "react";
interface TchOptionTagsProps {
  style?: React.CSSProperties;
  className?: string;
  options: {
    label: string;
    value: string;
  }[];
  value?: string;
  multiple?: boolean;
  onChange: (value: string | string[]) => void;
}

export function TchOptionTags({
  style = {},
  className = "",
  options,
  value,
  onChange,
  multiple = false,
}: TchOptionTagsProps) {
  const selectedValues = useSignal<string[]>([]);
  
  useEffect(() => {
    if (multiple) {
      selectedValues.value = value?.split(",") || [];
    } else {
      selectedValues.value = value ? [value] : [];
    }
  }, [value, multiple, selectedValues]);
  const onChangeValue = (value: string) => {
    if (multiple) {
      const include = selectedValues.value.includes(value);
      selectedValues.value = include
        ? selectedValues.value.filter((item) => item !== value)
        : [...selectedValues.value, value];
      onChange(selectedValues.value);
    } else {
      selectedValues.value = [value];
      onChange(value);
    }
  };
  return (
    <div className={cn("flex flex-wrap items-start gap-1", className)} style={style}>
      {options.map((option) => (
        <div
          key={option.value}
          className={cn(
            "text-gray-1 rounded-md px-3 py-1 text-sm leading-5.5 cursor-pointer hover:bg-primary-6 hover:text-primary-1 active:bg-primary-6 active:text-primary-1",
            selectedValues.value.includes(option.value) &&
              "bg-primary-6 text-primary-1"
          )}
          onClick={() => onChangeValue(option.value)}
        >
          {option.label}
        </div>
      ))}
    </div>
  );
}
