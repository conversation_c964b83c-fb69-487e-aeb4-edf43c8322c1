import { OssSignature } from "../types/oss";
import { get } from "./fetcher";

interface SignatureParams {
  url: string;
  params?: Record<string, string>;
}

interface UseOssUploadParams {
  file: File;
  signature: SignatureParams;
}

async function getOssSignature(param: SignatureParams) {
  const { url, params } = param;
  try {
    const ossSignature = await get<OssSignature>(url, {
      query: params,
    });
    return ossSignature;
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function upload(params: UseOssUploadParams) {
  const { file, signature } = params;

  if (!file) {
    return {
      url: "",
      error: "文件为空",
    };
  }
  const ossSignature = await getOssSignature({
    url: signature.url,
    params: { ...signature.params, fileName: file.name },
  });
  if (!ossSignature) {
    return {
      url: "",
      error: "获取签名失败",
    };
  }
  const { formData: form, url, fileName } = ossSignature;
  const { policyToken } = form;
  const { dir, host, ...rest } = policyToken;
  const formData = new FormData();
  formData.append("key", `${dir}/${fileName}`);
  Object.entries(rest).forEach(([key, value]) => {
    formData.append(key, value as string);
  });
  formData.append("file", file);
  console.log(formData);

  try {
    const response = await fetch(host, {
      method: "POST",
      body: formData,
    });
    if (response.ok) {
      return {
        url,
        error: "",
      };
    } else {
      return {
        url: "",
        error: "上传失败",
      };
    }
  } catch (error) {
    console.error(error);
    return {
      url: "",
      error: "上传失败",
    };
  }
}

export { upload };
