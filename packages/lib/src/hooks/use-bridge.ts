/**
 * useBridge - React Hook 封装JSBridge调用
 * 提供更便捷的方式在React组件中使用JSBridge功能
 */
"use client";

import { useCallback, useEffect, useState } from "react";
import bridge, { ALLOWED_API_METHODS } from "../utils/jsbridge";

/**
 * 使用JSBridge的React Hook
 * @returns 封装了JSBridge调用方法的对象
 */
export const useBridge = () => {
  // 是否支持JSBridge
  const [isSupported, setIsSupported] = useState<boolean>(false);

  // 检查环境是否支持JSBridge
  useEffect(() => {
    const checkSupport = () => {
      const supported =
        typeof window !== "undefined" &&
        window.AndroidBridge !== undefined &&
        typeof window.AndroidBridge.invokeSync === "function" &&
        typeof window.AndroidBridge.invokeAsync === "function";
      setIsSupported(supported);
      return supported;
    };

    // 初始检查
    checkSupport();

    // 创建一个检测间隔，以防JSBridge延迟初始化
    const intervalId = setInterval(() => {
      if (checkSupport()) {
        clearInterval(intervalId);
      }
    }, 200);

    // 清理检测间隔
    return () => clearInterval(intervalId);
  }, []);

  /**
   * 检查API是否在允许列表中
   * @param apiName API名称
   * @returns 如果API在白名单中返回true，否则返回false
   */
  const isApiAllowed = useCallback((apiName: string): boolean => {
    return ALLOWED_API_METHODS.includes(apiName);
  }, []);

  /**
   * 同步调用Native API
   * @param apiName API名称
   * @param params 参数对象
   * @returns 调用结果
   */
  const callSync = useCallback(
    <T>(apiName: string, params: object = {}): T | null => {
      if (!isSupported) {
        console.warn("JSBridge不可用，无法同步调用", apiName);
        return null;
      }

      // 检查API是否在白名单中
      if (!isApiAllowed(apiName)) {
        console.error(`API不在允许列表中: ${apiName}`);
        throw new Error(`不允许调用此API: ${apiName}`);
      }
      try {
        return bridge.invokeSync<T>(apiName, params);
      } catch (error) {
        console.error(`同步调用出错: ${apiName}`, error);
        return null;
      }
    },
    [isSupported, isApiAllowed]
  );

  /**
   * 异步调用Native API
   * @param apiName API名称
   * @param params 参数对象
   * @param persist 是否持久化回调
   * @returns Promise对象
   */
  const callAsync = useCallback(
    <T>(
      apiName: string,
      params: object = {},
      persist: boolean = false
    ): Promise<T> => {
      if (!isSupported) {
        return Promise.reject(
          new Error("JSBridge不可用，无法异步调用: " + apiName)
        );
      }

      // 检查API是否在白名单中
      if (!isApiAllowed(apiName)) {
        console.error(`API不在允许列表中: ${apiName}`);
        return Promise.reject(new Error(`不允许调用此API: ${apiName}`));
      }
      return bridge.invokeAsync<T>(apiName, params, persist);
    },
    [isSupported, isApiAllowed]
  );

  /**
   * 创建一个异步API调用的封装函数
   * 适用于需要频繁调用同一个API的场景
   * @param apiName API名称
   * @returns 封装了指定API调用的异步函数
   */
  // const createApiCaller = useCallback(
  //   <T = any, P extends Record<string, any> = Record<string, any>>(
  //     apiName: string
  //   ) => {
  //     // 检查API是否在白名单中
  //     if (!isApiAllowed(apiName)) {
  //       console.error(`API不在允许列表中，无法创建调用者: ${apiName}`);
  //       return () => Promise.reject(new Error(`不允许调用此API: ${apiName}`));
  //     }

  //     return (params: P = {} as P): Promise<T> => callAsync<T>(apiName, params);
  //   },
  //   [callAsync, isApiAllowed]
  // );

  return {
    isSupported,
    callSync,
    callAsync,
    // createApiCaller,
    isApiAllowed, // 导出API白名单检查函数，方便UI展示
  };
};

export default useBridge;
