declare namespace API {
  /**
   * 获取学科学段等枚举值
   */
  type PhaseSubjectDataResponse = {
    code: number;
    data: PhaseSubjectData;
    message: string;
    [property: string]: any;
  };
  type PhaseSubjectData = {
    phaseSubjects: PhaseSubjectType[];
    [property: string]: any;
  };
  type PhaseSubjectType = {
    key: number;
    subjects: SubjectType[];
    value: string;
    [property: string]: any;
  };
  type SubjectType = {
    key: number;
    value: string;
    [property: string]: any;
  };
  type FilterOption = {
    label: string;
    value: string;
    [property: string]: any;
  };

  type FilterGroup = {
    name: string;
    field: string;
    options: FilterOption[];
    [property: string]: any;
  };

  type QuestionFilterEnumResponse = {
    code: number;
    message: string;
    data: {
      searchTypes: FilterOption[];
      filters: FilterGroup[];
      sortType: FilterOption[];
      [property: string]: any;
    };
  };

  export type EnumConstantItem<T extends string | number> = {
    nameEn: string;
    nameZh: string;
    value: T;
    isDefault?: boolean;
  };

  export type EnumConstantData = {
    /**
     * 任务类型
     * 1新增试题审核 2选题审核
     */
    auditTypeList: EnumConstantItem<1 | 2>[];
    /**
     * 审核状态
     * 1待审核 2通过 3不通过 4撤回
     */
    auditStatusList: (EnumConstantItem<1 | 2 | 3 | 4> & { status: string })[];
    /**
     * 学段列表
     */
    phaseList: EnumConstantItem<number>[];
    /**
     * 学科列表
     */
    subjectList: EnumConstantItem<number>[];
    /**
     * 学段与学科关系
     */
    phaseSubjectRelation: Array<
      EnumConstantItem<number> & {
        subjectList: EnumConstantItem<number>[];
      }
    >;
    provinceList: EnumConstantItemType[];
    questionAnswerModeList: EnumConstantItem<number>[];
    yearList: EnumConstantItemType[];
    materialList: EnumConstantItem<number>[];
    /**
     * 题目难度列表
     * 1简单 2较易 3中等 4较难 5困难
     */
    questionDifficultList: (EnumConstantItem<1 | 2 | 3 | 4 | 5> & { color: string })[];
    /**
     * 类似常量枚举结构
     */
    questionSourceList: EnumConstantItem<number>[];
    /**
     * 试卷类型列表
     */
    paperTypeList: EnumConstantItem<number>[];
    /**
     * 上架状态列表
     */
    shelfStatusList: EnumConstantItem<number>[];
    /**
     * 题目类型列表
     */
    questionTypeList: EnumConstantItem<number>[];
    /**
     * 省份列表(value对应provinceCode)
     */
    provinceList: EnumConstantItem<number>[];
    /**
     * 年份列表(value对应year)
     */
    yearList: EnumConstantItem<number>[];
    /**
     * 场景分类列表
     * 1ai课 2巩固练习 3个性化练习
     */
    sceneCategoryList: EnumConstantItem<1 | 2 | 3>[];
    /**
     * 学科标签与题目类型关系列表
     */
    subjectLabelQuestionTypeRelationList: Array<
      EnumConstantItem<number> & { labelQuestionTypeList?: EnumConstantItem<number>[] }
    >;
    /**
     * 最近年份列表
     */
    recentYearList: EnumConstantItem<number>[];
  };

  type RegionListData = {
    provinceList: Array<{
      nameEn: string;
      nameZh: string;
      value: number;
      cityList?: Array<{
        nameEn: string;
        nameZh: string;
        value: number;
        areaList?: Array<{
          nameEn: string;
          nameZh: string;
          value: number;
        }>;
      }>;
    }>;
  };
  export type RegionListResponse = {
    code: number;
    message: string;
    data: RegionListData;
  };

  /**
   * 提取枚举常量数据值
   */
  export type ExtractEnumConstantDataValue<T extends keyof EnumConstantData> =
    EnumConstantData[T][number]['value'];
}
