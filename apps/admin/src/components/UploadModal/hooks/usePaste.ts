import { message } from 'antd';
import type { RcFile } from 'antd/es/upload/interface';
import { useEffect } from 'react';

interface UsePasteProps {
  handleUpload: (file: RcFile) => Promise<void>;
  acceptFileTypes: string[];
  disabled?: boolean;
}

const usePaste = ({ handleUpload, acceptFileTypes, disabled }: UsePasteProps) => {
  useEffect(() => {
    if (disabled) {
      return; // 如果禁用，不添加事件监听
    }

    const handlePaste = async (event: ClipboardEvent) => {
      const items = event.clipboardData?.items;

      if (!items) return;

      for (const item of items) {
        if (item.kind === 'file') {
          const file = item.getAsFile();
          const fileType = item.type || file?.name.split('.').pop() || '';
          const fileName = file?.name || '';
          const fileExt = fileName.split('.').pop() || '';

          console.log('items', item, file, fileType, acceptFileTypes, fileName, fileExt);

          if (
            file &&
            acceptFileTypes.some((type) => {
              const t = type.toLowerCase();
              const ft = fileType.toLowerCase();
              const fe = fileExt.toLowerCase();
              return t.includes(ft) || `.${ft}` === t || t.includes(fe);
            })
          ) {
            // 转换为 Antd 需要的 RcFile 类型
            const rcFile = new File([file], file.name, { type: file.type }) as RcFile;
            // 触发上传逻辑
            await handleUpload(rcFile);
          } else {
            message.warning(`只支持 ${acceptFileTypes.join(', ')} 格式的文件`);
          }
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [handleUpload, acceptFileTypes, disabled]);
};

export default usePaste;
