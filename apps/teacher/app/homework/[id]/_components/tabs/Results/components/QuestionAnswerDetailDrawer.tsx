"use client";
import { PageHeader } from "@/components/PageHeader";
import <PERSON>Viewer from "@/ui/imageViewer";
import { Sheet, SheetContent, SheetTitle } from "@/ui/sheet";
import { cn } from "@/utils";

interface QuestionAnswerDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  answerData: {
    answer: string;
    answerResult: number;
    costTime: number;
    isCorrect: boolean;
    studentId: number;
  };
}

export function QuestionAnswerDetailDrawer({
  open,
  onOpenChange,
  answerData,
}: QuestionAnswerDetailDrawerProps) {
  if (!answerData) return null;
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTitle>{null}</SheetTitle>
      <SheetContent
        side="right"
        className={cn(
          "h-full w-full gap-0 overflow-y-auto bg-[#F4F7FE] pb-6 sm:max-w-md",
          "flex flex-col"
        )}
        closeable={false}
      >
        <PageHeader
          className="sticky top-0 z-10 flex items-center bg-[#F4F7FE] px-6 py-5"
          onBack={() => onOpenChange(false)}
          needBack={true}
        >
          {/* <div className="sticky top-0 z-10 flex items-center justify-between bg-[#F4F7FE] px-6 py-5"> */}
          <div className="ml-2 flex flex-1 justify-between">
            <div className="flex items-center gap-[0.625rem]">
              <span className="text-lg font-medium text-[#444963]">
                作答详情
              </span>
            </div>
          </div>
        </PageHeader>
        <div className="pl-6 pr-6">
          <div className="rounded-2xl border border-[#e9ecf5] bg-white p-5">
            <div className="text-gray-2 mb-6 text-base font-medium">
              作答结果
            </div>
            {!answerData.answer.includes("https://") ? (
              <div>
                <span className="mr-4 text-sm text-gray-600">答案</span>
                <span className="text-sm text-red-600">
                  {answerData.answer || "未作答"}
                </span>
              </div>
            ) : (
              <div>
                <div className="flex gap-4">
                  {(answerData.answer || "")
                    .split(";")
                    .map((imgUrl: string, index) => (
                      <ImageViewer key={index} imageUrl={imgUrl} />
                    ))}
                </div>
                {answerData.answer && (
                  <div className="text-gray-4 mt-4 flex items-end justify-end text-sm font-normal">
                    （可上传
                    <span>{(answerData.answer || "").split(";").length}/3</span>
                    ）
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
