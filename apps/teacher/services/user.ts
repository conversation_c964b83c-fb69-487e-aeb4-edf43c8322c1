import { r } from "../libs/axios";
import { UserInfo } from "@/types";
import {
  UnreadMessageCountResult,
  FeedbackTypeConfigList,
  SubmitFeedbackRequest,
  GetMessageListResponse,
} from "@/types/user";
import { FEEDBACK_TYPE } from "@/enums";

export const getUserInfo = () => {
  return r.get<UserInfo>("/teacher/detail");
};

/**
 * 获取未读消息数量
 */
export const getUnreadMessageCount = () => {
  return r.get<UnreadMessageCountResult>(`/message/unread/count`, {
    showToast: false,
  });
};

/**
 * 获取消息列表
 */
export const getMessageList = (params: { page: number; page_size: number }) => {
  return r
    .get<GetMessageListResponse>(`/message/list`, {
      showToast: false,
      params,
    })
    .then(({ messages, ...rest }) => {
      return {
        ...rest,
        list: messages,
      };
    });
};

/**
 * 标记消息为已读
 */
export const markMessageAsRead = (data: { message_ids: number[] }) => {
  return r.post(`/message/read`, data, {
    showToast: false,
  });
};

/**
 * 获取反馈类型配置
 */
export const getFeedbackTypeConfig = (feedbackType: FEEDBACK_TYPE) => {
  return r.get<FeedbackTypeConfigList>(`/feedback/types`, {
    showToast: false,
    params: { feedbackType },
  });
};

/**
 * 获取截图上传的签名信息
 */
export const getScreenshotUploadSignature = (props: {
  feedbackType: FEEDBACK_TYPE;
  fileName: string;
}) => {
  return r.post<{
    expireTime: number;
    objectKey: string;
    presignedURL: string;
    fileUrl: string;
  }>("/feedback/upload/presignedURL", props, {
    showToast: false,
  });
};

/**
 * 提交反馈
 */
export const submitFeedback = (data: SubmitFeedbackRequest) => {
  return r.post(`/feedback/save`, data);
};
