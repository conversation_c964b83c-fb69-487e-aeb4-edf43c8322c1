"use client";

import * as React from "react";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { DayPicker } from "react-day-picker";
import { subMonths, addMonths, subYears, addYears, format } from "date-fns";
import { cn } from "@/utils/utils";
import { Button, buttonVariants } from "@/ui/button";

function NavButton({
  onClick,
  icon: Icon,
  label,
  size = "h-5 w-5",
}: {
  onClick: () => void;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  size?: string;
}) {
  return (
    <button
      onClick={onClick}
      className={cn(
        size,
        "flex h-4 w-4 items-center justify-center bg-transparent p-0 opacity-50 hover:rounded hover:bg-[#E7E8EA] hover:opacity-100"
      )}
      aria-label={label}
      type="button"
    >
      <Icon className="h-4 w-4" />
    </button>
  );
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: React.ComponentProps<typeof DayPicker>) {
  // Add internal state to manage the month
  const [month, setMonth] = React.useState<Date>(props.month || new Date());

  // Update internal state when props change
  React.useEffect(() => {
    if (props.month) {
      setMonth(props.month);
    }
  }, [props.month]);

  // Handle month and year navigation
  const handleNavigation = (
    action: "prevMonth" | "nextMonth" | "prevYear" | "nextYear"
  ) => {

    let newMonth;

    switch (action) {
      case "prevMonth":
        newMonth = subMonths(month, 1);
        break;
      case "nextMonth":
        newMonth = addMonths(month, 1);
        break;
      case "prevYear":
        newMonth = subYears(month, 1);
        break;
      case "nextYear":
        newMonth = addYears(month, 1);
        break;
      default:
        newMonth = month;
    }

    // Update internal state
    setMonth(newMonth);

    // Notify parent component if onMonthChange handler exists
    props.onMonthChange?.(newMonth);
  };

  return (
    <DayPicker
      month={month}
      onMonthChange={setMonth}
      showOutsideDays={showOutsideDays}
      className={cn("p-4", className)}
      weekStartsOn={0} // 从周日开始
      classNames={{
        months: "flex flex-col sm:flex-row gap-4 ",
        month:
          "flex flex-col gap-6 first:after:hidden after:absolute after:right-0 after:top-0 after:h-[95%] after:w-[1px] after:top-[10.5%] after:-left-[3%] after:bg-line-2 relative",
        caption: "flex justify-center pt-1 relative items-center w-full",
        caption_label: "text-sm font-medium",
        nav: "flex items-center gap-1",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "size-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute left-1 border-none",
        nav_button_next: "absolute right-1 border-none",
        table: "w-full border-collapse space-x-1 py-4 pl-4 pr-3",
        head_row: "flex",
        head_cell: "text-gray-4 w-8 !font-extrabold text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: cn(
          " relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-primary-6 [&:has([aria-selected].day-range-end)]:rounded-r-full",
          props.mode === "range"
            ? "[&:has(>.day-range-end)]:rounded-r-full [&:has(>.day-range-start)]:rounded-l-full [&:has(>.range_start)]:rounded-l-full [&:has(>.range_end)]:rounded-r-full"
            : "[&:has([aria-selected])]:rounded-full",
          "[&:has(.day-outside)]:!bg-transparent"
        ),
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "font-[600] size-8 p-0 aria-selected:opacity-100 hover:rounded-none"
        ),
        day_range_start:
          "day-range-start aria-selected:bg-primary m-0.5 aria-selected:text-primary-foreground rounded-full",
        day_range_end:
          "day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground rounded-full",
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today:
          " after:absolute after:-bottom-1 after:left-1/2 after:-translate-x-1/2 after:w-[4px] after:h-[4px] after:rounded-full after:bg-primary-2",
        day_outside:
          "day-outside !text-gray-5 aria-selected:text-gray-3 aria-selected:bg-transparent !rounded-none",
        day_disabled: "text-gray-3  opacity-50",
        day_range_middle: "aria-selected:bg-primary-6 aria-selected:text-inherit",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        Caption: ({ displayMonth, displayIndex }) => (
          <div className="relative flex items-center justify-center gap-1">
            {!displayIndex && (
              <div className="absolute left-0 flex gap-1">
                <NavButton
                  onClick={() => handleNavigation("prevYear")}
                  icon={ChevronsLeft}
                  label="上一年"
                  size="h-5 w-5"
                />
                <NavButton
                  onClick={() => handleNavigation("prevMonth")}
                  icon={ChevronLeft}
                  label="上个月"
                  size="h-5 w-5"
                />
              </div>
            )}
            <span className="text-sm font-[600]">
              {format(displayMonth, "yyyy年 - MM月")}
            </span>
            {displayIndex || props.mode === "single" ? (
              <div className="absolute right-0 flex gap-1">
                <NavButton
                  onClick={() => handleNavigation("nextMonth")}
                  icon={ChevronRight}
                  label="下个月"
                />
                <NavButton
                  onClick={() => handleNavigation("nextYear")}
                  icon={ChevronsRight}
                  label="下一年"
                />
              </div>
            ) : null}
          </div>
        ),
      }}
      {...props}
    />
  );
}

export { Calendar };
