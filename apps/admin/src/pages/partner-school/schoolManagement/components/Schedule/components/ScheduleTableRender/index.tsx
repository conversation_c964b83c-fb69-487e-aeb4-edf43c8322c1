import { NodeInfo, NodeType } from '@/components/GradeTree';
import { ScheduleContext } from '@/pages/partner-school/schoolManagement/contexts/ScheduleContext';
import { Spin } from 'antd';
import React, { useContext } from 'react';
import ScheduleTable from '../ScheduleTable';

const ScheduleTableRender: React.FC = () => {
  const {
    selectedNodeInfo,
    scheduleData,
    editMode,
    schoolInfo,
    semesterInfo,
    handleImportSuccess,
    setEditMode,
    weekInfo,
    loading,
    handleScheduleAction,
  } = useContext(ScheduleContext);
  if (!selectedNodeInfo || !scheduleData || editMode === 'grade' || !weekInfo) return null;

  const { nodeType, nodeId, nodeInfo } = selectedNodeInfo;
  const isGradeView = nodeType === NodeType.Grade;
  const isEditingClass = editMode === 'class';
  const schoolId = Number(schoolInfo?.schoolId);
  let gradeId: number | undefined = undefined;
  let classId: number | undefined = undefined;

  if (nodeType === NodeType.Grade) {
    gradeId = Number(nodeId);
  } else if (nodeType === NodeType.Class) {
    classId = Number(nodeId);
    gradeId = Number((nodeInfo as NodeInfo<NodeType.Class>).gradeInfo.grade_id);
  }

  return (
    <Spin spinning={loading.schedule}>
      <ScheduleTable
        weekInfo={weekInfo}
        scheduleData={scheduleData}
        isGradeTemplate={isGradeView}
        isEditMode={isEditingClass}
        onSave={() => handleScheduleAction('save')}
        onCancel={() => handleScheduleAction('cancel')}
        schoolId={schoolId}
        gradeId={gradeId}
        classId={classId}
      />
    </Spin>
  );
};

export default ScheduleTableRender;
