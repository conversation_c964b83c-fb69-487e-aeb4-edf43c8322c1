import { UserInfo } from "@/types";
import { Signal } from "@preact-signals/safe-react";
import React from "react";
import { useUserRoles } from "./useUserRoles";
export const AppContext = React.createContext<AppContextProps | null>(null);

export function useApp() {
  const context = React.useContext(AppContext);

  if (!context) {
    throw new Error("useApp must be used within a AppProvider.");
  }

  return context;
}

export type AppContextProps = {
  primarySubject: { subjectId: number; subjectName: string } | null;
  /**
   * 状态栏高度，单位：px
   */
  statusBarHeight: number;
  // getAllSubjects: ReturnType<typeof useUserRoles>["getAllSubjects"];
  hasJobTypes: ReturnType<typeof useUserRoles>["hasJobTypes"];
  hasJobType: ReturnType<typeof useUserRoles>["hasJobType"];
  getRoleSummary: ReturnType<typeof useUserRoles>["getRoleSummary"];
  open: Signal<boolean>;
  setOpen: (open: boolean) => void;
  userInfo: UserInfo | null;
};
