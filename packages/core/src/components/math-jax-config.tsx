import { MathJaxContext } from "better-react-mathjax";
import {
  createContext,
  FC,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

const CDN_URL = "https://static.xiaoluxue.com/assets";
const MATHJAX_CORE_URL = `${CDN_URL}/mathjax/tex-mml-chtml.js`;
const MATHJAX_FONT_URL = `${CDN_URL}/mathjax/output/chtml/fonts/woff-v2`;
const MATHJAX_LOAD = [
  "[tex]/ams", // AMS 数学符号
  "[tex]/cancel", // 删除线
  "[tex]/mhchem", // 化学式
  "[tex]/color", // 颜色
  "[tex]/boldsymbol", // 加粗符号
  "[tex]/braket", // 量子力学符号
  "[tex]/physics", // 物理命令
  "[tex]/upgreek", // 直立希腊字母
  "[tex]/newcommand", // 自定义命令
  "[tex]/extpfeil", // 扩展箭头
  "[tex]/textmacros", // 文本模式宏
  "[tex]/enclose", // 盒子命令
];
const MATHJAX_PACKAGES = [
  "ams",
  "cancel",
  "mhchem",
  "color",
  "boldsymbol",
  "braket",
  "physics",
  "upgreek",
  "newcommand",
  "extpfeil",
  "textmacros",
  "enclose",
];

// 创建一个上下文来跟踪 MathJax 的加载状态
export const MathJaxLoadedContext = createContext(false);

export const MathJaxConfig: FC<{ children: ReactNode }> = ({ children }) => {
  const [mathJaxLoaded, setMathJaxLoaded] = useState(false);
  useEffect(() => {
    if (typeof window !== "undefined" && (window as any).MathJax) {
      setMathJaxLoaded(true);
    }
  }, []);
  return (
    <MathJaxContext
      config={{
        options: {
          enableMenu: false,
        },
        loader: {
          load: MATHJAX_LOAD,
        },
        tex: {
          packages: {
            "[+]": MATHJAX_PACKAGES,
          },
          inlineMath: [
            ["$", "$"],
            ["\\(", "\\)"],
          ], // 行内公式起止符号
          displayMath: [
            ["$$", "$$"],
            ["\\[", "\\]"],
          ], // 块级公式起止符号
        },
        chtml: {
          fontURL: MATHJAX_FONT_URL,
        },
      }}
      src={MATHJAX_CORE_URL}
      asyncLoad
      onStartup={(mathJax) => {
        mathJax.startup.promise.then(() => {
          setMathJaxLoaded(true);
        });
      }}
      onError={() => {
        console.log("MathJax 安装失败 ....");
      }}
    >
      <MathJaxLoadedContext.Provider value={mathJaxLoaded}>
        {children}
      </MathJaxLoadedContext.Provider>
    </MathJaxContext>
  );
};
// 提供一个 hook 来访问 MathJax 加载状态
export const useMathJaxLoaded = () => useContext(MathJaxLoadedContext);
