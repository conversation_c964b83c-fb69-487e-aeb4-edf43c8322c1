/* eslint-disable */
import { Extension } from '@tiptap/core';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import cs from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { EditorConfig } from '../global';
import { EditorConfigProvider, useEditorDefaultConfig } from './config-ctx';
import ContextMenu from './ContextMenu';
import { Dot, Formula, Horizontal, Question, ResizableImg, Wavy } from './extensions';
import MenuBar from './menu-bar';
import './style.scss';
import { calculateFeatures, handleOldData, stripOuterNode } from './utils';
import { Feature } from './utils/enum';

// 自定义扩展来处理空格保留
const WhitespacePreserver = Extension.create({
  name: 'whitespacePreserver',

  addGlobalAttributes() {
    return [
      {
        types: ['paragraph'],
        attributes: {
          preserveWhitespace: {
            default: 'full',
          },
        },
      },
    ];
  },

  addProseMirrorPlugins() {
    const { Plugin } = require('@tiptap/pm/state');
    return [
      new Plugin({
        key: 'whitespace-parser',
        props: {
          transformPastedHTML(html: string) {
            // 创建临时 DOM 来处理 HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 递归清理不必要的 span 标签
            const cleanSpans = (element: Element) => {
              const spans = Array.from(element.querySelectorAll('span'));

              spans.forEach((span) => {
                // 跳过公式相关的重要 span
                if (
                  span.style.display === 'none' ||
                  span.closest('.formula-widget') ||
                  span.hasAttribute('data-formula-start') ||
                  span.hasAttribute('data-formula-end') ||
                  span.hasAttribute('data-custom-span') ||
                  span.classList.contains('formula-widget') ||
                  span.classList.contains('katex') ||
                  span.classList.contains('katex-mathml') ||
                  span.classList.contains('katex-html')
                ) {
                  return;
                }

                // 检查是否有重要的样式或类
                const hasImportantAttrs =
                  span.hasAttribute('class') ||
                  span.hasAttribute('style') ||
                  span.hasAttribute('data-tiptype') ||
                  span.hasAttribute('data-index');

                // 如果没有重要属性，则移除 span 但保留内容
                if (!hasImportantAttrs) {
                  // 将 span 的所有子节点移动到 span 的父节点中
                  const parent = span.parentNode;
                  if (parent) {
                    // 创建文档片段来保存子节点
                    const fragment = document.createDocumentFragment();
                    while (span.firstChild) {
                      fragment.appendChild(span.firstChild);
                    }
                    // 用片段替换 span
                    parent.replaceChild(fragment, span);
                  }
                }
              });
            };

            // 执行清理
            cleanSpans(tempDiv);

            // 预处理 HTML，确保空格被保留
            let processedHtml = tempDiv.innerHTML;
            processedHtml = processedHtml.replace(/\s+/g, (match: string) => {
              // 保留所有空格，包括多个连续空格
              return match;
            });

            return processedHtml;
          },
        },
      }),
    ];
  },
});

const CustomTableCell = TableCell.extend({
  parseHTML() {
    return [
      {
        tag: 'td',
        preserveWhitespace: 'full',
      },
    ];
  },
});
const CustomTableHeader = TableHeader.extend({
  parseHTML() {
    return [
      {
        tag: 'th',
        preserveWhitespace: 'full',
      },
    ];
  },
});

const CustomEditor = (props: EditorConfig) => {
  const features: Feature[] = calculateFeatures(props.includeFeatures, props.excludeFeatures);
  const defaultConfig = useEditorDefaultConfig();
  const config = { ...defaultConfig, ...props };

  const [uniqueId] = useState('tiptap_' + uuidv4().replace(/-/g, ''));
  const lastHTMLRef = useRef<string>('');
  const editor: any = useEditor({
    extensions: [
      StarterKit.configure({
        // 保留空白字符
        paragraph: {
          HTMLAttributes: {
            class: 'tiptap-paragraph',
          },
        },
      }),
      Underline,
      ResizableImg.configure({
        inline: true,
      }),
      Formula,
      Dot,
      Wavy,
      Horizontal,
      Question,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: 'left',
      }),
      Color,
      TextStyle,
      Highlight.configure({
        multicolor: true, // 允许多种颜色
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      CustomTableHeader,
      CustomTableCell,
      // Span, // 暂时禁用 Span 扩展以解决粘贴问题
      Placeholder.configure({
        placeholder: props.placeholder || '请输入 …',
      }),
      WhitespacePreserver,
    ],
    editorProps: {
      attributes: { 'data-id': uniqueId },
      handleDOMEvents: {
        // 点击工具栏的时候阻止失焦
        blur: (view, event) => {
          const editorId = view.dom.getAttribute('data-id');
          const relatedTarget = (event as FocusEvent).relatedTarget as HTMLElement;
          const noBlur = relatedTarget?.closest('.no-blur');
          const toolBar = relatedTarget?.closest('#' + editorId);
          if (toolBar || noBlur) return true;
          return false;
        },
      },
    },
    // 移除全局 preserveWhitespace 配置，避免影响表格等复杂结构
    parseOptions: {
      preserveWhitespace: false,
    },
    content: handleOldData(props.content) || '',
    onCreate({ editor }) {
      // 延迟执行，确保编辑器和内容完全准备好
      setTimeout(() => {
        Question.descendants(editor);
      }, 200);
    },
    onUpdate({ editor }) {
      let html = editor.getHTML();
      const json = editor.getJSON();

      // 清理 HTML 中不必要的 span 标签
      const cleanHTML = (htmlString: string) => {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlString;

        // 查找并清理不必要的 span
        const spans = Array.from(tempDiv.querySelectorAll('span'));
        spans.forEach((span) => {
          // 跳过重要的 span
          if (
            span.style.display === 'none' ||
            span.closest('.formula-widget') ||
            span.hasAttribute('data-formula-start') ||
            span.hasAttribute('data-formula-end') ||
            span.hasAttribute('data-custom-span') ||
            span.hasAttribute('data-tiptype') ||
            span.classList.contains('formula-widget') ||
            span.classList.contains('katex') ||
            span.classList.contains('katex-mathml') ||
            span.classList.contains('katex-html') ||
            span.classList.contains('dot') ||
            span.classList.contains('wavy')
          ) {
            return;
          }

          // 如果 span 没有重要属性，移除它但保留内容
          const hasImportantAttrs =
            span.hasAttribute('class') ||
            span.hasAttribute('style') ||
            span.hasAttribute('data-tiptype') ||
            span.hasAttribute('data-index');

          if (!hasImportantAttrs) {
            const parent = span.parentNode;
            if (parent) {
              const fragment = document.createDocumentFragment();
              while (span.firstChild) {
                fragment.appendChild(span.firstChild);
              }
              parent.replaceChild(fragment, span);
            }
          }
        });

        return tempDiv.innerHTML;
      };

      // 清理 HTML
      html = cleanHTML(html);

      // 清除空段落
      if (json?.content?.length === 1) {
        const firstNode = json.content[0];
        if (firstNode.type === 'paragraph' && !firstNode.content) {
          delete json.content;
          html = '';
        }

        if (config.stripOuterNode) {
          html = stripOuterNode(html);
        }
      }
      // html = unwrapMathFieldToLatex(html);

      //onUpdate 在 Tiptap 中的触发机制：哪怕你只是点击编辑器、没有改变内容，但如果 Tiptap 在内部调整了文档结构（比如补全空段落、自动清理节点等），也会触发
      const newHTML = editor.getHTML();
      if (newHTML !== lastHTMLRef.current) {
        lastHTMLRef.current = newHTML;
        props?.onUpdate && props.onUpdate({ html, json });
        props?.onChange && props.onChange(html);
      }
    },
    onFocus(arg) {
      if (config.clickToEdit) {
        startEdit();
      }
      config.onFocus && config.onFocus(arg);
    },
    onBlur(arg) {
      editor.setEditable(false);
      config.onBlur && config.onBlur(arg);
    },
    editable: Boolean(props.editable),
  });

  useEffect(() => {
    return () => {
      if (editor) {
        editor.destroy();
      }
    };
  }, [editor]);

  const startEdit = useCallback(() => {
    if (editor && !editor.isEditable) {
      editor.setEditable(true);
      editor.commands.focus(); // 先聚焦编辑器
      const docSize = editor.state.doc.content.size;
      editor.commands.setTextSelection(docSize); // 将光标移到文档的最后
    }
  }, [editor]);

  const stopEdit = useCallback(() => {
    if (editor) {
      editor.setEditable(false);
      editor.commands.blur();
    }
  }, [editor]);

  useEffect(() => {
    if (props.editable === editor.isEditable) return;
    if (props.editable) {
      startEdit();
    } else {
      stopEdit();
    }
  }, [props.editable]);

  useEffect(() => {
    if (!editor) return;

    // 如果不一样，再覆盖，否则插入的横线有问题，未知原因
    if (props.content !== editor.getHTML()) {
      const processedContent = handleOldData(props.content);
      // 使用更精确的空格处理方案，避免影响表格等复杂结构
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = processedContent;

      // 只处理文本节点中的连续空格，避免影响表格等结构化内容
      const walker = document.createTreeWalker(tempDiv, NodeFilter.SHOW_TEXT, {
        acceptNode: (node) => {
          // 只处理不在表格内的文本节点
          const parent = node.parentElement;
          if (
            parent &&
            (parent.tagName === 'TD' || parent.tagName === 'TH' || parent.closest('table'))
          ) {
            return NodeFilter.FILTER_REJECT;
          }
          return NodeFilter.FILTER_ACCEPT;
        },
      });

      let node;
      while ((node = walker.nextNode())) {
        if (node.textContent) {
          // 将多个连续空格替换为不间断空格，但保持单个空格不变
          node.textContent = node.textContent.replace(/  +/g, (match) => {
            return '\u00A0'.repeat(match.length);
          });
        }
      }

      // 设置处理后的内容，不使用可能导致表格错误的 preserveWhitespace
      editor.commands.setContent(tempDiv.innerHTML);

      // 内容设置后，延迟更新填空点属性，确保 DOM 完全渲染
      setTimeout(() => {
        Question.descendants(editor);
      }, 100);
    }
    const mathFields = document.querySelectorAll('math-field');
    mathFields.forEach((field: any) => {
      field.readonly = true;
    });
  }, [props.content]);

  useEffect(() => {
    if (editor) {
      const { onQuestionFillAreaInsert, onQuestionFillAreaDelete, onQuestionFillAreaClick } = props;
      editor.on('question:insert', (event: any) => {
        onQuestionFillAreaInsert && onQuestionFillAreaInsert(event);
      });
      editor.on('question:delete', (event: any) => {
        onQuestionFillAreaDelete && onQuestionFillAreaDelete(event);
      });
      editor.on('question:click', (event: any) => {
        onQuestionFillAreaClick && onQuestionFillAreaClick(event);
      });
    }
    return () => {
      if (editor) {
        editor.off('question:insert');
        editor.off('question:delete');
        editor.off('question:click');
      }
    };
  }, [
    editor,
    props.onQuestionFillAreaInsert,
    props.onQuestionFillAreaDelete,
    props.onQuestionFillAreaClick,
  ]);

  // onPointerDownCapture 是因为 math-field 的点击事件会冒泡到父级，导致无法执行
  return (
    <EditorConfigProvider {...{ ...config, editor, features }}>
      <div
        className={cs(['tiptap-editor', { editable: editor.isEditable }])}
        id={uniqueId}
        onPointerDownCapture={startEdit}
      >
        {editor.isEditable && <MenuBar />}
        <ContextMenu editor={editor}>
          <EditorContent editor={editor} className="editorContent" />
        </ContextMenu>
      </div>
    </EditorConfigProvider>
  );
};

export default CustomEditor;
