/**
 * 静默加载组件
 * 
 * 用于需要保持布局但不显示加载文字的场景
 * 特别适用于预览页面或不希望显示加载状态的组件
 */

import { FC } from 'react';
import { cn } from '@repo/ui/lib/utils';

interface SilentLoadingProps {
  /** 自定义类名 */
  className?: string;
  /** 背景色，默认为练习页面背景色 */
  backgroundColor?: string;
  /** 是否完全透明 */
  transparent?: boolean;
}

export const SilentLoading: FC<SilentLoadingProps> = ({
  className,
  backgroundColor = 'bg-[#FEF8F4]',
  transparent = false,
}) => {
  return (
    <div 
      className={cn(
        'silent-loading h-full w-full',
        transparent ? 'opacity-0' : backgroundColor,
        className
      )}
    >
      {/* 静默加载：不显示任何内容，只保持布局 */}
    </div>
  );
};

export default SilentLoading;
