import "@testing-library/jest-dom";
import { vi } from "vitest";

// 检查是否在浏览器环境（jsdom）
const isBrowserEnvironment = typeof window !== "undefined";

if (isBrowserEnvironment) {
  // 只在浏览器环境下设置这些mock

  // Mock Next.js router
  vi.mock("next/navigation", () => ({
    useRouter: () => ({
      push: vi.fn(),
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
    }),
    usePathname: () => "/",
    useSearchParams: () => new URLSearchParams(),
  }));

  // Mock Next.js image
  vi.mock("next/image", () => ({
    default: ({ src, alt, ...props }: any) => {
      return { src, alt, ...props };
    },
  }));

  // Global test setup
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock window.matchMedia
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

// Mock environment variables (适用于所有环境)
process.env.NEXT_PUBLIC_API_HOST = "http://localhost:3000";
