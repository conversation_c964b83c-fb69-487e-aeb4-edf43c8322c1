import FixedLayout from "@/components/common/FixedLayout";
import { RESOURCE_TYPE } from "@/enums";
import { CourseBaseInfo } from "@/types/assign/course";
import { memo } from "react";
import { AiCoursePreviewProps } from "../AiCoursePreview";
import AiCoursePreviewLayout from "./AiCoursePreviewLayout";
import PracticePreviewLayout from "./PracticePreviewLayout";

type CommonResourcePreviewProps = {
  onBack?: () => void;

  headerSuffixNode?: React.ReactNode;
};

export type AiCourseResourcePreviewProps = {
  resourceId: string;
  resourceType: RESOURCE_TYPE.RESOURCE_TYPE_AI_COURSE;
  subjectKey?: number;
} & Pick<
  AiCoursePreviewProps,
  "handlerRef" | "onEnterFullscreen" | "onExitFullscreen"
>;

export type PracticeResourcePreviewProps = {
  resourceId: string;
  resourceType: RESOURCE_TYPE.RESOURCE_TYPE_PRACTICE;
  subjectKey: number;
  treeNodeInfo?: CourseBaseInfo;
};

export type SpecificRecoursePreviewProps =
  | AiCourseResourcePreviewProps
  | PracticeResourcePreviewProps
  | {
      resourceType: RESOURCE_TYPE;
      [key: string]: any;
    };

export type ResourcePreviewProps = CommonResourcePreviewProps &
  SpecificRecoursePreviewProps;

// 资源预览组件
function ResourcePreview(props: ResourcePreviewProps) {
  const { resourceId, resourceType, headerSuffixNode, onBack } = props;

  if (resourceType === RESOURCE_TYPE.RESOURCE_TYPE_AI_COURSE) {
    const { handlerRef, onEnterFullscreen, onExitFullscreen } = props;
    return (
      <FixedLayout>
        <AiCoursePreviewLayout
          id={resourceId}
          onBack={onBack}
          headerSuffixNode={headerSuffixNode}
          handlerRef={handlerRef}
          onEnterFullscreen={onEnterFullscreen}
          onExitFullscreen={onExitFullscreen}
        />
      </FixedLayout>
    );
  }

  if (resourceType === RESOURCE_TYPE.RESOURCE_TYPE_PRACTICE) {
    const { subjectKey, treeNodeInfo } = props;
    return (
      <FixedLayout>
        <PracticePreviewLayout
          id={resourceId}
          onBack={onBack}
          subjectKey={subjectKey}
          treeNodeInfo={treeNodeInfo}
        />
      </FixedLayout>
    );
  }

  onBack?.();

  return null;
}

export default memo(ResourcePreview);
