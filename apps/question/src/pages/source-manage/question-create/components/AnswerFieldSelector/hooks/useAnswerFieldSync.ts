import { Form } from 'antd';
import lodash from 'lodash';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useAnswerFieldSyncContext } from '../contexts/answerContext';

export interface UseAnswerFieldSyncOptions {
  /** 是否启用匹配线模式（m选n模式） */
  isMatchLine: boolean;
  /** 当前活跃的插入点索引 */
  activeFillIdx: number;
  /** 表单字段路径 */
  questionOptionMatrixName: string | (string | number)[];
  /** 表单实例 */
  form: any;
  /** Form.List的字段信息 */
  fields: any[];
}

/**
 * 答案字段实时同步 Hook
 * 用于处理 m选n 模式下的答案实时同步逻辑
 */
export const useAnswerFieldSync = ({
  isMatchLine,
  activeFillIdx,
  questionOptionMatrixName,
  form,
  fields,
}: UseAnswerFieldSyncOptions) => {
  // 使用Context管理同步状态
  const { isSyncing, setIsSyncing, updateSyncInfo } = useAnswerFieldSyncContext();

  // 使用 ref 存储最后同步时间，避免作为依赖导致无限循环
  const lastSyncTimeRef = useRef(0);

  // 防抖时间配置
  const SYNC_DEBOUNCE_TIME = 100; // 100ms防抖，减少频繁触发

  /**
   * 监听整个答案数组
   */
  const allAnswersFieldPath = useMemo(() => {
    return questionOptionMatrixName;
  }, [questionOptionMatrixName]);

  /**
   * 检查值是否有实际内容
   */
  const hasValidContent = useCallback((value: any): boolean => {
    if (!lodash.isPlainObject(value)) {
      return false;
    }

    // 只检查单选题选项内容
    if (value.singleChoice?.items) {
      const hasNonEmptyItems = value.singleChoice.items.some(
        (item: any) => lodash.isString(item.value) && !lodash.isEmpty(item.value.trim()),
      );
      if (hasNonEmptyItems) return true;
    }

    // 检查其他类型的答案内容
    return lodash.some(value, (val) => {
      if (lodash.isString(val)) {
        return !lodash.isEmpty(val.trim());
      }
      return !lodash.isEmpty(val);
    });
  }, []);

  /**
   * 构建字段路径
   */
  const buildFieldPath = useCallback(
    (index: number): any => {
      return Array.isArray(questionOptionMatrixName)
        ? [...questionOptionMatrixName, index]
        : [questionOptionMatrixName, index];
    },
    [questionOptionMatrixName],
  );

  /**
   * 执行同步操作 - 只同步单选题选项内容(items)，不同步选中状态
   */
  const performSync = useCallback(
    (sourceValue: any, sourceIndex: number) => {
      const now = Date.now();

      // 防抖检查 - 使用 ref 避免依赖循环
      if (now - lastSyncTimeRef.current < SYNC_DEBOUNCE_TIME) {
        return;
      }

      // 更新防抖时间戳
      lastSyncTimeRef.current = now;

      // 设置同步状态和信息
      setIsSyncing(true);
      updateSyncInfo({
        lastSyncTime: now,
        sourceIndex,
        targetCount: fields.length - 1, // 排除源字段本身
      });

      // 收集需要更新的字段
      const updates: Array<{ path: any; items: any; answerType: string; index: number }> = [];

      for (let i = 0; i < fields.length; i++) {
        // 排除当前正在编辑的插入点
        if (i === sourceIndex) {
          continue;
        }

        const targetFieldPath = buildFieldPath(i);
        const targetValue = form.getFieldValue(targetFieldPath);

        // 只同步单选题的选项内容
        if (sourceValue?.singleChoice?.items && targetValue?.singleChoice) {
          const sourceItems = sourceValue.singleChoice.items;
          const targetItems = targetValue.singleChoice.items;

          if (!lodash.isEqual(sourceItems, targetItems)) {
            updates.push({
              path: [...targetFieldPath, 'singleChoice', 'items'],
              items: lodash.cloneDeep(sourceItems),
              answerType: 'singleChoice',
              index: i,
            });
          }
        }
      }

      // 批量执行更新
      if (updates.length > 0) {
        requestAnimationFrame(() => {
          updates.forEach(({ path, items }) => {
            form.setFieldValue(path, items);
          });

          // 在更新完成后清除同步标记
          requestAnimationFrame(() => {
            setIsSyncing(false);
          });
        });
      } else {
        setIsSyncing(false);
      }
    },
    [fields, buildFieldPath, form, SYNC_DEBOUNCE_TIME, setIsSyncing, updateSyncInfo],
  );

  /**
   * 监听整个答案数组的变化 - 使用静态路径避免动态路径警告
   */
  const allAnswerValues = Form.useWatch(allAnswersFieldPath, form);

  /**
   * 从整个答案数组中提取当前活跃插入点的值
   */
  const currentAnswerValue = useMemo(() => {
    if (!allAnswerValues || !Array.isArray(allAnswerValues)) {
      return null;
    }
    return allAnswerValues[activeFillIdx] || null;
  }, [allAnswerValues, activeFillIdx]);

  /**
   * 实时同步效果
   */
  useEffect(() => {
    // 检查是否启用同步模式
    if (!isMatchLine || fields.length === 0) {
      return;
    }

    // 检查是否正在同步中
    if (isSyncing) {
      return;
    }

    // 检查当前值是否有效
    if (!hasValidContent(currentAnswerValue)) {
      return;
    }

    // 执行同步
    performSync(currentAnswerValue, activeFillIdx);
  }, [
    isMatchLine,
    currentAnswerValue,
    activeFillIdx,
    fields.length,
    hasValidContent,
    performSync,
    isSyncing,
  ]);

  /**
   * 组件卸载时的清理
   */
  useEffect(() => {
    return () => {
      setIsSyncing(false);
      updateSyncInfo({
        lastSyncTime: 0,
        sourceIndex: null,
        targetCount: 0,
      });
    };
  }, [setIsSyncing, updateSyncInfo]);

  return {
    /**
     * 当前监听的答案值
     */
    currentAnswerValue,
    /**
     * 是否正在同步中
     */
    isSyncing,
    /**
     * 手动触发同步（用于特殊场景）
     */
    triggerSync: performSync,
  };
};
