import { fileToBase64 } from '@/utils/common';
import { request } from '@umijs/max';

/**
 * 搜索题目列表
 */
export async function fetchQuestionList(
  params: API.QuestionListRequestParams,
): Promise<API.QuestionListResponse> {
  return request('/api/v1/resource/get/question/list', {
    method: 'POST',
    data: params,
  });
}

/**
 * 搜索我的题目列表
 */
export async function fetchMyQuestionList(
  params: API.QuestionListRequestParams,
): Promise<API.QuestionListResponse> {
  return request('/api/v1/resource/my/upload/question/list', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取备用图片
 */
export async function fetchQuestionResource(
  params: API.QuestionResourceRequestParams,
): Promise<API.QuestionResourceResponse> {
  return request('/api/v1/resource/get/resource/content', {
    method: 'GET',
    params,
  });
}

/**
 * 上传题目附件
 */
export async function uploadQuestionAttach(params: any): Promise<any> {
  return request('/api/v1/resource/upload/question/attach', {
    method: 'POST',
    data: params,
  });
}

export async function uploadQuestionAttachHelper(fileToUpload: File): Promise<any> {
  const fileBase64 = await fileToBase64(fileToUpload);
  const res = await uploadQuestionAttach({ attach_base64_data: fileBase64 });
  return res.data.attach_url;
}
