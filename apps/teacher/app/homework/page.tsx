"use client";
import { useApp } from "@/hooks";
import { useUmeng } from "@/hooks/useUmeng";
import { UmengCategory } from "@/utils";
import {
  getStorageSync,
  removeStorageAsync,
  setStorageAsync,
} from "@/utils/storage-utils";
import { useMount, useUnmount } from "ahooks";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { Navigation } from "./_components/navigation";
import { TaskList } from "./_components/task-list";
import { HomeworkListProvider, useHomeworkListContext } from "./context";
function HomeworkContent() {
  useUmeng(UmengCategory.HOMEWORK, "homework_list");
  const { initializeData, initializeQuery, setQuery } =
    useHomeworkListContext();
  const searchParams = useSearchParams();
  const { setOpen } = useApp();
  const source = searchParams?.get("source");

  // 组件挂载时初始化数据
  // useEffect(() => {
  //   initializeData();
  // }, [initializeData]);

  // 统一处理侧边栏状态和来源信息
  useEffect(() => {
    if (source === "assign") {
      // URL 中有 source=assign 参数
      setOpen(false);
      setStorageAsync("homework_source", "assign").catch(console.error);
    } else {
      // URL 中没有 source 参数，检查 localStorage
      const storedSource = getStorageSync<string>("homework_source");
      if (storedSource === "assign") {
        // 从详情页返回的情况，保持侧边栏收起状态
        setOpen(false);
      } else {
        // 正常访问作业列表，展开侧边栏
        setOpen(true);
        if (storedSource !== "courseId") {
          const query = initializeQuery(true);
          setQuery(query, false);
        }
      }
    }
  }, [source, setOpen, initializeQuery, setQuery]);

  useMount(() => {
    // console.log("mount");
    initializeData();
  });

  useUnmount(() => {
    // console.log("unmount");
    removeStorageAsync("homework_source").catch(console.error);
  });

  return (
    <div className="flex h-full flex-col">
      <Navigation source={source} />
      <div className="flex-1 overflow-auto pb-4">
        <TaskList />
      </div>
    </div>
  );
}

export default function ReportPage() {
  return (
    <HomeworkListProvider>
      <HomeworkContent />
    </HomeworkListProvider>
  );
}
