import { CardContent, Card as OriginCard } from "@/ui/card";
import { cn } from "@/utils/utils";
import Header from "./Header";

interface CardProps {
  style?: React.CSSProperties;
  className?: string;
  children?: React.ReactNode;
  title?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function Card({
  style = {},
  className = "",
  children,
  title = "",
  onScroll,
}: CardProps) {
  return (
    <OriginCard
      className={cn(
        "rounded-2xl border-0 bg-white p-6 shadow-none outline-[1px] outline-offset-[-1px] outline-slate-200",
        className
      )}
      style={style}
      onScroll={onScroll}
    >
      {title && <Header content={title} />}
      <CardContent className="h-full p-0">{children}</CardContent>
    </OriginCard>
  );
}
