.selectorContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selectorItem {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;

  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }

  &.visited {
    color: #52c41a;
    border-color: #52c41a;

    &.selected {
      color: white;
      background-color: #1890ff;
      border-color: #1890ff;
    }
  }

  &.error {
    color: #ff4d4f;
    border-color: #ff4d4f;
  }

  &.selected {
    color: white;
    background-color: #1890ff;
    border-color: #1890ff;
  }

  &.disabled {
    color: #d9d9d9;
    cursor: not-allowed;
    &:hover {
      color: #d9d9d9;
      border-color: #d9d9d9;
    }
    &.selected {
      background-color: rgba(24, 144, 255, 0.5);
      border-color: #d9d9d9;
    }
  }
}
