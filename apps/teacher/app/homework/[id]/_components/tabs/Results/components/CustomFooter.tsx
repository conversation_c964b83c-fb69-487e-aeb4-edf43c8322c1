"use client";

import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import IcCorrect from "@/public/icons/ic_correct.svg";
import IcHalfcorrect from "@/public/icons/ic_halfcorrect.svg";
import IcIncorrect from "@/public/icons/ic_incorrect.svg";
import IcUnanswered from "@/public/icons/ic_noanswer.svg";
import IcUndecided from "@/public/icons/ic_undecided.svg";
import { Separator } from "@/ui/separator";
import { QuestionContext } from "@repo/core/views/tch-question-view";
interface CustomFooterProps {
  context: Omit<QuestionContext, "options">;
}

export default function CustomFooter({ context }: CustomFooterProps) {
  const { qaContent, correctRatePercent, isCommonWrong } = context;

  // console.log("context", context, isCommonWrong);

  // 从 task-context 中获取 viewMode
  const { viewMode, viewModeNew } = useTaskContext();

  // 计算错误人数
  const totalAnswers = qaContent.answerCount || qaContent.answerDetails.length;
  // 计算正确人数
  const correctAnswers = qaContent.answerDetails.filter(
    (detail) => detail.isCorrect || detail.answerResult === 1
  ).length;
  // 计算错误人数
  const incorrectAnswers =
    qaContent.incorrectCount || totalAnswers - correctAnswers;
  // console.log("qaContent", {
  //   qaContent,
  //   incorrectAnswers,
  //   correctAnswers,
  //   totalAnswers,
  // });
  const getAnswerStatus = () => {
    let status = "unanswered";
    if (qaContent.answerDetails.length > 0) {
      const tempStatus = qaContent.answerDetails[0].answerResult;
      switch (tempStatus) {
        case 1:
          status = "correct";
          break;
        case 2:
          status = "incorrect";
          break;
        case 3:
          status = "halfcorrect";
          break;
        case 0:
          status = "unanswered";
          break;
        case 99:
          status = "undecided";
          break;
      }
    }
    return status;
  };
  const answerStatus = getAnswerStatus();
  // 根据正确率决定颜色
  const rateColorClass =
    correctRatePercent > 50 ? "text-green-600" : "text-red-600";

  // 只返回左侧内容，不包含查看解析按钮
  return (
    <div className="flex items-center gap-2">
      {/* 如果是班级视图，显示特定的统计信息 */}
      {viewMode.value === "class" && viewModeNew.value !== "student" ? (
        <div className="flex items-center text-sm">
          <div className="flex items-center gap-1">
            <span>正确率</span>
            <span className={`font-medium ${rateColorClass}`}>
              {correctRatePercent}%
            </span>
          </div>
          {/* 分割线 */}
          <Separator
            orientation="vertical"
            className="bg-line-2 mx-2 !h-4 w-[1px]"
          />

          <div className="flex items-center gap-1">
            <span className="font-medium text-red-600">{incorrectAnswers}</span>
            <span className="text-sm text-gray-500">人错误</span>
          </div>
          <Separator
            orientation="vertical"
            className="bg-line-2 mx-2 !h-4 w-[1px]"
          />

          <div className="flex items-center gap-1">
            <span className="font-medium">{totalAnswers}</span>
            <span className="text-sm text-gray-500">人作答</span>
          </div>
        </div>
      ) : (
        <>
          {!totalAnswers ? (
            <div className="flex items-center gap-1">
              {/* <Image src={ic_unanswered} alt="correct" width={22} height={22} /> */}
              <IcUnanswered className="h-6 w-6" />
              <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                尚无作答
              </span>
            </div>
          ) : (
            <>
              {/* {correctAnswers ? (
                <div className="flex items-center gap-1">
                  <Image
                    src={ic_correct}
                    alt="correct"
                    width={22}
                    height={22}
                  />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    正确
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Image
                    src={ic_incorrect}
                    alt="incorrect"
                    width={22}
                    height={22}
                  />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    错误
                  </span>
                </div>
              )} */}
              {qaContent.answerDetails.length === 0 ? (
                <div className="flex items-center gap-1">
                  {/* <Image
                    src={ic_unanswered}
                    alt="correct"
                    width={22}
                    height={22}
                  /> */}
                  <IcUnanswered className="h-6 w-6" />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    尚无作答
                  </span>
                </div>
              ) : answerStatus === "correct" ? (
                <div className="flex items-center gap-1">
                  {/* <Image
                    src={ic_correct}
                    alt="correct"
                    width={22}
                    height={22}
                  /> */}
                  <IcCorrect className="h-6 w-6" />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    正确
                  </span>
                </div>
              ) : answerStatus === "incorrect" ? (
                <div className="flex items-center gap-1">
                  {/* <Image
                    src={ic_incorrect}
                    alt="incorrect"
                    width={22}
                    height={22}
                  /> */}
                  <IcIncorrect className="h-6 w-6" />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    错误
                  </span>
                </div>
              ) : answerStatus === "halfcorrect" ? (
                <div className="flex items-center gap-1">
                  {/* <Image
                    src={ic_halfcorrect}
                    alt="halfcorrect"
                    width={22}
                    height={22}
                  /> */}
                  <IcHalfcorrect className="h-6 w-6" />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    半对
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  {/* <Image
                    src={ic_undecided}
                    alt="undecided"
                    width={22}
                    height={22}
                  /> */}
                  <IcUndecided className="h-6 w-6" />
                  <span className="text-[0.875rem] text-sm font-normal leading-[150%] text-[#444963]">
                    未判定
                  </span>
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}
