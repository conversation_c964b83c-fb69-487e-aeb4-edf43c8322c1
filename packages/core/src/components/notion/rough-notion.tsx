"use client";
import React, { useCallback, useEffect, useRef } from "react";

import { annotate } from "@repo/rough-notation";

import { Annotation, RoughNotionProps } from "./types";

const RoughNotion: React.FunctionComponent<RoughNotionProps> = ({
  isLatex = false,
  animate = true,
  animationDelay = 0,
  animationDuration = 800,
  brackets,
  children,
  color,
  customElement = "span",
  getAnnotationObject,
  iterations = 2,
  multiline = true,
  order,
  padding = 5,
  show = false,
  strokeWidth = 1,
  type = "underline",
  ...rest
}) => {
  const element = useRef<HTMLElement>(null);
  const annotation = useRef<Annotation>(null);
  const innerVars = useRef<{
    playing: boolean;
    timeout: null | number;
  }>({
    playing: false,
    timeout: null,
  });
  const initialOptions = useRef({
    animate,
    animationDuration,
    brackets,
    color,
    getAnnotationObject,
    iterations,
    multiline,
    padding,
    strokeWidth,
    type,
  });

  const showAnnotation = useCallback(() => {
    if (!innerVars.current.timeout) {
      innerVars.current.timeout = window.setTimeout(() => {
        innerVars.current.playing = true;

        annotation.current?.show?.();

        window.setTimeout(() => {
          innerVars.current.playing = false;
          innerVars.current.timeout = null;
        }, animationDuration);
      }, animationDelay);
    }
  }, [animationDelay, animationDuration]);

  const hideAnnotation = useCallback(() => {
    annotation.current?.hide?.();
    innerVars.current.playing = false;

    if (innerVars.current.timeout) {
      clearTimeout(innerVars.current.timeout);
      innerVars.current.timeout = null;
    }
  }, []);

  const getAnnotation = useCallback(() => {
    return annotation.current!;
  }, [annotation]);

  useEffect(() => {
    const options = initialOptions.current;
    const { getAnnotationObject: getAnnotationObjectFromOptions } = options;

    annotation.current = annotate(element.current!, options);

    if (getAnnotationObjectFromOptions) {
      getAnnotationObjectFromOptions(annotation.current);
    }

    // 监听 resize 事件，重新计算位置
    const handleResize = () => {
      if (annotation.current && show) {
        // 重新计算位置
        annotation.current.hide();
        setTimeout(() => {
          annotation.current?.show();
        }, 50);
      }
    };

    // 添加 data 属性用于选择器
    if (element.current) {
      element.current.setAttribute("data-rough-notation", "true");
    }

    // 监听 resize 事件
    element.current?.addEventListener("resize", handleResize);

    return () => {
      annotation.current?.remove?.();
      element.current?.removeEventListener("resize", handleResize);
    };
  }, [show]);

  useEffect(() => {
    if (show) {
      showAnnotation();
    } else {
      hideAnnotation();
    }
  }, [
    annotation,
    show,
    animationDelay,
    innerVars,
    animationDuration,
    showAnnotation,
    hideAnnotation,
  ]);

  useEffect(() => {
    if (annotation.current) {
      annotation.current.animate = animate;
      annotation.current.animationDuration = animationDuration;
      annotation.current.color = color;
      annotation.current.strokeWidth = strokeWidth;
      annotation.current.padding = padding;
    }
  }, [annotation, animate, animationDuration, color, strokeWidth, padding]);

  return React.createElement(
    customElement,
    {
      ref: element,
      ...rest,
      className: isLatex ? "inline-block" : "inline",
    },
    children
  );
};

export default RoughNotion;
