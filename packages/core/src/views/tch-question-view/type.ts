import { ReactNode } from "react";

// 使用本地定义的 Answer 类型，与 answers.ts 中的定义保持一致
export interface QaContentType {
  questionTags: string[];
  questionId: string;
  questionVersionId: number;
  answer?: string;
  questionAnswer: QuestionAnswer;
  questionAnswerMode: number;
  answerExplain: string;
  avgCostTime: number;
  content: string;
  questionType: number;
  resourceId: string;
  resourceType: number;
  answerDetails: AnswerDetail[];
  answerCount?: number;
  incorrectCount?: number;
  correctRate?: number;
  questionDifficult?: number;
  subQuestionList?: QaContentType[];
  subQuestionAnswers?: Array<
    QaContentType & {
      answers?: Array<{
        isCorrect: boolean;
        studentId: number;
        costTime: number;
        answerResult: number;
        answer: string;
      }>;
      questionAnswerMode: number;
    }
  >;
  questionIndex?: string;
  questionIndexStr?: string;
  phase: number;
  // 选项数据，来自新版数据结构中的 questionOptionList
  options?: { key: string; content: string }[];
  rootQuestionId?: string;
}

export interface QuestionAnswer {
  answerOptionList: QuestionOptionItem[];
}

export interface QuestionOptionItem {
  optionKey: string;
  optionVal: string;
}

export interface AnswerDetail {
  answer: string;
  costTime: number;
  isCorrect: boolean;
  studentId: number;
  answerResult?: number;
}
// 定义题目数据上下文，包含所有题目相关信息
export interface QuestionContext {
  qaContent: QaContentType;
  index: number;
  correctCount: number;
  totalCount: number;
  correctRate: number;
  correctRatePercent: number;
  isCommonWrong: boolean;
  options: { key: string; content: string }[];
  getQuestionTypeLabel: (type: number) => string;
  getQuestionTypeColor: (type: number) => string;
}

export interface QuestionItemProps {
  qaContent: QaContentType;
  index?: number;
  customIndex?: string;
  onClickView?: (id: string) => void;
  // 自定义卡片底部组件，接收题目上下文数据
  customFooter?: React.ComponentType<{
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    context: Omit<QuestionContext, "options"> | any;
  }>;
  // 自定义底部按钮组件，接收与customFooter相同的上下文数据
  footerButton?: ReactNode;
  hasFooterButton?: boolean;
  className?: string;
}
