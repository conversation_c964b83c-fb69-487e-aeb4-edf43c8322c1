import { BookOpen } from 'lucide-react'

export function InteractionDetails() {
  return (
    <section className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-gray-900">课堂互动详情</h2>
        <button className="flex items-center space-x-1 text-sm text-blue-600">
          <BookOpen className="w-4 h-4" />
          <span>知识树</span>
        </button>
      </div>
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-3 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900">第{i}章 三角函数</h3>
            <p className="text-xs text-gray-500 mt-1">已完成 3/5 个知识点</p>
          </div>
        ))}
      </div>
    </section>
  )
} 