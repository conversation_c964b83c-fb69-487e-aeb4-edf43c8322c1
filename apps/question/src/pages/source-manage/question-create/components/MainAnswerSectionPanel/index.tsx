import { Flex, Form } from 'antd';
import React, { memo, useMemo } from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';
import { getQuestionStemFillPointUuids } from '../../utils/questionBlank';
import AnswerFieldSelector from '../AnswerFieldSelector';
import QuestionAnswerModeSelector from '../QuestionAnswerModeSelector';
import QuestionMatchLine from '../QuestionMatchLine';

/**
 * 主题目答案区域组件
 * 负责管理主题目的作答方式和答案字段
 */
const MainAnswerSectionPanel: React.FC<{
  currentFillIdx: number;
}> = ({ currentFillIdx }) => {
  const { form } = useQuestionCreate();

  const subQuestionList = Form.useWatch('subQuestionList', form) || [];
  const questionStem = Form.useWatch('questionStem', form);
  const questionAnswerMode = Form.useWatch('questionAnswerMode', form);
  const hasSubQuestions = subQuestionList.length > 0;
  const isMatchLineName = 'isMatchLine';
  const isMatchLine = Form.useWatch(isMatchLineName, form);
  const questionOptionMatrixName = 'questionOptionMatrix';
  const count = useMemo(() => {
    return getQuestionStemFillPointUuids(questionStem || '').length;
  }, [questionStem]);

  // 当有一级题目时，不显示主题干的作答字段
  if (hasSubQuestions) {
    return null;
  }

  return (
    <>
      <Flex justify="space-between">
        <QuestionAnswerModeSelector name="questionAnswerMode" count={count} />
        <QuestionMatchLine
          name={'isMatchLine'}
          count={count}
          isMatchLineName={isMatchLineName}
          questionAnswerMode={questionAnswerMode}
        />
      </Flex>
      <AnswerFieldSelector
        questionAnswerMode={questionAnswerMode}
        isMatchLine={isMatchLine}
        fieldName="questionOptionMatrix"
        questionOptionMatrixName={questionOptionMatrixName}
        questionStem={questionStem}
        currentFillIdx={currentFillIdx}
      />
    </>
  );
};
export default memo(MainAnswerSectionPanel);
