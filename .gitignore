# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist
cache


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
.cursor/rules/global-rules/monorepo&global.mdc
.cursor/rules/project-rules/stu.mdc
.cursor/rules/project-rules/tch.mdc
apps/admin/src/.umi/
apps/question/src/.umi/
apps/teacher/next-env.d.ts
apps/teacher/tsconfig.tsbuildinfo
.claude/
.cursor/rules/project-rules/teacher.mdc
.cursor/rules/reviewGata.mdc
apps/admin/src/.umi/
.augment
