import {
  BaseEdge,
  EdgeProps,
  getSimpleBezierPath,
  Position,
} from "@xyflow/react";
import { FC } from "react";

export const MindMapEdge: FC<EdgeProps> = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
  id,
}) => {
  const [edgePath] = getSimpleBezierPath({
    sourceX,
    sourceY,
    sourcePosition: Position.Right,
    targetX,
    targetY,
    targetPosition: Position.Left,
  });
  return (
    <BaseEdge id={id} path={edgePath} className="stroke-gray-2 stroke-1" />
  );
};
