import { cn } from "@/utils/utils";
import Image from "next/image";
import studentAvatar from "@/public/assign/student-avatar.png";
import { StudentItem } from "@/types/assign";

interface StudentProfileProps {
    style?: React.CSSProperties,
    className?: string,
    studentInfo: StudentItem,
}

export function StudentProfile({
    style = {},
    className = "",
    studentInfo,
}: StudentProfileProps) {
    
    return (
        <div className={cn("flex gap-2 px-2 py-1.5 items-center", className)} style={style}>
           <Image src={studentAvatar} alt="profile" width={32} height={32} className="size-8 rounded-full" />
           <div className="text-neutral-900 text-sm font-normal leading-tight">{studentInfo.name}</div>
        </div>
    )
}

