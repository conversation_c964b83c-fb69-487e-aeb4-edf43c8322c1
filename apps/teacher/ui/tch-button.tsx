"use client";

import { cn } from "@/utils/utils";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
  "inline-flex items-center justify-center cursor-pointer whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none",
  {
    variants: {
      type: {
        primary:
          "bg-indigo-500 text-white hover:bg-indigo-400 active:bg-indigo-600",
        outline:
          "border border-indigo-500 bg-transparent text-indigo-500 hover:bg-indigo-50 active:bg-indigo-100",
        default:
          "border border-slate-200 bg-white text-slate-600 hover:bg-slate-50 active:bg-slate-200",
        text: "bg-white text-slate-600 hover:bg-slate-50 active:bg-slate-200",
        error:
          "border border-red-200 bg-white text-red-600 hover:bg-rose-50 active:bg-red-100",
      },
      radius: {
        default: "rounded",
        full: "rounded-full",
      },
      state: {
        default: "",
        hover: "",
        pressed: "",
        disabled: "cursor-not-allowed",
        loading: "cursor-wait",
      },
      size: {
        sm: "h-7 px-3 text-xs",
        md: "h-8 px-4 text-sm",
        lg: "h-9 px-5 text-sm",
      },
      isIconOnly: {
        true: "p-0",
        false: "",
      },
    },
    defaultVariants: {
      type: "default",
      radius: "default",
      size: "md",
      state: "default",
      isIconOnly: false,
    },
    compoundVariants: [
      // 中大尺寸圆角
      {
        size: ["md", "lg"],
        radius: "default",
        className: "rounded-md",
      },
      // 图标按钮尺寸
      {
        isIconOnly: true,
        size: "sm",
        className: "h-7 w-7",
      },
      {
        isIconOnly: true,
        size: "md",
        className: "h-8 w-8",
      },
      {
        isIconOnly: true,
        size: "lg",
        className: "h-9 w-9",
      },
      // 主要按钮禁用状态
      {
        type: "primary",
        state: "disabled",
        className: "bg-indigo-200",
      },
      {
        type: "primary",
        state: "loading",
        className: "bg-indigo-300",
      },
      // 描边按钮禁用状态
      {
        type: "outline",
        state: "disabled",
        className: "border-indigo-200 bg-indigo-50 text-indigo-300",
      },
      {
        type: "outline",
        state: "loading",
        className: "border-indigo-300 text-indigo-400 bg-transparent",
      },
      // 默认按钮禁用状态
      {
        type: "default",
        state: "disabled",
        className: "border-slate-200 bg-slate-100 text-slate-400",
      },
      {
        type: "default",
        state: "loading",
        className: "border-slate-200 bg-slate-50 text-slate-500",
      },
      // error按钮禁用状态
      {
        type: "error",
        state: "disabled",
        className: "border-red-100 bg-rose-50 text-red-300",
      },
      {
        type: "error",
        state: "loading",
        className: "border-red-200 bg-rose-50 text-red-400",
      },
      // 无边框按钮禁用状态
      {
        type: "text",
        state: "disabled",
        className: "bg-slate-100 text-slate-400",
      },
      {
        type: "text",
        state: "loading",
        className: "bg-slate-50 text-slate-500",
      },
    ],
  }
);

type ButtonBaseProps = {
  asChild?: boolean;
  loading?: boolean | { icon?: React.ReactNode };
  icon?: React.ReactNode;
  iconPlacement?: "start" | "end";
  isIconOnly?: boolean;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
} & VariantProps<typeof buttonVariants>;

type ButtonAsButtonProps = ButtonBaseProps &
  Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, keyof ButtonBaseProps> & {
    href?: undefined;
  };

type ButtonAsAnchorProps = ButtonBaseProps &
  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof ButtonBaseProps> & {
    href: string;
  };

type ButtonProps = ButtonAsButtonProps | ButtonAsAnchorProps;

const Button = React.forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>((props, ref) => {
  const {
    className,
    radius,
    size,
    type,
    asChild = false,
    loading = false,
    icon,
    href,
    iconPlacement = "start",
    isIconOnly = false,
    disabled,
    children,
    ...rest
  } = props;

  const renderIcon = () => {
    if (loading) {
      const isLoadingObject = typeof loading === "object";
      const loadingIcon =
        isLoadingObject && loading.icon ? (
          loading.icon
        ) : (
          <svg
            className={cn("h-4 w-4 animate-spin")}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 12 12"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M1.36541 7.8763C0.954085 6.86031 0.888013 5.73731 1.17736 4.6801C1.46671 3.62288 2.09544 2.69004 2.96681 2.0251C3.83818 1.36017 4.90391 0.999993 6 0.999993C6.27614 0.999993 6.5 1.22385 6.5 1.49999C6.5 1.77614 6.27614 1.99999 6 1.99999C5.12313 1.99999 4.27054 2.28813 3.57345 2.82008C2.87635 3.35203 2.37337 4.0983 2.14189 4.94408C1.91041 5.78985 1.96327 6.68825 2.29233 7.50104C2.62138 8.31383 3.20841 8.99598 3.96308 9.44251C4.71774 9.88904 5.59823 10.0752 6.46906 9.9724C7.33988 9.86957 8.15279 9.48344 8.7827 8.87342C9.41261 8.2634 9.82462 7.46329 9.95532 6.59621C10.086 5.72913 9.92818 4.84312 9.50608 4.07453C9.37315 3.83248 9.46161 3.52851 9.70365 3.39558C9.9457 3.26266 10.2497 3.35111 10.3826 3.59316C10.9102 4.55391 11.1075 5.66141 10.9441 6.74526C10.7808 7.82911 10.2658 8.82925 9.47838 9.59178C8.69099 10.3543 7.67485 10.837 6.58632 10.9655C5.49779 11.094 4.39718 10.8613 3.45384 10.3031C2.51051 9.74498 1.77673 8.89229 1.36541 7.8763Z"
              fill="#ffffff"
            />
          </svg>
        );

      return (
        <span
          className={cn(
            "-ml-1.5 mr-1 inline-flex",
            isIconOnly
              ? "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
              : ""
          )}
        >
          {loadingIcon}
        </span>
      );
    }
    return icon ? (
      <span
        className={cn(
          !isIconOnly && children && iconPlacement === "start"
            ? "-ml-1 mr-1"
            : "",
          !isIconOnly && children && iconPlacement === "end"
            ? "-mr-1 ml-1"
            : "",
          size === "sm" ? "text-base" : size === "lg" ? "text-xl" : "text-lg"
        )}
      >
        {icon}
      </span>
    ) : null;
  };

  const classes = cn(
    buttonVariants({
      radius,
      type,
      size,
      state: disabled ? "disabled" : loading ? "loading" : "default",
      isIconOnly,
      className,
    })
  );

  if (href) {
    return (
      <a
        className={classes}
        ref={ref as React.Ref<HTMLAnchorElement>}
        href={href}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {!isIconOnly && iconPlacement === "start" && renderIcon()}
        {children}
        {!isIconOnly && iconPlacement === "end" && renderIcon()}
        {isIconOnly && renderIcon()}
      </a>
    );
  }

  const Comp = asChild ? Slot : "button";
  return (
    <Comp
      className={classes}
      ref={ref as React.Ref<HTMLButtonElement>}
      disabled={
        disabled ||
        (typeof loading === "boolean" ? loading : loading?.icon !== undefined)
      }
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {!isIconOnly && iconPlacement === "start" && renderIcon()}
      {children}
      {!isIconOnly && iconPlacement === "end" && renderIcon()}
      {isIconOnly && renderIcon()}
    </Comp>
  );
});

Button.displayName = "Button";

export { Button, buttonVariants };
