import ArrowLeftIcon from "@/public/icons/ic_arrow_left.svg";
import ArrowRightIcon from "@/public/icons/ic_arrow_right.svg";
import { Button } from "@/ui/tch-button";
import { cn } from "@/utils";
import dayjs from "dayjs";

interface DatePickerProps {
  currentDate: dayjs.Dayjs;
  weekDays: dayjs.Dayjs[];
  onToday: () => void;
  onPrevWeek: () => void;
  onNextWeek: () => void;
}

// 生成日期范围文案的工具函数
export function getDateRangeText(
  startDate: dayjs.Dayjs,
  endDate: dayjs.Dayjs
): string {
  const startYear = startDate.year();
  const startMonth = startDate.month() + 1;
  const startDay = startDate.date();

  const endYear = endDate.year();
  const endMonth = endDate.month() + 1;
  const endDay = endDate.date();

  if (startDate.isSame(endDate, "year")) {
    // 同年
    if (startDate.isSame(endDate, "month")) {
      // 同年同月：只显示日
      return `${startYear}年${startMonth}月${startDay}日 - ${endDay}日`;
    } else {
      // 同年跨月：显示月日
      return `${startYear}年${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
    }
  } else {
    // 跨年：显示年月日
    return `${startYear}年${startMonth}月${startDay}日 - ${endYear}年${endMonth}月${endDay}日`;
  }
}

export function DatePicker({
  currentDate,
  weekDays,
  onToday,
  onPrevWeek,
  onNextWeek,
}: DatePickerProps) {
  const isToday = currentDate.isSame(dayjs(), "day");

  return (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-6">
        <Button
          size="sm"
          className={cn(
            "text-gray-4 border-line-3 rounded-md text-sm font-medium",
            !isToday && "border-primary-2 text-primary-2 active:bg-primary-6"
          )}
          onClick={() => {
            if (!isToday) {
              onToday();
            }
          }}
        >
          回到今天
        </Button>

        <div className="flex items-center gap-3">
          <Button
            size="sm"
            type="outline"
            className="border-line-3 flex h-7 w-7 items-center justify-center rounded-md p-0"
            onClick={onPrevWeek}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>

          <div className="flex items-center gap-1.5">
            <span className="text-gray-1 translate-y-px text-base font-medium">
              {getDateRangeText(weekDays[0], weekDays[6])}
            </span>
          </div>

          <Button
            size="sm"
            type="outline"
            className="border-line-3 flex h-7 w-7 items-center justify-center rounded-md p-0"
            onClick={onNextWeek}
          >
            <ArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
