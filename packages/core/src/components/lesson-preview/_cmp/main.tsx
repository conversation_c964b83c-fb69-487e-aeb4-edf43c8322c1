import { GuidePlayer } from "@repo/core/components/player/guide-player";
import { StudyType } from "@repo/core/enums";
import { ExerciseView } from "@repo/core/exercise/preview/view/exercise-view";
import { Loading } from "@repo/ui/components/loading";
import { FC } from "react";
import { usePreviewCtx } from "../_helper";
import { InteractiveView } from "./interactive/interactive-view";

export const Main: FC = () => {
  const { widget, widgets, loading, simulation } = usePreviewCtx();

  return (
    <div className="main h-full w-full">
      {loading ? (
        <div className="lesson_preview_loading_container flex h-full w-full items-center justify-center">
          <Loading
            variant="orbit"
            size="lg"
            text="正在加载课程内容..."
            className="lesson_preview_loading"
          />
        </div>
      ) : (
        <>
          {widget.widgetType === "guide" && (
            <div className="h-[600px] w-[1000px] overflow-auto">
              <GuidePlayer
                data={widget.data}
                width={1000}
                height={600}
                className="h-full w-full"
                partIndex={widget.widgetIndex}
                totalPartsCount={widgets.length}
              />
            </div>
          )}

          {widget.widgetType === "exercise" && (
            <div className="h-8/10 w-full" style={{ zoom: 0.73 }}>
              {widget.data.length > 0 ? (
                <ExerciseView
                  className="h-full w-full"
                  questionList={widget.data}
                  // questionData={widgetData}
                  // isPreviewMode={true}
                  showExplanations={true}
                  studyType={StudyType.REINFORCEMENT_EXERCISE}
                  onBack={() => window.history.back()}
                />
              ) : (
                <div className="m-60">暂无数据</div>
              )}
            </div>
          )}

          {widget.widgetType === "interactive" && (
            <div className="overflow-y-auto p-1">
              {widget.data.url ? (

                <div className="overflow-y-auto px-4 w-[960px] h-[600px]">
                  <InteractiveView
                    active={true}
                    index={widget.index}
                    url={widget.data.url}
                    type={widget.data.typeName}
                    onReport={(e) => {
                      console.log(e);
                    }}
                  />
                </div>
              ) : (
                <div className="flex h-full w-full items-center justify-center">
                  暂未配置，请前去设置
                </div>
              )}
            </div>
          )}

          {/* TODO: 接入视频组件 */}
          {widget.widgetType === "video" && (
            <div className={simulation ? 'w-full h-full flex items-center bg-black' : 'w-full h-full'}>
              <video
                src={widget?.data?.url}
                controls
              ></video>
            </div>
          )}
        </>
      )}
    </div>
  );
};
