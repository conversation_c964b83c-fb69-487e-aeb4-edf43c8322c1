import {
  InteractiveWidgetDetail,
  VideoWidgetDetail,
} from "@repo/core/components/ai-course-preview/type";
import { CommonResponseType, QuestionType } from ".";

export interface QuestionSetDetailData {
  estimatedTime: number;
  list: QuestionSetGroupItem[];
  questionSetId: number;
  questionSetVersionId: number;
  questionTypeCount: number;
}

export interface QuestionSetDifficultStat {
  difficultName: string;
  difficultNeedQuestionCount: number;
  difficultQuestionCount: number;
}

export interface QuestionSetGroupItem {
  questionGroupId?: number;
  questionGroupName?: string;
  questionGroupOrder?: number;
  questionGroupQuestionList?: QuestionGroupQuestionItem[];
}

export interface QuestionGroupQuestionItem {
  questionId: string;
  questionInfo: QuestionType;
}

export type QuestionSetDetailResponse =
  CommonResponseType<QuestionSetDetailData>;

export interface AiCourseDetail {
  bizTreeNodeList: BizTreeNodeList[];
  lessonId: number;
  lessonName: string;
  lessonShowInfo: LessonShowInfo;
  lessonType: number;
  phase: number;
  publishVersion: string;
  subject: number;
  theme: string;
}

export interface BizTreeNodeList {
  bizTreeId?: number;
  bizTreeName?: string;
  bizTreeNodeId?: number;
  bizTreeNodeName?: string;
  bizTreeNodeParentPath?: string;
  bizTreeSerial?: string;
}

export interface LessonShowInfo {
  lessonId: number;
  theme: string;
  totalWidgetNum: number;
  widgetList: WidgetListData[];
  widgetProgress: WidgetProgress[];
}

export type WidgetListData =
  | WidgetListGuide
  | WidgetListExercise
  | WidgetListVideo
  | WidgetListInteractive;

export interface WidgetListGuide {
  data: WidgetListDataGuide;
  duration?: number;
  widgetIndex: number;
  widgetName: string;
  widgetType: "guide";
}

export interface WidgetListExercise {
  data: WidgetListDataExercise[];
  duration?: number;
  widgetIndex: number;
  widgetName: string;
  widgetType: "exercise";
}

export interface WidgetListVideo {
  // 这个数据可能是不存在的
  data?: VideoWidgetDetail["data"];
  duration?: number;
  widgetIndex: number;
  widgetName: string;
  widgetType: "video";
}

export interface WidgetListInteractive {
  data: InteractiveWidgetDetail["data"];
  duration?: number;
  widgetIndex: number;
  widgetName: string;
  widgetType: "interactive";
}

export interface WidgetListDataExercise {
  exerciseType?: number;
  exerciseIds: string[];
}

export interface WidgetListDataGuide {
  avatar: Avatar;
  content: DataContent[];
  subtitles: Subtitle[];
}

export interface Avatar {
  durationInFrames: number;
  fps: number;
  height: number;
  url: string;
  width: number;
  [property: string]: any;
}

export interface DataContent {
  content: PurpleContent[];
  icon: string;
  id: string;
  inFrame: number;
  level: number;
  outFrame: number;
  pic: Pic;
  tag: string;
  width: string;
}

export interface PurpleContent {
  content: FluffyContent[];
  icon: null;
  id: string;
  inFrame: number;
  level: number;
  notation: null;
  outFrame: number;
  pic: null;
  tag: string;
}

export interface FluffyContent {
  content: string;
  inFrame: number;
  notation: null | Notation;
  outFrame: number;
  tag: string;
}

export interface Notation {
  color: string;
  duration: number;
  inFrame: number;
  outFrame: number;
  strokeWidth: number;
  type: string;
}

export interface Pic {
  height: number;
  url: string;
  width: number;
}

export interface Subtitle {
  id: number;
  inFrame: number;
  outFrame: number;
  text: string;
}

export interface WidgetProgress {
  duration: number;
  widgetIndex: number;
  widgetName: string;
  widgetType: string;
}
