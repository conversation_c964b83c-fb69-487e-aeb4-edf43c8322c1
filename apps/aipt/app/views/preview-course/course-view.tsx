"use client";

import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC } from "react";
import { CourseProgressBar } from "./course-progress-bar";
import { CourseViewProvider } from "./course-view-context";
import { CourseWidgetsLoaderView } from "./course-widgets-loader";

export const CourseView: FC<
  {
    type?: "draft" | "official"; //草稿和正式
    courseProgressBarProps?: ComponentProps<typeof CourseProgressBar>;
    knowledgeId: number;
  } & ComponentProps<"div">
> = ({ className, knowledgeId, courseProgressBarProps, type = "draft" }) => {
  return (
    <CourseViewProvider knowledgeId={knowledgeId} type={type}>
      <div className={cn("flex h-full w-full", className)}>
        {/* 导航条 */}
        <div className="h-full w-[200px] overflow-y-auto border-r border-gray-200 px-4">
          <CourseProgressBar {...courseProgressBarProps} />
        </div>

        {/* 组件内容：文稿 or 练习 or 视频 */}
        <div className="flex-1 overflow-auto">
          <CourseWidgetsLoaderView type={type} />
        </div>
      </div>
    </CourseViewProvider>
  );
};
