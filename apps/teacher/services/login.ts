import { PLATFORM } from "@/enums";
import { r } from "@/libs";
import { LoginResponse, School } from "@/types";

const NEXT_PUBLIC_API_HOST = process.env.NEXT_PUBLIC_API_HOST || "";

/**
 * 图形校验短信验证码发送
 */
export const sendSmsWithCaptcha = (data: {
  captchaVerifyParam: string;
  phone_number: string;
  platform: PLATFORM;
  sceneId: string;
}) => {
  return r.post(
    `${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/sms/sendWithCaptcha`,
    {
      ...data,
      business_type: "login",
    },
    {
      showToast: false,
    }
  );
};

/**
 * 教师身份验证
 */
export const verifyTeacher = (data: {
  phoneNumber: string;
  verificationCode: string;
}) => {
  return r.post<{
    isVerified: boolean;
    schools: School[];
  }>(`${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/access/teacher/verify`, data, {
    showToast: false,
  });
};

/**
 * 教师登录
 */
export const login = (userPhone: string, verifyCode: string) => {
  return r.post<LoginResponse>(
    `${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/access/login`,
    {
      // 登录方式 => 1为手机验证码 2为账号密码
      loginTypeId: 1,
      // 登录端 => 1为学生端 2为教师端
      platformId: 2,
      userPhone,
      verifyCode,
    },
    {
      showToast: false,
    }
  );
};
