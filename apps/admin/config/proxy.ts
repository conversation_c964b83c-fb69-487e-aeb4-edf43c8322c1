/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 如果需要自定义本地开发服务器  请取消注释按需调整
  dev: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      // 要代理的地址
      // 斯文
      // target: 'http://************:8080',
      // 周强
      // target: 'http://************:8080',
      // 建红
      // target: 'http://************:8080',
      // 开发环境
      // target: 'http://admin-api.local.xiaoluxue.cn',
      // target: 'https://apifoxmock.com/m1/5942418-5630363-default/',
      // 测试环境
      target: 'https://admin-api.dev.xiaoluxue.cn',
      // target: 'http://************:8080',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
      pathRewrite: { '^': '' },
      // 启用详细日志
      logLevel: 'debug',
    },
  },
  local: {
    '/api/': {
      // 开发环境
      target: 'http://admin-api.local.xiaoluxue.cn',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      // 启用详细日志
      logLevel: 'debug',
    },
  },

  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
