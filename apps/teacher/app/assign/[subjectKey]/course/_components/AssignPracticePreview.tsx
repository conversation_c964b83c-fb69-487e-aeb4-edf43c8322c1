import PracticePreviewLayout from "@/components/business/ResourcePreview/PracticePreviewLayout";
import { Button } from "@/ui/tch-button";
import { useComputed } from "@preact-signals/safe-react";
import { CircleMinusIcon, CirclePlusIcon } from "lucide-react";
import { useAssignCourseContext } from "../store";

export default function AssignPracticePreview() {
  const {
    practicePreviewData,
    goToSelectTarget,
    selectedPractices,
    selectPractice,
    unselectPractice,
    subjectKey,
  } = useAssignCourseContext();

  const hasJoined = selectedPractices.value.some(
    (item) =>
      item.practice.questionSetId ===
      practicePreviewData.value?.practice.questionSetId
  );

  const detail = practicePreviewData.value;

  const treeNodeInfo = useComputed(() => {
    if (!practicePreviewData.value) return;
    return {
      bizTreeNodeName: practicePreviewData.value?.bizTreeNodeName,
      bizTreeNodeId: practicePreviewData.value?.bizTreeNodeId,
    };
  });

  if (!detail) return null;

  const HeaderSuffixNode = hasJoined ? (
    <Button
      type="error"
      size="lg"
      radius="full"
      className="px-4"
      onClick={() => unselectPractice(detail)}
    >
      <CircleMinusIcon size={20} className="mr-1.5 size-5" />
      <span>取消加入</span>
    </Button>
  ) : (
    <Button
      type="primary"
      size="lg"
      radius="full"
      className="px-4"
      onClick={() => selectPractice(detail)}
    >
      <CirclePlusIcon size={20} className="mr-1.5 size-5" />
      <span>加入任务</span>
    </Button>
  );

  return (
    <PracticePreviewLayout
      id={practicePreviewData.value?.practice.questionSetId?.toString() || ""}
      subjectKey={Number(subjectKey)}
      onBack={goToSelectTarget}
      headerSuffixNode={HeaderSuffixNode}
      treeNodeInfo={treeNodeInfo.value}
    />
  );
}
