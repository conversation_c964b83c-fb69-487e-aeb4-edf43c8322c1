import { useSignal } from "@preact-signals/safe-react";
import { useEffect } from "@preact-signals/safe-react/react";
import { cn } from "@repo/ui/lib/utils";
import { FC, TextareaHTMLAttributes, useLayoutEffect, useRef } from "react";

export const AutoHeightInput: FC<{
  value: string;
  onChange: (value: string) => void;
  className?: string;
  attr?: TextareaHTMLAttributes<HTMLTextAreaElement>;
  maxLength?: number;
}> = ({ value, onChange, attr, className, maxLength }) => {
  const height = useSignal(44);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useLayoutEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // 设置最小高度
      textarea.style.height = "20px";
      const minHeight = 20;
      const newHeight = Math.max(textarea.scrollHeight, minHeight) + 24;
      height.value = newHeight;
      textarea.style.height = "100%";
    }
  }, [height, value]);

  useEffect(() => {
    const { current } = textareaRef;
    if (!current || !attr?.autoFocus) return;
    const stop = (e: Event) => {
      console.log(e);
      e.preventDefault();
      current.focus();
    };
    current.addEventListener("blur", stop);
    current.addEventListener("focusout", stop);
    return () => {
      current.removeEventListener("blur", stop);
      current.removeEventListener("focusout", stop);
    };
  }, [attr?.autoFocus]);

  return (
    <div
      className={cn("flex w-full items-start gap-4 p-3", className)}
      style={{ height: `${height.value}px` }}
      onMouseDown={() => {
        textareaRef.current?.focus();
      }}
      onPointerDown={() => {
        textareaRef.current?.focus();
      }}
    >
      <textarea
        onMouseDown={(e) => {
          e.stopPropagation();
        }}
        onPointerDown={(e) => {
          e.stopPropagation();
        }}
        ref={textareaRef}
        className="placeholder:text-text-4 text-text-1 max-h-full w-full flex-1 resize-none overflow-auto text-sm font-medium leading-tight"
        value={value}
        maxLength={maxLength}
        onChange={(e) => onChange(e.target.value)}
        {...attr}
      />
      {maxLength && (
        <div className="text-text-5 flex-[0_0_auto]">
          {value.length}/{maxLength}
        </div>
      )}
    </div>
  );
};
