declare namespace API {
  namespace Review {
    /**
     * 审核任务列表项
     */
    interface AuditTaskItem {
      auditTaskId: number; // 任务id
      auditTaskType: API.EnumConstantData['auditTypeList'][number]['value']; // 任务类型
      auditTaskName: string; // 名称
      phase: number; // 学段
      subject: number; // 学科
      auditStatus: API.EnumConstantData['auditStatusList'][number]['value']; // 审核状态
      submiter: string; // 提交人
      createTime: number; // 提交时间（时间戳）
    }

    interface AttachFileList {
      fileName: string;
      fileType: number;
      fileData: string;
      ossPath: string;
    }

    /**
     * 导入题目审核数据
     */
    interface ImportQuestionAudit {
      list: Array<{
        questionId: string; // 题目id
        questionType: number; // 类型 1: 单选题 2: 多选题
        questionAnswerMode: number; // 作答方式
        questionVersionId: number; // 版本id
        questionContent: {
          questionStem: string; // 题目
          questionOptionList: {
            optionKey: string;
            optionVal: string;
          }[];
        };
        questionAnswer: {
          answerOptionList: {
            optionKey: string;
            optionVal: string;
          }[];
        }; // 答案
        questionExplanation: string; // 解析
        questionDifficult: number; // 难度
        baseTreeNodeIds: number[]; // 知识点列表
        BaseTreeNames: string[]; // 知识点名称列表
        baseTreeId: number; // 基础树id
        attachFileList: AttachFileList[];
        auditStatus: API.EnumConstantData['auditStatusList'][number]['value'];
        questionWrong: string; // 题目错误
        answerWrong: string; // 答案错误
        explanationWrong: string; // 解析错误
        subject: number; // 学科
      }>;
      paperId: string;
    }

    /**
     * 选择题目审核数据
     */
    interface ChooseQuestionAudit {
      bizTreeNodeId: number; // 业务树节点ID
      bizTreeNodeName: string; // 业务树节点名称
      difficultStat: Array<{
        difficultLevel: number; // 难度等级
        difficultNeedQuestionCount: number; // 需要的题目数量
        difficultQuestionCount: number; // 当前题目数量
      } | null>;
      questionSetId: string; // 题目集ID
      list: Array<{
        questionId: string; // 题目ID
        questionContent: string; // 题目内容
        questionAnswer: string; // 答案
        questionExplanation: string; // 解析
        questionExtra: string; // 额外信息
        addDelStatusText: string; // 新增删除题目文本
        addDelStatus: number; // 新增删除状态
        difficultLevel: number; // 难度等级
        /**
         * 审核状态
         */
        auditStatus: API.EnumConstantData['auditStatusList'][number]['value'];
      }>;
    }

    /**
     * 审核任务详情
     */
    interface AuditTaskDetail {
      auditResultInfo: string; // 审核结果信息
      auditStatus: API.EnumConstantData['auditStatusList'][number]['value'];
      auditTaskId: number; // 审核任务编号
      auditTaskType: number; // 任务类型
      auditTaskName: string; // 任务名称
      phase: string; // 学段
      subject: string; // 学科
      auditStatus: string; // 审核状态
      importQuestion: ImportQuestionAudit; // 题目录入
      chooseQuestion: API.ExerciseQuestionListData; // 选题
    }

    interface AuditTaskListRequestParams {
      auditTaskId?: number; // 任务id
      auditTaskType?: API.EnumConstantData['auditTypeList'][number]['value']; // 任务类型
      auditTaskNameKey?: string; // 名称
      auditStatus?: API.EnumConstantData['auditStatusList'][number]['value']; // 审核状态
      submiterKey?: string; // 提交人
      phaseList?: number[]; // 学段
      subjectList?: number[]; // 学科
      page: number; // 页码
      pageSize: number; // 每页条数
    }
  }
}
