<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#189;&#156;&#228;&#184;&#154;&#229;&#155;&#190;&#230;&#160;&#135;">
<g id="&#228;&#185;&#166;&#230;&#156;&#172;">
<g id="Rectangle 34624304" filter="url(#filter0_ii_916_10120)">
<path d="M1 8C1 4.68629 3.68629 2 7 2H32C36.9706 2 41 6.02944 41 11V43C41 47.9706 36.9706 52 32 52H7C3.68629 52 1 49.3137 1 46V8Z" fill="url(#paint0_linear_916_10120)"/>
</g>
<g id="Rectangle 34624308" filter="url(#filter1_di_916_10120)">
<path d="M1 8C1 4.68629 3.68629 2 7 2V52C3.68629 52 1 49.3137 1 46V8Z" fill="url(#paint1_linear_916_10120)"/>
</g>
<path id="Vector 5559" d="M13 12.5H29" stroke="url(#paint2_linear_916_10120)" stroke-opacity="0.9" stroke-width="4" stroke-linecap="round"/>
<path id="Vector 5560" d="M13 21H23" stroke="url(#paint3_linear_916_10120)" stroke-opacity="0.9" stroke-width="4" stroke-linecap="round"/>
</g>
<g id="&#229;&#137;&#141;&#233;&#157;&#162;">
<foreignObject x="30.0378" y="20.5625" width="27.0872" height="25.375"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4px);clip-path:url(#bgblur_0_916_10120_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624305" filter="url(#filter2_i_916_10120)" data-figma-bg-blur-radius="8">
<path d="M39.0252 31.9219C37.3472 31.0899 37.9394 28.5625 39.8123 28.5625H43.8516C46.764 28.5625 49.125 30.9235 49.125 33.8359C49.125 36.1012 47.2887 37.9375 45.0234 37.9375H42.8619C41.9697 37.9375 41.3222 37.0885 41.5582 36.228C41.9859 34.6685 41.249 33.0245 39.8002 32.3061L39.0252 31.9219Z" fill="#64FCFF" fill-opacity="0.4"/>
</g>
<foreignObject x="11" y="12" width="50" height="50"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_1_916_10120_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34624306" filter="url(#filter3_dii_916_10120)" data-figma-bg-blur-radius="10">
<rect x="21" y="22" width="30" height="30" rx="8" fill="url(#paint4_linear_916_10120)" fill-opacity="0.4" shape-rendering="crispEdges"/>
<rect x="21.5" y="22.5" width="29" height="29" rx="7.5" stroke="url(#paint5_linear_916_10120)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Vector 5558" filter="url(#filter4_d_916_10120)">
<path d="M27.0938 33.2814C27.4086 32.9701 40.9467 29.8555 42.8357 33.2814C44.7247 36.707 32.3411 37.0184 30.8718 39.1982C30.2421 40.1324 29.9273 42.0008 41.5764 42.3122" stroke="url(#paint6_linear_916_10120)" stroke-opacity="0.9" stroke-width="3" stroke-linecap="round" shape-rendering="crispEdges"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_ii_916_10120" x="1" y="-2" width="40" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.578036 0 0 0 0 0.415099 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_916_10120"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_916_10120" result="effect2_innerShadow_916_10120"/>
</filter>
<filter id="filter1_di_916_10120" x="-3" y="-2" width="14" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.572549 0 0 0 0 0.415686 0 0 0 0 0.988235 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_10120"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_10120" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.578036 0 0 0 0 0.415099 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_10120"/>
</filter>
<filter id="filter2_i_916_10120" x="30.0378" y="20.5625" width="27.0872" height="25.375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.455573 0 0 0 0 1 0 0 0 0 0.992692 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_916_10120"/>
</filter>
<clipPath id="bgblur_0_916_10120_clip_path" transform="translate(-30.0378 -20.5625)"><path d="M39.0252 31.9219C37.3472 31.0899 37.9394 28.5625 39.8123 28.5625H43.8516C46.764 28.5625 49.125 30.9235 49.125 33.8359C49.125 36.1012 47.2887 37.9375 45.0234 37.9375H42.8619C41.9697 37.9375 41.3222 37.0885 41.5582 36.228C41.9859 34.6685 41.249 33.0245 39.8002 32.3061L39.0252 31.9219Z"/>
</clipPath><filter id="filter3_dii_916_10120" x="11" y="12" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.580392 0 0 0 0 0.419608 0 0 0 0 0.996078 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_10120"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_10120" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.662087 0 0 0 0 0.448218 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_916_10120"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_916_10120" result="effect3_innerShadow_916_10120"/>
</filter>
<clipPath id="bgblur_1_916_10120_clip_path" transform="translate(-11 -12)"><rect x="21" y="22" width="30" height="30" rx="8"/>
</clipPath><filter id="filter4_d_916_10120" x="21.5938" y="26.1872" width="26.9378" height="21.625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.635294 0 0 0 0 0.490196 0 0 0 0 0.996078 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_916_10120"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_916_10120" result="shape"/>
</filter>
<linearGradient id="paint0_linear_916_10120" x1="13.5" y1="0.499999" x2="41" y2="71.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#B89BFF"/>
<stop offset="1" stop-color="#956DFD"/>
</linearGradient>
<linearGradient id="paint1_linear_916_10120" x1="33" y1="37.5" x2="4" y2="43" gradientUnits="userSpaceOnUse">
<stop stop-color="#C3AAFF"/>
<stop offset="1" stop-color="#A07BFF"/>
</linearGradient>
<linearGradient id="paint2_linear_916_10120" x1="19" y1="9" x2="21" y2="16.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#E9E9FF"/>
<stop offset="1" stop-color="#DBCDFF"/>
</linearGradient>
<linearGradient id="paint3_linear_916_10120" x1="16.75" y1="17.5" x2="19.6497" y2="24.2961" gradientUnits="userSpaceOnUse">
<stop stop-color="#E9E9FF"/>
<stop offset="1" stop-color="#DBCDFF"/>
</linearGradient>
<linearGradient id="paint4_linear_916_10120" x1="36" y1="22" x2="19.89" y2="47.6889" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5D7FF" stop-opacity="0.9"/>
<stop offset="1" stop-color="#D6BDFF" stop-opacity="0.8"/>
</linearGradient>
<linearGradient id="paint5_linear_916_10120" x1="28" y1="22" x2="51.1054" y2="54.277" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="1" stop-color="#BF8EFF" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint6_linear_916_10120" x1="27.0937" y1="31.6872" x2="35.0625" y2="42.3122" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFECFF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
