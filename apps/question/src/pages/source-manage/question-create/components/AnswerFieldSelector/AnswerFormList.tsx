import { Form } from 'antd';
import React from 'react';
import AnswerFormContent from './AnswerFormContent';
import type { AnswerFormListProps } from './types';
import { validateFormListAnswer } from './validate';

/**
 * 答案表单列表组件
 */
const AnswerFormList: React.FC<AnswerFormListProps> = ({
  fieldName,
  questionAnswerMode,
  fieldManager,
  isMatchLine,
  activeFillIdx,
  questionOptionMatrixName,
}) => {
  return (
    <Form.List name={fieldName} rules={validateFormListAnswer(questionAnswerMode)}>
      {(fields, operations, { errors }) => {
        // 更新字段管理器的引用
        fieldManager.setFormListRef({ ...operations, fields });

        return (
          <AnswerFormContent
            fields={fields}
            errors={errors}
            questionAnswerMode={questionAnswerMode}
            activeFillIdx={activeFillIdx}
            fieldManager={fieldManager}
            isMatchLine={isMatchLine}
            questionOptionMatrixName={questionOptionMatrixName}
          />
        );
      }}
    </Form.List>
  );
};

export default AnswerFormList;
