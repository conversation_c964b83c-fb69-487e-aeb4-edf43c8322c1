import { getQuestionListByIds } from "@/services/assign-homework";
import { getAiCourseDetail } from "@/services/preview";
import { useSignal } from "@preact-signals/safe-react";
import { CourseWidgetDetail } from "@repo/core/components/ai-course-preview/type";
import { useRequest } from "ahooks";

export function useAiCoursePreviewModel(id: string, options?: { onBack?: () => void }) {
  const { data: aiCourseData, loading: isFetchingAiCourse } = useRequest(
    async () => {
      if (!id) return;
      try {
        const res = await getAiCourseDetail(Number(id));
        return res ? res : undefined;
      } catch (err) {
        console.error(err);
        options?.onBack?.();
        return undefined;
      }
    }
  );

  const activeIndex = useSignal(0);

  const fetchQuestionList = useRequest(
    async (ids: string[]) => {
      return await getQuestionListByIds({ questionIds: ids });
    },
    {
      manual: true,
    }
  );


  const { data: widgetData, loading: isWidgetDataLoading } = useRequest<
    CourseWidgetDetail | undefined,
    []
  >(
    async () => {
      if (!aiCourseData) {
        return undefined;
      }

      const index = activeIndex.value;

      const widgetInfo = aiCourseData.lessonShowInfo.widgetList[index];

      // 练习题，后端返回的是练习题id列表，需要查题库
      if (widgetInfo.widgetType === "exercise") {
        const ids =
          widgetInfo.data?.reduce<string[]>(
            (res, e) => res.concat(e.exerciseIds || []),
            []
          ) || [];
        if (ids.length === 0) {
          return undefined;
        }

        const res = await fetchQuestionList.runAsync(ids);

        return {
          index: widgetInfo.widgetIndex,
          name: widgetInfo.widgetName,
          duration: widgetInfo.duration || 0,
          type: "exercise",
          data: res.map((e, index) => {
            return {
              questionInfo: {
                ...e,
                questionIndex: index, // 题目序号不能为负，可能为 null
                questionDifficulty: e.questionDifficult, // 难度系数不能为负，可能为 null
                hasNextQuestion: res[index + 1] ? true : null, // 是否有下一题可能为 null
                isResume: false, // 是否为恢复练习状态
              },
            };
          }),
        };
      }

      return {
        index: widgetInfo.widgetIndex,
        name: widgetInfo.widgetName,
        type: widgetInfo.widgetType,
        data: widgetInfo.data,
        duration: widgetInfo.duration || 0,
      };
    },
    {
      ready: aiCourseData !== undefined,
      refreshDeps: [aiCourseData, activeIndex.value],
      cacheKey: `fetchWidgetDetail-${aiCourseData?.lessonId || -1}-${activeIndex.value}`,
    }
  );

  const handleActiveIndexChange = (index: number) => {
    activeIndex.value = index;
  };

  return {
    aiCourseData,
    isFetchingAiCourse,

    widgetData,
    isWidgetDataLoading,
    activeIndex,
    handleActiveIndexChange,
  }
}