.editableTiptap {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.2s;
  &:hover {
    border-color: #1890ff;
  }

  &.isFocus {
    width: 100% !important;
    min-width: 300px;
    border: none;
    :global {
      .menuBar {
        border-top-color: #1890ff;
        border-right-color: #1890ff;
        border-left-color: #1890ff;
      }
      .tiptap-editor {
        border-right-color: #1890ff;
        border-bottom-color: #1890ff;
        border-left-color: #1890ff;
      }
    }
  }

  &.isInput {
    width: 100%;
    min-width: 300px;
    height: max-content;
    :global(.tiptap > p) {
      margin: 0;
    }
  }
}
