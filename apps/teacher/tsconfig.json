{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@repo/core/*": ["../../packages/core/src/*"], "@repo/lib/*": ["../../packages/lib/src/*"]}}, "include": ["./svgr.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "dist/types/**/*.ts"], "exclude": ["node_modules", "dist/**/*", "build/**/*", "out/**/*"]}