import { QuestionCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Flex, Modal, Typography } from 'antd';
import { FC } from 'react';
import ExerciseProgressInfo from '../ExerciseProgressInfo';
const { Title } = Typography;

export interface ExerciseProgressProps {
  node?: API.Common.BaseTreeNodeAntdType;
  onEdit: () => void;
  onHandleException: () => void;
}
const ExerciseProgress: FC<ExerciseProgressProps> = (props) => {
  const { onEdit, onHandleException } = props;
  const [modal, contextHolder] = Modal.useModal();

  const ease = {
    least: 20,
    current: 10,
  };
  const relativelyEasy = {
    least: 20,
    current: 10,
  };
  const medium = {
    least: 20,
    current: 10,
  };
  const relativelyHard = {
    least: 20,
    current: 10,
  };
  const hard = {
    least: 20,
    current: 10,
  };

  const confirm = () => {
    modal.confirm({
      title: '是否确认上架',
      icon: <QuestionCircleOutlined />,
      content: '点击确认将进入审核阶段，通过后自动完成',
      okText: '确认',
      cancelText: '返回',
      onOk: () => {
        console.log('确认');
        submitWarning();
      },
    });
  };
  const revocation = () => {
    modal.confirm({
      title: '是否确认撤回审核',
      content: '点击确认将撤回审核，可以编辑选题后重新提交',
      icon: <QuestionCircleOutlined />,
      okText: '确认',
      cancelText: '返回',
      onOk: () => {
        alert('revocation确认');
      },
    });
  };
  const submitWarning = () => {
    modal.warning({
      content: '不满足上架题目要求的数量，请添加题目后重试',
      okText: '返回',
    });
  };
  return (
    <Card>
      <Flex justify="space-between" align="center" gap="small">
        <Title level={5} style={{ margin: 0, fontWeight: 'normal' }}>
          选题进度
        </Title>
        <ExerciseProgressInfo
          ease={ease}
          relativelyEasy={relativelyEasy}
          medium={medium}
          relativelyHard={relativelyHard}
          hard={hard}
        />
        <Flex gap="small" wrap>
          <Button type="primary" size="middle" onClick={onEdit}>
            编辑
          </Button>
          <Button size="middle" onClick={confirm}>
            上架
          </Button>
          <Button size="middle" onClick={revocation}>
            撤回审核
          </Button>
          <Button size="middle" type="primary" danger onClick={onHandleException}>
            处理异常
          </Button>
        </Flex>
      </Flex>
      {contextHolder}
      {/* {revocationContextHolder} */}
    </Card>
  );
};
export default ExerciseProgress;
