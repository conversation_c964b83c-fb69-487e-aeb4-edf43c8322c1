import { cn } from "@/utils/utils";
import { EyeIcon } from "lucide-react";
import { AssignTagBtn } from "./assign-tag-btn";
import { AssignButton } from "./assign-button";
import { Separator } from "@/ui/separator";
interface AssignLatestPreviewProps {
  style?: React.CSSProperties;
  className?: string;
  onContinueAssign: () => void;
}

export function AssignLatestPreview({
  style = {},
  className = "",
  onContinueAssign,
}: AssignLatestPreviewProps) {
  return (
    <div
      className={cn(
        "bottom-4.25 inset-x-10 md:inset-x-50 fixed flex flex-nowrap items-center gap-3",
        "rounded-xl bg-gradient-to-l from-white/70 to-sky-50/70 px-4 py-2 shadow-[0px_5px_16.799999237060547px_-2px_rgba(0,0,0,0.10)] outline-[0.50px] outline-offset-[-0.50px] outline-slate-600/20 backdrop-blur-[9.55px]",
        "text-gray-2 text-sm font-normal leading-normal overflow-hidden",
        className
      )}
      style={style}
    >
      <div className="flex-1 h-5.25 flex items-center gap-3 overflow-hidden">
        <div className="flex items-center gap-1 shrink-0">
          <EyeIcon className="size-5" />
          <span className="text-nowrap">最近预览</span>
        </div>
        <Separator orientation="vertical" />
        <div className="flex-1 flex items-center gap-1 min-w-0 overflow-hidden">
          <AssignTagBtn content="课程" className="shrink-0 bg-blue-2 text-nowrap m-0" />
          <span className="flex-1 text-nowrap whitespace-nowrap overflow-hidden text-ellipsis">这是很多很多多的文这是很多很多多的文字这是很多很多很多字这是很多很多很多</span>
        </div>
      </div>

      <AssignButton className="shrink-0 bg-primary-2 hover:bg-primary-2/90 rounded-full px-5" onClick={onContinueAssign}>
        继续备课
      </AssignButton>
    </div>
  );
}
