"use client";

// 1. 导入 useState
import { Button } from "@/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { Input } from "@/ui/input";
import { Sheet, SheetContent } from "@/ui/sheet";
import { Switch } from "@/ui/switch";
import { toast } from "@/ui/toast";
import { cn } from "@/utils/utils";
import { useComputed } from "@preact-signals/safe-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";
import { DrawerCard } from "../../../../../../components/common/drawer-card";
import { useHomeworkSettings } from "../../../_context/homework-settings-context";

interface SettingsDrawerProps {
  open: boolean; // Sheet 的打开状态
  onOpenChange: (open: boolean) => void; // Sheet 的状态变更回调
  className?: string;
}

export function SettingsDrawer({
  open: sheetOpen,
  onOpenChange: onSheetOpenChange,
  className,
}: SettingsDrawerProps) {
  // 使用设置上下文
  const { settings, updateSetting, updatePeopleCount, tempPeopleCount } =
    useHomeworkSettings();
  const settingsValue = useComputed(() => settings.value);

  // 从上下文中获取设置值
  const { oneClickPraise, oneClickReminder, learningStatusValues } =
    settingsValue.value;

  // 2. 创建状态来追踪打开的下拉菜单 ID
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // 学习状态列表项 (保持不变)
  const learningStatusItems = [
    {
      id: "learningPoints",
      label: "学习能量",
      value: learningStatusValues.learningPoints,
    },
    {
      id: "completionProgress",
      label: "完成进度",
      value: learningStatusValues.completionProgress,
    },
    {
      id: "accuracyRate",
      label: "正确率",
      value: learningStatusValues.accuracyRate,
    },
    {
      id: "questionDifficulty",
      label: "答题难度",
      value: learningStatusValues.questionDifficulty,
    },
    {
      id: "wrongQuestions",
      label: "答题/错题",
      value: learningStatusValues.wrongQuestions,
    },
  ];

  // 生成 10%-100% 的选项
  const percentageOptions = Array.from(
    { length: 10 },
    (_, i) => `${i * 10 + 10}%`
  );

  // 正确率下拉菜单选项 (添加 '全部' 选项)
  const accuracyRateOptions = [
    { label: "全部", value: "全部" },
    ...percentageOptions.map((option) => ({ label: option, value: option })),
  ];

  // 更新学习状态列表中的某个值的处理函数 (保持不变)
  const handleValueChange = (id: string, value: string) => {
    const newValues = {
      ...learningStatusValues,
      [id]: value,
    };
    updateSetting("learningStatusValues", newValues);
    toast.success("设置已更新");
  };

  // 处理下拉菜单状态变化的通用函数
  const handleDropdownOpenChange = (isOpen: boolean, dropdownId: string) => {
    // 如果 isOpen 是 true，则设置当前打开的 ID 为此 dropdownId
    // 如果 isOpen 是 false，则表示要关闭，将 openDropdownId 设回 null
    setOpenDropdownId(isOpen ? dropdownId : null);
  };

  return (
    // 使用重命名的 Props，避免与内部状态变量冲突
    <Sheet open={sheetOpen} onOpenChange={onSheetOpenChange}>
      <SheetContent
        side="right"
        className={cn(
          "w-full gap-0 overflow-y-auto bg-[#F4F7FE] px-6 sm:max-w-[32.5rem]",
          className
        )}
        closeable={false}
      >
        <div className="flex items-center justify-between py-5">
          <div className="flex items-center gap-[0.625rem]">
            {/* 关闭按钮现在使用 onSheetOpenChange */}
            <div
              className="flex cursor-pointer items-center justify-center"
              onClick={() => onSheetOpenChange(false)}
            >
              <ChevronLeft className="h-5 w-5 text-[#444963]" />
            </div>
            <span className="text-lg font-medium text-[#444963]">设置</span>
          </div>
        </div>

        <div className="">
          {/* 一键鼓励设置 */}
          <DrawerCard
            title="一键鼓励"
            titleAction={
              <div className="mb-2 flex items-center justify-between">
                <span className="font-medium"></span>
                <Switch
                  checked={oneClickPraise}
                  onCheckedChange={(checked) => {
                    updateSetting("oneClickPraise", checked);
                    toast.success("设置已更新");
                  }}
                />
              </div>
            }
          >
            <p className="text-sm leading-[150%] text-[#646B8A]">
              一键对列表中未鼓励学生群发鼓励，鼓励学生进步
            </p>
          </DrawerCard>

          {/* 一键提醒设置 */}
          <DrawerCard
            title="一键提醒"
            titleAction={
              <div className="mb-2 flex items-center justify-between">
                <span className="font-medium"></span>
                <Switch
                  checked={oneClickReminder}
                  onCheckedChange={(checked) => {
                    updateSetting("oneClickReminder", checked);
                    toast.success("设置已更新");
                  }}
                />
              </div>
            }
          >
            <p className="text-sm leading-[150%] text-[#646B8A]">
              对列表中未提醒学生群发消息，区分每个人分发不同的提示文案，提醒学生更专注
            </p>
          </DrawerCard>

          {/* 共性错题设置 */}
          <DrawerCard title="共性错题">
            <p className="mb-4 text-sm leading-[150%] text-[#646B8A]">
              根据&quot;答题人数&quot;设定人数，在多中上错率（设定数据），可标记共性错题：
            </p>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <label className="text-gray-2 whitespace-nowrap text-[0.875rem] font-normal leading-[1.3125rem]">
                  人数设定
                </label>
                <Input
                  placeholder="输入人数"
                  value={tempPeopleCount.value}
                  onChange={(e) => {
                    updatePeopleCount(e.target.value);
                  }}
                  className="h-9 shadow-none"
                />
              </div>

              {/* 正确率设定 DropdownMenu */}
              <div className="flex items-center gap-3">
                <label className="text-gray-2 whitespace-nowrap text-[0.875rem] font-normal leading-[1.3125rem]">
                  正确率设定
                </label>
                {/* 3. 使用受控模式 */}
                <DropdownMenu
                  open={openDropdownId === "accuracyRateDropdown"} // 由 state 控制打开状态
                  onOpenChange={(isOpen) =>
                    handleDropdownOpenChange(isOpen, "accuracyRateDropdown")
                  } // 更新 state
                >
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex h-9 w-full flex-1 items-center justify-between shadow-none"
                    >
                      {/* 显示当前选中的 accuracyRate 值 */}
                      {settingsValue.value.accuracyRate || "选择正确率"}
                      <ChevronRight
                        className={cn(
                          "text-gray-2 h-4 w-4 transition-transform duration-300",
                          openDropdownId === "accuracyRateDropdown"
                            ? "rotate-90"
                            : ""
                        )}
                      />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="center"
                    side="bottom"
                    sideOffset={4}
                    className="h-[18rem] w-[var(--radix-dropdown-menu-trigger-width)] overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]"
                  >
                    {/* 动态生成选项 */}
                    {accuracyRateOptions.map((option) => (
                      <DropdownMenuItem
                        key={option.value}
                        // 点击时更新设置并关闭下拉菜单 (关闭是自动的，因为状态会变)
                        onClick={() => {
                          updateSetting("accuracyRate", option.value);
                          toast.success("设置已更新");
                          // setOpenDropdownId(null); // 可选：如果点击后需要立即关闭，但通常 onOpenChange 已处理
                        }}
                        // 添加选中状态样式
                        className={cn(
                          "text-gray-2 flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%]",
                          settingsValue.value.accuracyRate === option.value &&
                            "bg-primary-6 text-primary-1" // 高亮选中项
                        )}
                      >
                        {option.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </DrawerCard>

          {/* --- 学习状态列表 --- */}
          <DrawerCard title="学习状态列表">
            <p className="mb-4 text-sm leading-[150%] text-[#646B8A]">
              以学生个体展示数据，展示在当前数据范围下的学习任务详情，基于当前学习状态，将提示&quot;有进步&quot;，&quot;需提醒&quot;的同学
            </p>
            <div className="flex flex-col gap-y-2">
              {learningStatusItems.map((item) => {
                // 为每个学习状态项生成唯一 ID
                const dropdownId = `learningStatus-${item.id}`;
                return (
                  <div key={item.id} className="relative">
                    {/* 3. 使用受控模式 */}
                    <DropdownMenu
                      open={openDropdownId === dropdownId} // 由 state 控制
                      onOpenChange={(isOpen) =>
                        handleDropdownOpenChange(isOpen, dropdownId)
                      } // 更新 state
                    >
                      <DropdownMenuTrigger asChild>
                        <div className="data-[state=open]:border-primary-2 flex h-14 w-full items-center justify-between gap-2 self-stretch rounded-xl border border-[#DFE3F0] bg-white px-4">
                          <span className="text-base font-normal leading-[150%] text-[#444963]">
                            {item.label}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="font-variant-numeric-[lining-nums_proportional-nums] text-[0.875rem] font-normal leading-[150%] text-[#444963]">
                              {/* 显示当前 learningStatusValues 中对应的值 */}
                              {item.value || "选择"}
                            </span>
                            <ChevronRight
                              className={cn(
                                "h-4 w-4 text-gray-400 transition-transform duration-300",
                                openDropdownId === dropdownId ? "rotate-90" : ""
                              )}
                            />
                          </div>
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="center"
                        side="bottom"
                        sideOffset={4}
                        className="w-[var(--radix-dropdown-menu-trigger-width)] overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]"
                      >
                        {/* 动态生成百分比选项 */}
                        {percentageOptions.map((percentage) => (
                          <DropdownMenuItem
                            key={percentage}
                            onClick={() =>
                              handleValueChange(item.id, percentage)
                            }
                            // 添加选中状态样式
                            className={cn(
                              "flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963]",
                              item.value === percentage &&
                                "bg-primary-6 text-primary-1" // 高亮选中项
                            )}
                          >
                            {percentage}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                );
              })}
            </div>
          </DrawerCard>
        </div>
      </SheetContent>
    </Sheet>
  );
}
