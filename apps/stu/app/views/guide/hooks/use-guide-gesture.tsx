import { batch, useSignal } from "@preact-signals/safe-react";
import { RefObject, useEffect } from "react";
import { useThrottledCallback } from "use-debounce";

export const useGuideGesture = (
  ref: RefObject<HTMLDivElement | null>,
  onPan: () => void
) => {
  const isMoving = useSignal(false);
  const startY = useSignal(0);
  const offsetY = useSignal(0);
  const hasTriggerOnPan = useSignal(false);

  const container = ref.current;

  const throttledTouchMove = useThrottledCallback((e: TouchEvent) => {
    if (!container) return;
    const touch = e.touches[0];
    if (!touch) return;
    const currentY = touch.clientY;
    const offset = currentY - startY.value;
    offsetY.value = offset;
    // 先处理原有onPan逻辑
    if (
      hasTriggerOnPan.value === false &&
      isMoving.value === true &&
      Math.abs(offsetY.value) > 72
    ) {
      onPan();
      hasTriggerOnPan.value = true;
    }
  }, 40);

  useEffect(() => {
    if (!container) {
      return;
    }
    const handleTouchStart = (e: TouchEvent) => {
      if (isMoving.value === true) return;
      const touch = e.touches[0];
      if (!touch) return;
      isMoving.value = true;
      startY.value = touch.clientY;
    };
    const handleTouchEnd = () => {
      batch(() => {
        isMoving.value = false;
        offsetY.value = 0;
        hasTriggerOnPan.value = false;
      });
    };

    // container.addEventListener("wheel", handleUserAction);
    container.addEventListener("touchstart", handleTouchStart);
    container.addEventListener("touchmove", throttledTouchMove);
    container.addEventListener("touchend", handleTouchEnd);
    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      // container.removeEventListener("wheel", handleUserAction);
      container.removeEventListener("touchend", handleTouchEnd);
      container.removeEventListener("touchmove", throttledTouchMove);
    };
  }, [
    container,
    onPan,
    throttledTouchMove,
    isMoving,
    offsetY,
    startY,
    hasTriggerOnPan,
  ]);
};
