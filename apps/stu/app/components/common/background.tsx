import { cn } from "@repo/ui/lib/utils";
import Image, { StaticImageData } from "next/image";
import { FC, ReactNode } from "react";

export const Background: FC<{
  bgImage: StaticImageData;
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
}> = ({ bgImage, children, className, style, onClick }) => {
  return (
    <div
      className={cn("relative h-screen w-full", className)}
      style={style}
      onClick={onClick}
    >
      <Image
        src={bgImage}
        alt="bg"
        fill
        priority
        className="pointer-events-none object-cover object-center"
      />
      <div className="relative z-10 h-full w-full">{children}</div>
    </div>
  );
};
