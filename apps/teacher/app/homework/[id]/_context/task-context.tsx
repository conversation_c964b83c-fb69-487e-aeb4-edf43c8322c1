"use client";

import {
  OriginalClassData,
  TaskData,
} from "@/app/homework/_components/task-card";
import { TASK_TYPE } from "@/enums";
import {
  getHomeworkDetail,
  GetHomeworkDetailParams,
  getTaskDetail,
  TaskDetailResponse,
} from "@/services/homework";
import {
  HomeworkDetailsData,
  HomeworkReport,
  StudentBase,
} from "@/types/homeWork";
import { toast } from "@/ui/toast";
import {
  computed,
  Signal,
  signal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import type { Result } from "ahooks/lib/useRequest/src/types";
import * as React from "react";
import { createContext, useContext, useEffect } from "react";
import { TabValue } from "../_components/layout/TabNav";

/**
 * 视图类型：班级视图或学生个人视图
 */
export type ViewMode = "class" | "student";

/**
 * 报告详情共用数据
 */
export interface ReportDetailData {
  title: string; // 任务标题
  publishTime: number; // 发布时间
  deadline: number; // 截止时间
  className: string; // 班级名称
  scope: string; // 范围
}

/**
 * 学生个人视图数据
 */
export interface StudentReportData extends ReportDetailData {
  studentName: string; // 学生姓名
  studentId: string; // 学生ID
  progress: number; // 进度
  accuracy: number; // 正确率
  pendingQuestions: number; // 待处理问题数量
  praiseCount: number; // 鼓励数量
  praiseDefaultText?: string;
  pushDefaultText?: string;
  studentType?: "praise" | "attention" | "other";
}

/**
 * 班级报告数据
 */
interface ClassReportData {
  title: string; // 任务标题
  publishTime: number; // 发布时间
  deadline: number; // 截止时间
  className: string; // 班级名称
  scope?: string; // 范围
  progress?: number; // 进度
  classId?: number; // 班级ID
  needAttentionQuestionNum?: number; // 需要关注的问题数量
  contentId?: string; // 课程内容ID
  taskId: number; // 任务ID
  taskName: string; // 任务名称
  taskType: number; // 任务类型
  assignId: number; // 任务布置ID
  subject: number; // 学科id
}
export type TaskDataWithClassData = TaskData & { classData: OriginalClassData };

/**
 * 任务Context类型定义
 */
interface TaskContextType {
  showStudentDetail: Signal<boolean>;
  taskId: Signal<number>;
  taskName: Signal<string>;
  taskType: Signal<TASK_TYPE | 0>;
  taskData: Signal<TaskDataWithClassData | null>; // 任务数据信号
  viewMode: Signal<ViewMode>; // 视图模式信号
  viewModeNew: Signal<ViewMode>;
  setViewMode: (mode: ViewMode) => void; // 设置视图模式的方法
  activeTab: Signal<TabValue>; // 当前激活的标签页信号
  setActiveTab: (tab: TabValue) => void; // 设置当前标签页的方法
  classData: Signal<ClassReportData>; // 班级报告数据信号
  studentData: Signal<StudentReportData>; // 学生报告数据信号
  setStudentId: (id: string | null) => void; // 设置学生ID的方法
  setStudentName: (name: string | null) => void; // 设置学生姓名的方法
  setCurrentCourse: (courseId: string | null) => void; // 设置当前课程的方法
  updateStudentData: (data: Partial<StudentReportData>) => void; // 更新学生数据的方法
  loading: Signal<boolean>; // 加载状态信号
  currentClass: Signal<{
    // 当前选中的班级信号
    id: number;
    name: string;
  } | null>;
  setCurrentClass: (classData: { id: number; name: string } | null) => void; // 设置当前班级

  currentCourse: Signal<{
    id: string;
    name: string;
    type: number;
  } | null>;
  selectedCourseList: Signal<
    Array<{
      id: string;
      name: string;
      type: number;
    }>
  >;

  homeworkData: Signal<HomeworkDetailsData | null>; // 作业详情数据信号
  studentListMap: Signal<Record<number, StudentBase>>; // 学生列表Map
  setStudentListMap: (students: StudentBase[]) => void; // 设置学生列表Map
  getStudentName: (studentId: number) => string; // 获取学生姓名
  getStudentAvatar: (studentId: number) => string; // 获取学生头像
  updateCurrentCourseFromHomeworkData: () => void; // 更新课程信息
  setTaskId: (id: number | string) => void; // 设置任务ID的方法
  useFetchHomeworkDetailRequest: () => Result<
    HomeworkDetailsData | undefined,
    []
  >; // 获取作业详情请求
  assignId: Signal<string>;
  useFetchTaskDetailRequest: () => Result<TaskDetailResponse | undefined, []>;
  classList: Signal<
    Array<{
      classId: number;
      className: string;
      taskId: number;
      assignId: number;
    }>
  >;
}
// 初始化默认数据
const initialClassData: ClassReportData = {
  title: "",
  publishTime: 0,
  deadline: 0,
  className: "",
  taskId: 0,
  taskName: "",
  taskType: 0,
  assignId: 0,
  subject: 0,
};

const initialStudentData: StudentReportData = {
  title: "3.2.2 函数的最值",
  publishTime: 1715904000000,
  deadline: 1716163200000,
  className: "高一 (3) 班",
  scope: "全部 (2节课)",
  studentName: "张三",
  studentId: "student-001",
  progress: 65,
  accuracy: 78,
  pendingQuestions: 10,
  praiseCount: 0,
};

// 创建全局状态信号
export const taskDataSignal = signal<TaskDataWithClassData | null>(null);
export const viewModeSignal = signal<ViewMode>("class");
export const viewModeNewSignal = signal<ViewMode>("class");
export const activeTabSignal = signal<TabValue>("report");
export const classDataSignal = signal<ClassReportData>(initialClassData);
export const studentDataSignal = signal<StudentReportData>(initialStudentData);
// 当前选中的班级
export const currentClassSignal = signal<{ id: number; name: string } | null>(
  null
);
export const showStudentDetailSignal = signal<boolean>(false);
export const loadingSignal = signal<boolean>(false);
export const currentCourseSignal = signal<{
  id: string;
  name: string;
  type: number;
} | null>(null);
export const selectedCourseListSignal = signal<
  Array<{
    id: string;
    name: string;
    type: number;
  }>
>([]);
// 作业详情数据信号
export const homeworkDataSignal = signal<HomeworkDetailsData | null>(null);
// 学生列表 map
export const studentListMapSignal = signal<Record<number, StudentBase>>({});
// 添加独立的任务ID信号
export const taskIdSignal = signal<number>(0);
export const assignIdSignal = signal<string>("");
export const classListSignal = signal<
  Array<{
    classId: number;
    className: string;
    taskId: number;
    assignId: number;
  }>
>([]);

function useFetchHomeworkDetailRequest() {
  return useRequest(
    async () => {
      if (
        !classDataSignal.value.classId ||
        !assignIdSignal.value ||
        !taskIdSignal.value
      ) {
        // toast.error("获取作业详情失败，请重新进入");
        // router.back();
        return;
      }

      const params: GetHomeworkDetailParams = {
        taskId: taskIdSignal.value,
        assignId: assignIdSignal.value ? Number(assignIdSignal.value) : 0, // 作业ID
        resourceId: classDataSignal.value.contentId,
        // ? Number(classDataSignal.value.contentId.replace("content-", ""))
        // : undefined,
      };
      return getHomeworkDetail(params);
    },
    {
      pollingInterval: 60000,
      pollingWhenHidden: false,
      manual: true,
      debounceWait: 500,
      onError: (error) => {
        console.error("获取作业详情失败:", error);
        toast.error("获取作业详情失败");
      },
    }
  );
}

function useFetchTaskDetailRequest() {
  return useRequest(
    async () => {
      if (!assignIdSignal.value || !taskIdSignal.value) return;
      const params: { taskId: number; assignId: number } = {
        taskId: taskIdSignal.value,
        assignId: assignIdSignal.value ? Number(assignIdSignal.value) : 0,
      };
      return getTaskDetail(params);
    },
    {
      manual: true,
      debounceWait: 500,
      onError: (error) => {
        console.error("获取任务详情失败:", error);
        toast.error("获取任务详情失败");
      },
      onSuccess: (data) => {
        if (data) {
          const currentAssign = data.assignInfos.find(
            (item) => item.assignId === Number(assignIdSignal.value)
          );
          classDataSignal.value = {
            ...classDataSignal.value,
            assignId: currentAssign?.assignId || Number(assignIdSignal.value),
            taskId: data.taskInfo.taskId,
            publishTime: currentAssign?.startTime || 0,
            deadline: currentAssign?.deadline || 0,
            classId: currentAssign?.classId,
            title: data.taskInfo.taskName,
            taskType: data.taskInfo.taskType,
            className: currentAssign
              ? currentAssign.gradeName + currentAssign.className
              : "",
            subject: data.taskInfo.subject,
          };
          TaskContextValue.updateStudentData({
            publishTime: currentAssign?.startTime || 0,
            deadline: currentAssign?.deadline || 0,
            title: data.taskInfo.taskName,
            className: currentAssign
              ? currentAssign.gradeName + currentAssign.className
              : "",
          });
          currentClassSignal.value = {
            id: currentAssign?.classId || 0,
            name: currentAssign
              ? currentAssign.gradeName + currentAssign.className
              : "",
          };
          classListSignal.value = data.assignInfos.map((assign) => ({
            classId: assign.classId,
            className: assign.gradeName + assign.className,
            taskId: assign.taskId,
            assignId: assign.assignId,
          }));
        }
      },
    }
  );
}

const TaskContextValue = {
  // 使用独立的taskIdSignal替代从taskDataSignal计算
  showStudentDetail: showStudentDetailSignal,
  taskId: taskIdSignal,
  taskName: computed(() => taskDataSignal.value?.typeName || ""),
  taskType: computed(() => taskDataSignal.value?.type || 0),
  taskData: taskDataSignal,
  viewMode: viewModeSignal,
  viewModeNew: viewModeNewSignal,
  setViewMode: (mode: ViewMode) => {
    viewModeSignal.value = mode;
  },
  activeTab: activeTabSignal,
  setActiveTab: (tab: TabValue) => {
    activeTabSignal.value = tab;
  },
  classData: classDataSignal,
  studentData: studentDataSignal,
  setStudentId: (id: string | null) => {
    studentDataSignal.value = {
      ...studentDataSignal.value,
      studentId: id || "",
    };
  },
  setStudentName: (name: string | null) => {
    studentDataSignal.value = {
      ...studentDataSignal.value,
      studentName: name || "",
    };
  },
  setCurrentCourse: (courseId: string | null) => {
    if (homeworkDataSignal.value?.detail?.resourceReports?.length) {
      const courseInfo = homeworkDataSignal.value.detail.resourceReports.find(
        (report) => report.resourceId === courseId
      );
      if (courseInfo) {
        currentCourseSignal.value = {
          id: courseId || "",
          name: courseInfo.resourceName,
          type: courseInfo.resourceType,
        };
      }
    } else {
      currentCourseSignal.value = {
        id: courseId || "",
        name: "",
        type: 0,
      };
    }
  },
  updateStudentData: (data: Partial<StudentReportData>) => {
    studentDataSignal.value = {
      ...studentDataSignal.value,
      ...data,
    };
  },
  currentClass: currentClassSignal,
  setCurrentClass: (classData: { id: number; name: string } | null) => {
    currentClassSignal.value = classData;
  },
  currentCourse: currentCourseSignal,
  selectedCourseList: selectedCourseListSignal,
  loading: loadingSignal,
  homeworkData: homeworkDataSignal,
  studentListMap: studentListMapSignal,
  setStudentListMap: (students: StudentBase[]) => {
    studentListMapSignal.value = students?.reduce(
      (acc, student) => {
        acc[student.studentId] = student;
        return acc;
      },
      {} as Record<number, StudentBase>
    );
  },

  getStudentName: (studentId: number) => {
    return studentListMapSignal.value?.[studentId]?.studentName || "";
  },
  getStudentAvatar: (studentId: number) => {
    return studentListMapSignal.value?.[studentId]?.avatar || "";
  },
  updateCurrentCourseFromHomeworkData: () => {
    if (!homeworkDataSignal.value?.detail?.resourceReports) return;

    const courseId = currentCourseSignal.value?.id;
    console.log(
      "[updateCurrentCourseFromHomeworkData] courseId",
      courseId,
      homeworkDataSignal.value?.detail?.resourceReports
    );

    if (!courseId) return;

    const courseInfo = homeworkDataSignal.value.detail.resourceReports.find(
      (report) => report.resourceId === courseId
    );

    if (courseInfo) {
      currentCourseSignal.value = {
        id: courseId,
        name: courseInfo.resourceName,
        type: courseInfo.resourceType,
      };
    }
  },
  // 简化为仅设置taskId的方法
  setTaskId: (id: number | string) => {
    const numericId = typeof id === "string" ? parseInt(id, 10) : id;
    if (isNaN(numericId)) return;

    // 直接设置独立的taskIdSignal
    console.log(`[TaskContext] 设置任务ID: ${numericId}`);
    taskIdSignal.value = numericId;
  },
  useFetchHomeworkDetailRequest: useFetchHomeworkDetailRequest,
  assignId: assignIdSignal,
  useFetchTaskDetailRequest: useFetchTaskDetailRequest,
  classList: classListSignal,
};

export const fetchHomeworkDetail = async () => {
  if (
    !classDataSignal.value.classId ||
    !assignIdSignal.value ||
    !taskIdSignal.value
  ) {
    // toast.error("获取作业详情失败，请重新进入");
    // router.back();
    return;
  }

  try {
    // 构建请求参数
    const params: GetHomeworkDetailParams = {
      taskId: taskIdSignal.value,
      assignId: assignIdSignal.value ? Number(assignIdSignal.value) : 0, // 作业ID
      resourceId: classDataSignal.value.contentId,
      // ? Number(classData.value.contentId.replace("content-", ""))
      // : undefined,
    };

    // 调用服务获取数据
    const data = await getHomeworkDetail(params);

    homeworkDataSignal.value = data;
    // 构建学生列表 map - 只在获取数据后设置一次
    TaskContextValue.setStudentListMap(
      data.detail.studentReports as unknown as StudentBase[]
    );
  } catch (error) {
    console.error("获取作业详情失败:", error);
    toast.error("获取作业详情失败");
  }
};

// 创建任务上下文
export const TaskContext = createContext<TaskContextType>(TaskContextValue);

/**
 * 使用任务上下文的Hook
 * @throws 如果在TaskProvider外部使用会抛出错误
 */
export function useTaskContext() {
  const context = useContext(TaskContext);
  if (!context) {
    throw new Error("useTaskContext must be used within a TaskProvider");
  }
  return context;
}

/**
 * 任务数据提供者组件
 * 负责管理任务相关的状态和数据流
 */
export function TaskProvider({ children }: { children: React.ReactNode }) {
  // 初始化数据：从localStorage加载任务数据
  useEffect(() => {
    const storedTask = localStorage.getItem("homework_currentTask");
    if (storedTask) {
      try {
        const parsedTask = JSON.parse(storedTask) as TaskDataWithClassData;
        taskDataSignal.value = parsedTask;

        // 设置默认选中的班级
        if (parsedTask.classes && parsedTask.classes.length > 0) {
          const currentClass = parsedTask.classes[0];
          currentClassSignal.value = {
            id: currentClass.id,
            name: currentClass.name,
          };

          // 查找并更新对应班级的报告数据
          const report = parsedTask.homeworkData.reports.find(
            (r) => r.assignObject.id === currentClass.id
          );

          if (report) {
            updateClassData(report, parsedTask);
          }
        }
        if (
          (taskDataSignal.value as TaskDataWithClassData & { from: string })
            .from === "course"
        ) {
          TaskContextValue.updateStudentData(taskDataSignal.value);
          classDataSignal.value = {
            ...classDataSignal.value,
            assignId: taskDataSignal.value.classes[0].assignId,
          };
          assignIdSignal.value =
            taskDataSignal.value.classes[0].assignId.toString();
        }
      } catch (error) {
        console.error("解析任务数据失败", error);
      }
    }
  }, []);

  // 监听当前班级变化，更新相关数据
  useSignalEffect(() => {
    if (!taskDataSignal.value || !currentClassSignal.value) return;

    const selectedClass = taskDataSignal.value.homeworkData.reports.find(
      (report) => report.assignObject.id === currentClassSignal.value?.id
    );

    if (selectedClass) {
      updateClassData(selectedClass, taskDataSignal.value);
    }
  });

  /**
   * 更新班级数据
   * @param classItem - 班级报告数据
   * @param taskData - 任务数据
   */
  const updateClassData = (
    classItem: HomeworkReport,
    taskData: TaskDataWithClassData
  ) => {
    classDataSignal.value = {
      needAttentionQuestionNum:
        classItem.statData?.needAttentionQuestionNum || 0,
      title: taskData.title,
      publishTime: taskData.startTime,
      deadline: taskData.endTime,
      className: classItem.assignObject.name,
      scope: "全部课程", // 设置默认scope
      progress: classItem.statData?.completionRate || 0,
      classId: taskData.classData.assignObject.id,
      taskId: taskData.id,
      taskName: taskData.classData.taskName,
      taskType: taskData.classData.taskType,
      assignId: taskData.classData.assignId,
      subject: taskData.subject,
    };
  };

  return (
    <TaskContext.Provider value={TaskContextValue}>
      {children}
    </TaskContext.Provider>
  );
}
