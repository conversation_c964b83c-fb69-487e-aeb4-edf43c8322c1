# Exercise Preview - 练习预览模块

## 概述

这是一个用于预览已完成练习题目的模块，支持查看学生答题记录、答案解析和统计信息。主要用于答题回顾、错题查看等场景。

## 架构设计

### 重构后的架构

#### Model 层
- `types.ts` - 前端类型定义，包含题目、答题记录等数据结构
- `schemas.ts` - 后端数据验证Schema，使用Zod进行类型校验
- `index.ts` - 统一导出Model层的所有类型和Schema

#### ViewModel 层
- `preview-viewmodel.ts` - 预览模式的业务逻辑，管理题目列表导航、显示配置等

#### View 层
- `exercise-view.tsx` - **核心组件**，支持单题和题目列表两种模式
- `question-view.tsx` - 题目展示组件
- 其他辅助组件...

#### Contexts 层
- `question-preview-context.tsx` - 预览模式的Context，管理题目状态和配置

#### Page 层
- `page.tsx` - **简化后的入口页面**，只负责数据准备和事件处理

## 重构变更

### 🔥 主要变更

1. **ExerciseView 组件能力增强**
   - 支持传入题目列表 (`questionList`) 
   - 内置ViewModel逻辑管理列表导航
   - 保持向后兼容，仍支持单题模式

2. **Page 层职责简化**
   - 只保留 mock 数据、handleBack、handleComplete
   - 移除 ViewModel 逻辑到 ExerciseView 内部
   - 作为不同平台的使用入口，负责数据转换

3. **类型定义修复**
   - 修复 `StudentAnswerContent` 类型，增加 `evaluationResult` 字段
   - 确保前端类型与后端Schema一致

### 🎯 重构目标

- **提高复用性**: ExerciseView 可在多个平台使用
- **简化集成**: 其他平台只需关心数据准备和事件处理
- **职责分离**: Page 层专注数据转换，View 层专注业务逻辑

## 使用方式

### 1. 题目列表模式 (推荐)

```tsx
import { ExerciseView, PreviewQuestionData } from './view/exercise-view';

// 准备题目数据
const questionList: PreviewQuestionData[] = [
  {
    questionInfo: { /* NextQuestionInfo */ },
    studentAnswer: { /* StudentAnswer */ },
    answerStat: [ /* AnswerStat[] */ ]
  },
  // ... 更多题目
];

// 使用组件
<ExerciseView
  questionList={questionList}
  initialIndex={0}
  studyType={StudyType.REINFORCEMENT_EXERCISE}
  onComplete={handleComplete}
  onBack={handleBack}
  isPreviewMode={true}
/>
```

### 2. 单题模式 (兼容)

```tsx
<ExerciseView
  questionData={singleQuestion}
  studyType={StudyType.REINFORCEMENT_EXERCISE}
  onComplete={handleComplete}
  onBack={handleBack}
  isPreviewMode={false}
/>
```

## 数据类型

### PreviewQuestionData

```typescript
interface PreviewQuestionData {
  // 题目基础信息
  questionInfo: NextQuestionInfo;
  // 学生答题记录（可选，有则表示已作答）
  studentAnswer?: StudentAnswer;
  // 答案选择分布统计（可选）
  answerStat?: AnswerStat[];
}
```

### ExerciseViewProps

```typescript
interface ExerciseViewProps {
  // 题目列表模式
  questionList?: PreviewQuestionData[];
  initialIndex?: number;
  
  // 单题模式
  questionData?: Question;
  
  // 通用配置
  studyType?: StudyType;
  isPreviewMode?: boolean;
  onComplete?: (nextQuestionInfo?: NextQuestionInfo) => void;
  onBack?: () => void;
  
  // 预览数据（单题模式时使用）
  previewData?: {
    answerDuration?: number;
    answerResult?: 0 | 1 | 2 | 3;
    answerStat?: Array<{ optionKey: string; rate: number }>;
    studentAnswer?: StudentAnswer;
  };
}
```

## 特性

### ✅ 支持的功能

- **题目类型**: 单选题、多选题、填空题、解答题等所有题型
- **答题记录**: 显示学生的答题内容和用时
- **答案解析**: 自动显示题目解析（预览模式默认开启）
- **统计信息**: 显示答案选择分布（适用于选择题）
- **导航功能**: 上一题/下一题按钮，支持键盘快捷键
- **错误处理**: 完善的加载状态和错误提示
- **状态管理**: 使用Context管理组件间的状态共享

### 🔧 内置功能

- **自动状态管理**: 题目列表导航状态自动维护
- **数据转换**: 自动将后端数据格式转换为前端组件需要的格式
- **错误边界**: 题目转换失败时的优雅降级处理
- **加载状态**: 题目数据加载时的友好提示

## 注意事项

### ⚠️ 重要提醒

1. **数据一致性**: 确保 `questionList` 数组的索引与 `initialIndex` 对应
2. **类型安全**: 使用 TypeScript 严格模式，确保类型正确
3. **错误处理**: 组件内置了错误处理，但仍需关注控制台警告
4. **性能优化**: 大列表时考虑虚拟滚动或分页加载

### 📝 最佳实践

1. **数据准备**: 在 Page 层完成数据格式转换和验证
2. **事件处理**: handleBack 和 handleComplete 保持简洁，避免复杂业务逻辑
3. **错误监控**: 添加适当的错误监控和日志记录
4. **用户体验**: 考虑加载状态和网络异常的用户提示

## TODO

- [ ] 添加键盘快捷键支持（左右箭头切换题目）
- [ ] 支持题目收藏功能
- [ ] 添加题目难度可视化展示
- [ ] 优化大列表性能（虚拟滚动）
- [ ] 添加题目搜索和筛选功能

## 更新记录

### v1.1.0 (2024-01-XX)
- 🔥 重构 ExerciseView 支持题目列表模式
- 🎯 简化 Page 层职责，提高组件复用性
- 🔧 修复 StudentAnswerContent 类型定义
- ✨ 增强错误处理和加载状态管理 