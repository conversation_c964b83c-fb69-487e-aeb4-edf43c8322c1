"use client";

import ThumbsUpGif from "@/public/assign/thumbs-up.gif";
import ThumbsUpSVG from "@/public/assign/thumbs-up.svg";
import ThumbsUpSVG2 from "@/public/icons/ic_praised.svg";
// import ThumbsUpSVG3 from "@/public/icons/ic_praise.svg";
import { GifButton } from "./gif-button";

interface ThumbsUpButtonProps
  extends Omit<
    React.ComponentProps<typeof GifButton>,
    | "src"
    | "activeSrc"
    | "duration"
    | "activedSrc"
    | "SrcCompo"
    | "ActiveSrcCompo"
  > {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;

  children?: React.ReactNode;
  className?: string;
  imgWidth?: number;
  imgHeight?: number;
  imgClassName?: string;
  duration?: number;
  praiseCount?: number;
}

const ThumbsUpSVGNew = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.00001 3.33315L5.59998 6.40002V13.6H10.7738C11.4972 13.6 12.1307 13.1146 12.3188 12.416L13.3239 8.68238C13.5976 7.66575 12.8318 6.66643 11.7789 6.66643H9.33334L9.66667 3.99971C10 2.33319 8.00001 1.6666 7.00001 3.33315Z"
      fill="currentColor"
    />
    <path
      d="M3.19998 6.39996H4.79998V13.6H3.19998C2.31632 13.6 1.59998 12.8836 1.59998 12V7.99996C1.59998 7.11631 2.31632 6.39996 3.19998 6.39996Z"
      fill="currentColor"
    />
  </svg>
);

export function ThumbsUpButton({
  onClick,
  children,
  className,
  imgWidth,
  imgHeight,
  // imgClassName,
  duration,
  praiseCount,
  ...props
}: ThumbsUpButtonProps) {
  return (
    <GifButton
      // src={ThumbsUpSVG}
      activeSrc={ThumbsUpGif.src}
      // activedSrc={ThumbsUpSVG2.src}
      duration={duration ?? 1500}
      imgClassName="scale-200"
      activeImgClassName="top-[-0.0625rem] left-0 scale-210 mt-[0.1875rem] "
      onClick={onClick}
      className={className}
      imgWidth={imgWidth ?? 16}
      imgHeight={imgHeight ?? 16}
      alt="鼓励"
      praiseCount={praiseCount}
      // iconComponent={ThumbsUpSVGNew}
      {...props}
      SrcCompo={ThumbsUpSVG}
      ActiveSrcCompo={ThumbsUpSVG2}
    >
      {children}
    </GifButton>
  );
}
