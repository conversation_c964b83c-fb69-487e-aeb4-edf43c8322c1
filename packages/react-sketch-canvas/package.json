{"name": "@repo/react-sketch-canvas", "version": "1.0.0", "type": "module", "description": "react-sketch-canvas - Freehand vector drawing tool for React using SVG as canvas", "author": "Vinoth Pandian", "homepage": "https://vinoth.info/react-sketch-canvas", "license": "MIT", "repository": "https://github.com/vinothpandian/react-sketch-canvas.git", "keywords": ["react-component", "sketch", "canvas", "drawing", "freehand", "vector", "svg-canvas", "react-sketch"], "typings": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=10"}, "devDependencies": {"@repo/config-eslint": "workspace:*", "@babel/core": "^7.17.5", "@cypress/code-coverage": "^3.9.12", "@size-limit/preset-small-lib": "^7.0.8", "@testing-library/cypress": "^8.0.2", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "babel-loader": "^8.2.3", "babel-plugin-istanbul": "^6.1.1", "babel-plugin-transform-class-properties": "^6.24.1", "concurrently": "^7.0.0", "cross-env": "^7.0.3", "cypress": "^9.5.1", "dts-cli": "^1.4.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^7.0.4", "merge-cypress-jest-coverage": "^1.7.0", "react": "catalog:react19", "react-dom": "catalog:react19", "react-is": "v17.0.2", "release-it": "^14.12.5", "rimraf": "^3.0.2", "size-limit": "^7.0.8", "start-server-and-test": "^1.14.0", "tslib": "^2.3.1", "typescript": "^4.6.2"}, "dependencies": {"uuid": "^11.1.0"}, "peerDependencies": {"react": ">=16.8"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "exports": "./src/index.tsx"}