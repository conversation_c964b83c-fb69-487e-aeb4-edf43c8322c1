import { Button, Space } from 'antd';
import debounce from 'lodash/debounce';
import React, { useState } from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';
import { useQuestionCreateActions } from '../../hooks/useQuestionCreateActions';
import { QuestionCreateProps } from '../../types';
import AuditFailModal from '../shared/AuditFailModal';
import styles from './index.module.less';

/**
 * 题目操作按钮组件
 * 根据不同的模式（创建/上传/审核）显示对应的操作按钮
 */
const QuestionActionButtons: React.FC<QuestionCreateProps> = ({ way, data, onBack, onSubmit }) => {
  const { questionList, isSingleQuestion } = useQuestionCreate();

  const { correctionHandleSubmit, handleFormSubmit, handleAuditPassSubmit, handleBlurSave } =
    useQuestionCreateActions({
      way,
      data,
      onBack,
      onSubmit,
    });
  const { auditTaskFunc } = useQuestionCreateActions({ way, data, onBack, onSubmit });
  // 审核相关
  const [auditFailModalVisible, setAuditFailModalVisible] = useState(false);

  const handleAuditNoPassSubmit = () => {
    handleBlurSave();
    setAuditFailModalVisible(true);
  };

  if (way === 'correction') {
    return (
      <Button type="primary" onClick={debounce(correctionHandleSubmit, 300)}>
        提交
      </Button>
    );
  }

  // 单题且非审核模式：显示提交按钮
  if (isSingleQuestion && way !== 'audit') {
    return (
      <Button
        type="primary"
        disabled={questionList.length === 0}
        onClick={debounce(handleFormSubmit, 300)}
      >
        提交
      </Button>
    );
  }

  // 多题且上传模式：显示提交审核按钮
  if (!isSingleQuestion && way === 'upload') {
    return (
      <Button
        type="primary"
        disabled={questionList.length === 0}
        onClick={debounce(handleFormSubmit, 300)}
      >
        提交审核
      </Button>
    );
  }

  // 审核模式：显示通过/不通过按钮
  if (way === 'audit') {
    return (
      <>
        <Space align="center" className={styles.auditBtnContainer}>
          <Button type="primary" danger onClick={handleAuditNoPassSubmit}>
            不通过
          </Button>
          <Button type="primary" onClick={debounce(handleAuditPassSubmit, 300)}>
            通过
          </Button>
        </Space>
        <AuditFailModal
          visible={auditFailModalVisible}
          onClose={() => setAuditFailModalVisible(false)}
          onSubmit={(reason) => auditTaskFunc(3, reason)}
        />
      </>
    );
  }

  // 其他情况不显示按钮
  return null;
};

export default QuestionActionButtons;
