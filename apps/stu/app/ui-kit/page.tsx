"use client";
import Button from "@repo/ui/components/press-button";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";
import {
  componentCategories,
  getComponent,
  getComponentsByCategory,
} from "./componentDemos";

// 进度条演示组件已移至独立文件：./progress-bar-demo/page.tsx

// 按钮演示组件
const ButtonDemo: React.FC = () => {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {}
  );

  const toggleLoading = (key: string) => {
    setLoadingStates((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="space-y-8">
      {/* 基础按钮 */}
      <div>
        <h3 className="mb-4 text-lg font-medium">基础按钮</h3>
        <div className="flex flex-wrap gap-4">
          <Button>默认按钮</Button>
          <Button color="red">红色按钮</Button>
          <Button color="green">绿色按钮</Button>
          <Button color="gray">灰色按钮</Button>
          <Button color="white">白色按钮</Button>
        </div>
      </div>

      {/* 尺寸变化 */}
      <div>
        <h3 className="mb-4 text-lg font-medium">尺寸变化</h3>
        <div className="flex flex-wrap items-center gap-4">
          <Button size="sm">小按钮</Button>
          <Button size="md">中按钮</Button>
          <Button size="lg">大按钮</Button>
        </div>
      </div>

      {/* 加载状态演示 */}
      <div>
        <h3 className="mb-4 text-lg font-medium">加载状态演示 🎯</h3>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button
              loading={loadingStates.demo1}
              onClick={() => toggleLoading("demo1")}
            >
              {loadingStates.demo1 ? "加载中" : "点击加载"}
            </Button>
            <Button
              loading={loadingStates.demo2}
              color="red"
              onClick={() => toggleLoading("demo2")}
            >
              {loadingStates.demo2 ? "提交中" : "提交"}
            </Button>
            <Button
              loading={loadingStates.demo3}
              color="green"
              size="sm"
              onClick={() => toggleLoading("demo3")}
            >
              {loadingStates.demo3 ? "保存中" : "保存"}
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            💡 点击按钮查看三个圆点加载动画效果
          </p>
        </div>
      </div>

      {/* 状态对比 */}
      <div>
        <h3 className="mb-4 text-lg font-medium">状态对比</h3>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button>正常状态</Button>
            <Button disabled>禁用状态</Button>
            <Button loading>加载状态</Button>
            <Button disabled loading>
              禁用+加载
            </Button>
          </div>
          <div className="flex flex-wrap gap-4">
            <Button color="red">正常红色</Button>
            <Button color="red" disabled>
              禁用红色
            </Button>
            <Button color="red" loading>
              加载红色
            </Button>
            <Button color="red" disabled loading>
              禁用+加载红色
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            💡 状态优先级：disabled（灰色+透明度） &gt; loading（三个圆点） &gt;
            normal
          </p>
          <p className="text-sm text-gray-600">
            🎯 动画效果：三个圆点依次从 0.2 → 1 → 0.2
            变化，形成经典的波浪加载效果
          </p>
        </div>
      </div>

      {/* 其他状态 */}
      <div>
        <h3 className="mb-4 text-lg font-medium">其他状态</h3>
        <div className="flex flex-wrap gap-4">
          <Button secondary>次要按钮</Button>
          <Button showShadow={false}>无阴影</Button>
        </div>
      </div>
    </div>
  );
};

interface SidebarProps {
  activeComponent: string;
  onComponentSelect: (component: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  activeComponent,
  onComponentSelect,
}) => {
  return (
    <aside className="sticky top-0 h-screen w-64 overflow-y-auto border-r border-gray-200 bg-white p-5">
      <h1 className="mb-6 text-xl font-bold text-gray-800">组件库</h1>

      <nav>
        {componentCategories.map((category) => {
          const components = getComponentsByCategory(category.id);

          if (components.length === 0) return null;

          return (
            <div key={category.id} className="mb-6">
              <h2 className="mb-3 text-sm font-medium uppercase tracking-wider text-gray-500">
                {category.name}
              </h2>
              <ul className="space-y-2">
                {components.map((component) => (
                  <li key={component.id}>
                    <button
                      onClick={() => onComponentSelect(component.id)}
                      className={`w-full rounded-md px-3 py-2 text-left text-sm ${
                        activeComponent === component.id
                          ? "bg-orange-100 font-medium text-orange-600"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {component.name} ({component.id})
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </nav>
    </aside>
  );
};

interface ComponentDisplayProps {
  componentId: string;
}

const ComponentDisplay: React.FC<ComponentDisplayProps> = ({ componentId }) => {
  const component = getComponent(componentId);

  const renderComponentDemo = () => {
    if (!component) {
      return (
        <div className="flex h-64 items-center justify-center rounded-lg bg-gray-100">
          <p className="text-gray-500">找不到该组件...</p>
        </div>
      );
    }

    // 按钮组件演示
    if (componentId === "button") {
      return <ButtonDemo />;
    }

    // 进度条组件演示 - 使用独立的 demo 组件
    if (componentId === "progress-bar") {
      const ProgressBarDemoComponent = React.lazy(
        () => import("./progress-bar-demo/page")
      );
      return (
        <React.Suspense fallback={<div>加载进度条演示中...</div>}>
          <ProgressBarDemoComponent />
        </React.Suspense>
      );
    }

    const DemoComponent = () => <div>123</div>;
    return <DemoComponent />;
  };

  if (!component) {
    return (
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h2 className="text-lg font-medium capitalize text-gray-800">
            未找到组件
          </h2>
        </div>
        <div className="p-6">
          <div className="flex h-64 items-center justify-center rounded-lg bg-gray-100">
            <p className="text-gray-500">
              找不到 ID 为 &quot;{componentId}&quot; 的组件
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm">
      <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
        <h2 className="text-lg font-medium text-gray-800">
          {component.name} 组件
        </h2>
        <p className="mt-1 text-gray-600">{component.description}</p>
      </div>

      <div className="border-b border-gray-200 p-6">
        <h3 className="text-md mb-4 font-medium text-gray-700">组件演示</h3>
        {renderComponentDemo()}
      </div>

      <div className="p-6">
        <h3 className="text-md mb-4 font-medium text-gray-700">使用示例</h3>
        <pre className="overflow-x-auto rounded-md bg-gray-50 p-4 text-sm">
          <code>{component.usage}</code>
        </pre>
      </div>
    </div>
  );
};

const ComponentGalleryPage: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const componentParam = searchParams.get("component");
  const [activeComponent, setActiveComponent] = useState<string>(
    componentParam || "button"
  );

  useEffect(() => {
    if (componentParam) {
      setActiveComponent(componentParam);
    }
  }, [componentParam]);

  const handleComponentSelect = (id: string) => {
    setActiveComponent(id);
    router.push(`/ui-kit?component=${id}`);
  };

  const componentExists = !!getComponent(activeComponent);

  useEffect(() => {
    if (!componentExists && activeComponent !== "button") {
      router.push("/ui-kit?component=button");
    }
  }, [activeComponent, componentExists, router]);

  return (
    <div className="flex h-full w-full overflow-hidden">
      <Sidebar
        activeComponent={activeComponent}
        onComponentSelect={handleComponentSelect}
      />
      <div className="flex-1 overflow-auto p-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">组件库展示</h1>
          <p className="text-gray-600">浏览和测试项目中的UI组件库</p>
        </div>
        <ComponentDisplay componentId={activeComponent} />
      </div>
    </div>
  );
};

export default function UIKitPage() {
  return (
    <div className="flex h-screen flex-col bg-gray-50">
      <header className="sticky top-0 z-10 border-b border-gray-200 bg-white">
        <div className="container mx-auto flex items-center justify-between px-4 py-3">
          <h1 className="text-xl font-bold text-gray-800">学生端组件库</h1>
          <Link
            href="/"
            className="rounded-md bg-gray-100 px-4 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-200"
          >
            返回主页
          </Link>
        </div>
      </header>
      <main className="h-full flex-1 overflow-hidden">
        <Suspense
          fallback={
            <div className="flex h-full items-center justify-center">
              加载中...
            </div>
          }
        >
          <ComponentGalleryPage />
        </Suspense>
      </main>
    </div>
  );
}
