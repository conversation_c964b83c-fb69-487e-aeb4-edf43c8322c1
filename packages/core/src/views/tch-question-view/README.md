# 教师端题目视图组件库

## 概述

`tch-question-view` 是一套专为教师端设计的题目展示组件库，提供了完整的题目渲染、选项布局、统计展示等功能。组件库采用纯 UI 设计原则，完全解耦业务逻辑，支持高度自定义和多项目复用。

## 特性

- ✅ **纯 UI 组件**: 无业务逻辑耦合，完全数据驱动
- ✅ **高性能**: 智能选项布局计算，防闪烁渲染
- ✅ **数学公式支持**: 集成 KaTeX 和 MathJax，完美渲染数学公式
- ✅ **响应式布局**: 自适应选项排列（1列/2列/4列）
- ✅ **TypeScript**: 完整类型定义，类型安全
- ✅ **可定制**: 支持自定义样式、回调和组件覆盖
- ✅ **多项目复用**: 设计为通用组件库，可在多个项目中使用

## 组件列表

### 核心组件

#### QuestionItem
单个题目展示组件，包含题目内容、选项、标签和底部操作区域。

#### QuestionList  
题目列表组件，支持虚拟滚动和自定义状态展示。

#### QuestionOptions
智能选项布局组件，自动计算最优排列方式（1列/2列/4列）。

#### RichTextWithMath
数学公式渲染组件，支持 LaTeX 公式和富文本内容。

### 辅助组件

#### QuestionStats
题目统计组件，展示答题数据和学生分布。

## 安装和使用

### 基础导入

```tsx
import { 
  QuestionItem, 
  QuestionList, 
  QaContentType,
  RichTextWithMath 
} from "@repo/core/views/tch-question-view";
```

### 基础使用示例

```tsx
import { QuestionItem, QaContentType } from "@repo/core/views/tch-question-view";

function MyComponent() {
  const questionData: QaContentType = {
    questionId: "q1",
    content: "<p>这是题目内容，包含数学公式 $x^2 + y^2 = z^2$</p>",
    questionTags: ["数学", "几何"],
    questionType: 1,
    answer: "A",
    questionAnswer: {
      answerOptionList: [
        { optionKey: "A", optionVal: "正确答案" }
      ]
    },
    answerExplain: "这是解析内容",
    avgCostTime: 120,
    resourceId: "res1",
    resourceType: 1,
    answerDetails: [],
    options: [
      { key: "A", content: "选项A" },
      { key: "B", content: "选项B" },
      { key: "C", content: "选项C" },
      { key: "D", content: "选项D" }
    ]
  };

  return (
    <QuestionItem
      qaContent={questionData}
      index={0}
      onClickView={(id) => console.log('查看题目:', id)}
      ossHost="https://example.com"
    />
  );
}
```

## 高级用法

### 自定义底部组件

```tsx
const CustomFooter = ({ context }) => {
  const { correctRatePercent, isCommonWrong } = context;
  
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">
        正确率: {correctRatePercent}%
      </span>
      {isCommonWrong && (
        <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
          共性错题
        </span>
      )}
    </div>
  );
};

<QuestionItem
  qaContent={questionData}
  index={0}
  customFooter={CustomFooter}
  onAnalysisToggle={(isShow) => {
    // 自定义埋点逻辑
    console.log('解析展开状态:', isShow);
  }}
/>
```

### 题目列表使用

```tsx
<QuestionList
  questions={questionList}
  loading={false}
  onClickView={(id) => handleViewQuestion(id)}
  customFooter={CustomFooter}
  emptyComponent={<div>暂无题目</div>}
  loadingComponent={<div>加载中...</div>}
  ossHost="https://example.com"
/>
```

### 数学公式渲染

```tsx
<RichTextWithMath 
  htmlContent="<p>勾股定理: $a^2 + b^2 = c^2$</p>"
  ossHost="https://example.com"
/>
```

## API 文档

### QuestionItem Props

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| qaContent | `QaContentType` | - | ✅ | 题目数据 |
| index | `number` | - | ✅ | 题目序号 |
| onClickView | `(id: string) => void` | - | ❌ | 查看题目回调 |
| customFooter | `React.ComponentType` | - | ❌ | 自定义底部组件 |
| footerButton | `React.ReactNode` | - | ❌ | 自定义底部按钮 |
| hasFooterButton | `boolean` | `true` | ❌ | 是否显示底部按钮 |
| className | `string` | `''` | ❌ | 自定义样式类 |
| onAnalysisToggle | `(isShow: boolean) => void` | - | ❌ | 解析展开回调 |
| showAnalysisButton | `boolean` | `true` | ❌ | 是否显示解析按钮 |
| ossHost | `string` | - | ❌ | OSS 主机地址 |

### QaContentType 数据结构

```typescript
interface QaContentType {
  questionTags: string[];           // 题目标签
  questionId: string;               // 题目ID
  answer: string;                   // 正确答案
  questionAnswer: QuestionAnswer;   // 答案详情
  answerExplain: string;           // 答案解析
  avgCostTime: number;             // 平均用时（秒）
  content: string;                 // 题目内容（HTML）
  questionType: number;            // 题目类型（1:单选 2:多选 3:填空 4:判断 5:解答）
  resourceId: string;              // 资源ID
  resourceType: number;            // 资源类型
  answerDetails: AnswerDetail[];   // 学生答题详情
  questionDifficult?: number;      // 题目难度
  options?: { key: string; content: string }[]; // 选项数据
}
```

## 样式定制

组件使用项目的自定义颜色变量，确保视觉一致性：

```css
/* 主要颜色变量 */
--color-fill-gray-2: #F4F7FE;  /* 背景色 */
--color-gray-2: rgba(51, 48, 45, 0.85);  /* 文字色 */
--color-line-2: #DFE3F0;  /* 边框色 */
--color-line-3: #CFD5E8;  /* 分隔线色 */
```

## 性能优化

### 选项布局计算
- 使用 RAF（requestAnimationFrame）优化布局计算
- 缓存数学公式渲染结果
- 防抖处理窗口 resize 事件

### 虚拟滚动支持
对于大量题目，建议在父组件中实现虚拟滚动：

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

// 参考 InfiniteQuestionList 组件的实现
```

## 注意事项

1. **依赖项**: 组件依赖 `katex` 和 `better-react-mathjax` 用于数学公式渲染
2. **样式变量**: 确保项目中定义了必要的 CSS 变量
3. **图片处理**: 使用 `ossHost` 参数处理图片链接
4. **事件处理**: 所有交互事件通过回调函数处理，保持组件纯净

## 迁移指南

从旧版本迁移请参考：
- [迁移指南](./MIGRATION.md)
- [样式修复报告](./STYLE_FIXES.md)
- [迁移完成报告](./MIGRATION_COMPLETE.md)

## 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持题目展示、选项布局、数学公式渲染
- ✅ 完整的 TypeScript 类型定义
- ✅ 样式系统与原版本保持一致
