import { request } from '@umijs/max';
import { RegionItem } from '../partner-school/types';

/**
 * 图形校验短信验证码发送
 */
export const sendSmsWithCaptcha = (data: {
  captchaVerifyParam: string;
  phone_number: string;
  sceneId: string;
  platform: number;
}) => {
  return request<API.ResponseBody<API.UserInfo>>('/api/v1/sms/sendWithCaptcha', {
    method: 'POST',
    data: {
      ...data,
      business_type: 'login',
    },
    // showToast: false,
  });
};

/** 登录接口 POST /api/v1/access/login */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>('/api/v1/access/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 确认登录接口（顶号） POST /api/v1/access/direct_login */
export async function confirmLogin(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>('/api/v1/access/direct_login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getUserInfo(options?: { [key: string]: any }) {
  return request<API.ResponseBody<API.UserInfo>>('/api/v1/user/detail_by_token?platformId=1', {
    method: 'GET',
    ...(options || {}),
  });
}
export async function getProvinceList(options?: { [key: string]: any }) {
  return request<API.ResponseBody<RegionItem[]>>('/api/v1/dict/listAllProvinces', {
    method: 'GET',
  });
}

// 退出登录
export async function logout() {
  return request<API.ResponseBody<API.UserInfo>>('/api/v1/user/logout', {
    method: 'POST',
  });
}
