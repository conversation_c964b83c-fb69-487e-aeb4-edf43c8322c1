import KnowledgePointModal from '@/pages/common/components/KnowledgePointModal';
import { fetchBaseTreeDetail } from '@/services/api';
import { getBaseTreeNodeMap } from '@/utils/tree';
import { css } from '@emotion/css';
import { message, Select, Tooltip } from 'antd';
import to from 'await-to-js';
import React, { useEffect, useMemo, useState } from 'react';

export interface KnowledgePointSelectorProps {
  value?: number[];
  onChange?: (value: { baseTreeNodeIds: number[]; BaseTreeNames: string[] }) => void;
  canEdit?: boolean;
  baseTreeId?: number;
  onlyLeaf?: boolean;
}

const KnowledgePointSelector: React.FC<KnowledgePointSelectorProps> = ({
  value,
  onChange,
  canEdit = true,
  baseTreeId,
  onlyLeaf = false,
}) => {
  const [knowledgePointModalOpen, setKnowledgePointModalOpen] = useState(false);
  const [treeData, setTreeData] = useState<API.BaseTreeNode[]>([]);
  const [treeDataMap, setTreeDataMap] = useState<Map<number, API.BaseTreeNode> | null>(null);
  const [loading, setLoading] = useState(false);

  const options = useMemo(() => {
    if (!treeDataMap || !value || value.length === 0) return [];

    return value.map((id: number) => ({
      label: treeDataMap?.get(id)?.baseTreeNodeName || '',
      value: id,
    }));
  }, [value, treeDataMap]);

  // 获取详情数据
  const fetchDetail = async () => {
    if (!baseTreeId) return;

    setLoading(true);
    try {
      const [err, res] = await to(fetchBaseTreeDetail(baseTreeId));
      if (err || res.code) {
        message.error(err?.message || res?.message);
        return;
      }

      setTreeData([res.data.baseTreeDetail]);
      const treeDataMap = getBaseTreeNodeMap(res.data.baseTreeDetail);
      setTreeDataMap(treeDataMap);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetail();
  }, [baseTreeId]);

  const handleModalOk = (checkedNodes: API.BaseTreeNode[]) => {
    setKnowledgePointModalOpen(false);
    onChange?.({
      baseTreeNodeIds: checkedNodes.map((node) => node.baseTreeNodeId),
      BaseTreeNames: checkedNodes.map((node) => node.baseTreeNodeName),
    });
  };
  const renderContent = () => (
    <div className={styles.container}>
      <Select
        loading={loading}
        onClick={() => {
          setKnowledgePointModalOpen(true);
        }}
        placeholder="请选择知识点"
        mode="multiple"
        maxTagCount="responsive"
        open={false}
        options={options}
        maxTagPlaceholder={(omittedValues) => {
          const omittedLabels = omittedValues.map(({ label }) => label).join('、');
          return (
            <Tooltip title={omittedLabels}>
              <span>+{omittedValues.length}...</span>
            </Tooltip>
          );
        }}
        value={value}
        onDeselect={(val) => {
          if (!treeDataMap || !value) return;
          // 从当前选中值中移除该节点ID
          const newBaseTreeNodeIds = value.filter((id) => id !== val);
          // 获取对应的节点名称列表，确保只包含非空字符串
          const newBaseTreeNames = newBaseTreeNodeIds
            .map((id) => treeDataMap.get(id)?.baseTreeNodeName)
            .filter((name): name is string => typeof name === 'string');

          onChange?.({
            baseTreeNodeIds: newBaseTreeNodeIds,
            BaseTreeNames: newBaseTreeNames,
          });
        }}
      />

      {canEdit && Boolean(treeData.length) && (
        <KnowledgePointModal
          treeData={treeData}
          open={knowledgePointModalOpen}
          onOk={handleModalOk}
          onCancel={() => setKnowledgePointModalOpen(false)}
          defaultCheckedKeys={value || []}
          defaultCheckedNames={options.map((item) => item.label) || []}
          onlyLeaf={onlyLeaf}
        />
      )}
    </div>
  );

  return renderContent();
};

export default KnowledgePointSelector;

const styles = {
  container: css`
    .ant-space {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  `,
};
