import { useApp } from "@/hooks";
import { UserSubjectItem } from "@/types/assign";
import { useCallback } from "react";

export interface CurrentSubjectLocalStorage {
  getSubjectLocalStorageKey: () => string;
  getCurrentSubjectFromLocalStorage: () => Pick<UserSubjectItem, "subjectKey"> | null;
  setCurrentSubjectToLocalStorage: (subject: Pick<UserSubjectItem, "subjectKey">) => void;
}

export function useCurrentSubjectLocalStorage(): CurrentSubjectLocalStorage {
  const { userInfo } = useApp();

  const getSubjectLocalStorageKey = useCallback(() => {
    return `current-subject-${userInfo?.userID}`;
  }, [userInfo?.userID]);

  const setCurrentSubjectToLocalStorage = useCallback((subject: Pick<UserSubjectItem, "subjectKey">) => {
    const key = getSubjectLocalStorageKey();
    localStorage.setItem(key, JSON.stringify(subject));
  }, [getSubjectLocalStorageKey]);

  const getCurrentSubjectFromLocalStorage = useCallback(() => {
    const key = getSubjectLocalStorageKey();
    const subject = localStorage.getItem(key);
    return subject ? JSON.parse(subject) : null;
  }, [getSubjectLocalStorageKey]);

  return {
    getSubjectLocalStorageKey,
    getCurrentSubjectFromLocalStorage,
    setCurrentSubjectToLocalStorage,
  };
}
