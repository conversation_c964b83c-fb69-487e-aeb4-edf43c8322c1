import { cn } from "@/utils/utils";
// TODO: 惊现Ant Design
import { Tree, TreeProps } from "antd";
import "antd/es/tree/style";
// import { treeMockData } from "./mock-tree";
// import { CaretDownOutlined } from "@ant-design/icons";
interface AssignTreeProps extends TreeProps {
  className?: string;
}

const antTreeNodeCls = `
[&_.ant-tree-treenode]:!flex 
[&_.ant-tree-treenode]:!items-start 
[&_.ant-tree-treenode]:!px-0 
[&_.ant-tree-treenode]:!mb-1 
[&_.ant-tree-treenode]:!leading-5.25 
`;
const antTreeSwitcherCls = `
[&_.ant-tree-switcher]:!w-6 
[&_.ant-tree-switcher]:!h-9.5 
[&_.ant-tree-switcher]:!mr-0 
[&_.ant-tree-switcher]:!flex 
[&_.ant-tree-switcher]:!items-center 
[&_.ant-tree-switcher]:!justify-center 
[&_.ant-tree-switcher:hover:before]:!w-3 
[&_.ant-tree-switcher:hover:before]:!h-9.5 
[&_.ant-tree-switcher:hover:before]:!bg-transparent
`;
const antTreeNodeContentWrapperCls = `
[&_.ant-tree-node-content-wrapper]:!m-h-5.25 
[&_.ant-tree-node-content-wrapper]:!text-sm 
[&_.ant-tree-node-content-wrapper]:!text-gray-1
[&_.ant-tree-node-content-wrapper]:!py-2
[&_.ant-tree-node-content-wrapper:hover]:!bg-transparent
[&_.ant-tree-node-content-wrapper:hover]:!rounded-md
[&_.ant-tree-node-content-wrapper.ant-tree-node-selected]:!bg-primary-6
[&_.ant-tree-node-content-wrapper.ant-tree-node-selected:hover]:!bg-primary-6
[&_.ant-tree-node-content-wrapper.ant-tree-node-selected]:!text-primary-1
[&_.ant-tree-node-content-wrapper.ant-tree-node-selected]:!font-medium
`;

const antTreeSwitcherLeafLineCls = `
[&_.ant-tree-switcher-leaf-line:after]:!h-5 
`;
const antTreeTreeNodeLeafLastCls = `
[&_.ant-tree-treenode-leaf-last_.ant-tree-switcher-leaf-line:before]:!h-5 
`;

export function AssignTree({ className = "", ...props }: AssignTreeProps) {
  return (
    <Tree
      className={cn(
        "rain-tree w-full",
        antTreeNodeCls,
        antTreeSwitcherCls,
        antTreeNodeContentWrapperCls,
        antTreeSwitcherLeafLineCls,
        antTreeTreeNodeLeafLastCls,
        className
      )}
      fieldNames={{
        title: "name",
        key: "id",
        children: "children",
      }}
      {...props}
    />
  );
}
