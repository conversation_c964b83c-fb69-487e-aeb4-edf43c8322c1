import ClearIcon from "@/public/assign/clear.svg";
import { Button } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
interface TchClearBtnProps {
  style?: React.CSSProperties;
  className?: string;
  content?: string;
  onClick?: () => void;
}

export function TchClearBtn({
  style = {},
  className = "",
  content = "清空",
  onClick,
}: TchClearBtnProps) {
  return (
    <Button
      type="text"
      className={cn("px-1", className)}
      style={style}
      onClick={onClick}
    >
      <ClearIcon width={14} height={14} className="mr-0.5 size-3.5" />
      <span className="text-sm font-normal leading-tight">{content}</span>
    </Button>
  );
}
