"use client";

import {  useState, useEffect, memo } from '@preact-signals/safe-react/react';
import { TabValue } from './layout/TabNav';
import { ReactNode } from 'react';

interface TabCacheProps {
  activeTab: TabValue;
  children: {
    [key in TabValue]: ReactNode;
  };
}

/**
 * TabCache组件用于维护所有标签页组件的状态
 * 只显示当前激活的标签页,同时将其他标签页保持在DOM中但隐藏
 */
export default memo(function TabCache({ activeTab, children }: TabCacheProps) {
  // 跟踪哪些标签页至少渲染过一次
  const [renderedTabs, setRenderedTabs] = useState<Set<TabValue>>(new Set([activeTab]));

  // 将当前激活的标签页添加到已渲染标签页集合中
  useEffect(() => {
    // 将新标签页添加到已渲染集合
    if (!renderedTabs.has(activeTab)) {
      console.log(`Adding tab ${activeTab} to rendered tabs`);
      setRenderedTabs(prev => new Set([...prev, activeTab]));
    }
  }, [activeTab, renderedTabs]);

  return (
    <div className="h-full relative">
      {Object.entries(children).map(([tab, component]) => {
        const tabKey = tab as TabValue;
        // 只渲染至少查看过一次的标签页
        if (!renderedTabs.has(tabKey)) {
          return null;
        }

        const isActive = activeTab === tabKey;

        return (
          <div
            key={tab}
            className={`absolute inset-0 w-full h-full transition-opacity duration-200 ${
              isActive ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
            }`}
            aria-hidden={!isActive}
            data-tab-content={tab}
          >
            {component}
          </div>
        );
      })}
    </div>
  );
});
