"use client";
import { Badge } from "@/ui/badge";
// import { But<PERSON> } from "@/ui/button";
import IcNoData from "@/public/icons/ic_noanswerdata.svg";
import { StudentBase } from "@/types";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import Avatar from "@/ui/tch-avatar";
import { cn } from "@/utils/utils";
import { ReactNode, useRef } from "react";

export interface StudentSectionProps {
  classNames?: {
    title?: string;
    titlePrefix?: string;
  };
  title: string | ReactNode;
  students?: StudentBase[];
  button: ReactNode;
  badgeClassName?: string;
  showButton?: boolean; // 是否显示按钮
  onStudentClick?: (studentId: number, student: StudentBase) => void;
  type: "praise" | "attention";
  setType?: (value: "praise" | "attention") => void;
  loading: boolean;
}

export function StudentSection({
  title,
  students,
  badgeClassName,
  showButton = true, // 默认显示按钮
  onStudentClick,
  classNames,
  button,
  type,
  setType,
  loading,
}: StudentSectionProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 使用改进的useScrollDetection钩子，仅监听bottom方向
  // const { sentinels, hasOverflow, scrollState } =
  //   useScrollDetection(scrollContainerRef);

  return (
    <div className="border-px border-line-1 flex-1 rounded-[0.75rem] border bg-white p-3 px-5">
      <div className="h-full">
        <div className="mb-3 flex h-[1.75rem] items-center justify-between">
          <h3 className="flex items-center gap-1 text-base font-semibold leading-[150%] text-[#0F1114]">
            <div
              className={cn(
                "bg-green-1 inline-block h-[0.875rem] w-[0.1875rem] rounded-[0.125rem]",
                classNames?.titlePrefix
              )}
            ></div>
            {title}
          </h3>
          {showButton && button}
        </div>
        {loading ? (
          <div className="relative h-24">
            <div className="flex flex-wrap gap-2">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <div
                    className="flex w-full items-center justify-between gap-2"
                    key={i}
                  >
                    <Skeleton
                      key={i + "praise_left"}
                      className="h-6 w-1/2 rounded-full"
                    />
                    <Skeleton
                      key={i + "praise_right"}
                      className="h-6 w-1/2 rounded-full"
                    />
                  </div>
                ))}
            </div>
          </div>
        ) : (
          <ScrollArea className="relative h-24">
            <div
              ref={scrollContainerRef}
              className="grid grid-cols-2 gap-x-1 gap-y-1"
            >
              {students && students.length > 0 ? (
                students.map((student) =>
                  student ? (
                    <Badge
                      onClick={() => {
                        onStudentClick?.(student.studentId || 0, student);
                        setType?.(type);
                      }}
                      key={student.studentId || 0}
                      variant="outline"
                      className={`${badgeClassName} hover:bg-fill-gray-2 w-full cursor-pointer justify-start whitespace-nowrap border-none p-1`}
                    >
                      <Avatar
                        src={student.avatar || ""}
                        alt={student.studentName || ""}
                        className="mr-1 h-6 w-6 rounded-full"
                      />
                      <span className="overflow-hidden text-ellipsis text-xs font-normal leading-[150%] text-[#101019]">
                        {student.studentName || ""}
                      </span>
                    </Badge>
                  ) : null
                )
              ) : (
                <div className="col-span-2 flex h-16 flex-col items-center justify-center text-sm text-gray-400 opacity-80">
                  <IcNoData className="mb-1 h-7 w-7 opacity-60" />
                  暂无数据
                </div>
              )}
              {/* 底部哨兵元素 */}
              {/* {sentinels.bottom} */}
            </div>
            <div
              className={cn(
                "pointer-events-none absolute bottom-0 h-[3rem] w-full rounded-b-[0.5rem] transition-opacity duration-100",
                // hasOverflow.y && !scrollState.bottom ? "opacity-100" : "opacity-0"
                "opacity-100"
              )}
              style={{
                background:
                  "linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.00) 131.94%)",
              }}
            />
          </ScrollArea>
        )}
      </div>
    </div>
  );
}
