"use client";

import { useUmeng } from "@/hooks/useUmeng";
import { UmengCategory } from "@/utils";
import { useSearchParams } from "next/navigation";
import { memo, startTransition, use, useCallback, useEffect } from "react";
import { TabValue } from "./_components/layout/TabNav";
import TabCache from "./_components/TabCache";
import Ask from "./_components/tabs/Ask";
import Report from "./_components/tabs/Report";
import Results from "./_components/tabs/Results";
// import GiLLoading from "@/ui/spin";
// import dynamic from "next/dynamic";
import { useTaskContext } from "./_context/task-context";

// 配置动态加载并添加Suspense
const TabComponents = {
  // report: dynamic(() => import("./_components/tabs/Report"), {
  //   loading: () => (
  //     <GiLLoading
  //       loading={true}
  //       mask={true}
  //       className="flex h-[calc(100vh-200px)] items-center justify-center"
  //     />
  //   ),
  //   ssr: false, // 禁用SSR以减少服务器负载，加快客户端渲染
  // }),
  // results: dynamic(() => import("./_components/tabs/Results"), {
  //   loading: () => (
  //     <GiLLoading
  //       loading={true}
  //       mask={true}
  //       className="flex h-[calc(100vh-200px)] items-center justify-center"
  //     />
  //   ),
  //   ssr: false,
  // }),
  // ask: dynamic(() => import("./_components/tabs/Ask"), {
  //   loading: () => (
  //     <GiLLoading
  //       loading={true}
  //       mask={true}
  //       className="flex h-[calc(100vh-200px)] items-center justify-center"
  //     />
  //   ),
  //   ssr: false,
  // }),
  report: Report,
  results: Results,
  ask: Ask,
};

// 使用memo优化组件性能
const MemoizedReport = memo(TabComponents.report);
const MemoizedResults = memo(TabComponents.results);
const MemoizedAsk = memo(TabComponents.ask);

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  console.log("id", id);
  // 在组件挂载时检查 localStorage 中的任务数据
  useEffect(() => {
    try {
      // 尝试从 localStorage 获取任务数据
      const storedTask = localStorage.getItem("homework_currentTask");
      if (storedTask) {
        console.log("Found stored task data");
      }
    } catch (error) {
      console.error("Failed to get task from localStorage:", error);
    }
  }, []);

  const {
    activeTab,
    setActiveTab,
    viewMode,
    setViewMode,
    studentData,
    setStudentId,
    setStudentName,
    currentCourse,
    setCurrentCourse,
    homeworkData,
    updateCurrentCourseFromHomeworkData,
  } = useTaskContext();

  useUmeng(
    UmengCategory.HOMEWORK,
    viewMode.value === "student"
      ? "homework_list_report_student"
      : "homework_list_report_student_detail"
  );

  const searchParams = useSearchParams();

  // URL同步到状态的函数，使用 startTransition 优化
  const syncStateFromUrl = useCallback(() => {
    const tabParam = searchParams?.get("tab") as TabValue | null;
    const viewParam = searchParams?.get("view");
    const courseIdParam = searchParams?.get("courseId");
    const studentIdParam = searchParams?.get("studentId");
    const studentNameParam = searchParams?.get("studentName"); // 可选：URL可能也带学生名称

    // 使用 startTransition 包装非紧急的状态更新
    startTransition(() => {
      if (courseIdParam) {
        setCurrentCourse(courseIdParam);
      }

      // 优先处理视图模式
      if (viewParam === "student" && studentIdParam) {
        if (
          viewMode.peek() !== "student" ||
          studentData.peek().studentId !== studentIdParam
        ) {
          setViewMode("student");
          setStudentId(studentIdParam);

          // 如果URL中有学生名称，使用它；否则保留当前名称或用默认名称
          if (studentNameParam) {
            setStudentName(studentNameParam);
          } else if (studentData.peek().studentId !== studentIdParam) {
            // 只有当学生ID变了，但URL没有指定名称时，重置为默认名称
            setStudentName("学生");
          }
        }

        // 学生视图下，如果没有tab或tab是report，默认显示results
        const effectiveTab =
          !tabParam || tabParam === "report" ? "results" : tabParam;
        if (activeTab.peek() !== effectiveTab) {
          setActiveTab(effectiveTab);
        }
      } else if (viewParam === "class" || !viewParam) {
        // 如果是班级视图或没指定视图（默认为班级）
        if (viewMode.peek() !== "class") {
          setViewMode("class");
          // 班级视图不需要学生ID和名称
          setStudentId(null);
          setStudentName(null);
        }

        // 班级视图下的默认tab是report
        const effectiveTab = tabParam || "report";
        if (activeTab.peek() !== effectiveTab) {
          setActiveTab(activeTab.peek() || effectiveTab);
        }
      }
    });
  }, [
    searchParams,
    setActiveTab,
    setViewMode,
    setStudentId,
    setStudentName,
    activeTab,
    viewMode,
    studentData,
    currentCourse,
    setCurrentCourse,
  ]);
  useEffect(() => {
    syncStateFromUrl();
  }, [searchParams, syncStateFromUrl]);

  // 监听homeworkData变化，更新课程信息
  useEffect(() => {
    if (homeworkData.value?.detail?.resourceReports) {
      updateCurrentCourseFromHomeworkData();
    }
  }, [homeworkData.value, updateCurrentCourseFromHomeworkData]);

  // 使用TabCache组件缓存所有标签页组件，并使用提前memo化的组件
  return (
    <TabCache activeTab={activeTab.value}>
      {{
        report: <MemoizedReport />,
        results: <MemoizedResults />,
        ask: <MemoizedAsk />,
      }}
    </TabCache>
  );
}
