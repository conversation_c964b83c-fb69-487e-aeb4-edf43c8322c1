import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import weekOfYear from 'dayjs/plugin/weekOfYear';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(weekOfYear);

/**
 * 计算给定日期所在周的开始和结束日期
 * @param {string} dateStr - 日期字符串，格式为 YYYY-MM-DD
 * @returns {{ startDate: string, endDate: string }} - 包含开始和结束日期的对象
 */
export function getWeekRange(dateStr: string): { startDate: string; endDate: string } {
  const inputDate = dayjs(dateStr);

  // 获取周一（本周开始）
  const startOfWeek =
    inputDate.day() === 0
      ? inputDate.subtract(6, 'day') // 如果是周日，向前推6天到周一
      : inputDate.startOf('week'); // 默认dayjs以周日为一周开始，需要调整

  // 获取周日（本周结束）
  const endOfWeek = startOfWeek.add(6, 'day');

  // 格式化为YYYY-MM-DD
  const formatDate = (date: dayjs.Dayjs): string => date.format('YYYY-MM-DD');

  return {
    startDate: formatDate(startOfWeek),
    endDate: formatDate(endOfWeek),
  };
}
