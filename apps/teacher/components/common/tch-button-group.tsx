import { But<PERSON> } from "@/ui/tch-button";
import { cn } from "@/utils/utils";

export interface ButtonItem {
  id: string;
  label: string;
}

interface TchButtonGroupProps {
  style?: React.CSSProperties;
  className?: string;
  btns: ButtonItem[];
  activeBtn: string;
  onActiveChange?: (id: string) => void;
}

export function TchButtonGroup({
  style = {},
  className = "",
  btns,
  activeBtn,
  onActiveChange,
}: TchButtonGroupProps) {
  return (
    <div
      className={cn("flex items-center gap-3", className)}
      style={style}
    >
      {btns.map((btn) => (
        <Button
          key={btn.id}
          type="default"
          onClick={() => onActiveChange?.(btn.id)}
          className={cn(
            activeBtn === btn.id && "bg-indigo-50 border-indigo-400"
          )}
        >
          {btn.label}
        </Button>
      ))}
    </div>
  );
}
