'use client';

import SubjectPhaseCascader from '@/components/SubjectPhaseCascader';
import {
  fetchUploadPolicy2,
  noticUploadResult,
  uploadFileToOss,
  uploadPaperUrl,
} from '@/services/api';
import {
  AuditOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  FilePptOutlined,
  FileSearchOutlined,
  FileWordOutlined,
  LoadingOutlined,
  ScanOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Flex,
  Input,
  message,
  Modal,
  Select,
  Space,
  Steps,
  Typography,
  Upload,
} from 'antd';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type React from 'react';
import { useMemo, useState } from 'react';

const { Text } = Typography;

const uploadTypeOptions = [
  { label: 'Word-2J', value: 'word-2J' },
  // { label: 'PDF-M', value: 'pdf-m' },
  { label: 'URL', value: 'url' },
];

export interface FileUploadWorkflowProps {
  onUploadSucess: (type?: string) => void;
}

const FileUploadWorkflow: React.FC<FileUploadWorkflowProps> = ({ onUploadSucess }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [currentStep] = useState<number>(0);
  const [uploading, setUploading] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 科目选择
  const [selectedSubject, setSelectedSubject] = useState<number[]>([]);
  // 上传类型
  const [uploadType, setUploadType] = useState<string>('word-2J');
  // url地址
  const [uploadUrl, setUploadUrl] = useState<string>('');

  const isWordUpload = useMemo(() => {
    return uploadType === 'word-2J';
  }, [uploadType]);

  const accept = useMemo(() => {
    if (isWordUpload) {
      return '.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }
    return '.pdf';
  }, [uploadType]);

  const uploadProps: UploadProps = useMemo(
    () => ({
      onRemove: (file) => {
        const index = fileList.indexOf(file);
        const newFileList = fileList.slice();
        newFileList.splice(index, 1);
        setFileList(newFileList);
      },
      beforeUpload: (file) => {
        // 检查文件类型
        const isWordFile =
          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
          file.type === 'application/msword';
        const isPdfFile = file.type === 'application/pdf';
        if (uploadType === 'word-2J' && !isWordFile) {
          messageApi.error('请上传Word文件!');
          return Upload.LIST_IGNORE;
        }
        if (uploadType === 'pdf-m' && !isPdfFile) {
          messageApi.error('请上传PDF文件!');
          return Upload.LIST_IGNORE;
        }

        setFileList([file]);
        return false;
      },
      fileList,
      accept,
      maxCount: 1,
      itemRender: (originNode, file, fileList, { remove }) => {
        return (
          <Flex justify="flex-start" align="center" gap={4}>
            <Button
              type="link"
              icon={isWordUpload ? <FileWordOutlined /> : <FilePptOutlined />}
              style={{ padding: 0, width: 'auto' }}
            />

            <Text ellipsis style={{ flex: 1 }}>
              {file.name}
            </Text>
            <Button
              type="link"
              onClick={() => remove()}
              icon={<DeleteOutlined />}
              style={{ padding: 0, width: 'auto' }}
            />
          </Flex>
        );
      },
    }),
    [fileList, accept],
  );

  const steps = [
    {
      title: '上传word文件',
      icon: <FileWordOutlined />,
    },
    {
      title: '后台自动识别',
      icon: <ScanOutlined />,
    },
    {
      title: '确认识别结果',
      icon: <FileSearchOutlined />,
    },
    {
      title: '审核',
      icon: <AuditOutlined />,
    },
    {
      title: '数据入库',
      icon: <DatabaseOutlined />,
    },
  ];

  const onUploadTypeChange = (value: string) => {
    setUploadType(value);
    setFileList([]);
  };

  const handleUploadUrl = async () => {
    if (!uploadUrl) {
      messageApi.error('请输入URL地址');
      return;
    }
    console.log('handleUploadUrl', uploadUrl);
    setUploading(true);
    const res = await uploadPaperUrl({
      resourceDownloadUrl: uploadUrl,
      phase: selectedSubject[0],
      subject: selectedSubject[1],
    });
    if (res.code !== 0) {
      messageApi.error('提交URL失败');
      setUploading(false);
      return;
    }
    messageApi.success('提交URL成功');
    onUploadSucess();
    setUploadUrl('');
    handleCancel();
    setUploading(false);
  };

  const handleOk = () => {
    if (selectedSubject.length === 0) {
      messageApi.error('请先选择学科学段');
      return;
    }
    if (uploadType === 'url') {
      handleUploadUrl();
    } else {
      handleUpload();
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const onPhaseSubjectValueChange = (value: number[]) => {
    setSelectedSubject(value);
  };

  async function handleUpload() {
    if (fileList.length === 0) {
      messageApi.error('请先选择文件');
      return;
    }
    setUploading(true);

    try {
      const file = fileList[0];
      const [phase, subject] = selectedSubject;

      // step1: 获取oss上传信息
      const policyRes = await fetchUploadPolicy2({
        fileName: file.name,
        phase,
        subject,
        generateChannel: isWordUpload ? 1 : 3,
      });
      if (policyRes.code !== 0 || !policyRes.data) {
        messageApi.error('获取上传签名失败');
        return;
      }
      const { paperId, formData, fileName } = policyRes.data;
      // step2: 上传文件到oss
      const result = await uploadFileToOss(fileName, formData.policyToken, file);

      if (result.code !== 0) {
        messageApi.error('上传失败');
      }
      const uploadStatus = result.code === 0 ? 1 : 0;
      // step3: 通知后端上传的结果
      const noticeRes = await noticUploadResult({
        paperId,
        result: {
          uploadStatus,
        },
      });
      if (noticeRes.code !== 0) {
        messageApi.error('通知服务端失败');
        return;
      }
      // step4: 如果上传成功，则提示成功
      if (uploadStatus === 1) {
        messageApi.success('上传成功');
        onUploadSucess();
        handleCancel();
        setFileList([]);
      }
    } catch (error) {
      messageApi.error('上传流程异常');
      console.log('上传流程异常： ', error);
    } finally {
      setUploading(false);
    }
  }

  return (
    <div>
      <Flex gap={60} justify="space-between" align="center">
        <div style={{ flex: 1, marginRight: '40px', marginTop: '5px' }}>
          <Steps
            current={currentStep}
            items={steps.map((item) => ({
              title: item.title,
              icon: item.icon,
              status: 'wait',
            }))}
          />
        </div>
        <div>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="middle"
            onClick={() => setIsModalOpen(true)}
          >
            上传
          </Button>
        </div>
      </Flex>

      {contextHolder}

      <Modal
        title="上传文件"
        open={isModalOpen}
        onOk={handleOk}
        okButtonProps={{
          disabled: uploading,
        }}
        onCancel={handleCancel}
        okText="提交"
        cancelText="返回"
        closable={false}
      >
        <Space direction="vertical" size={16} style={{ width: '100%', padding: '30px 20px' }}>
          <Flex align="center" gap={10}>
            <Text style={{ wordBreak: 'keep-all' }}>学科学段</Text>
            <SubjectPhaseCascader
              selectedSubject={selectedSubject}
              onChange={onPhaseSubjectValueChange}
              isPrimary={false}
              changeOnSelect={false}
            />
          </Flex>
          <Flex align="center" gap={10}>
            <Text style={{ wordBreak: 'keep-all' }}>上传类型</Text>
            <Select
              value={uploadType}
              style={{ width: 120 }}
              onChange={onUploadTypeChange}
              options={uploadTypeOptions}
              className="flex-1"
              size="large"
            />
          </Flex>
          {uploadType !== 'url' && (
            <Upload {...uploadProps}>
              <Flex align="center" gap={10}>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  size="middle"
                  disabled={selectedSubject.length === 0 || uploading}
                >
                  选择文件
                </Button>
                {uploading && <LoadingOutlined />}
              </Flex>
            </Upload>
          )}
          {uploadType === 'url' && (
            <Flex align="center" gap={10}>
              <Text style={{ wordBreak: 'keep-all' }}>URL地址</Text>
              <Input
                placeholder="请输入URL"
                type="url"
                size="large"
                value={uploadUrl}
                onChange={(e) => setUploadUrl(e.target.value)}
                disabled={selectedSubject.length === 0}
              />
            </Flex>
          )}
        </Space>
      </Modal>
    </div>
  );
};

export default FileUploadWorkflow;
