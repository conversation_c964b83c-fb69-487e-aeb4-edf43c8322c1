import { Modal } from 'antd';
import React from 'react';

interface FullScreenModalProps {
  open: boolean;
  children: React.ReactNode;
  onClose?: () => void;
}

const FullScreenModal: React.FC<FullScreenModalProps> = ({ open, children, onClose }) => {
  // const { token } = theme.useToken();

  return (
    <Modal
      centered
      open={open}
      mask={false}
      width={'100vw'}
      height={'100vh'}
      closable={false}
      footer={null}
      destroyOnHidden
      onCancel={onClose}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        borderRadius: 0,
        maxWidth: '100vw',
        maxHeight: '100vh',
      }}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 0,
          height: '100vh',
          borderRadius: 0,
          background: 'linear-gradient(#ffffff, #f5f5f5 28%)',
          overflowY: 'scroll',
        },
      }}
    >
      {children}
    </Modal>
  );
};

export default FullScreenModal;
