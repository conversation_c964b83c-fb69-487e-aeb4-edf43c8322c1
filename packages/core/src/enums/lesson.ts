import EnumManager from "../utils/enum-manager";
export * from "./assign"

/**
 * 学段枚举
 */
export enum PHASE {
  PHASE_PRIMARY = 1, // 小学
  PHASE_JUNIOR = 2, // 初中
  PHASE_HIGH = 3, // 高中
}

/**
 * 学段枚举管理器
 */
export const phaseEnumManager = new EnumManager([
  {
    label: "小学",
    value: PHASE.PHASE_PRIMARY,
  },
  {
    label: "初中",
    value: PHASE.PHASE_JUNIOR,
  },
  {
    label: "高中",
    value: PHASE.PHASE_HIGH,
  },
]);

/**
 * 职务枚举
 */
export enum JOB_TYPE {
  JOB_TYPE_PRINCIPAL = 1, // 校长
  JOB_TYPE_GRADE_HEAD = 2, // 年级主任
  JOB_TYPE_SUBJECT_HEAD = 3, // 学科组长
  JOB_TYPE_SUBJECT_TEACHER = 4, // 学科教师
  JOB_TYPE_CLASS_TEACHER = 5, // 班主任
}

/**
 * 职务枚举管理器
 */
export const jobTypeEnumManager = new EnumManager([
  {
    label: "校长",
    value: JOB_TYPE.JOB_TYPE_PRINCIPAL,
  },
  {
    label: "年级主任",
    value: JOB_TYPE.JOB_TYPE_GRADE_HEAD,
  },
  {
    label: "学科组长",
    value: JOB_TYPE.JOB_TYPE_SUBJECT_HEAD,
  },
  {
    label: "学科教师",
    value: JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER,
  },
  {
    label: "班主任",
    value: JOB_TYPE.JOB_TYPE_CLASS_TEACHER,
  },
]);

/**
 * 学科枚举
 */
export enum SUBJECT {
  SUBJECT_CHINESE = 1, // 语文
  SUBJECT_MATH = 2, // 数学
  SUBJECT_ENGLISH = 3, // 英语
  SUBJECT_PHYSICS = 4, // 物理
  SUBJECT_CHEMISTRY = 5, // 化学
  SUBJECT_BIOLOGY = 6, // 生物
  SUBJECT_HISTORY = 7, // 历史
  SUBJECT_GEOGRAPHY = 8, // 地理
  SUBJECT_POLITICAL = 9, // 政治
  SUBJECT_MORAL = 10, // 道德与法治
  SUBJECT_JAPANESE = 11, // 日语
  SUBJECT_RUSSIAN = 12, // 俄语
}

export const SUBJECT_OPTIONS = [
  {
    label: "语文",
    value: SUBJECT.SUBJECT_CHINESE,
    as: '语',
    bg: "bg-blue-0"
  },
  {
    label: "数学",
    value: SUBJECT.SUBJECT_MATH,
    as: '数',
    bg: "bg-danger-2"
  },
  {
    label: "英语",
    value: SUBJECT.SUBJECT_ENGLISH,
    as: '英',
    bg: "bg-[#BD60FF]"
  },
  {
    label: "物理",
    value: SUBJECT.SUBJECT_PHYSICS,
    as: '物',
    bg: "bg-[#24C1F8]"
  },
  {
    label: "化学",
    value: SUBJECT.SUBJECT_CHEMISTRY,
    as: '化',
    bg: "bg-green-1"
  },
  {
    label: "生物",
    value: SUBJECT.SUBJECT_BIOLOGY,
    as: '生',
    bg: "bg-orange-1"
  },
  {
    label: "历史",
    value: SUBJECT.SUBJECT_HISTORY,
    as: '史',
    bg: "bg-carmine-1"
  },
  {
    label: "地理",
    value: SUBJECT.SUBJECT_GEOGRAPHY,
    as: '地',
    bg: "bg-[#2CF77D]"
  },
  {
    label: "政治",
    value: SUBJECT.SUBJECT_POLITICAL,
    as: '政',
    bg: "bg-primary-2"
  },
  {
    label: "道德与法治",
    value: SUBJECT.SUBJECT_MORAL,
    as: '道',
    bg: "bg-green-2"
  },
  {
    label: "日语",
    value: SUBJECT.SUBJECT_JAPANESE,
    as: '日',
    bg: "bg-gray-400"
  },
  {
    label: "俄语",
    value: SUBJECT.SUBJECT_RUSSIAN,
    as: '俄',
    bg: "bg-gray-400"
  },

]

/**
 * 学科枚举管理器
 */
export const subjectEnumManager = new EnumManager(SUBJECT_OPTIONS);

/**
 * 任务类型枚举（10-99）
 */
export enum TASK_TYPE {
  // 课程任务（10-19）
  TASK_TYPE_COURSE = 10, // 课程

  // 作业任务（20-29）
  TASK_TYPE_HOMEWORK = 20, // 作业

  // 测验任务（30-39）
  TASK_TYPE_TEST = 30, // 测验

  // 资源任务（40-49）
  TASK_TYPE_RESOURCE = 40, // 资源
}
// 任务类型映射
export const TASK_TYPE_OPTIONS = [

  {
    label: "课程",
    value: TASK_TYPE.TASK_TYPE_COURSE,
    bg: "bg-blue-5",
    lineColor: "bg-[#D7DDF1]"
  },
  {
    label: "作业",
    value: TASK_TYPE.TASK_TYPE_HOMEWORK,
    bg: "bg-purple-5",
    lineColor: "bg-[#DFDFF9]"
  },
  {
    label: "测验",
    value: TASK_TYPE.TASK_TYPE_TEST,
    bg: "bg-[#FAF2E6]",
    lineColor: "bg-[#F9E0BD]"
  },
  {
    label: "资源",
    value: TASK_TYPE.TASK_TYPE_RESOURCE,
    bg: "bg-[#EAF7E6]",
    lineColor: "bg-[#D4E8CE]"
  },
]

/**
 * 任务类型枚举管理器
 */
export const taskTypeEnumManager = new EnumManager(TASK_TYPE_OPTIONS);

/**
 * 素材资源类型枚举（100-199）
 */
export enum RESOURCE_TYPE {
  RESOURCE_TYPE_OTHER = 100, // 其它资源
  RESOURCE_TYPE_AI_COURSE = 101, // AI课，内容平台
  RESOURCE_TYPE_PRACTICE = 102, // 巩固练习，内容平台
  RESOURCE_TYPE_QUESTION = 103, // 试题，内容平台
  RESOURCE_TYPE_PAPER = 104, // 试卷，内容平台
}

/**
 * 素材资源类型枚举管理器
 */
export const resourceTypeEnumManager = new EnumManager([
  {
    label: "其它资源",
    value: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
  },
  {
    label: "AI课",
    value: RESOURCE_TYPE.RESOURCE_TYPE_AI_COURSE,
  },
  {
    label: "巩固练习",
    value: RESOURCE_TYPE.RESOURCE_TYPE_PRACTICE,
  },
  {
    label: "试题",
    value: RESOURCE_TYPE.RESOURCE_TYPE_QUESTION,
  },
  {
    label: "试卷",
    value: RESOURCE_TYPE.RESOURCE_TYPE_PAPER,
  },
]);

/**
 * 题目难度枚举
 */
export enum QUESTION_DIFFICULT {
  QUESTION_DIFFICULT_SIMPLE = 1, // 简单
  QUESTION_DIFFICULT_EASY = 2, // 较易
  QUESTION_DIFFICULT_MEDIUM = 3, // 中等
  QUESTION_DIFFICULT_CHALLENGING = 4, // 较难
  QUESTION_DIFFICULT_HARD = 5, // 困难
}

/**
 * 题目难度枚举管理器
 */
export const questionDifficultEnumManager = new EnumManager([
  {
    label: "简单",
    value: QUESTION_DIFFICULT.QUESTION_DIFFICULT_SIMPLE,
  },
  {
    label: "较易",
    value: QUESTION_DIFFICULT.QUESTION_DIFFICULT_EASY,
  },
  {
    label: "中等",
    value: QUESTION_DIFFICULT.QUESTION_DIFFICULT_MEDIUM,
  },
  {
    label: "较难",
    value: QUESTION_DIFFICULT.QUESTION_DIFFICULT_CHALLENGING,
  },
  {
    label: "困难",
    value: QUESTION_DIFFICULT.QUESTION_DIFFICULT_HARD,
  },
]);

/**
 * 分组类型枚举
 */
export enum GROUP_TYPE {
  GROUP_TYPE_ALL = 0, // 全部
  GROUP_TYPE_TEMP = 1, // 临时小组
  GROUP_TYPE_CLASS = 2, // 班级
  GROUP_TYPE_STUDENT = 3, // 学生小组
}

/**
 * 分组类型枚举管理器
 */
export const groupTypeEnumManager = new EnumManager([
  {
    label: "全部",
    value: GROUP_TYPE.GROUP_TYPE_ALL,
  },
  {
    label: "临时小组",
    value: GROUP_TYPE.GROUP_TYPE_TEMP,
  },
  {
    label: "班级",
    value: GROUP_TYPE.GROUP_TYPE_CLASS,
  },
  {
    label: "学生小组",
    value: GROUP_TYPE.GROUP_TYPE_STUDENT,
  },
]);

/**
 * 题目类型枚举
 */
export enum QUESTION_TYPE {
  QUESTION_TYPE_ALL = 0, // 全部题型
  QUESTION_TYPE_SINGLE_CHOICE = 1, // 单选题
  QUESTION_TYPE_MULTIPLE_CHOICE = 2, // 多选题
  QUESTION_TYPE_FILL_BLANK = 3, // 填空题
  QUESTION_TYPE_TRUE_FALSE = 4, // 判断题
  QUESTION_TYPE_SOLUTION = 5, // 解答题
}

/**
 * 题目类型枚举管理器
 */
export const questionTypeEnumManager = new EnumManager([
  {
    label: "全部题型",
    value: QUESTION_TYPE.QUESTION_TYPE_ALL,
  },
  {
    label: "单选题",
    value: QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
  },
  {
    label: "多选题",
    value: QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE,
  },
  {
    label: "填空题",
    value: QUESTION_TYPE.QUESTION_TYPE_FILL_BLANK,
  },
  {
    label: "判断题",
    value: QUESTION_TYPE.QUESTION_TYPE_TRUE_FALSE,
  },
  {
    label: "解答题",
    value: QUESTION_TYPE.QUESTION_TYPE_SOLUTION,
  },
]);
