import {
  AppInfo,
  DeviceInfo,
  NetworkHeaderParams,
  StudentUserInfo,
} from "@/types/client/jsbridge";
import { createContext, FC, useContext } from "react";
import useScreen from "../hooks/use-screen";
import {
  getAppInfo,
  getDeviceInfo,
  getNetworkHeaderParams,
  getStudentUserInfo,
} from "../utils/device";

type ClientContextType = {
  studentUserInfo: StudentUserInfo | null;
  deviceInfo: DeviceInfo | null;
  appInfo: AppInfo | null;
  networkHeaders: NetworkHeaderParams | null;
  screen: { width: number; height: number };
};

const ClientContext = createContext<ClientContextType>({} as ClientContextType);

/**
 * 获取客户端信息
 */
const useClientContext = () => useContext(ClientContext);

/**
 * 客户端信息Provider
 */
const ClientProvider: FC<{ children: React.ReactNode }> = ({ children }) => {
  const studentUserInfo = getStudentUserInfo();
  const screen = useScreen();
  const deviceInfo = getDeviceInfo();
  const appInfo = getAppInfo();
  const networkHeaders = getNetworkHeaderParams();

  const value = {
    studentUserInfo,
    screen,
    deviceInfo,
    appInfo,
    networkHeaders,
  };
  return <ClientContext value={value}>{children}</ClientContext>;
};

export { ClientProvider, useClientContext, type ClientContextType };
