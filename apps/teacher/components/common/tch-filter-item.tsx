import { cn } from "@/utils/utils";
import { useMemo } from "react";
import { TchOptionTags } from "./tch-option-tags";
import { AssignSelect } from "../assign/assign-select";
interface TchFilterItemProps {
  style?: React.CSSProperties;
  className?: string;
  contentClassName?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any[];
  fieldNames?: {
    label: string;
    value: string;
  };
  name?: string;
  value?: string;
  multiple?: boolean;
  type?: "tags" | "select";
  ariaHidden?: boolean;
  onChange: (value: string | string[]) => void;
}

export function TchFilterItem({
  style = {},
  className = "",
  contentClassName = "",
  options,
  fieldNames = { label: "label", value: "value" },
  name,
  value,
  onChange,
  type = "tags",
}: TchFilterItemProps) {
  const innerOptions = useMemo(() => {
    return options.map((option) => ({
      label: option[fieldNames.label],
      value: String(option[fieldNames.value]),
    }));
  }, [options, fieldNames]);
  return (
    <div
      className={cn("flex select-none items-start justify-start gap-3", className)}
      style={style}
    >
      <div className="flex items-center w-10.5 h-7.5 text-gray-3 py-1 text-sm font-normal leading-5.5 flex-shrink-0">{name}</div>
      {type === "tags" ? (
        <TchOptionTags
          options={innerOptions}
          value={value}
          onChange={onChange}
          className={contentClassName}
        />
      ) : (
        <AssignSelect
          options={innerOptions}
          value={value ?? ""}
          onValueChange={onChange}
          size="md"
          variant="border"
          className={contentClassName}
        />
        
        
        
      )}
    </div>
  );
}
