import React, { FC, ReactNode, ComponentProps } from 'react';
import Image from 'next/image';
import { cn } from "@repo/ui/lib/utils";

interface BackButtonProps {
  onClick: () => void;
  className?: string;
}

/**
 * 练习页面返回按钮组件
 */
export const AskButton: React.FC<BackButtonProps> = ({
  onClick,
  className = '',
}) => {
  return (
    <button
      onClick={onClick}
      className={`flex w-10 h-10 justify-center items-center gap-1 flex-shrink-0 rounded-lg border-[0.5px] border-[rgba(31,35,43,0.12)] bg-[rgba(255,255,255,0.90)] shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] ${className}`}
      aria-label="问一问"
    >
      <Image src="/icons/ask-icon.svg" alt="back" width={20} height={20} />
    </button>
  );
};

export const TranslucentGlassButtonExercise: FC<
  { icon?: ReactNode } & ComponentProps<"button">
> = ({ icon, children, className, ...props }) => {
  return (
    <button
      className={cn(
        "inline-flex h-11 items-center justify-center gap-1 rounded-xl bg-white px-3 shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] outline-1 outline-offset-[-1px] outline-white",
        className
      )}
      {...props}
    >
      {icon && <div className="size-auto">{icon}</div>}
      {children && (
        <div className="text-base font-bold leading-tight">
          {children}
        </div>
      )}
    </button>
  );
};
export default AskButton; 