"use client";

import * as React from "react";
import { createPortal } from "react-dom";
import { cn } from "@/utils/utils";
import {
  X,
  AlertCircle,
  CheckCircle2,
  Info,
  AlertTriangle,
} from "lucide-react";
import { Button } from "./tch-button";

interface ModalProps {
  open?: boolean;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  extraFooter?: React.ReactNode;
  onClose?: () => void | Promise<void>;
  className?: string;
  closeOnClickOutside?: boolean;
  confirmLoading?: boolean;
  okText?: React.ReactNode;
  cancelText?: React.ReactNode;
  onOk?: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
  type?: "info" | "success" | "warning" | "error";
  showCloseButton?: boolean;
}

const errorIcon = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="info_danger">
      <path
        id="Ellipse 2302"
        d="M19.1666 10C19.1666 15.0626 15.0625 19.1667 9.99992 19.1667C4.93731 19.1667 0.833252 15.0626 0.833252 10C0.833252 4.93743 4.93731 0.833374 9.99992 0.833374C15.0625 0.833374 19.1666 4.93743 19.1666 10Z"
        fill="#FA5A57"
      />
      <path
        id="Union"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.63895 6.58971C7.34918 6.29995 6.87939 6.29995 6.58962 6.58971C6.29986 6.87947 6.29986 7.34927 6.58962 7.63903L8.95065 10.0001L6.58974 12.361C6.29997 12.6507 6.29997 13.1205 6.58974 13.4103C6.8795 13.7001 7.3493 13.7001 7.63906 13.4103L9.99997 11.0494L12.3609 13.4103C12.6507 13.7001 13.1205 13.7001 13.4102 13.4103C13.7 13.1205 13.7 12.6507 13.4102 12.361L11.0493 10.0001L13.4103 7.63902C13.7001 7.34925 13.7001 6.87946 13.4103 6.58969C13.1206 6.29993 12.6508 6.29993 12.361 6.58969L9.99997 8.95073L7.63895 6.58971Z"
        fill="white"
      />
    </g>
  </svg>
);
const iconMap = {
  info: <Info className="h-6 w-6 text-blue-500" />,
  success: <CheckCircle2 className="h-6 w-6 text-green-500" />,
  warning: <AlertTriangle className="h-6 w-6 text-yellow-500" />,
  error: errorIcon,
};

const Modal = React.forwardRef<HTMLDivElement, ModalProps>((props, ref) => {
  const {
    open = false,
    title,
    subtitle,
    children,
    footer,
    extraFooter,
    onClose,
    className,
    closeOnClickOutside = true,
    confirmLoading = false,
    okText = "确定",
    cancelText = "取消",
    onOk,
    onCancel,
    type,
    showCloseButton = true,
  } = props;

  const [loading, setLoading] = React.useState(false);
  const [mounted, setMounted] = React.useState(false);
  const [isVisible, setIsVisible] = React.useState(false);
  const [isAnimating, setIsAnimating] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  React.useEffect(() => {
    if (open) {
      setIsVisible(true);
      // 确保在下一帧应用动画类
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });
    } else {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [open]);

  if (!mounted) return null;
  if (!open && !isVisible) return null;

  const handleClose = async () => {
    if (onClose) {
      await onClose();
    }
  };

  const handleClickOutside = () => {
    if (closeOnClickOutside) {
      handleClose();
    }
  };

  const handleOk = async () => {
    if (onOk) {
      setLoading(true);
      try {
        await onOk();
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCancel = async () => {
    if (onCancel) {
      await onCancel();
    }
    handleClose();
  };

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className={cn(
          "fixed inset-0 bg-black/50 transition-opacity duration-200",
          isAnimating ? "opacity-100" : "opacity-0"
        )}
        onClick={handleClickOutside}
      />
      <div
        ref={ref}
        className={cn(
          "relative z-50 w-full max-w-lg bg-white px-6 py-5 shadow-lg transition-all duration-200",
          type ? "rounded-2xl" : "rounded-lg",
          isAnimating
            ? "translate-y-0 scale-100 opacity-100"
            : "translate-y-4 scale-95 opacity-0",
          className
        )}
      >
        <div className="mb-4 flex items-start justify-between">
          <div className="flex items-start">
            {type && (
              <span className="relative -left-0.5 top-1 mr-0.5">
                {iconMap[type]}
              </span>
            )}
            <div className="">
              {title && (
                <h3 className="text-xl font-semibold leading-6 text-[var(--color-gray-1)]">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="mt-1 text-sm text-[var(--color-gray-2)]">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {showCloseButton && (
            <button
              onClick={handleClose}
              className="rounded-md hover:bg-gray-100"
            >
              <X className="h-5 w-5 text-gray-400" />
            </button>
          )}
        </div>

        <div className="mb-6">{children}</div>

        {footer === undefined ? (
          <div className="flex items-center justify-between gap-2">
            <div className="flex-1">{extraFooter}</div>
            <div className="flex gap-2">
              <Button
                type="default"
                size="lg"
                radius="full"
                onClick={handleCancel}
              >
                {cancelText}
              </Button>
              <Button
                type="primary"
                size="lg"
                radius="full"
                onClick={handleOk}
                loading={loading || confirmLoading}
              >
                {okText}
              </Button>
            </div>
          </div>
        ) : (
          footer
        )}
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
});

Modal.displayName = "Modal";

export { Modal };
