#!/bin/bash

# 显示每个执行的命令
set -x

# 检查并安装 sshpass
if ! command -v sshpass &> /dev/null; then
    echo "未检测到 sshpass，正在安装..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if ! command -v brew &> /dev/null; then
            echo "需要先安装 Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        brew install sshpass
    elif command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y sshpass
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y sshpass
    else
        echo "无法自动安装 sshpass，请手动安装后重试"
        exit 1
    fi
fi

# 定义远程服务器信息
REMOTE_HOST="<EMAIL>"
REMOTE_PATH="/home/<USER>/nginx/html/question"
PASSWORD="Yhs8jCfcys"

# SSH 和 SCP 的通用选项
SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"

# 1. 安装依赖并构建
echo "开始安装依赖..."
pnpm i

echo "开始清空dist目录..."
rm -rf dist

echo "开始构建项目..."
pnpm build:local

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "构建失败，退出部署"
    exit 1
fi

# 2. 清空远程目录
echo "清空远程目录..."
sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "rm -rf $REMOTE_PATH/*"
if [ $? -ne 0 ]; then
    echo "清空远程目录失败，请检查 SSH 连接和权限"
    exit 1
fi

# 3. 上传文件
echo "开始上传文件..."

# 首先检查 dist 目录是否存在且不为空
if [ ! -d "dist" ] || [ -z "$(ls -A dist)" ]; then
    echo "错误：dist 目录不存在或为空"
    exit 1
fi

# 检查远程目录是否可写
echo "检查远程目录权限..."
sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "test -w $REMOTE_PATH"
if [ $? -ne 0 ]; then
    echo "错误：无法写入远程目录 $REMOTE_PATH，请检查权限"
    exit 1
fi

# 使用 scp 上传文件，添加详细输出
sshpass -p "$PASSWORD" scp $SSH_OPTIONS -v -r dist/* "$REMOTE_HOST:$REMOTE_PATH"
SCP_STATUS=$?

if [ $SCP_STATUS -ne 0 ]; then
    echo "文件上传失败，错误代码：$SCP_STATUS"
    echo "请检查："
    echo "1. 网络连接是否正常"
    echo "2. 远程服务器是否可达（尝试 ping $REMOTE_HOST）"
    echo "3. SSH 密码是否正确"
    echo "4. 远程目录权限是否正确"
    echo "5. 查看详细日志：$LOG_FILE"
    exit 1
fi

# 4. 重启 nginx 容器
echo "重启 nginx 容器..."
sshpass -p "$PASSWORD" ssh $SSH_OPTIONS "$REMOTE_HOST" "cd /home/<USER>/nginx && docker-compose restart"
if [ $? -ne 0 ]; then
    echo "重启 nginx 容器失败"
    exit 1
fi

echo "部署完成！"
echo "部署日志已保存到：$LOG_FILE" 