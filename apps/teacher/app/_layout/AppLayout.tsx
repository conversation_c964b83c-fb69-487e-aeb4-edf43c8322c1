"use client";
import { TEACHER_APP_LAYOUT_ID } from "@/configs";
import { useAuth, useDevice } from "@/hooks";
// import { useMount } from "ahooks";
import dynamic from "next/dynamic";
import React from "react";
import { match, P } from "ts-pattern";
import "./init-app";

const AppProvider = dynamic(
  () =>
    import(
      /* webpackChunkName: "app-provider" */
      /* webpackPrefetch: true */
      "./AppProvider"
    ),
  { ssr: false }
);

export default function AppLayout({
  children,
  login,
}: Readonly<{
  children: React.ReactNode;
  login: React.ReactNode;
}>) {
  const { token } = useAuth();
  const { screenSize } = useDevice();

  // useMount(() => {
  //   window.TecJsb?.hideStartLoading();
  // });

  return (
    <div
      id={TEACHER_APP_LAYOUT_ID}
      className="h-screen select-none overflow-auto"
      style={{ minHeight: screenSize?.height }}
    >
      {match(token)
        // 主界面
        .with(P.string, () => <AppProvider>{children}</AppProvider>)
        // 登录页
        .otherwise(() => login)}
    </div>
  );
}
