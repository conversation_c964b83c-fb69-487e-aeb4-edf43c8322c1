/**
 * AnswerFieldSelector 组件的类型定义
 */

export interface AnswerFieldSelectorProps {
  /** 题目答题模式 */
  questionAnswerMode: number;
  /** 是否启用匹配线模式（m选n模式） */
  isMatchLine: boolean;
  /** 表单字段名称 */
  fieldName: string | (string | number)[];
  /** 表单字段路径 */
  questionOptionMatrixName: string | (string | number)[];
  /** 表单字段额外属性 */
  restField?: any;
  /** 题干内容 */
  questionStem: string;
  /** 自定义样式类名 */
  className?: string;
  /** 当前填空索引 */
  currentFillIdx?: number;
}

export interface AnswerFormListProps {
  /** 表单字段名称 */
  fieldName: string | (string | number)[];
  /** 题目答题模式 */
  questionAnswerMode: number;
  /** 字段管理器实例 */
  fieldManager: any;
  /** 是否启用匹配线模式 */
  isMatchLine: boolean;
  /** 当前活跃的插入点索引 */
  activeFillIdx: number;
  /** 表单字段路径 */
  questionOptionMatrixName: string | (string | number)[];
}

export interface AnswerFormContentProps {
  /** Form.List字段信息 */
  fields: any[];
  /** 表单错误信息 */
  errors: any[];
  /** 题目答题模式 */
  questionAnswerMode: number;
  /** 当前活跃的插入点索引 */
  activeFillIdx: number;
  /** 字段管理器实例 */
  fieldManager: any;
  /** 是否启用匹配线模式 */
  isMatchLine: boolean;
  /** 表单字段路径 */
  questionOptionMatrixName: string | (string | number)[];
  /** 表单实例 */
}

export interface FormListOperations {
  /** 添加字段 */
  add: (defaultValue?: any, insertIndex?: number) => void;
  /** 删除字段 */
  remove: (index: number | number[]) => void;
  /** 字段列表 */
  fields: any[];
}
