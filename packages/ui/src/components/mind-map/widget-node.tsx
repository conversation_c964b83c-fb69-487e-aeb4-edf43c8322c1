import { Handle, Node, NodeProps, Position } from "@xyflow/react";
import { FC, memo } from "react";

type WidgetNode = Node<{
  label: string;
}>;

const WidgetNode: FC<NodeProps<WidgetNode>> = ({ data }) => {
  return (
    <>
      <div className="bg-Semantic-Course-2 outline-text-gray-1 flex w-auto items-center justify-start gap-2.5 rounded-full px-4 py-2 outline outline-1 outline-offset-[-0.50px]">
        <div className="text-text-gray-1 justify-start font-['Alibaba_PuHuiTi_3.0'] text-sm font-medium leading-loose">
          {data.label}
        </div>
      </div>
      <Handle type="target" position={Position.Left} />
    </>
  );
};

export default memo(WidgetNode);
