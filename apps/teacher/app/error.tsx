"use client";
import forbidden from "@/public/images/403.png";
import * as Sentry from "@sentry/nextjs";
import Image from "next/image";
import { useEffect } from "react";

export default function Error({
  error,
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    Sentry.captureException(error);

    if (process.env.NODE_ENV === "development") {
      console.error(error);
    }
  }, [error]);

  return (
    <div className="flex size-full flex-col items-center justify-center">
      <Image src={forbidden} alt="403" className="size-30" />

      <div className="text-gray-4 mt-2 text-sm/normal">
        系统出错，请联系管理员
        <div className="hidden">{error.message}</div>
      </div>
    </div>
  );
}
