import QuestionItem from '@/components/QuestionItem';
import { fetchQuestionList } from '@/services/api';
import { useRequest } from '@umijs/max';
import { Button, Card, List } from 'antd';
import { FC, useEffect } from 'react';
import useStyles from './index.style';

export interface ExerciseQuestionListProps {
  requestParams?: API.QuestionListRequestParams;
}
const ExerciseQuestionList: FC<ExerciseQuestionListProps> = ({ requestParams }) => {
  const { styles } = useStyles();

  const {
    data: questionListData,
    run: fetchQuestionListRun,
    loading: fetchQuestionListLoading,
  } = useRequest(
    () => {
      if (!requestParams) {
        return Promise.resolve({
          data: {
            total: 0,
            page: 1,
            pageSize: 10,
            list: [],
          },
        });
      }
      return fetchQuestionList(requestParams);
    },
    {
      manual: true,
      throttleInterval: 1000,
    },
  );

  useEffect(() => {
    fetchQuestionListRun();
  }, [requestParams]);

  return (
    <div>
      <Button type="primary" className={styles.selectedButton}>
        已选题目
      </Button>
      <Card className={styles.questionCard}>
        <List
          itemLayout="vertical"
          loading={fetchQuestionListLoading}
          dataSource={questionListData?.list ?? []}
          renderItem={(question: API.QuestionItemType) => (
            <List.Item style={{ padding: 0, marginBottom: 16, borderBottom: 'none' }}>
              <QuestionItem question={question} />
            </List.Item>
          )}
          style={{ width: '100%' }}
        />
      </Card>
    </div>
  );
};
export default ExerciseQuestionList;
