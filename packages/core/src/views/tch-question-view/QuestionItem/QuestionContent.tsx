import MdPreview from "../MdPreview";
import { QaContentType } from "../type";
import QuestionOptionsDisplay from "./QuestionOptions";
import { SubQuestionList } from "./SubQuestionList";

export function QuestionContent({
  qaContent,
  ossHost,
  levels,
}: {
  qaContent: QaContentType;
  ossHost?: string;
  levels?: (string | number)[];
}) {
  // NOTE（7.30）：这里是临时兜底可能的子母题逻辑，之前约定是questionType=7是子母题，现在改成只要有subQuestionList就显示子母题，预计产品回公司后回尽快拉齐展示逻辑
  if (qaContent.subQuestionList && qaContent.subQuestionList.length > 0) {
    return (
      <SubQuestionList
        qaContent={qaContent}
        ossHost={ossHost}
        levels={levels}
      />
    );
  }

  return (
    <div style={{ lineHeight: "2.5" }}>
      <MdPreview
        content={qaContent.content}
        ossHost={ossHost}
        questionId={qaContent.questionId}
      />
      <QuestionOptionsDisplay qaContent={qaContent} ossHost={ossHost} />
    </div>
  );
}
