"use client";

import { Suspense } from "react";
import { QuestionList } from "@/app/views/question-list/question-list-view";
import { closePage } from "@/app/utils/device";
import { StudyTypeThemeProvider } from "@repo/core/exercise/theme";
import { useSearchParams } from "next/navigation";
import { StudyType } from "@/app/models/exercise";
import { toast } from "@/app/components/common/toast";

// 将参数获取逻辑提取到单独的组件中
function QuestionListContent() {
  const searchParams = useSearchParams();
  const studyType = searchParams.get("studyType") as unknown as StudyType;

  return (
    <StudyTypeThemeProvider studyType={studyType as StudyType}>
      <QuestionList
        onBack={() => {
          closePage();
        }}
      />
    </StudyTypeThemeProvider>
  );
}

export default function QuestionListPage() {
  return (
    <Suspense
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          加载中...
        </div>
      }
    >
      <QuestionListContent />
    </Suspense>
  );
}
