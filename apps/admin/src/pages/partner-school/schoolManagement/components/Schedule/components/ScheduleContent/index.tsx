import { NodeType } from '@/components/GradeTree';
import { ScheduleContext } from '@/pages/partner-school/schoolManagement/contexts/ScheduleContext';
import { Empty, Spin } from 'antd';
import React, { useCallback, useContext, useMemo } from 'react';
import ScheduleButtons from '../ScheduleButtons';
import ScheduleNoData from '../ScheduleNoData';
import ScheduleTableRender from '../ScheduleTableRender';
import ScheduleTemplateEditor from '../ScheduleTemplateEditor';

const ScheduleContent: React.FC = () => {
  const {
    selectedNodeInfo,
    scheduleData,
    hasSemesterData,
    loading,
    SemesterWeekSelector,
    editMode,
    handleScheduleAction,
    setStartDate,
  } = useContext(ScheduleContext);
  console.log('rain context', selectedNodeInfo, scheduleData);

  // 获取标题文本
  const titleText = useMemo(() => {
    if (selectedNodeInfo) {
      const { nodeType, scheduleName } = selectedNodeInfo;
      const isGradeView = nodeType === NodeType.Grade;
      return (isGradeView && scheduleData?.tpl_info?.template_name) || scheduleName;
    }
    return '';
  }, [selectedNodeInfo, scheduleData]);

  const isGrade = selectedNodeInfo && editMode === 'grade';
  const isClass = selectedNodeInfo && editMode === 'class';

  // 渲染空状态
  const renderEmptyState = useCallback(() => {
    if (!selectedNodeInfo) {
      return (
        <div className="flex items-center justify-center h-full">
          <Empty description="请选择左侧年级或班级" />
        </div>
      );
    }
    return null;
  }, [selectedNodeInfo]);
  const emptyState = renderEmptyState();
  if (emptyState) return emptyState;

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center">
        <div className="text-lg font-medium">{titleText}</div>
        {hasSemesterData ? <ScheduleButtons /> : null}
      </div>

      <div className="mb-4">{SemesterWeekSelector}</div>

      <div className="flex-1 rounded">
        {hasSemesterData ? (
          <Spin spinning={loading.schedule}>
            {/* 年级课表编辑器 */}
            {isGrade && (
              <ScheduleTemplateEditor
                grade={Number(selectedNodeInfo.nodeId)}
                periods={scheduleData?.tpl_info?.period_info}
                templateInfo={scheduleData?.tpl_info}
                onCancel={() => handleScheduleAction('cancel')}
                onSave={({ startDate }) => {
                  setStartDate(startDate);

                  handleScheduleAction('save');
                }}
              />
            )}
            <ScheduleTableRender />
            <ScheduleNoData />
          </Spin>
        ) : null}
      </div>
    </div>
  );
};

export default ScheduleContent;
