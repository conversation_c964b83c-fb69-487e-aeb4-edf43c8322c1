import { Form, InputNumber, Modal, TimePicker } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

interface QuickSettingsModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (settings: QuickSettings) => void;
}

export interface QuickSettings {
  morningStart: string;
  afternoonStart: string;
  eveningStart: string;
  periodDuration: number;
  breakDuration: number;
  periodsPerDay: number;
}

const QuickSettingsModal: React.FC<QuickSettingsModalProps> = ({ visible, onCancel, onOk }) => {
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const settings: QuickSettings = {
        morningStart: values.morningStart.format('HH:mm'),
        afternoonStart: values.afternoonStart.format('HH:mm'),
        eveningStart: values.eveningStart.format('HH:mm'),
        periodDuration: values.periodDuration,
        breakDuration: values.breakDuration,
        periodsPerDay: values.periodsPerDay,
      };
      onOk(settings);
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <Modal title="快捷设置" open={visible} onOk={handleOk} onCancel={onCancel} destroyOnHidden>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          morningStart: dayjs('08:00', 'HH:mm'),
          afternoonStart: dayjs('14:00', 'HH:mm'),
          eveningStart: dayjs('18:00', 'HH:mm'),
          periodDuration: 45,
          breakDuration: 10,
          periodsPerDay: 8,
        }}
      >
        <Form.Item
          label="上午开始时间"
          name="morningStart"
          rules={[{ required: true, message: '请选择上午开始时间' }]}
        >
          <TimePicker format="HH:mm" />
        </Form.Item>

        <Form.Item
          label="下午开始时间"
          name="afternoonStart"
          rules={[{ required: true, message: '请选择下午开始时间' }]}
        >
          <TimePicker format="HH:mm" />
        </Form.Item>

        <Form.Item
          label="晚上开始时间"
          name="eveningStart"
          rules={[{ required: true, message: '请选择晚上开始时间' }]}
        >
          <TimePicker format="HH:mm" />
        </Form.Item>

        <Form.Item
          label="每节课时长(分钟)"
          name="periodDuration"
          rules={[{ required: true, message: '请输入每节课时长' }]}
        >
          <InputNumber min={30} max={120} />
        </Form.Item>

        <Form.Item
          label="课间休息时间(分钟)"
          name="breakDuration"
          rules={[{ required: true, message: '请输入课间休息时间' }]}
        >
          <InputNumber min={5} max={30} />
        </Form.Item>

        <Form.Item
          label="每天课程节数"
          name="periodsPerDay"
          rules={[{ required: true, message: '请输入每天课程节数' }]}
        >
          <InputNumber min={1} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuickSettingsModal;
