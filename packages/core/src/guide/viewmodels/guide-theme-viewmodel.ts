import { GuideTheme } from "@repo/core/types/data/widget-guide";

const CDN_URL = "https://static.xiaoluxue.com/assets";

export const useCDNAssets = (path: string) => {
  return path ? `${CDN_URL}/guide-theme/${path}` : "";
};
//${CDN_URL}/guide-theme/h2-title/
export const useH2TitleBg = (name: string = "v20250726") => {
  const bgLeft = useCDNAssets(`h2-title/${name}-left.png`);
  const bgRepeat = useCDNAssets(`h2-title/${name}-repeat.png`);
  const bgRight = useCDNAssets(`h2-title/${name}-right.png`);

  return { bgLeft, bgRepeat, bgRight };
};

export const useGuideThemeViewmodel = (theme: GuideTheme | undefined) => {
  const defaultTheme: GuideTheme = {
    titleBg: "v20250726",
    backCover: "back-cover-v3.png",
  };

  return { ...defaultTheme, ...theme };
};
