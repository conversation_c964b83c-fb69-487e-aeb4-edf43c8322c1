{"name": "@repo/lib", "version": "0.0.1", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "GIL", "license": "ISC", "packageManager": "pnpm@10.7.0", "dependencies": {"@preact-signals/safe-react": "^0.9.0", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "ali-oss": "^6.22.0", "clsx": "^2.1.1", "react": "catalog:react19", "react-dom": "catalog:react19", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.1", "typescript": "^5.8.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19"}, "exports": {"./style": {"types": "./src/style.ts", "default": "./src/style.ts"}, "./hooks/*": {"types": "./src/hooks/*.ts", "default": "./src/hooks/*.ts"}, "./utils/*": {"types": "./src/utils/*.ts", "default": "./src/utils/*.ts"}, "./types/*": {"types": "./src/types/*.ts", "default": "./src/types/*.ts"}}}