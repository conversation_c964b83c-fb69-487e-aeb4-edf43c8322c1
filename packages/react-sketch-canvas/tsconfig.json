{"extends": "@repo/config-typescript/base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "moduleResolution": "node", "module": "esnext", "target": "es2017", "lib": ["dom", "dom.iterable", "es6"], "strict": false, "noImplicitAny": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}