import { useSignal } from "@preact-signals/safe-react";
import { useEffect, useRef } from "react";
import { Markdown } from "./markdown";

interface TypedMessageProps {
  message: string;
  onComplete?: () => void;
  onTextUpdate?: () => void;
  isTyping?: boolean;
  isComplete?: boolean;
}

const TypedMessage: React.FC<TypedMessageProps> = ({
  message,
  onComplete,
  onTextUpdate,
  isTyping: externalIsTyping,
  isComplete: externalIsComplete,
}) => {
  const len = useSignal(externalIsTyping ? 0 : message.length);
  const timer = useRef<number>(null);

  useEffect(() => {
    const handler = () => {
      if (!externalIsTyping) {
        len.value = message.length;
        return;
      }
      if (len.value >= message.length) {
        return;
      }
      len.value++;
      timer.current = requestAnimationFrame(handler);
    };
    if (timer.current) {
      cancelAnimationFrame(timer.current);
    }
    handler();
    return () => {
      if (timer.current) {
        cancelAnimationFrame(timer.current);
      }
    };
  }, [externalIsTyping, len, message.length]);

  const showText =
    externalIsTyping === false && !externalIsComplete
      ? message + "..."
      : message.slice(0, len.value);

  useEffect(() => {
    onTextUpdate?.();
  }, [onTextUpdate, showText]);

  return (
    <div className="leading-inherit relative inline-block w-full align-top">
      <Markdown content={showText} />
    </div>
  );
};

export default TypedMessage;
