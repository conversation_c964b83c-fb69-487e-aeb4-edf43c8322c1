<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 774 274" fill="none">
  <g filter="url(#filter0_f_5621_17877)">
    <path d="M748 30C865.637 30 961 125.363 961 243C961 360.637 865.637 456 748 456C679.489 456 618.534 423.654 579.57 373.401C554.795 393.77 523.076 406 488.5 406C428.378 406 376.893 369.026 355.539 316.577C335.706 327.674 312.842 334 288.5 334C236.072 334 190.499 304.656 167.305 261.497C159.628 263.135 151.665 264 143.5 264C80.8157 264 30 213.184 30 150.5C30 87.8157 80.8157 37 143.5 37C176.482 37 206.177 51.0693 226.914 73.5322C245.441 64.2352 266.359 59 288.5 59C344.27 59 392.284 92.2027 413.855 139.919C435.603 126.647 461.158 119 488.5 119C515.346 119 540.469 126.374 561.957 139.204C598.385 74.0499 668.049 30 748 30Z" fill="url(#paint0_linear_5621_17877)" fill-opacity="0.6"/>
  </g>
  <defs>
    <filter id="filter0_f_5621_17877" x="0" y="0" width="991" height="486" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_5621_17877"/>
    </filter>
    <linearGradient id="paint0_linear_5621_17877" x1="532" y1="37" x2="422.5" y2="474" gradientUnits="userSpaceOnUse">
      <stop stop-color="#86C0F7"/>
      <stop offset="0.773093" stop-color="#86C0F7" stop-opacity="0"/>
    </linearGradient>
  </defs>
</svg>