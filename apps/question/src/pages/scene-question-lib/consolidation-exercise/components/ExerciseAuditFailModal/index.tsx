import { LeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Flex, Modal, theme, Typography } from 'antd';
import { FC } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';

import ExerciseExceptionQuestionList from '../ExerciseExceptionQuestionList';

export interface ExerciseAuditFailModalProps {
  node?: API.Common.BaseTreeNodeAntdType;
  showModal: boolean;
  onCloseModal: () => void;
}
const ExerciseAuditFailModal: FC<ExerciseAuditFailModalProps> = (props) => {
  const { node, showModal, onCloseModal } = props;
  const [modal, contextHolder] = Modal.useModal();
  const { useToken } = theme;
  const { token } = useToken();

  const onBack = () => {
    onCloseModal();
  };

  const onViewReason = () => {
    modal.info({
      title: '不通过原因',
      content: 'blabla。。。',
      okText: '返回',
    });
  };

  return (
    <Modal
      centered
      open={showModal}
      mask={false}
      width={'100vw'}
      height={'100vh'}
      closable={false}
      footer={null}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        // padding: 0,
        borderRadius: 0,
        maxWidth: '100vw',
        maxHeight: '100vh',
      }}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 0,
          height: '100vh',
          borderRadius: 0,
          backgroundColor: token.colorBgLayout,
        },
      }}
    >
      <div style={{ height: '100vh', padding: '50px 0 0' }}>
        <Flex
          align="center"
          justify="space-between"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            height: 50,
            padding: '0 20px 0 0',
          }}
          gap={16}
        >
          <Button
            type="text"
            size="large"
            icon={<LeftOutlined />}
            style={{ gap: 4 }}
            onClick={onBack}
          >
            返回
          </Button>
          <ExerciseBaseInfo node={node} copyable={false} />
          <Typography.Link onClick={onViewReason}>查看原因</Typography.Link>
        </Flex>
        <Card style={{ height: '100%', overflow: 'auto', borderRadius: 0 }}>
          <ExerciseExceptionQuestionList questions={[]} />
        </Card>
      </div>
      {contextHolder}
    </Modal>
  );
};
export default ExerciseAuditFailModal;
