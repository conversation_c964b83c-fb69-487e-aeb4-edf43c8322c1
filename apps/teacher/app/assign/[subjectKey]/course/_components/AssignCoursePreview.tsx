import AiCoursePreviewLayout from "@/components/business/ResourcePreview/AiCoursePreviewLayout";
import { useApp } from "@/hooks";
import { useFeedbackByType } from "@/hooks/useReportFeedback";
import { Button } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
import { AiCoursePreviewHandler } from "@repo/core/components/ai-course-preview";
import { CircleMinusIcon, CirclePlusIcon } from "lucide-react";
import { useRef, useState } from "react";
import { useAssignCourseContext } from "../store";

export default function AssignCoursePreviewWrapper() {
  console.log("AssignCoursePreviewWrapper BEFORE");
  const {
    aiCoursePreviewData,
    goToSelectTarget,
    selectedAiCourses,
    selectAiCourse,
    unselectAiCourse,
  } = useAssignCourseContext();

  const { isOpen } = useFeedbackByType();

  const hasJoined = selectedAiCourses.value.some(
    (item) => item.aiCourse.id === aiCoursePreviewData.value?.aiCourse.id
  );

  const detail = aiCoursePreviewData.value;

  const { statusBarHeight } = useApp();

  const aiCoursePreviewHandlerRef = useRef<AiCoursePreviewHandler | null>(null);

  const [isFullscreen, setIsFullscreen] = useState(false);
  const handleEnterFullscreen = () => {
    setIsFullscreen(true);
  };
  const handleExitFullscreen = () => {
    setIsFullscreen(false);
  };

  const refreshWidget = () => {
    aiCoursePreviewHandlerRef.current?.refresh();
  };

  if (!detail) {
    return null;
  }

  const HeaderSuffixNode = (
    <>
      <div
        className={cn(
          isOpen ? "duration-800 pointer-events-none opacity-0" : "",
          "right-38 z-999999 fixed top-[1.375rem] flex items-center overflow-visible transition-opacity duration-300 ease-in-out"
        )}
      >
        <Button
          type="outline"
          size="lg"
          radius="full"
          className={cn("bg-fill-light relative px-4 ease-in-out")}
          style={{
            top: `calc(${statusBarHeight}px - 0.3125rem)`,
            display: isFullscreen ? "none" : "block",
          }}
          onClick={refreshWidget}
        >
          刷新
        </Button>
        <Button
          type="default"
          size="md"
          radius="full"
          className={cn(
            "relative h-7 items-center rounded-[5.6px] px-2.5 text-xs font-normal active:bg-white active:opacity-60"
          )}
          style={{
            boxShadow: "0px 2.8px 11.2px 0px rgba(35, 42, 64, 0.05)",
            top: `0.0625rem`,
            display: isFullscreen ? "block" : "none",
          }}
          onClick={refreshWidget}
        >
          刷新
        </Button>
      </div>
      {hasJoined ? (
        <Button
          type="error"
          size="lg"
          radius="full"
          className="px-4"
          onClick={() => unselectAiCourse(detail)}
        >
          <CircleMinusIcon size={20} className="mr-1.5 size-5" />
          <span>取消加入</span>
        </Button>
      ) : (
        <Button
          type="primary"
          size="lg"
          radius="full"
          className="px-4"
          onClick={() => selectAiCourse(detail)}
        >
          <CirclePlusIcon size={20} className="mr-1.5 size-5" />
          <span>加入任务</span>
        </Button>
      )}
    </>
  );

  return (
    <AiCoursePreviewLayout
      id={aiCoursePreviewData.value?.bizTreeNodeId?.toString() || ""}
      onBack={goToSelectTarget}
      headerSuffixNode={HeaderSuffixNode}
      handlerRef={aiCoursePreviewHandlerRef}
      onEnterFullscreen={handleEnterFullscreen}
      onExitFullscreen={handleExitFullscreen}
    />
  );
}
