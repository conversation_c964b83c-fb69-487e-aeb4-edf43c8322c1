import { TempSelectionCreateResponseData, TempSelectionDeleteParams, TempSelectionItem } from "@/types/assign/temp-selection";
import { r } from "../libs/axios";
import { AssignCourseResourceItem } from "@/types/assign/course";

export const getTeacherTempSelectList = (subject: number) => {
  return r.get<TempSelectionItem[]>("/temp-selection/list", {
    params: {
      subject,
    },
  });
};


export const createTeacherTempSelection = (params: AssignCourseResourceItem) => {
  return r.post<TempSelectionCreateResponseData>("/temp-selection/create", params);
};

export const deleteTeacherTempSelection = (params: TempSelectionDeleteParams) => {
  return r.post<unknown>("/temp-selection/delete", params);
};

