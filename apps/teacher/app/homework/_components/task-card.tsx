'use client'
import { TASK_TYPE, taskTypeEnumManager } from "@/enums"
import { useApp } from "@/hooks"
import { Homework, HomeworkReport, StatData } from "@/types/homeWork"
import { Skeleton } from "@/ui/skeleton"
import { cn } from "@/utils/utils"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { MoreMenu } from "./more-menu"

export interface TaskData {
  creatorId: number
  homeworkData: Homework
  id: number
  title: string
  startTime: number
  endTime: number
  type: TASK_TYPE
  typeName: string
  date: string
  bgColor: string
  lineColor: string
  classes: ClassData[]
  subject: number
}

export type OriginalClassData = {
  taskId: number
  taskName: string
  taskType: TASK_TYPE
} & HomeworkReport


// 班级数据类型
export interface ClassData {
  assignId: number
  id: number
  statData: StatData
  name: string
  stats: Array<{
    label: string
    value: string
  }>
  isDue?: boolean,
  classData: OriginalClassData
}

// TaskCard 属性类型
export interface TaskCardProps {
  taskData: TaskData
  className?: string,
  onlyShow?: boolean
  onClickClass?: (classData: ClassData) => void
  onClick?: () => void
  classNames?: {
    classContent?: string
  }
}

// 格式化日期
export function formatDate(statData?: StatData): string {
  if (!statData?.startTime) return "";

  const startTimestamp = statData.startTime * 1000;
  const endTimestamp = statData?.deadline ? statData.deadline * 1000 : null;

  const startStr = format(startTimestamp, "MM/dd HH:mm", { locale: zhCN });
  const endStr = endTimestamp
    ? ` 至 ${format(endTimestamp, "MM/dd HH:mm", { locale: zhCN })}`
    : "";

  return `${startStr}${endStr}`;
}
// 处理各种率
export function formatRate(rate: number): string {
  if (rate === 0) return "";
  return (rate * 100).toFixed(1) + "%";
}

// 根据任务类型获取统计数据
export function getStatsFromReport(statData: StatData, taskType: number): Array<{ label: string, value: string }> {
  if (taskType === 40) { // 资源类型
    return [
      { label: "平均进度", value: formatRate(statData.averageProgress || 0) },
      { label: "课时数", value: `${statData.classHours || 0}节` },
    ];
  }

  return [
    { label: "完成率", value: formatRate(statData?.completionRate || 0) },
    { label: "正确率", value: formatRate(statData?.correctRate || 0) },
    { label: "待关注", value: `${statData?.needAttentionQuestionNum || 0}题` },
  ];
}

// 将API数据转换为UI数据的工具函数
export function transformHomeworkToTaskData(homework: Homework): TaskData {
  const { taskId, taskName, taskType, reports, subject, creatorId } = homework

  const classes = reports.map(report => {
    const stats = getStatsFromReport(report.statData, taskType)
    const isDue = report.statData?.deadline ? report.statData.deadline < Date.now() / 1000 : false

    return {
      assignId: report.assignId,
      id: report.assignObject.id,
      statData: report.statData,
      name: report.assignObject.name,
      stats,
      isDue,
      classData: {
        taskId, taskName, taskType,
        ...report
      }
    }
  })

  return {
    creatorId,
    subject,
    id: taskId,
    title: taskName,
    type: taskType,
    typeName: taskTypeEnumManager.getLabelByValue(taskType) || "",
    date: formatDate(homework.reports[0]?.statData),
    startTime: homework.reports[0]?.statData?.startTime || 0,
    endTime: homework.reports[0]?.statData?.deadline || 0,
    bgColor: taskTypeEnumManager.getEnumByValue(taskType)?.bg || "",
    lineColor: taskTypeEnumManager.getEnumByValue(taskType)?.lineColor || "",
    classes,
    homeworkData: homework,
  }
}
/**
 * 任务卡片组件
 * 显示任务详情和相关班级数据
 */
export function TaskCard({ taskData, className, onlyShow, onClick, onClickClass, classNames }: TaskCardProps) {
  const { userInfo } = useApp();
  const { title, typeName, date, bgColor, classes, lineColor } = taskData
  const isOwnTask = userInfo?.userID === taskData.creatorId

  const handleClick = () => {
    // 使用 RAF 确保状态更新后再执行点击回调
    requestAnimationFrame(() => {
      onClick?.();
    });
  };

  return (
    // 任务卡片容器，添加点击状态样式
    <div
      className={cn(
        "transition-all duration-200 rounded-2xl flex flex-col items-start gap-4 flex-1 p-6 cursor-pointer",
        "active:scale-[0.98] active:opacity-40",
        className
      )}  
      onClick={handleClick}
    >
      <div className={`w-full`}>
        {/* 任务标题区域 */}
        <h2 className="text-gray-2 text-base font-medium mb-0.25 leading-[150%]">{title}</h2>
        {/* 任务类型和日期信息 */}
        <p className="whitespace-nowrap text-gray-4 text-xs mb-4 font-normal leading-[150%]">
          <span className="text-[#8388ab]">{typeName}</span> {date}
        </p>

        {/* 班级数据列表 */}
        {classes.map((classData, index) => (
          <div
            onClick={() => {
              onClickClass?.(classData)
            }}
            key={index} className={cn(`flex flex-col items-start px-4 py-3 mb-4 last:mb-0 rounded-[0.75rem] self-stretch`, bgColor,
              classNames?.classContent
            )}>
            {/* 班级名称和状态区域 */}
            <div className="w-full flex items-center justify-between">
              {/* 班级名称 */}
              <div className="flex h-5 flex-col justify-between flex-1 text-gray-2 text-sm font-medium leading-[150%]">{classData.name}</div>
              {/* 条件渲染：到期标记或更多菜单 */}
              {classData.isDue && (
                // 到期标记
                <div className="flex h-[1.125rem] px-[0.25rem] items-center gap-[0.125rem] text-[#64698a] rounded-[6px] bg-white/40">
                  <p className="text-xs leading-[1] align-middle ">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path fillRule="evenodd" clipRule="evenodd" d="M1.5 6.5C1.5 4.01472 3.51472 2 6 2C8.48528 2 10.5 4.01472 10.5 6.5C10.5 8.98528 8.48528 11 6 11C3.51472 11 1.5 8.98528 1.5 6.5ZM6.5 4.5C6.5 4.22386 6.27614 4 6 4C5.72386 4 5.5 4.22386 5.5 4.5V6.5C5.5 6.63261 5.55268 6.75979 5.64645 6.85355L6.64645 7.85355C6.84171 8.04882 7.15829 8.04882 7.35355 7.85355C7.54882 7.65829 7.54882 7.34171 7.35355 7.14645L6.5 6.29289V4.5Z" fill="#838BAB" />
                      <path d="M2.30003 2.65003C2.07912 2.81571 1.76571 2.77094 1.60003 2.55003C1.43434 2.32912 1.47912 2.01571 1.70003 1.85003L2.70003 1.10003C2.92094 0.934344 3.23434 0.979115 3.40003 1.20003C3.56571 1.42094 3.52094 1.73434 3.30003 1.90003L2.30003 2.65003Z" fill="#838BAB" />
                      <path d="M9.30003 1.10009C9.07912 0.934403 8.76571 0.979174 8.60003 1.20009C8.43434 1.421 8.47912 1.7344 8.70003 1.90009L9.70003 2.65009C9.92094 2.81577 10.2343 2.771 10.4 2.55009C10.5657 2.32917 10.5209 2.01577 10.3 1.85009L9.30003 1.10009Z" fill="#838BAB" />
                    </svg>
                  </p>

                  <span className="text-xxs align-middle " style={{ lineHeight: "normal" }}>到期</span>
                </div>
              )}
              {/* 更多操作菜单 */}
              {isOwnTask && !onlyShow && (
                <MoreMenu taskData={taskData} />
              )}
            </div>
            {/* 分割线 */}
            <div className={cn("w-full h-[1px] my-[0.625rem] scale-y-50", lineColor)}></div>

            {/* 统计数据区域 */}
            <div
              className={
                classData.stats.length === 2
                  ? "flex justify-start gap-8 w-full" // 两个统计项时：第一个在开始位置，第二个有间距
                  : "flex justify-between w-full" // 三个或更多统计项时：均匀分布
              }
            >
              {/* 统计数据项 */}
              {classData.stats.map((stat, statIndex) => (
                <div key={statIndex} className="flex flex-col">
                  {/* 统计项标签 */}
                  <span className="text-gray-4 text-[0.625rem] font-normal leading-[150%]">{stat.label}</span>
                  {/* 统计项数值 */}
                  <span className="text-gray-2 text-sm font-medium leading-[150%] lining-nums proportional-nums">{
                    stat.value ?
                      stat.value :"—"}
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function TaskCardSkeleton() {
  return (
    <div className="rounded-2xl p-6 h-[221px]">
      {/* 标题和时间 */}
      <Skeleton className="h-6 w-2/3 mb-1 bg-gray-100" />
      <Skeleton className="h-4 w-9/10 mb-4 bg-gray-100" />

      {/* 两个班级卡片 */}
      <Skeleton className="h-28 w-full rounded-xl mb-4 bg-gray-100" />
    </div>
  )
}