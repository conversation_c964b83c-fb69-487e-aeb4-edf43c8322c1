import { FC } from "react";
import { InteractiveContentView } from "./interactive-content-view";
// import { InteractiveMenuView } from "./interactive-menu-view";
// import { InteractiveViewProvider } from "./interactive-view-context";

export const InteractiveView: FC<{
  active: boolean;
  index: number;
  url: string;
  type: string;
  onReport: (e: CustomEvent<unknown>) => void;
}> = ({ active, index, url, type, onReport }) => {
  return (
    // </InteractiveViewProvider>
    <InteractiveContentView url={url} type={type} onReport={onReport} />
  );
};
