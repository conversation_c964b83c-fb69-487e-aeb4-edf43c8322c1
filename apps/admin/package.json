{"name": "admin", "version": "0.0.1", "private": true, "description": "运营管理平台", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "build:dev": "cross-env REACT_APP_ENV=dev_xlx UMI_ENV=dev_xlx max build", "build:local": "cross-env REACT_APP_ENV=local UMI_ENV=local max build", "build:prod": "cross-env REACT_APP_ENV=prod_xlx UMI_ENV=prod_xlx max build", "build:test": "cross-env REACT_APP_ENV=test_xlx UMI_ENV=test_xlx max build", "deploy": "npm run build && npm run gh-pages", "deploy:dev": "pnpm build:local && rm -rf /home/<USER>/nginx/html/admin/* && mv dist/* /home/<USER>/nginx/html/admin && cd /home/<USER>/nginx && docker-compose restart", "dev": "npm run start:dev", "dev:force": "rm -rf node_modules/.cache && npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "online": "sh ./deploy.sh", "openapi": "max openapi", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:local": "cross-env REACT_APP_ENV=local MOCK=none UMI_ENV=local max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.8.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@antv/l7": "^2.20.13", "@antv/l7-maps": "^2.20.13", "@antv/l7-react": "^2.4.3", "@fingerprintjs/fingerprintjs": "^4.6.1", "@fullcalendar/core": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@repo/core": "workspace:*", "@repo/lib": "workspace:*", "@repo/ui": "workspace:*", "@stagewise/toolbar": "^0.6.2", "@umijs/route-utils": "^2.2.2", "antd": "5.25.4", "antd-style": "^3.7.1", "await-to-js": "^3.0.0", "classnames": "^2.5.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "md5": "^2.3.0", "numeral": "^2.0.6", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-util": "^5.38.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-fittext": "^1.0.0", "swr": "^2.3.3"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/lodash.debounce": "^4.0.9", "@types/md5": "^2.3.5", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.1.0", "@umijs/max": "^4.1.0", "code-inspector-plugin": "^0.20.1", "cross-env": "^7.0.3", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockjs": "^1.1.0", "prettier": "^3.1.1", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.4.8", "prettier-plugin-two-style-order": "^1.0.1", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "tailwindcss": "^3", "ts-node": "^10.9.2", "typescript": "^4.9.5", "umi-presets-pro": "^2.0.3"}, "engines": {"node": ">=12.0.0"}, "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless"], "ignore": [".dockerignore", ".git", ".github", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "CNAME", "create-umi"]}}