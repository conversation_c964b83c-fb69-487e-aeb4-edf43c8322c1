namespace JSBridgeSchema {
  export type DeviceInfo = {
    screenOrientation: "portrait" | "landscape";
    contentScreenWidth: number;
    contentScreenHeight: number;
    fullScreenWidth: number;
    fullScreenHeight: number;
    statusBarHeight: number;
    navigationBarHeight: number;
    deviceManufacturer: string;
    deviceBrand: string;
    deviceModel: string;
    deviceProduct: string;
    deviceBoard: string;
    deviceHardware: string;
    deviceAndroidVersion: string;
    deviceApiLevel: number;
    deviceSecurityPatch: string;
    deviceBuildId: string;
    deviceBuildTime: number;
    deviceBuildType: string;
    deviceBuildTags: string;
    deviceScreenDensity: number;
    deviceScreenDensityDpi: number;
    statusBarColor: number;
    statusBarVisible: boolean;
    statusBarDarkFont: boolean;
    statusBarTransparent: boolean;
    deviceId: string;
  };

  export type PermissionInfo = {
    granted: boolean;
  };

  export type AppInfo = {
    versionName: string;
    versionCode: string;
    channel: string;
  };

  export type PickImagesResult = {
    imageUriList: { imageUri: string }[];
  };
}

export default JSBridgeSchema;
