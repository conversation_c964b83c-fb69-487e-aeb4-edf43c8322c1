// 简单测试 parseFillBlank 函数
const { JSDOM } = require('jsdom');

// 模拟浏览器环境
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.Node = dom.window.Node;

// 导入函数（需要转换为 CommonJS 格式测试）
function parseContent(content) {
    // 创建临时 DOM 元素来解析 HTML
    const div = document.createElement("div");
    div.innerHTML = content;
  
    const result = [];
  
    // 递归处理节点，返回 (string | null)[]
    function processNode(node) {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent ? [node.textContent] : [];
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node;
  
        // 检查是否是普通的填空标记
        if (
          element.tagName.toLowerCase() === "span" &&
          element.getAttribute("data-tiptype") === "question-blank_filling"
        ) {
          // 普通的填空标记，返回null
          return [null];
        }
  
        // 递归处理子节点，拼接为 innerHTML
        let inner = "";
        for (const child of Array.from(element.childNodes)) {
          const childParts = processNode(child);
          childParts.forEach((part) => {
            if (part === null) {
              inner += "[[[BLANK]]]"; // 普通填空标记的占位符
            } else {
              inner += part;
            }
          });
        }
  
        // 保留标签和属性
        let html = element.outerHTML;
  
        // 用 inner 替换 element.innerHTML
        html = html.replace(element.innerHTML, inner);
  
        // 拆分 [[[BLANK]]] 为 null，其余为 html 字符串
        const parts = [];
        const splitArr = html.split("[[[BLANK]]]");
  
        for (let i = 0; i < splitArr.length; i++) {
          if (typeof splitArr[i] === "string" && splitArr[i]) {
            parts.push(splitArr[i]);
          }
  
          // 添加普通填空标记
          if (i < splitArr.length - 1) {
            parts.push(null);
          }
        }
  
        return parts;
      }
      return [];
    }
  
    // 处理所有顶级元素
    function processTopLevelElements() {
      // 获取所有顶级元素
      const topElements = Array.from(div.childNodes);
      
      // 当前段落的内容
      let currentParagraph = [];
  
      for (const element of topElements) {
        if (element.nodeType === Node.ELEMENT_NODE) {
          const el = element;
          const tagName = el.tagName.toLowerCase();
  
          if (tagName === "p") {
            // 遇到 p 标签，先保存当前段落（如果有内容）
            if (currentParagraph.length > 0) {
              result.push(currentParagraph);
              currentParagraph = [];
            }
            
            // 处理 p 标签内的内容作为新段落
            const parts = [];
            for (const node of Array.from(el.childNodes)) {
              if (node) {
                parts.push(...processNode(node));
              }
            }
            if (parts.length > 0) {
              result.push(parts);
            }
          } else {
            // 其他所有元素（table、div、span等）都添加到当前段落中，不换行
            const parts = processNode(el);
            currentParagraph.push(...parts);
          }
        } else if (
          element.nodeType === Node.TEXT_NODE &&
          element.textContent?.trim()
        ) {
          // 处理顶级文本节点，添加到当前段落
          currentParagraph.push(element.textContent);
        }
      }
      
      // 处理最后一个段落
      if (currentParagraph.length > 0) {
        result.push(currentParagraph);
      }
    }
  
    // 开始处理
    processTopLevelElements();
  
    // 如果没有找到任何内容，将整个内容作为一个段落
    if (result.length === 0) {
      result.push([content]);
    }
  
    return result;
}

// 测试用例
console.log("测试1: 只有 p 标签换行");
const test1 = '<p>第一段</p><div>这是div</div><span>这是span</span><p>第二段</p>';
console.log("输入:", test1);
console.log("输出:", JSON.stringify(parseContent(test1), null, 2));

console.log("\n测试2: 包含表格的情况");
const test2 = '<p>段落1</p><table><tr><td>表格内容</td></tr></table><div>div内容</div><p>段落2</p>';
console.log("输入:", test2);
console.log("输出:", JSON.stringify(parseContent(test2), null, 2));

console.log("\n测试3: 包含填空的情况");
const test3 = '<p>这是<span data-tiptype="question-blank_filling">填空</span>题目</p><div>div内容</div>';
console.log("输入:", test3);
console.log("输出:", JSON.stringify(parseContent(test3), null, 2));
