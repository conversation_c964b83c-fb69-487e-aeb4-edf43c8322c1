"use client";

import { Signal, signal, useSignal } from "@preact-signals/safe-react";
import { useCallback, useMemo } from "@preact-signals/safe-react/react";
import { motion } from "motion/react";
import { FC } from "react";
import { createRoot } from "react-dom/client";
import { v4 as uuid } from "uuid";
interface Methods {
  show(message: string): void;
  success(message: string): void;
  error(message: string): void;
}

const Container: FC<{ methodsRef: Signal<Methods | null> }> = ({
  methodsRef,
}) => {
  const messages = useSignal<
    {
      id: string;
      message: string;
      color: string;
      backgroundColor: string;
    }[]
  >([]);
  const show = useCallback(
    (
      message: string,
      color = "var(--color-text-2)",
      backgroundColor = "#fff"
    ) => {
      const id = uuid();
      messages.value = [
        ...messages.value,
        {
          id,
          message,
          color,
          backgroundColor,
        },
      ];
      setTimeout(() => {
        messages.value = messages.value.filter((opt) => opt.id !== id);
      }, 3000);
    },
    [messages]
  );
  const error = useCallback(
    (message: string) => show(message, "var(--color-dim-red)", "#FFEFEB"),
    [show]
  );
  const success = useCallback(
    (message: string) => show(message, "#4EA610", "#D5FABB"),
    [show]
  );
  const methods = useMemo(
    () => ({
      show,
      error,
      success,
    }),
    [error, show, success]
  );
  if (!methodsRef.value) methodsRef.value = methods;
  const last = messages.value.at(-1);
  return (
    <div className="z-200 pointer-events-none fixed left-1/2 top-8 flex -translate-x-1/2 flex-col items-center gap-2">
      {last && (
        <motion.div
          className="min-w-26 rounded-full border-2 border-white bg-white p-4 text-center shadow-[0px_4px_8px_0px_rgba(64,43,26,0.03)]"
          initial={{
            opacity: 0,
            y: -20,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          key={last.id}
          style={{ color: last.color, backgroundColor: last.backgroundColor }}
        >
          {last.message}
        </motion.div>
      )}
    </div>
  );
};

const ref: Signal<Methods | null> = signal(null);
if (typeof window !== "undefined" && document && !ref.value) {
  const container = document.createElement("div");
  container.id = "notify-container";
  document.body.append(container);
  const root = createRoot(container);
  root.render(<Container methodsRef={ref} />);
}

export const notify = {
  show: (message: string) => {
    ref.value?.show(message);
  },
  success: (message: string) => {
    ref.value?.success(message);
  },
  error: (message: string) => {
    ref.value?.error(message);
  },
};
