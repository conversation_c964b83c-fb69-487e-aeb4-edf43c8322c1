import { cn } from "@/utils/utils";
import { useDebounce } from "ahooks";
import { Search, X } from "lucide-react";
import * as React from "react";

function InputSearch({
  classNames,
  onSearch,
  className,
  type,
  clearable = true,
  searchable = true,
  value,
  disabled = false,
  onChange,
  debounce = false,
  wait = 300,
  ...props
}: React.ComponentProps<"input"> & {
  classNames?: {
    input?: string;
    search?: string;
    clear?: string;
  };
  clearable?: boolean;
  searchable?: boolean;
  debounce?: boolean;
  wait?: number;
  onSearch?: (value: string) => void;
}) {
  const [innerValue, setInnerValue] = React.useState(value);
  const previousValueRef = React.useRef(value);

  // 使用 useDebounce 处理防抖
  const debouncedValue = useDebounce(innerValue, { wait: debounce ? wait : 0 });

  // 只在防抖值和上一次不同时触发 onChange
  React.useEffect(() => {
    if (onChange && debouncedValue !== previousValueRef.current) {
      previousValueRef.current = debouncedValue;
      onChange({
        target: { value: debouncedValue },
      } as React.ChangeEvent<HTMLInputElement>);
    }
  }, [debouncedValue, onChange]);

  // 监听外部 value 变化
  React.useEffect(() => {
    if (value !== innerValue) {
      setInnerValue(value);
      previousValueRef.current = value;
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInnerValue(newValue);
    // 如果不需要防抖，直接比较并触发 onChange
    if (!debounce && onChange && newValue !== previousValueRef.current) {
      previousValueRef.current = newValue;
      onChange(e);
    }
  };

  const handleClear = () => {
    handleChange({
      target: { value: "" },
    } as React.ChangeEvent<HTMLInputElement>);
    onSearch?.("");
  };

  const isShowClear = clearable && innerValue && !disabled;

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onSearch?.(innerValue as string);
    }
  };

  return (
    <div className={cn("relative w-full", className)}>
      <input
        disabled={disabled}
        type={type}
        value={innerValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        data-slot="input"
        className={cn(
          "file:text-foreground placeholder:text-gray-4 selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-full w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-[0.875rem] outline-none transition-all duration-300 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
          "font-normal leading-[150%] text-[#838BAB]",
          searchable && "pr-12",
          isShowClear && "pr-17",
          "text-gray-2 border-line-3 rounded-[1.125rem] border bg-white outline-none",
          "focus:border-primary-2 focus-visible:border-primary-2 caret-primary-2 focus:shadow-[0px_4px_8px_0px_rgba(16,18,25,0.04)] focus-visible:bg-white focus-visible:shadow-[0px_4px_8px_0px_rgba(16,18,25,0.04)]",
          classNames?.input
        )}
        {...props}
      />

      {searchable && (
        <Search
          onClick={() => onSearch?.(innerValue as string)}
          className={cn(
            "absolute right-3 top-1/2 h-full -translate-y-1/2 transform sm:py-[0.325rem] lg:py-[0.325rem]",
            disabled ? "text-gray-5" : "text-gray-4",
            classNames?.search
          )}
        />
      )}

      {isShowClear && (
        <div
          onClick={handleClear}
          className={cn(
            "absolute right-6 top-1/2 -translate-y-1/2 transform cursor-pointer sm:h-[calc(100%-0.785rem)] lg:h-[calc(100%-0.785rem)]",
            searchable && "right-10",
            classNames?.clear
          )}
        >
          <X className="bg-gray-5 h-full w-full rounded-full p-[0.125rem] text-white" />
        </div>
      )}
    </div>
  );
}

export { InputSearch };
