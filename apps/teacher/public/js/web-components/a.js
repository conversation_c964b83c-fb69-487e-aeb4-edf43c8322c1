if (!customElements.get('web-component-vector-classification')) {
  class WebComponentVectorClassification extends HTMLElement {
    constructor() {
      super();
      this.katexLoaded = false;
      this.itemsData = [
        { id: 'item1', content: '\\mathbf{a} = \\lambda \\mathbf{b}', type: 'math', category: 'collinear' },
        { id: 'item2', content: '\\mathbf{p} = x\\mathbf{a} + y\\mathbf{b}', type: 'math', category: 'coplanar' },
        { id: 'item3', content: '方向相同或相反', type: 'text', category: 'collinear' },
        { id: 'item4', content: '确定直线方向', type: 'text', category: 'collinear' },
        { id: 'item5', content: '证明四点共面', type: 'text', category: 'coplanar' },
        { id: 'item6', content: '任意两向量', type: 'text', category: 'coplanar' },
      ];
      this.draggedItem = null;
      this.touchDragClone = null;
      this.touchStartPos = { x: 0, y: 0 };
    }

    async connectedCallback() {
      this.style.opacity = '0';
      this.style.transition = 'opacity 0.3s ease-in-out';
      const shadow = this.attachShadow({ mode: 'open' });
      await this._loadKaTeX();
      this._render(shadow);
      await this._renderMath(shadow);
      this._addEventListeners();
      this.style.opacity = '1';
    }

    _render(shadow) {
      shadow.innerHTML = `
        <style>
          :host {
            display: block;
            font-family: 'Resource Han Rounded SC', sans-serif;
            --main-bg-color: #FFFFFF;
            --card-bg-color: #FAFAFA;
            --primary-color: #FEA100;
            --correct-color: #58CC02;
            --correct-bg-color: #EDFFF0;
            --correct-border-color: rgba(132, 214, 75, 0.6);
            --incorrect-color: #FF6139;
            --incorrect-bg-color: #FFF8F7;
            --incorrect-border-color: rgba(255, 123, 89, 0.6);
            --text-title: #292F32;
            --text-body: #1F232B;
            --text-body-secondary: #4A515D;
            --border-color: #E7E6E5;
          }
          .main-container {
            width: 100%;
            min-height: 100%;
            background-color: var(--main-bg-color);
            border-radius: 20px;
            padding: 24px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            position: relative;
            overflow: hidden;
          }
          .title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-title);
            text-align: center;
          }
          .instructions {
            font-size: 15px;
            font-weight: 400;
            color: var(--text-body-secondary);
            text-align: center;
            margin-top: -12px;
          }
          .drop-zones {
            display: flex;
            gap: 20px;
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
          }
          .drop-zone {
            flex: 1;
            min-width: 250px;
            min-height: 200px;
            background-color: var(--card-bg-color);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            transition: background-color 0.2s;
          }
          .drop-zone.drag-over {
            background-color: rgba(31, 29, 27, 0.05);
            border: 2px dashed var(--primary-color);
          }
          .zone-title {
            font-size: 17px;
            font-weight: 700;
            color: var(--text-body);
          }
          .items-pool {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            padding: 20px;
            width: 100%;
            min-height: 80px;
            background: rgba(31, 29, 27, 0.05);
            border-radius: 12px;
          }
          .item {
            padding: 12px 20px;
            background-color: var(--main-bg-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: grab;
            font-size: 16px;
            font-weight: 400;
            color: rgba(51, 48, 45, 0.7);
            user-select: none;
            transition: border-color 0.3s, background-color 0.3s;
            touch-action: none; /* For touch drag */
          }
          .item.dragging {
            opacity: 0.5;
          }
          .item.submitted.correct {
            border: 1.5px solid var(--correct-color);
            background-color: var(--correct-bg-color);
            color: #333;
          }
          .item.submitted.incorrect {
            border: 1.5px solid var(--incorrect-color);
            background-color: var(--incorrect-bg-color);
            color: #333;
          }
          .item.touch-dragging {
            position: fixed;
            z-index: 1000;
            pointer-events: none;
            opacity: 0.8;
          }
          .submit-button {
            font-weight: 700;
            font-size: 15px;
            color: #FFFFFF;
            padding: 12px 20px;
            background-color: var(--primary-color);
            border-radius: 12px;
            border: none;
            cursor: pointer;
            width: 200px;
            margin-top: 20px;
          }
          .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
          .popup-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
          }
          .popup-content {
            background-color: var(--main-bg-color);
            border-radius: 16px;
            padding: 24px 20px 20px 20px;
            width: 300px;
            text-align: center;
            position: relative;
          }
          .popup-message {
            padding: 10px 18px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 15px;
          }
          .popup-message.correct {
            color: var(--correct-color);
            background-color: var(--correct-bg-color);
            border: 1px solid var(--correct-border-color);
          }
          .popup-message.incorrect {
            color: var(--incorrect-color);
            background: var(--incorrect-bg-color);
            border: 1px solid var(--incorrect-border-color);
          }
          .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: var(--primary-color);
            opacity: 0;
            animation: fall 3s ease-out forwards;
          }
          @keyframes fall {
            from { transform: translateY(0) rotate(0); opacity: 1; }
            to { transform: translateY(200px) rotate(360deg); opacity: 0; }
          }
          [data-math] { min-height: 1.2em; display: inline-block; vertical-align: middle; }
        </style>
        <div class="main-container">
          <div class="title">向量关系分类</div>
          <p class="instructions">请将下方卡片拖拽到正确的分类中</p>
          <div class="drop-zones">
            <div id="collinear" class="drop-zone" data-category="collinear">
              <div class="zone-title">向量共线</div>
            </div>
            <div id="coplanar" class="drop-zone" data-category="coplanar">
              <div class="zone-title">向量共面</div>
            </div>
          </div>
          <div class="items-pool">
            ${this.itemsData.sort(() => 0.5 - Math.random()).map(item => `
              <div class="item" id="${item.id}" draggable="true" data-category="${item.category}">
                ${item.type === 'math' ? `<span data-math="${item.content}"></span>` : item.content}
              </div>
            `).join('')}
          </div>
          <button class="submit-button" disabled>提交</button>
        </div>
      `;
    }

    _addEventListeners() {
      const shadow = this.shadowRoot;
      const items = shadow.querySelectorAll('.item');
      const dropZones = shadow.querySelectorAll('.drop-zone');
      const submitButton = shadow.querySelector('.submit-button');

      items.forEach(item => {
        item.addEventListener('dragstart', this._handleDragStart.bind(this));
        item.addEventListener('dragend', this._handleDragEnd.bind(this));
        item.addEventListener('touchstart', this._handleTouchStart.bind(this), { passive: false });
      });

      shadow.addEventListener('touchmove', this._handleTouchMove.bind(this), { passive: false });
      shadow.addEventListener('touchend', this._handleTouchEnd.bind(this));

      dropZones.forEach(zone => {
        zone.addEventListener('dragover', this._handleDragOver.bind(this));
        zone.addEventListener('dragenter', this._handleDragEnter.bind(this));
        zone.addEventListener('dragleave', this._handleDragLeave.bind(this));
        zone.addEventListener('drop', this._handleDrop.bind(this));
      });

      submitButton.addEventListener('click', this._handleSubmit.bind(this));
    }

    _handleDragStart(e) {
      this.draggedItem = e.target;
      e.dataTransfer.setData('text/plain', e.target.id);
      setTimeout(() => e.target.classList.add('dragging'), 0);
    }

    _handleDragEnd(e) {
      e.target.classList.remove('dragging');
      this.draggedItem = null;
    }

    _handleDragOver(e) {
      e.preventDefault();
    }

    _handleDragEnter(e) {
      if (e.target.classList.contains('drop-zone')) {
        e.target.classList.add('drag-over');
      }
    }

    _handleDragLeave(e) {
      if (e.target.classList.contains('drop-zone')) {
        e.target.classList.remove('drag-over');
      }
    }

    _handleDrop(e) {
      e.preventDefault();
      const dropZone = e.target.closest('.drop-zone');
      if (dropZone && this.draggedItem) {
        dropZone.classList.remove('drag-over');
        dropZone.appendChild(this.draggedItem);
        this._updateSubmitButtonState();
      }
    }

    _handleTouchStart(e) {
      if (this.shadowRoot.querySelector('.submit-button').disabled === false) return; // Game submitted
      e.preventDefault();
      this.draggedItem = e.target.closest('.item');
      if (!this.draggedItem) return;

      const rect = this.draggedItem.getBoundingClientRect();
      this.touchStartPos = {
        x: e.touches[0].clientX - rect.left,
        y: e.touches[0].clientY - rect.top
      };

      this.touchDragClone = this.draggedItem.cloneNode(true);
      this.touchDragClone.classList.add('touch-dragging');
      this.touchDragClone.style.width = `${rect.width}px`;
      this.touchDragClone.style.height = `${rect.height}px`;
      this.shadowRoot.querySelector('.main-container').appendChild(this.touchDragClone);

      this.draggedItem.style.opacity = '0.5';
      this._updateTouchClonePosition(e.touches[0]);
    }

    _handleTouchMove(e) {
      if (!this.touchDragClone) return;
      e.preventDefault();
      this._updateTouchClonePosition(e.touches[0]);

      this.shadowRoot.querySelectorAll('.drop-zone').forEach(zone => zone.classList.remove('drag-over'));
      const dropTarget = this._getDropTarget(e.touches[0]);
      if (dropTarget) {
        dropTarget.classList.add('drag-over');
      }
    }

    _updateTouchClonePosition(touch) {
      this.touchDragClone.style.left = `${touch.clientX - this.touchStartPos.x}px`;
      this.touchDragClone.style.top = `${touch.clientY - this.touchStartPos.y}px`;
    }

    _handleTouchEnd(e) {
      if (!this.draggedItem || !this.touchDragClone) return;

      const dropTarget = this._getDropTarget(e.changedTouches[0]);

      if (dropTarget) {
        dropTarget.appendChild(this.draggedItem);
      }

      this.shadowRoot.querySelectorAll('.drop-zone').forEach(zone => zone.classList.remove('drag-over'));
      this.draggedItem.style.opacity = '1';
      this.shadowRoot.querySelector('.main-container').removeChild(this.touchDragClone);

      this.draggedItem = null;
      this.touchDragClone = null;
      this._updateSubmitButtonState();
    }

    _getDropTarget(touch) {
      this.touchDragClone.style.display = 'none';
      const element = this.shadowRoot.elementFromPoint(touch.clientX, touch.clientY);
      this.touchDragClone.style.display = '';
      return element ? element.closest('.drop-zone') : null;
    }

    _updateSubmitButtonState() {
      const pool = this.shadowRoot.querySelector('.items-pool');
      const submitButton = this.shadowRoot.querySelector('.submit-button');
      if (pool.children.length === 0) {
        submitButton.disabled = false;
      } else {
        submitButton.disabled = true;
      }
    }

    _handleSubmit() {
      const submitButton = this.shadowRoot.querySelector('.submit-button');
      submitButton.disabled = true;
      this.shadowRoot.querySelectorAll('.item').forEach(item => {
        item.setAttribute('draggable', 'false');
        item.style.cursor = 'default';
      });


      let allCorrect = true;
      const dropZones = this.shadowRoot.querySelectorAll('.drop-zone');
      dropZones.forEach(zone => {
        const zoneCategory = zone.dataset.category;
        const itemsInZone = zone.querySelectorAll('.item');
        itemsInZone.forEach(item => {
          item.classList.add('submitted');
          if (item.dataset.category === zoneCategory) {
            item.classList.add('correct');
          } else {
            item.classList.add('incorrect');
            allCorrect = false;
          }
        });
      });

      this._showFeedbackPopup(allCorrect);
      this._reportResult(allCorrect);
    }

    _showFeedbackPopup(isCorrect) {
      const container = this.shadowRoot.querySelector('.main-container');
      const popupOverlay = document.createElement('div');
      popupOverlay.className = 'popup-overlay';

      const popupContent = document.createElement('div');
      popupContent.className = 'popup-content';

      const message = document.createElement('div');
      message.className = 'popup-message';
      if (isCorrect) {
        message.textContent = '太棒啦，回答正确';
        message.classList.add('correct');
        this._createConfetti(container);
      } else {
        message.textContent = '再接再厉';
        message.classList.add('incorrect');
      }

      popupContent.appendChild(message);
      popupOverlay.appendChild(popupContent);
      container.appendChild(popupOverlay);
    }

    _createConfetti(container) {
      for (let i = 0; i < 30; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = `${Math.random() * 100}%`;
        confetti.style.top = `${-20 + Math.random() * 20}px`;
        const randomColor = ['#FEA100', '#FF6139', '#58CC02', '#4A90E2'][Math.floor(Math.random() * 4)];
        confetti.style.backgroundColor = randomColor;
        confetti.style.animationDelay = `${Math.random() * 2}s`;
        confetti.style.transform = `scale(${0.5 + Math.random()})`;
        container.appendChild(confetti);
      }
    }

    _reportResult(isCorrect) {
      const event = new CustomEvent('report', {
        detail: { correct: isCorrect },
        bubbles: true,
        composed: true
      });
      this.dispatchEvent(event);
    }

    async _loadKaTeX() {
      if (this.katexLoaded) return;
      try {
        if (!document.querySelector('link[href*="katex.min.css"]')) {
          const cssLink = document.createElement('link');
          cssLink.rel = 'stylesheet';
          cssLink.href = 'https://cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.css';
          document.head.appendChild(cssLink);
        }
        if (!window.katex) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        this.katexLoaded = true;
      } catch (error) {
        console.warn('KaTeX failed to load:', error);
      }
    }

    async _renderMath(shadow) {
      if (window.katex && this.katexLoaded) {
        const mathElements = shadow.querySelectorAll('[data-math]');
        mathElements.forEach(el => {
          try {
            window.katex.render(el.dataset.math, el, {
              displayMode: false,
              throwOnError: false,
              output: 'mathml'
            });
          } catch (error) {
            el.textContent = el.dataset.math;
          }
        });
      }
    }

    disconnectedCallback() {
      // Event listeners are on the shadow DOM, which will be garbage collected
      // with the component instance, so manual removal is not strictly necessary for them.
      // However, if we added listeners to `document` or `window`, we'd remove them here.
    }
  }
  customElements.define('web-component-vector-classification', WebComponentVectorClassification);
}
