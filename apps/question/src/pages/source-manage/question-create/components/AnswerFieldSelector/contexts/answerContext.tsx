import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';

/**
 * 答案字段同步状态的Context类型定义
 */
interface AnswerFieldSyncContextType {
  /** 是否正在同步中 */
  isSyncing: boolean;
  /** 设置同步状态 */
  setIsSyncing: (syncing: boolean) => void;
  /** 同步状态的详细信息 */
  syncInfo: {
    /** 最后一次同步的时间戳 */
    lastSyncTime: number;
    /** 同步的源索引 */
    sourceIndex: number | null;
    /** 同步的目标数量 */
    targetCount: number;
  };
  /** 更新同步信息 */
  updateSyncInfo: (info: Partial<AnswerFieldSyncContextType['syncInfo']>) => void;
}

/**
 * 创建答案字段同步Context
 */
const AnswerFieldSyncContext = createContext<AnswerFieldSyncContextType | undefined>(undefined);

/**
 * 答案字段同步Context Provider的Props
 */
interface AnswerFieldSyncProviderProps {
  children: ReactNode;
}

/**
 * 答案字段同步Context Provider
 * 提供全局的同步状态管理
 */
export const AnswerFieldSyncProvider: React.FC<AnswerFieldSyncProviderProps> = ({ children }) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncInfo, setSyncInfo] = useState({
    lastSyncTime: 0,
    sourceIndex: null as number | null,
    targetCount: 0,
  });

  const updateSyncInfo = useCallback((info: Partial<typeof syncInfo>) => {
    setSyncInfo((prev) => ({ ...prev, ...info }));
  }, []);

  const contextValue: AnswerFieldSyncContextType = {
    isSyncing,
    setIsSyncing,
    syncInfo,
    updateSyncInfo,
  };

  return (
    <AnswerFieldSyncContext.Provider value={contextValue}>
      {children}
    </AnswerFieldSyncContext.Provider>
  );
};

/**
 * 使用答案字段同步Context的Hook
 * @returns 同步状态和控制方法
 * @throws 如果在Provider外部使用会抛出错误
 */
export const useAnswerFieldSyncContext = (): AnswerFieldSyncContextType => {
  const context = useContext(AnswerFieldSyncContext);

  if (context === undefined) {
    throw new Error(
      'useAnswerFieldSyncContext must be used within an AnswerFieldSyncProvider. ' +
        'Please wrap your component with <AnswerFieldSyncProvider>.',
    );
  }

  return context;
};
