/**
 * 下载文件工具函数
 * @param blob 二进制数据
 * @param fileName 文件名
 */
export function downloadFile(blob: Blob, fileName: string): void {
  // 创建一个临时的URL对象
  const url = window.URL.createObjectURL(blob);
  
  // 创建一个a标签
  const link = document.createElement("a");
  link.href = url;
  link.download = fileName;
  
  // 添加到DOM中并触发点击
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}
