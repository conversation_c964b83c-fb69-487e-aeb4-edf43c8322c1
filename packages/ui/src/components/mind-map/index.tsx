import {
  addEdge,
  Controls,
  EdgeTypes,
  NodeTypes,
  OnConnect,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
  type Edge,
  type Node,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useCallback } from "react";
import CourseNode from "./course-node";
import { MindMapEdge } from "./edge";
import KnowledgePointNode from "./knowledge-point-node";
import WidgetNode from "./widget-node";
const nodeDefaults = {
  sourcePosition: Position.Right,
  targetPosition: Position.Left,
};

const nodeTypes = {
  course: CourseNode,
  knowledgePoint: KnowledgePointNode,
  widget: WidgetNode,
};

const edgeTypes = {
  mindMap: MindMapEdge,
};

const initialNodes: Node[] = [
  {
    id: "A",
    type: "course",
    position: { x: 150, y: 150 },
    data: { label: "集合及其表示方法" },
    ...nodeDefaults,
  },
  {
    id: "B",
    type: "knowledgePoint",
    position: { x: 450, y: 0 },
    data: { label: "课程引入" },
    ...nodeDefaults,
  },
  {
    id: "C",
    type: "knowledgePoint",
    position: { x: 450, y: 150 },
    data: { label: "集合及其表示方法-课程讲解" },
    ...nodeDefaults,
  },
  {
    id: "D",
    type: "knowledgePoint",
    position: { x: 450, y: 300 },
    data: { label: "落实检测练习（固定题目）" },
    ...nodeDefaults,
  },
  {
    id: "C1",
    type: "widget",
    position: { x: 750, y: 0 },
    data: { label: "元素与集合的含义" },
    ...nodeDefaults,
  },
  {
    id: "C2",
    type: "widget",
    position: { x: 750, y: 150 },
    data: { label: "元素与集合的关系" },
    ...nodeDefaults,
  },
  {
    id: "C3",
    type: "widget",
    position: { x: 750, y: 300 },
    data: { label: "集合的表示方法" },
    ...nodeDefaults,
  },
];

const initialEdges: Edge[] = [
  {
    id: "A-B",
    type: "mindMap",
    source: "A",
    target: "B",
  },
  {
    id: "A-C",
    type: "mindMap",
    source: "A",
    target: "C",
  },
  {
    id: "A-D",
    type: "mindMap",
    source: "A",
    target: "D",
  },
  {
    id: "C-C1",
    type: "mindMap",
    source: "C",
    target: "C1",
  },
  {
    id: "C-C2",
    type: "mindMap",
    source: "C",
    target: "C2",
  },
  {
    id: "C-C3",
    type: "mindMap",
    source: "C",
    target: "C3",
  },
];

const ColorModeFlow = () => {
  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect: OnConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  return (
    <ReactFlow
      nodeTypes={nodeTypes as unknown as NodeTypes}
      edgeTypes={edgeTypes as unknown as EdgeTypes}
      nodes={nodes}
      edges={edges}
      onConnect={onConnect}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      fitView
      // className="text-transparent"
    >
      <Controls />
    </ReactFlow>
  );
};

export const MindMap = () => {
  return (
    <div className="h-full w-full">
      <ColorModeFlow />
    </div>
  );
};
