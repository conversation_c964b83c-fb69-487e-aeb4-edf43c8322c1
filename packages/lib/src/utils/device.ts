import JSBridgeSchema from "../types/jsbridge";
import bridge from "./jsbridge";

export function getAndroidBridgeAvailable() {
  return bridge.isAndroidBridgeAvailable();
}

export function getDeviceId() {
  try {
    const deviceInfo =
      bridge.invokeSync<JSBridgeSchema.DeviceInfo>("getDeviceInfo");
    if (!deviceInfo) {
      return "";
    }
    return deviceInfo.deviceId;
  } catch (error) {
    console.error(error);
    return "";
  }
}

export function getDeviceInfo() {
  try {
    const deviceInfo =
      bridge.invokeSync<JSBridgeSchema.DeviceInfo>("getDeviceInfo");

    return deviceInfo;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getStatusBarHeight() {
  try {
    const deviceInfo =
      bridge.invokeSync<JSBridgeSchema.DeviceInfo>("getDeviceInfo");
    const statusBarHeight =
      deviceInfo?.statusBarHeight && deviceInfo.deviceScreenDensity
        ? deviceInfo.statusBarHeight / deviceInfo.deviceScreenDensity
        : 0;
    return statusBarHeight;
  } catch (error) {
    console.error(error);
    return 0;
  }
}

export function getScreenSize() {
  try {
    const deviceInfo =
      bridge.invokeSync<JSBridgeSchema.DeviceInfo>("getDeviceInfo");
    if (!deviceInfo) {
      return null;
    }
    return {
      width: Math.floor(
        deviceInfo.fullScreenWidth / deviceInfo.deviceScreenDensity
      ),
      height: Math.ceil(
        deviceInfo.fullScreenHeight / deviceInfo.deviceScreenDensity
      ),
      statusBarHeight: Math.floor(
        deviceInfo.statusBarHeight / deviceInfo.deviceScreenDensity
      ),
    };
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getAppInfo() {
  try {
    const appInfo = bridge.invokeSync<JSBridgeSchema.AppInfo>("getAppInfo");
    return appInfo;
  } catch (error) {
    console.warn(error);
    return null;
  }
}

export function pickImages(imageNumber: number) {
  try {
    return bridge.invokeAsync<JSBridgeSchema.PickImagesResult>("pickImages", {
      imageNumber,
    });
  } catch (error) {
    console.warn(error);
    return null;
  }
}

export function openScheme(payload: {
  url: string;
  title_bar?: 0 | 1;
  status_bar_color?: string;
  full_screen?: 0 | 1;
  status_bar_dark_font?: boolean;
}) {
  try {
    if (getAndroidBridgeAvailable()) {
      const queryString = Object.entries(payload)
        .filter(([_, value]) => value !== undefined) // 过滤掉 undefined
        .map(
          ([key, value]) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        )
        .join("&");

      return bridge.invokeAsync("openScheme", {
        uri: `xlx://router/vessel/webview?${queryString}`,
      });
    }

    window.open(payload.url, "_blank");
  } catch (error) {
    console.warn(error);
    return null;
  }
}

/**
 * 控制状态栏
 *
 * @typedef {Object} StatusBarEvent
 * @property {('setStatusBarVisibility'|'setStatusBarColor'|'setStatusBarTextColor')} eventType 具体的行为：
 *   - setStatusBarVisibility: 设置状态栏显示隐藏
 *   - setStatusBarColor: 设置状态栏颜色
 *   - setStatusBarTextColor: 设置状态栏文字颜色
 * @property {boolean} [isVisible] 状态栏显隐，true 显示，false 隐藏（仅 eventType 为 setStatusBarVisibility 时）
 * @property {string} [color] 状态栏颜色，16 进制色值字符串（仅 eventType 为 setStatusBarColor 时）
 * @property {boolean} [isDark] 文字是否为深色，true 深色，false 浅色（仅 eventType 为 setStatusBarTextColor 时）
 */
export function controlStatusBar(payload: {
  eventType:
  | "setStatusBarVisibility"
  | "setStatusBarStyle"
  | "setStatusBarColor";
  isVisible?: boolean;
  color?: string;
  isDark?: boolean;
}) {
  try {
    bridge.invokeSync("controlStatusBar", payload);
  } catch (error) {
    console.warn(error);
  }
}
