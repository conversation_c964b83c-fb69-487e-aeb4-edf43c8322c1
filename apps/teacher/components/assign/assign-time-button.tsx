import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { formatTime } from "@/app/assign/[subjectKey]/course/utils";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import { useClickAway } from "ahooks";
import { CircleHelp } from "lucide-react";
import { useRef } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./assign-tooltip";
interface AssignTimeButtonProps {
  style?: React.CSSProperties;
  content?: string;
  className?: string;
}

export function AssignTimeButton({
  style = {},
  className = "",
}: AssignTimeButtonProps) {
  const { totalPracticeTime, totalAiCourseTime, totalClassTimes } =
    useAssignCourseContext();
  const tipOpen = useSignal(false);
  const ref = useRef(null);
  useClickAway(() => {
    tipOpen.value = false;
  }, ref);

  return (
    <div
      className={cn(
        "text-gray-3 mr-[-4] inline-flex h-8 items-center whitespace-nowrap rounded-full bg-slate-100 px-3 text-xs font-normal leading-tight outline-1 outline-offset-[-0.50px] outline-slate-200",
        className
      )}
      style={style}
    >
      <div className="flex items-center gap-0.5 pr-2">
        <span className="leading-[normal]">预估时长</span>
        <TooltipProvider>
          <Tooltip open={tipOpen.value}>
            <TooltipTrigger asChild>
              <CircleHelp
                ref={ref}
                className="size-2.5 text-[#8F94AB]"
                onClick={() => (tipOpen.value = !tipOpen.value)}
              />
            </TooltipTrigger>
            <TooltipContent className="bg-slate-600" arrowPadding={10}>
              <div className="px-4 py-3">
                <div className="text-fill-fill-white text-sm font-medium leading-tight">
                  预估学习时长是系统基于你选取的内容，结合学生的
                  <br />
                  历史学习数据计算得出
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="w-0.25 h-full bg-slate-200"></div>

      {!totalPracticeTime.value && !totalPracticeTime.value ? (
        <div className="text-line-3 pl-3 text-[.5rem] leading-[normal]">|</div>
      ) : (
        <div className="text-gray-2 pl-3 leading-[normal]">
          {formatTime(totalPracticeTime.value + totalAiCourseTime.value)}（约
          {totalClassTimes.value}课时）
        </div>
      )}
    </div>
  );
}
