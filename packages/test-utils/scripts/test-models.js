#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
};

const printInfo = (message) => console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
const printSuccess = (message) => console.log(`${colors.green}✅ ${message}${colors.reset}`);
const printWarning = (message) => console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
const printError = (message) => console.error(`${colors.red}❌ ${message}${colors.reset}`);

// 表格输出函数
function printTable(data, title) {
  console.log(`\n${colors.blue}📊 ${title}${colors.reset}`);
  console.log('━'.repeat(80));
  
  if (data.length === 0) {
    console.log('   无数据');
    return;
  }
  
  // 计算列宽
  const headers = Object.keys(data[0]);
  const colWidths = headers.map(header => {
    const maxLength = Math.max(
      header.length,
      ...data.map(row => String(row[header] || '').length)
    );
    return Math.min(maxLength, 30); // 限制最大宽度
  });
  
  // 打印表头
  const headerRow = headers.map((header, i) => 
    header.padEnd(colWidths[i])
  ).join(' │ ');
  console.log(`${colors.yellow}${headerRow}${colors.reset}`);
  console.log(headers.map((_, i) => '─'.repeat(colWidths[i])).join('─┼─'));
  
  // 打印数据行
  data.forEach(row => {
    const dataRow = headers.map((header, i) => {
      const value = String(row[header] || '');
      const truncated = value.length > 30 ? value.substring(0, 27) + '...' : value;
      return truncated.padEnd(colWidths[i]);
    }).join(' │ ');
    console.log(dataRow);
  });
  
  console.log('━'.repeat(80));
}

// 解析测试结果
function parseTestResults(output) {
  const results = {
    summary: {},
    files: [],
    coverage: null
  };
  
  try {
    const lines = output.split('\n');
    
    // 解析测试文件和结果
    lines.forEach(line => {
      // 匹配测试文件行：✓ app/models/exercise-session/__tests__/transformers.test.ts  (7 tests) 5ms
      const testFileMatch = line.match(/^\s*[✓✗]\s+(.+?\.test\.ts)\s+\((\d+)\s+tests?\)\s*([\d.]+ms)?/);
      if (testFileMatch) {
        const [, filePath, testCount, duration] = testFileMatch;
        const status = line.includes('✓') ? '✅ 通过' : '❌ 失败';
        const fileName = filePath.split('/').pop().replace('.test.ts', '');
        
        results.files.push({
          '测试文件': fileName,
          '路径': filePath.replace('app/models/', ''),
          '测试数': testCount,
          '状态': status,
          '耗时': duration || '-'
        });
      }
      
      // 也匹配旧格式：✓ app/models/exercise-session/__tests__/transformers.test.ts (7)
      const oldFormatMatch = line.match(/^\s*[✓✗]\s+(.+?\.test\.ts)\s+\((\d+)\)(?!\s+tests)/);
      if (oldFormatMatch && !testFileMatch) {
        const [, filePath, testCount] = oldFormatMatch;
        const status = line.includes('✓') ? '✅ 通过' : '❌ 失败';
        const fileName = filePath.split('/').pop().replace('.test.ts', '');
        
        results.files.push({
          '测试文件': fileName,
          '路径': filePath.replace('app/models/', ''),
          '测试数': testCount,
          '状态': status,
          '耗时': '-'
        });
      }
      
      // 解析汇总信息
      const summaryMatch = line.match(/Test Files\s+(\d+)\s+passed\s+\((\d+)\)/);
      if (summaryMatch) {
        results.summary['测试文件'] = summaryMatch[1];
        results.summary['总文件数'] = summaryMatch[2];
      }
      
      const testsMatch = line.match(/Tests\s+(\d+)\s+passed\s+\((\d+)\)/);
      if (testsMatch) {
        results.summary['通过测试'] = testsMatch[1];
        results.summary['总测试数'] = testsMatch[2];
      }
      
      const durationMatch = line.match(/Duration\s+([\d.]+ms)/);
      if (durationMatch) {
        results.summary['执行时间'] = durationMatch[1];
      }
    });
    
    // 如果没有找到文件信息但有汇总信息，创建一个汇总行
    if (results.files.length === 0 && Object.keys(results.summary).length > 0) {
      results.files.push({
        '测试文件': 'Model 层汇总',
        '路径': '所有测试文件',
        '测试数': results.summary['通过测试'] || '0',
        '状态': '✅ 通过',
        '耗时': results.summary['执行时间'] || '-'
      });
    }
    
  } catch (error) {
    printWarning(`解析测试结果时出错: ${error.message}`);
  }
  
  return results;
}

function parseArgs(argv) {
  const args = { coverage: false };
  let i = 0;
  
  while (i < argv.length) {
    const arg = argv[i];
    
    // 确保 arg 是字符串类型
    if (!arg) {
      i++;
      continue;
    }
    
    if (arg.startsWith('-F=') || arg.startsWith('--filter=')) {
      args.app = arg.split('=')[1];
    } else if (arg === '--coverage') {
      args.coverage = true;
    } else if (arg === '-h' || arg === '--help') {
      args.showHelp = true;
      break;
    } else if (!arg.startsWith('-')) {
      const nextArg = argv[i + 1];
      if (!args.app && i === 0 && argv.length > 1 && (!nextArg || !nextArg.startsWith('-'))) {
        args.app = arg;
        if (nextArg && !nextArg.startsWith('-') && nextArg !== '--coverage') {
          args.modelName = nextArg;
          i++;
        }
      } else if (args.app && !args.modelName) {
        args.modelName = arg;
      } else if (!args.app) {
        args.app = arg;
      }
    } else {
      printError(`未知参数: ${arg}`);
      args.showHelp = true;
      break;
    }
    i++;
  }
  
  if (!args.app && !args.showHelp) {
    printWarning("未指定应用，将显示帮助信息。");
    args.showHelp = true;
  }
  
  return args;
}

function showHelp(projectRoot) {
  printInfo("Model 层测试脚本 (JavaScript Version)");
  console.log("");
  console.log("用法:");
  console.log("  🚀 pnpm 方式（推荐，支持直接参数）：");
  console.log("    pnpm test:models -F=<app>                           # 测试指定应用的所有 Model");
  console.log("    pnpm test:models -F=<app> <model-name>              # 测试指定 Model");
  console.log("    pnpm test:models -F=<app> --coverage                # 运行覆盖率测试");
  console.log("");
  console.log("示例:");
  console.log("  pnpm test:models -F=stu                             # 🚀 最简洁：测试 stu 应用所有 Model");
  console.log("  pnpm test:models -F=stu exercise-session              # 🚀 最简洁：测试 exercise-session Model");
  console.log("  pnpm test:models -F=stu --coverage                 # 🚀 最简洁：运行覆盖率测试");
  console.log("");
  console.log(`当前工作目录: ${process.cwd()}`);
  console.log(`项目根目录: ${projectRoot}`);
}

function main() {
  const projectRoot = path.resolve(__dirname, '../../..');
  const parsedArgs = parseArgs(process.argv.slice(2));

  if (parsedArgs.showHelp || !parsedArgs.app) {
    showHelp(projectRoot);
    process.exit(parsedArgs.showHelp && !parsedArgs.app ? 0 : 1);
  }

  const { app, modelName, coverage } = parsedArgs;

  if (!app) {
    printError("错误：必须通过 -F=<app> 指定应用名称。");
    showHelp(projectRoot);
    process.exit(1);
  }

  const appDir = path.join(projectRoot, 'apps', app);

  if (!fs.existsSync(appDir)) {
    printError(`应用目录 ${appDir} 不存在`);
    printInfo("请检查应用名称是否正确，可用的应用：");
    try {
      const appsRoot = path.join(projectRoot, 'apps');
      if (fs.existsSync(appsRoot)) {
        fs.readdirSync(appsRoot, { withFileTypes: true })
          .filter(dirent => dirent.isDirectory())
          .map(dirent => dirent.name)
          .slice(0, 10)
          .forEach(name => console.log(`  ${name}`));
      }
    } catch(e) {
      printWarning("无法列出应用目录。");
    }
    process.exit(1);
  }

  const vitestConfigPath = path.join(appDir, 'vitest.config.ts');
  if (!fs.existsSync(vitestConfigPath)) {
    printError(`${vitestConfigPath} 不存在，请先为应用 ${app} 配置 Vitest`);
    process.exit(1);
  }

  printInfo(`开始测试 ${app} 应用的 Model 层...`);
  printInfo(`项目根目录: ${projectRoot}`);
  printInfo(`应用目录: ${appDir}`);

  const originalCwd = process.cwd();
  process.chdir(appDir);

  let testCommand = 'npm run test --';
  const targetPath = 'app/models';

  let testOutput = '';
  const startTime = Date.now();
  
  try {
    if (coverage) {
      printInfo("运行覆盖率测试...");
      const coverageScript = `npm run test:coverage -- ${targetPath}`;
      printInfo(`执行: ${coverageScript} (在 ${appDir} 目录)`);
      testOutput = execSync(coverageScript, { encoding: 'utf8' });
      console.log(testOutput);
    } else if (modelName) {
      const modelPath = path.join(targetPath, modelName);
      const modelFullPath = path.join(appDir, modelPath);

      if (!fs.existsSync(modelFullPath)) {
        printError(`Model 目录 ${modelPath} (在 ${appDir}) 不存在`);
        try {
          const modelsRoot = path.join(appDir, targetPath);
          if (fs.existsSync(modelsRoot)) {
            printInfo("可用的 Model 目录：");
            fs.readdirSync(modelsRoot, { withFileTypes: true })
              .filter(dirent => dirent.isDirectory())
              .map(dirent => dirent.name)
              .slice(0, 10)
              .forEach(name => console.log(`  ${name}`));
          }
        } catch(e) {
          printWarning("无法列出 Model 目录。");
        }
        process.exit(1);
      }
      printInfo(`测试 ${modelName} Model...`);
      testCommand += ` ${modelPath}`;
      printInfo(`执行: ${testCommand} (在 ${appDir} 目录)`);
      testOutput = execSync(testCommand, { encoding: 'utf8' });
      console.log(testOutput);
    } else {
      printInfo("测试所有 Model...");
      testCommand += ` ${targetPath}`;
      printInfo(`执行: ${testCommand} (在 ${appDir} 目录)`);
      testOutput = execSync(testCommand, { encoding: 'utf8' });
      console.log(testOutput);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // 解析并显示测试结果
    const results = parseTestResults(testOutput);
    
    // 如果有测试文件结果，显示详细表格
    if (results.files.length > 0) {
      printTable(results.files, `测试结果详情 - ${app.toUpperCase()} 应用`);
    }
    
    // 显示汇总信息
    const summaryData = [{
      '应用': app.toUpperCase(),
      '测试范围': modelName || '所有 Model',
      '文件数': results.summary['测试文件'] || results.files.length,
      '测试数': results.summary['通过测试'] || results.files.reduce((sum, f) => sum + parseInt(f['测试数'] || '0'), 0),
      '状态': '✅ 通过',
      '总耗时': `${totalTime}ms`,
      '平均耗时': results.summary['执行时间'] || `${Math.round(totalTime/Math.max(1, results.files.length))}ms`
    }];
    
    printTable(summaryData, '测试执行汇总');
    
    printSuccess("🎉 所有测试通过！");
    
  } catch (error) {
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // 显示失败汇总
    const failureSummary = [{
      '应用': app.toUpperCase(),
      '测试范围': modelName || '所有 Model', 
      '状态': '❌ 失败',
      '总耗时': `${totalTime}ms`,
      '错误': error.message.split('\n')[0].substring(0, 50) + '...'
    }];
    
    printTable(failureSummary, '测试执行汇总');
    printError("❌ 测试执行失败，请检查错误信息。");
    process.exit(1);
  } finally {
    process.chdir(originalCwd);
  }
  
  printInfo("测试完成");
}

main();