export interface RichTextViewProps {
  htmlContent: string;
  questionId?: string;
  ossHost?: string;
  resourceApiHost?: string;
  className?: string;
}
export interface QuestionResourceRequestParams {
  questionId: string;
  resourceFileName: string;
}

export interface QuestionResourceResponse {
  code: number;
  message: string;
  data: {
    resourceData: string;
    resourceType: number;
  };
}
