/**
 * 生成 UUID 的辅助函数
 */
const generateUUID = () => {
  return performance.now().toString(36).replace('.', '') + Math.random().toString(36).slice(2);
};

/**
 * 获取题干中填空点的 fill-uuid 数组
 * @param questionStem 题干内容
 * @returns 填空点的 fill-uuid 数组
 */
export const getQuestionStemFillPointUuids = (questionStem: string) => {
  const div = document.createElement('div');
  div.innerHTML = questionStem;
  const blanks = div.querySelectorAll(
    '[data-tiptype="question-blank_filling"]',
  ) as NodeListOf<HTMLElement>;

  return Array.from(blanks).map((blank) => {
    let uuid = blank.getAttribute('data-fill-uuid');
    // 如果UUID为空或null，生成一个新的UUID并设置到元素上
    if (!uuid) {
      uuid = generateUUID();
      blank.setAttribute('data-fill-uuid', uuid);
    }

    return uuid;
  });
};

interface UuidChange {
  uuid: string;
  index: number;
}

interface UuidChanges {
  added: UuidChange[];
  removed: UuidChange[];
}

/**
 * 比较两个 uuid 数组，找出新增和删除的项及其位置
 * @param currentUuids 当前的 uuid 数组
 * @param previousUuids 之前的 uuid 数组
 * @returns 包含新增和删除项的对象
 */
export const compareUuidArrays = (currentUuids: string[], previousUuids: string[]): UuidChanges => {
  const added: UuidChange[] = [];
  const removed: UuidChange[] = [];

  // 找到新增的项
  currentUuids.forEach((uuid, index) => {
    if (!previousUuids.includes(uuid)) {
      added.push({ uuid, index });
    }
  });

  // 找到删除的项
  previousUuids.forEach((uuid, index) => {
    if (!currentUuids.includes(uuid)) {
      removed.push({ uuid, index });
    }
  });

  return { added, removed };
};
