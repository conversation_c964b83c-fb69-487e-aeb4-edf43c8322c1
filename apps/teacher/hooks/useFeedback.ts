import { FEED<PERSON>CK_TYPE } from "@/enums";
import type { Result } from "ahooks/lib/useRequest/src/types";
import { createContext, useContext } from "react";

export interface FeedbackSubmitData {
  description: string;
  images: File[];
  feedbackType: FEEDBACK_TYPE;
  feedbackSubType?: string[];
}

interface FeedbackContextType {
  isOpen: boolean;
  submitRequest: Result<unknown, [FeedbackSubmitData]>;
  openFeedback: (payload: {
    feedbackType: FEEDBACK_TYPE,
    [prop: string]: unknown;
  }) => void;
  closeFeedback: () => void;
}

export const FeedbackContext = createContext<FeedbackContextType | undefined>(
  undefined
);

export const useFeedback = () => {
  const ctx = useContext(FeedbackContext);
  if (!ctx) throw new Error("useFeedback 必须在 FeedbackProvider 内使用");
  return ctx;
};
