import { Handle, Node, NodeProps, Position } from "@xyflow/react";
import { FC, memo } from "react";

type CourseNode = Node<{
  label: string;
}>;

const CourseNode: FC<NodeProps<CourseNode>> = ({ data }) => {
  return (
    <>
      <div className="bg-Semantic-Primary-4 outline-text-gray-1 flex w-auto items-center justify-start gap-2.5 rounded-full px-4 py-2 outline outline-1 outline-offset-[-0.50px]">
        <div className="text-text-gray-1 justify-start font-['Alibaba_PuHuiTi_3.0'] text-xl font-semibold leading-loose">
          {data.label}
        </div>
      </div>

      <Handle type="source" position={Position.Right} className="opacity-0" />
    </>
  );
};

export default memo(CourseNode);
