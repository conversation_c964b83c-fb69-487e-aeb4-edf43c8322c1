import { PlayerRef } from "@remotion/player";
import { MathJaxConfig } from "@repo/core/components/math-jax-config";
import { GuideProvider } from "@repo/core/guide/context/guide-context";
import {
  CommonSketchProps,
  GuideWidgetData
} from "@repo/core/types/data/widget-guide";
import { FC, RefAttributes } from "react";
import { GuideCore } from "./guide-core";

interface GuideViewProps extends RefAttributes<PlayerRef>, CommonSketchProps {
  data: GuideWidgetData;
  partIndex: number;
  totalPartsCount: number;
  hideSubtitles?: boolean;
};

export const GuideView: FC<GuideViewProps> = ({
  data,
  startFrame = 0,
  outFrame = 0,
  duration,
  onDrawChange,
  eraserMode,
  highlighter,
  partIndex,
  totalPartsCount,
  mode,
  hideSubtitles = false,
}) => {
  return (
    <MathJaxConfig>
      <GuideProvider
        data={data}
        index={partIndex}
        totalGuideCount={totalPartsCount}
      >
        <GuideCore
          mode={mode}
          startFrame={startFrame}
          outFrame={outFrame}
          duration={duration}
          onDrawChange={onDrawChange}
          eraserMode={eraserMode}
          highlighter={highlighter}
          hideSubtitles={hideSubtitles}
        />
      </GuideProvider>
    </MathJaxConfig>
  );
};
