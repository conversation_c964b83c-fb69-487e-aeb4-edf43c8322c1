import React, { memo } from 'react';

interface ConditionalDisplayProps {
  visible: boolean;
  children: React.ReactNode;
}

// 条件显示组件
const ConditionalDisplay: React.FC<ConditionalDisplayProps> = memo(({ visible, children }) => {
  return (
    <div
      style={{
        display: visible ? 'block' : 'none',
        opacity: visible ? 1 : 0,
        transition: 'opacity 0.2s ease-in-out',
      }}
    >
      {children}
    </div>
  );
});

export default ConditionalDisplay;
