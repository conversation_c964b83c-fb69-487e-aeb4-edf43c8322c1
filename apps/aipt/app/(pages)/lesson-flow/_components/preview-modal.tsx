import {
  Dialog,
  DialogContent
} from "@/app/components/ui/dialog";
import { CourseView } from "@/app/views/preview-course/course-view";

interface PreviewProps {
  open: boolean;
  onClose: () => void;
  lessonId: string;
}

const PreviewModal: React.FC<PreviewProps> = (props) => {
  const { open, onClose, lessonId } = props;
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={'!max-w-none w-8/10 h-8/10 bg-white'} >
        <div className="flex flex-col overflow-hidden">
          <div className="font-bold text-xl">课程预览</div>
          <div className="lesson_detail_container h-full w-full ">
            <CourseView knowledgeId={Number(lessonId)} courseProgressBarProps={{ className: 'w-50' }} />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PreviewModal;
