/*
 * @Description: 发送获取验证码
 * @param phoneNumber 手机号
 */
import { Button, message } from 'antd';
import { useMemo, useState } from 'react';
import Captcha from './captcha';

interface IProps {
  phoneNumber: string;
}
const SendVerificationCode = (props: IProps) => {
  const { phoneNumber } = props;
  const [countdown, setCountdown] = useState(0);
  const [hasRequestedCode, setHasRequestedCode] = useState(false);

  const isPhoneNumberValid = useMemo(() => {
    const phoneRegex = /^[1][3-9][0-9]{9}$/;
    return phoneRegex.test(phoneNumber);
  }, [phoneNumber]);
  const handleGetCaptcha = () => {
    if (!phoneNumber) {
      message.error('请输入手机号');
      return false;
    }
    if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
      message.error('手机号格式错误');
      return false;
    }

    // 验证通过，返回 true 让 Captcha 组件继续处理
    return true;
  };

  const handleSendCode = () => {
    setHasRequestedCode(true);
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  return (
    <>
      <Captcha
        canCaptcha={isPhoneNumberValid && countdown === 0}
        phoneNumber={phoneNumber || ''}
        onBizResultCallback={handleSendCode}
      >
        {countdown === 0 && isPhoneNumberValid === true && (
          <Button
            size="large"
            disabled={countdown > 0}
            onClick={handleGetCaptcha}
            className="ml-2 w-[120px]"
          >
            {hasRequestedCode ? '重新获取' : '获取验证码'}
          </Button>
        )}
        {countdown === 0 && isPhoneNumberValid === false && (
          <Button
            size="large"
            disabled={true}
            onClick={handleGetCaptcha}
            className="ml-2 w-[120px]"
          >
            {hasRequestedCode ? '重新获取' : '获取验证码'}
          </Button>
        )}
        {countdown > 0 && (
          <Button size="large" disabled={true} className="ml-2 w-[120px]">
            {`请${countdown}秒后再试`}
          </Button>
        )}
      </Captcha>
    </>
  );
};
export default SendVerificationCode;
