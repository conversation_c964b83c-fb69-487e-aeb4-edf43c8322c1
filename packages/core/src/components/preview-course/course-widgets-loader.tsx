"use client";

import Interactive from "@repo/core/components/interactive-component";
import { StudyType } from "@repo/core/enums";
import { ExerciseView } from "@repo/core/exercise/preview/view/exercise-view";
import { CourseWidgetSummary } from "@repo/core/types/data/course";
import { ComponentProps, FC, useEffect, useMemo, useState } from "react";
import { GuidePlayer } from "../player/guide-player";
import { useCourseViewContext } from "./course-view-context";
import { get } from "./others/fetcher";

const WidgetLoader: FC<
  {
    data?: CourseWidgetSummary;
  } & ComponentProps<"div">
> = ({ data }) => {
  useEffect(() => {
    if (data?.type === "exercise") {
      syncExerciseData();
    } else {
      syncGuideData();
    }
  }, [data?.index]);

  const { lessonId } = useCourseViewContext();
  if (!data) return <div className="h-full w-full bg-red-300">NO DATA</div>;
  const [widgetData, setWidgetData] = useState<any>(null);
  const syncGuideData = async () => {
    const res: any = await get(`/api/v1/lesson_widget/preview/info`, {
      query: { lessonId: String(lessonId), widgetIndex: String(data.index) },
    });
    res.data = JSON.parse(res.data);
    setWidgetData(res);
  };

  const syncExerciseData = async () => {
    const res: any = await get(`/api/v1/lesson_widget/question/get`, {
      query: { lessonId: String(lessonId), widgetIndex: String(data.index) },
    });

    let questionList = res.questionList.flat().filter((item: any) => item);
    questionList = questionList.map((item: any) => ({
      questionInfo: {
        ...item,
        questionContent: item.questionContent.questionStem,
      },
      // questionInfo: transformQuestionData2(item),
      // studentAnswer: item.studentAnswer,
      // answerStats: item.answerStats,
    }));
    // res.data = JSON.parse(res.data);
    setWidgetData({
      questionList,
      widgetType: "exercise",
    });
  };

  if (!widgetData) return null;

  return (
    <>
      {/* TODO: 文稿组件 , 需要从接口获取数据 */}
      {widgetData.widgetType === "guide" && (
        <div className="h-full">
          <GuidePlayer
            data={widgetData.data}
            width={1000}
            height={600}
            className="h-full w-full"
          />
        </div>
      )}
      {/* TODO: 接入练习组件 */}
      {widgetData.widgetType === "exercise" && (
        <div className="w-500 h-full" style={{ zoom: 0.73 }}>
          {widgetData.questionList.length > 0 ? (
            <ExerciseView
              className="h-full w-full"
              questionList={widgetData.questionList}
              // questionData={widgetData}
              // isPreviewMode={true}
              showExplanations={true}
              studyType={StudyType.REINFORCEMENT_EXERCISE}
              onBack={() => window.history.back()}
            />
          ) : (
            <div className="mt-6 flex h-full w-full items-center justify-center">
              暂无数据
            </div>
          )}
        </div>
      )}

      {/* TODO: 接入视频组件 */}
      {widgetData.widgetType === "video" && (
        <div className="h-full">
          <video
            src={widgetData?.data?.url}
            controls
            className="h-full w-full"
          ></video>
        </div>
      )}

      {widgetData.widgetType === "interactive" && (
        <div className="h-full overflow-y-auto">
          <Interactive
            url={widgetData.data.url}
            type={widgetData.data.typeName}
            onReport={(e) => {
              console.log(e);
            }}
          >
            <div>loading...</div>
          </Interactive>
        </div>
      )}
    </>
  );
};

const CourseWidgetsLoaderView: FC<ComponentProps<"div">> = () => {
  const { activeIndex, widgetSummarySequence } = useCourseViewContext();
  if (!widgetSummarySequence) return null;

  const activeWidget = useMemo(() => {
    return widgetSummarySequence[activeIndex];
  }, [activeIndex, widgetSummarySequence]);

  return (
    <div className="relative h-full w-full">
      <div className="relative h-full w-full">
        <WidgetLoader data={activeWidget} />
      </div>
    </div>
  );
};

export { CourseWidgetsLoaderView };
