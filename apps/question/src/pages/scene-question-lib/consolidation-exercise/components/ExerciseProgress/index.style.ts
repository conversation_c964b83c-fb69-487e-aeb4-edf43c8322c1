import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => {
  console.log('style token: ', token);
  return {
    progressInfoItem: {
      fontSize: 14,
      color: token.colorText,
      padding: '0 0 0 20px',
      position: 'relative',
      wordBreak: 'break-all',
      whiteSpace: 'nowrap',

      '&::before': {
        content: '""',
        position: 'absolute',
        left: 0,
        top: '50%',
        transform: 'translateY(-50%)',
        width: '10px',
        height: '10px',
        borderRadius: '50%',
        backgroundColor: token.colorPrimary,
      },

      '&.green': {
        '&::before': {
          backgroundColor: token.colorSuccess,
        },
      },

      '&.cyan': {
        '&::before': {
          backgroundColor: token.colorInfo,
        },
      },

      '&.blue': {
        '&::before': {
          backgroundColor: token.colorPrimary,
        },
      },
      '&.orange': {
        '&::before': {
          backgroundColor: token.colorWarning,
        },
      },

      '&.red': {
        '&::before': {
          backgroundColor: token.colorError,
        },
      },
    },
  };
});

export default useStyles;
