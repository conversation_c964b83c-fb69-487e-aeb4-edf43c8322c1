import fetcher from '@/services/fetcher';
import { useModel } from '@umijs/max';
import { Button, Flex, message, Modal, Space, Table, Tag, Tree } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import useSWR from 'swr';
import type { StaffInfo } from '../../types';
import StaffModal from './components/StaffModal';
import usePermissionData, { separateMenuNodes } from './hooks/usePermissionData';

const PermissionManagement: React.FC = () => {
  const { schoolInfo } = useModel('schoolModel');
  const { schoolId, hasButtonPermission } = schoolInfo || {};
  const [modalVisible, setModalVisible] = useState(false);
  const [editingStaff, setEditingStaff] = useState<StaffInfo | undefined>();

  console.log('schoolInfo ==> ', schoolInfo);

  // 使用自定义 hook 获取权限数据
  const { menus, loadBaseData, revokeStaffPermission, getMenuTree } = usePermissionData();

  // 获取学校管理员列表
  const {
    data: staffList = [],
    isLoading,
    mutate: refreshList,
  } = useSWR(
    schoolId ? `/api/v1/userSchool/listAllStaff?schoolID=${schoolId}` : null,
    fetcher<StaffInfo[]>,
    {
      revalidateOnFocus: false,
    },
  );

  // 加载基础菜单数据，仅在组件挂载时调用一次
  useEffect(() => {
    loadBaseData();
    // 空依赖数组确保只在挂载时执行一次
  }, []);

  // 刷新学校管理员列表
  useEffect(() => {
    if (schoolId) {
      refreshList();
    }
  }, [schoolId, refreshList]);

  // 处理编辑
  const handleEdit = (staff: StaffInfo) => {
    setEditingStaff({ ...staff });
    setModalVisible(true);
  };

  // 处理取消授权
  const handleRevoke = (staff: StaffInfo) => {
    if (!schoolId) return;

    Modal.confirm({
      title: '确认取消授权',
      content: `确定要取消 ${staff.userName} 的管理员权限吗？`,
      onOk: async () => {
        try {
          await revokeStaffPermission({
            schoolId: Number(schoolId),
            userId: staff.userId,
          });
          message.success('取消授权成功');
          refreshList();
        } catch (error: any) {
          message.error(error.message || '取消授权失败');
        }
      },
    });
  };

  const columns: ColumnsType<StaffInfo> = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
      render: (_, record: StaffInfo) =>
        record.label ? (
          <Flex align="center" gap={10}>
            {record.userName}
            <Tag color="#108ee9">{record.label}</Tag>
          </Flex>
        ) : (
          record.userName
        ),
    },
    {
      title: '手机号',
      dataIndex: 'userPhone',
      key: 'userPhone',
      width: 120,
    },
    {
      title: '权限范围',
      key: 'menuIds',
      width: 200,
      render: (_, record: StaffInfo) => {
        if (record.top) {
          return '全部';
        }
        // 使用共享 hook 生成菜单树
        const menuTreeData = getMenuTree(record.menuIds);
        const checkedMenuIds = separateMenuNodes(
          menus.filter((menu) => menu.platformName === '运营平台')[0]?.menus || [],
          record.menuIds,
        );
        // console.log(
        //   ` menus.filter(menu => menu.platformName === '运营平台')[0]?.menus `,
        //   menus.filter((menu) => menu.platformName === '运营平台')[0]?.menus,
        //   checkedMenuIds,
        // );

        return (
          <Tree
            defaultExpandAll
            showLine
            selectable={false}
            checkedKeys={{
              checked: checkedMenuIds.leafNodes,
              halfChecked: checkedMenuIds.parentNodes,
            }}
            treeData={menuTreeData}
          />
        );
      },
    },
    {
      title: '添加人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record: StaffInfo) => {
        if (hasButtonPermission !== 1) {
          return null;
        }
        if (record.top) {
          return null;
        }

        return (
          <Space size="middle">
            <Button type="link" className="!p-0" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Button type="link" danger className="!p-0" onClick={() => handleRevoke(record)}>
              取消授权
            </Button>
          </Space>
        );
      },
    },
  ];

  // 处理弹窗关闭
  const handleModalClose = () => {
    setModalVisible(false);
    setEditingStaff(undefined);
  };

  // 处理添加/编辑成功
  const handleSuccess = () => {
    handleModalClose();
    refreshList();
  };

  if (!schoolId) {
    return null;
  }

  return (
    <div className="p-3 bg-white">
      {hasButtonPermission === 1 && (
        <div className="flex justify-end">
          <Button type="primary" className="mb-4" onClick={() => setModalVisible(true)}>
            添加学校管理员
          </Button>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={staffList}
        loading={isLoading}
        rowKey="userId"
        pagination={false}
      />

      <StaffModal
        existUserIds={staffList?.map((item) => item.userId)}
        open={modalVisible}
        schoolId={Number(schoolId)}
        editingStaff={editingStaff}
        onCancel={handleModalClose}
        onSuccess={handleSuccess}
      />
    </div>
  );
};

export default PermissionManagement;
