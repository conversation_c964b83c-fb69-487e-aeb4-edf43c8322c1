declare namespace API {
  interface UploadHistoryListResponse {
    code: number;
    message: string;
    data: {
      list: UploadHistoryItem[];
      total: number;
      page: number;
      pageSize: number;
    };
  }
  interface UploadHistoryItem {
    id: number;
    taskId: string;
    taskType: string;
    fileName: string;
    fileUrl: string;
    uploadTime: number;
    status: string;
  }

  export interface OssPresignResponse {
    code: number;
    data: OssPresignData;
    msg: string;
    [property: string]: any;
  }

  export interface OssPresignData {
    businessType: string;
    contentType: string;
    expiration: string;
    method: string;
    objectKey: string;
    signedHeaders: OssPresignHeaders;
    url: string;
    userId: string;
    [property: string]: any;
  }

  export interface OssPresignHeaders {
    'Content-Type': string;
    [property: string]: any;
  }

  interface UploadPolicyRequestParams {
    phase: number;
    subject: number;
    fileName: string;
    generateChannel?: number; // 1 用户上传word， 2 用户上传图片
  }

  interface UploadPolicyResponse {
    code: number;
    data: UploadSignData;
    msg: string;
    [property: string]: any;
  }

  interface UploadSignData {
    presignUrl?: string;
    paperId: string;
    uploadUrl: string;
    formData: OssPrsignFormDataType;
    [property: string]: any;
  }

  interface OssPrsignFormDataType {
    policyToken: OssPolicyTokenType;
    [property: string]: any;
  }

  interface OssPolicyTokenType {
    dir: string;
    host: string;
    policy: string;
    security_token: string;
    signature: string;
    x_oss_credential: string;
    x_oss_date: string;
    x_oss_signature_version: string;
    [property: string]: any;
  }

  interface UploadPolicyData {
    presignUrl: string;
    paperId: string;
    [property: string]: any;
  }

  interface NoticeUploadResultRequestParams {
    paperId: string;
    result?: NoticeUploadResultType;
  }
  interface UploadPaperUrlRequestParams {
    resourceDownloadUrl: string;
    phase: number;
    subject: number;
  }

  interface NoticeUploadResultType {
    uploadStatus: number;
    [property: string]: any;
  }

  interface UploadWordListResponse {
    code: number;
    data: UploadWordListData;
    message: string;
    [property: string]: any;
  }

  interface UploadWordListData {
    list: UploadWordListItem[];
    total: number;
    page: number;
    pageSize: number;
    [property: string]: any;
  }

  interface UploadWordListItem {
    /**
     * 上传时间，毫秒级时间戳
     */
    createTime: number;
    paperId: string;
    paperName: string;
    paperStatus: number;
    /**
     * 试卷处理状态文案
     */
    paperStatusText: string;
    updateTime: number;
    auditTaskId: number;
    [property: string]: any;
  }

  interface ModifyQuestionData {
    questionId: string;
    questionVersionId: number;
    questionType?: number;
    questionAnswerMode: number;
    questionStatus: number;
    questionYear?: number;
    questionTopic?: string;
    questionDifficult: number;
    baseTreeNodeIds: number[];
    questionContent: {
      questionOrder?: number;
      questionScore?: number;
      questionOriginName?: string;
      questionStem: string;
      questionOptionList: {
        optionKey: string;
        optionVal: string;
      }[];
    };
    questionAnswer?: {
      answerOptionList: {
        optionKey?: string;
        optionVal?: string;
      }[];
    };
    questionExplanation: string;
    subQuestionList: ModifyQuestionData[];
    attachFileList: {
      fileName: string;
      fileType: number;
      fileData: string;
      ossPath: string;
    }[];
  }
}
