import CheckIcon from "@/public/assign/check.svg";
import { cn } from "@/utils/utils";

interface AssignCheckboxButtonProps {
  style?: React.CSSProperties;
  content: string;
  className?: string;
  checked?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

export function AssignCheckboxButton({
  content,
  style = {},
  className = "",
  checked = false,
  disabled = false,
  onClick,
}: AssignCheckboxButtonProps) {
  const containerClassName = checked
    ? "bg-indigo-50 outline-indigo-400"
    : "bg-white outline-line-3";
  const iconContainerClassName = checked ? "bg-indigo-500" : "bg-slate-200";
  return (
    <div
      className={cn(
        "hover:bg-fill-gray-1 group relative inline-flex h-9 cursor-pointer select-none items-center justify-center overflow-hidden rounded-md px-4 py-[5px] outline-1 outline-offset-[-1px] transition-all",
        containerClassName,
        disabled &&
          "bg-fill-fill-gray-1 outline-line-3 hover:bg-fill-fill-gray-1 hover:outline-line-3 cursor-not-allowed",
        className
      )}
      style={style}
      onClick={() => !disabled && onClick?.()}
    >
      <div
        className={cn(
          "justify-start text-center text-sm font-medium leading-tight text-slate-600",
          disabled && "text-gray-5"
        )}
      >
        {content}
      </div>

      <div
        className={cn(
          "absolute bottom-0 right-0 flex h-3 w-3 items-center justify-center rounded-tl-md transition-all",
          iconContainerClassName,
          disabled && "group-hover:bg-slate-200"
        )}
      >
        <CheckIcon className="h-2 w-2" />
      </div>
    </div>
  );
}
