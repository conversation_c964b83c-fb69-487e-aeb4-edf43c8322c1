import React from "react";

export type NotionTypes =
  | "underline"
  | "box"
  | "circle"
  | "highlight"
  | "strike-through"
  | "crossed-off"
  | "bracket";

export const DefaultNotionColorMap: Record<NotionTypes, string> = {
  underline: "#33AAFF99",
  box: "#44CD2999",
  circle: "#FF4E3399",
  highlight: "#FFBF00B4",
  "strike-through": "#80008099",
  "crossed-off": "#80008099",
  bracket: "#80008099",
};

type brackets = "left" | "right" | "top" | "bottom";

interface RoughNotionProperties {
  animate?: boolean;
  animationDelay?: number;
  animationDuration?: number;
  brackets?: brackets | brackets[];
  color?: string;
  iterations?: number;
  multiline?: boolean;
  order?: number | string;
  padding?: number | [number, number, number, number] | [number, number];
  strokeWidth?: number;
}

export interface RoughNotionProps extends RoughNotionProperties {
  children: React.ReactNode;
  customElement?: string;
  getAnnotationObject?: (annotation: Annotation) => void;
  show?: boolean;
  type: NotionTypes;
  isLatex?: boolean;
}

export interface Annotation extends RoughNotionProperties {
  isShowing(): boolean;
  show(): void;
  hide(): void;
  remove(): void;
}
