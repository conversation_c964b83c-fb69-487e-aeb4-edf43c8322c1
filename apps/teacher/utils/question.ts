import { AssignResourceTypeEnum } from "@/configs/assign";
import {
  deleteTeacherTempSelection,
  getTeacherTempSelectList
} from "@/services";
import { getQuestionListByIds } from "@/services/assign-homework";
import { AssignCourseResourceItem } from "@/types/assign/course";
import { QaContentType } from "@repo/core/views/tch-question-view";
import to from "await-to-js";
import {
  QuestionFilterEnumDatasMap,
  QuestionType,
} from "../types/assign/assign-homework";

// ??：暂时不理解这个函数存在的意义
export const convertQuestion = (
  question: QuestionType,
  enumMap: QuestionFilterEnumDatasMap
): QaContentType => {
  const {
    questionId,
    questionContent,
    provinceCode,
    questionDifficult,
    questionType,
    questionYear,
    questionTags,
  } = question;
  const { questionOptionList, questionOriginName } = questionContent;
  const { questionTypeList, questionDifficultList, yearList, provinceList } =
    enumMap;
  const year = yearList.get(questionYear)?.nameZh ?? "";
  const yearStr = year === "更早" ? "更早" : year ? `${year}年` : "";
  const province = provinceList.get(provinceCode)?.nameZh ?? "";
  const type = questionTypeList.get(questionType)?.nameZh ?? "";
  const difficult = questionDifficultList.get(questionDifficult)?.nameZh ?? "";
  const infoTag = [yearStr, province, questionOriginName ?? ""]
    .filter((item) => !!item)
    .join("·");
  // 共同的字段映射逻辑
  const commonFields = {
    questionTags: questionTags ?? [infoTag, type, difficult],
    questionId,
    questionVersionId: question.questionVersionId,
    answer: JSON.stringify(question.questionAnswer),
    questionAnswer: question.questionAnswer,
    answerExplain: question.questionExplanation,
    avgCostTime: 0,
    content: question.questionContent.questionStem,
    questionType: question.questionType,
    questionDifficult: question.questionDifficult,
    resourceId: questionId,
    resourceType: AssignResourceTypeEnum.RESOURCE_QUESTION,
    questionAnswerMode: question.questionAnswerMode,
    // 将 questionOptionList 转换为 options 属性
    options: questionOptionList
      ? questionOptionList.map((opt) => ({
        key: opt.optionKey,
        content: opt.optionVal,
      }))
      : undefined,
    subQuestionList: question.subQuestionList?.map((subQuestion) =>
      convertQuestion(subQuestion, enumMap)
    ),
    subject: question.subject,
    phase: question.phase,
  };

  // 根据视图模式处理 answerDetails

  return {
    ...commonFields,
    answerDetails: [],
    answerCount: 0,
    incorrectCount: 0,
    correctRate: 0,
  };
};

export async function removeTempSelection(
  questionIds: string[],
  subject: number
) {
  const [err, res] = await to(
    deleteTeacherTempSelection({ questionIds, subject })
  );
  if (err) {
    console.error(err);
    return false;
  }
  console.log("删除问题到临时选题列表成功： ", res);
  return true;
}

export async function fetchHistorySelectionList(subject: number) {
  const [err, res] = await to(getTeacherTempSelectList(subject));
  if (err) {
    console.log("获取试题篮失败: ", err);
    return [];
  }
  const list = Array.isArray(res) ? res : [];
  if (list.length > 0) {
    const questionIds = list
      .filter(
        (item) => item.resourceType === AssignResourceTypeEnum.RESOURCE_QUESTION
      )
      .map((item) => item.resourceId);
    const [err, res] = await to(getQuestionListByIds({ questionIds }));
    if (err) {
      console.log("获取试题篮中的试题详情失败: ", err);
      return [];
    }
    return Array.isArray(res) ? res : [];
  }
  return [];
}

export async function fetchQuestionDetails(list: AssignCourseResourceItem[]) {
  const questionIds = list
    .filter(
      (item) => item.resourceType === AssignResourceTypeEnum.RESOURCE_QUESTION
    )
    .map((item) => item.resourceId);
  const [err, res] = await to(getQuestionListByIds({ questionIds }));
  if (err) {
    console.log("获取资源详情失败: ", err);
    return [];
  }
  return Array.isArray(res) ? res : [];
}
