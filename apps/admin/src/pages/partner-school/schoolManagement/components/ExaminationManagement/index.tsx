import SimpleUploadModal from '@/components/SimpleUploadModal';
import { get, getWithQuery, post } from '@/services/fetcher';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { FileDownloader } from '@repo/lib/utils/FileDownloader';
import { useModel, useSearchParams } from '@umijs/max';
import { Button, FormInstance, message, Popconfirm } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { ExaminationUploadRecordItem, ExaminationUploadRecordParams } from './type';

const ExaminationManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const schoolIdParam = searchParams.get('id');
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolIdParam ? Number(schoolIdParam) : schoolInfo?.schoolId || 0;
  const [modalVisible, setModalVisible] = useState(false);
  const actionRef = useRef<ActionType>(null);
  const formRef = useRef<FormInstance>(undefined);
  const [messageApi, messageContextHolder] = message.useMessage();
  const [downloading, setDownloading] = useState(false);
  const { trigger: mutateExaminationList } = useSWRMutation(
    '/api/v1/userSchool/examinationRecordList',
    getWithQuery<{
      list: ExaminationUploadRecordItem[];
      total: number;
      page: number;
      pageSize: number;
    }>,
  );
  const { trigger: downloadTemplate } = useSWRMutation(
    '/api/v1/userSchool/examinationRecordTpl/download',
    get<{ tplUrl: string; tplName: string }>,
  );
  const { trigger: deleteExaminationRecord } = useSWRMutation(
    '/api/v1/userSchool/deleteExaminationRecord',
    post,
  );
  const { trigger: createExaminationRecord, error: createExaminationRecordError } = useSWRMutation(
    '/api/v1/userSchool/createExaminationRecord',
    post<
      {
        id: number;
      },
      {
        objectKey: string;
        schoolId: number;
        fileName: string;
      }
    >,
  );
  const handleCloseModal = useCallback(() => {
    setModalVisible(false);
  }, []);
  const handleImportSuccess = useCallback(() => {
    setModalVisible(false);
    actionRef.current?.reload();
  }, []);

  const handleImportData = async (fileId: string, fileName: string) => {
    console.log('handleImportData fileId ===> ', fileId);
    await createExaminationRecord({
      objectKey: fileId,
      schoolId: schoolId,
      fileName,
    });
    if (createExaminationRecordError) {
      return {
        success: false,
        error: createExaminationRecordError.message,
      };
    }

    messageApi.success('上传成功');

    return {
      success: true,
    };
  };
  const handleDelete = async (record: ExaminationUploadRecordItem) => {
    console.log('record', record);
    try {
      await deleteExaminationRecord({
        id: record.id,
      });
      messageApi.success('删除成功');
    } catch (error) {
      console.log('error', error);
    } finally {
      actionRef.current?.reload();
    }
  };

  const handleRequestList = async (params: ExaminationUploadRecordParams) => {
    const res = await mutateExaminationList(params);
    if (!res) {
      return {
        data: [],
        success: true,
        total: 0,
      };
    }
    res.list = Array.isArray(res.list) ? res.list : [];
    if (res.list.length === 0 && params.page > 1) {
      return handleRequestList({
        ...params,
        page: params.page - 1,
      });
    }

    return {
      data: res.list,
      success: true,
      total: res.total || 0,
    };
  };

  const downloadTemplateHandler = async () => {
    if (downloading) {
      return;
    }
    setDownloading(true);
    try {
      const res = await downloadTemplate();
      if (!res) {
        return;
      }
      const { tplUrl, tplName } = res;
      await FileDownloader.downloadFromUrl(tplUrl, tplName);
      messageApi.success('下载成功');
    } catch (error) {
      messageApi.error('下载失败');
    } finally {
      setDownloading(false);
    }
  };

  const columns: ProColumns<ExaminationUploadRecordItem>[] = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      ellipsis: true,
      search: true,
      onCell: () => {
        return {
          style: {
            maxWidth: 400,
          },
        };
      },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 180,
      search: false,
    },
    {
      title: '操作人',
      dataIndex: 'uploader',
      width: 120,
      search: true,
    },

    {
      fixed: 'right',
      title: '操作',
      width: 100,
      valueType: 'option',
      render: (_: any, record: ExaminationUploadRecordItem) => {
        return [
          <Popconfirm
            key="status"
            title="确认操作"
            description={`确定要删除该文件吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button key="status" type="link" size="small">
              删除
            </Button>
          </Popconfirm>,
        ];
      },
    },
  ];
  return (
    <div className="">
      <ProTable<ExaminationUploadRecordItem, ExaminationUploadRecordParams>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        cardBordered
        scroll={{ x: 'max-content' }}
        request={async (params) => {
          const _params: ExaminationUploadRecordParams = {
            schoolId,
            fileName: params.fileName || '',
            uploader: params.uploader || '',
            page: params.current || 1,
            pageSize: params.pageSize || 10,
          };

          return await handleRequestList(_params);
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          span: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 6,
          },
        }}
        options={{
          reload: true,
          density: true,
          setting: true,
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          pageSizeOptions: [10, 20, 50, 100],
          showTotal: (total) => (
            <span>
              共 <a style={{ color: '#1890ff' }}>{total}</a> 条数据
            </span>
          ),
        }}
        dateFormatter="string"
        tableLayout="fixed"
        headerTitle="考情数据列表"
        toolBarRender={() => [
          <Button
            type="primary"
            key="download"
            onClick={downloadTemplateHandler}
            icon={<DownloadOutlined />}
          >
            {downloading ? '下载中...' : '下载考情模板'}
          </Button>,
          <Button
            type="primary"
            key="upload"
            onClick={() => {
              setModalVisible(true);
            }}
            icon={<UploadOutlined />}
          >
            上传考情数据
          </Button>,
        ]}
      />
      {messageContextHolder}
      <SimpleUploadModal
        acceptFileTypes={['.xlsx', '.xls']}
        visible={modalVisible}
        onClose={handleCloseModal}
        onSuccess={handleImportSuccess}
        title="上传考情数据"
        importData={handleImportData}
      />
    </div>
  );
};

export default ExaminationManagement;
