import { QuestionFilterSort, QuestionFilterSource } from "@/enums";

export const questionFilterSortOptions = [
  {
    nameEn: "createTime",
    nameZh: "按时间最新",
    value: QuestionFilterSort.CREATE_TIME,
  },
  {
    nameEn: "useCount",
    nameZh: "按时间最早",
    value: QuestionFilterSort.USE_COUNT,
  },
];

export const questionFilterSourceOptions = [
  {
    nameEn: "public",
    nameZh: "公共资源",
    value: QuestionFilterSource.PUBLIC,
  },
  {
    nameEn: "private",
    nameZh: "私有资源",
    value: QuestionFilterSource.PRIVATE,
  },
];

export const questionFilterExtraOptions = {
    sortList: questionFilterSortOptions,
    sourceList: questionFilterSourceOptions,
};

export const filterEnumFieldNames = {
    label: "nameZh",
    value: "value",
};


export const difficultColorScheme = {
  '简单': "#4DE03F",
  '较易': "#60B0FF",
  '一般': "#FCB363",
  '较难': "#FA5A57",
  '困难': "#E53E8C",
  '未知': "#A99FFF",
};
