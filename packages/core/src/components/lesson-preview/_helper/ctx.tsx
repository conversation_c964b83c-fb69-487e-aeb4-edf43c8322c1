import { createContext, useContext } from "react";

interface PreviewCtxProps {
  simulation: boolean;
  widgets: Array<any>;
  widget: any;
  loading: boolean;
  active: number;
  setActive: (active: number) => void;
}
type PreviewCtxProviderProps = PreviewCtxProps & { children: React.ReactNode };

// 默认值
const defaultPreviewCtx: PreviewCtxProps = {
  simulation: false,
  widgets: [],
  widget: {},
  active: 0,
  loading: false,
  setActive: () => { },
};

// 上下文
const PreviewCtx = createContext<PreviewCtxProps>(defaultPreviewCtx);
// 使用者：自定义hook
export const usePreviewCtx = () => useContext(PreviewCtx);
// 提供者
export const PreviewCtxProvider: React.FC<PreviewCtxProviderProps> = ({
  children,
  ...rest
}) => <PreviewCtx.Provider value={rest}>{children}</PreviewCtx.Provider>;
