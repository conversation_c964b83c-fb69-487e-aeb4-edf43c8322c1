import { useEffect } from 'react';
import { useQuestionCreate } from '../contexts/QuestionCreateContext';
import { transformQuestionDataToForm } from '../utils/dataTransform';

/**
 * 表单初始化Hook
 * 负责根据题目数据初始化表单字段
 */
export const useFormInitialization = () => {
  const { defaultBaseTreeNodeIds, form, questionList, activeQuestion } = useQuestionCreate();

  // 初始化单道题目的表单数据
  const initializeFormData = (questionData: any) => {
    if (!questionData) return;

    const formData = transformQuestionDataToForm(questionData);
    console.log('formData', formData);
    if (formData?.baseTreeNodeIds?.length === 0 && defaultBaseTreeNodeIds?.length > 0) {
      formData.baseTreeNodeIds = defaultBaseTreeNodeIds;
    }
    // 使用setFieldsValue来设置表单值
    form.setFieldsValue(formData);
  };

  // 当题目列表或当前激活题目改变时，重新初始化表单
  useEffect(() => {
    if (questionList.length > 0) {
      if (activeQuestion) {
        initializeFormData(activeQuestion);
      }
    } else {
      // 如果没有题目数据，清空表单
      form.resetFields();
    }
  }, [questionList, activeQuestion, form]);

  return {
    initializeFormData,
  };
};
