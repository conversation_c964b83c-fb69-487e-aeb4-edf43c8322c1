import { PlayerRef } from "@remotion/player";
import { useEffect, useRef } from "react";

export default function usePlayerPolyfill(playerRef: React.RefObject<PlayerRef | null>) {
    // 1.记录窗口隐藏时是否在播放
    const isPlayingBeforeWindowHidden = useRef(false);
    useEffect(() => {
        function handleWindowVisibilityChange() {
            if (document.visibilityState === "hidden") {
                if (!playerRef.current) return;

                isPlayingBeforeWindowHidden.current = playerRef.current.isPlaying();
                playerRef.current.pause();
                return;
            } else {
                if (isPlayingBeforeWindowHidden.current) {
                    playerRef.current?.play();
                    isPlayingBeforeWindowHidden.current = false;
                }
            }
        }

        window.addEventListener("visibilitychange", handleWindowVisibilityChange);

        return () => {
            window.removeEventListener("visibilitychange", handleWindowVisibilityChange);
        }
    }, [playerRef]);

    // 2.初始化音量
    useEffect(() => {
        if (playerRef.current) {
            playerRef.current.setVolume(1);
        }
    }, [playerRef]);
}
