import useEnumManagerMap from '@/hooks/useEnumManager';
import {
  getAuditTaskDetail,
  submitAuditTask,
  submitImportQuestionAudit,
} from '@/services/api/review';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { css } from '@emotion/css';
import { Button, Card, Flex, message, Modal, Space, Tag, Typography } from 'antd';
import { ButtonColorType } from 'antd/es/button';
import { to } from 'await-to-js';
import { produce } from 'immer';
import React, { useEffect, useRef, useState } from 'react';
import ImportQuestion from './components/ImportQuestion';
import RejectModal from './components/RejectModal';

const { Text, Paragraph } = Typography;

export type ImportQuestionReviewDetailProps = {
  onSubmit: () => void;
  onBack: () => void;
  canEdit: boolean;
  data: API.Review.AuditTaskItem;
};

const ImportQuestionReviewDetail: React.FC<ImportQuestionReviewDetailProps> = ({
  onSubmit,
  onBack,
  canEdit = false,
  data,
}) => {
  const [auditTaskName, setAuditTaskName] = useState(data.auditTaskName);
  const [auditStatus, setAuditStatus] = useState(data.auditStatus);
  const [questionList, setQuestionList] = useState<API.Review.ImportQuestionAudit['list']>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const activeQuestion = questionList[activeIndex];
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const { auditStatusList: auditStatusListEnumManager } = useEnumManagerMap();
  const pageNavRef = useRef<HTMLDivElement>(null);

  const fetchAuditTaskDetail = async () => {
    const [err, res] = await to(getAuditTaskDetail({ auditTaskId: data.auditTaskId }));

    if (err || res.code) {
      return;
    }

    setQuestionList(res.data.importQuestion?.list || []);
    setAuditTaskName(res.data.auditTaskName);
    setAuditStatus(res.data.auditStatus);
  };

  useEffect(() => {
    fetchAuditTaskDetail();
  }, []);

  const handleSubmit = () => {
    Modal.confirm({
      title: '确认提交',
      content: '是否确认提交审核结果？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const [err, ret] = await to(submitAuditTask({ auditTaskId: data.auditTaskId }));

        if (err || ret.code) {
          return;
        }

        onSubmit?.();
      },
    });
  };

  return (
    <Space direction="vertical" style={{ display: 'flex' }}>
      <Card>
        <Flex justify="space-between" align="center">
          <Space>
            <span
              style={{ cursor: 'pointer', fontWeight: 'normal', fontSize: 15 }}
              onClick={onBack}
            >
              <ArrowLeftOutlined />
              返回
            </span>

            <span style={{ fontSize: 18 }}>
              <span style={{ fontWeight: 'bold' }}>{auditTaskName} </span>
              【ID: {data.auditTaskId}】
            </span>
          </Space>
          {!canEdit && auditStatus === 2 && (
            <Paragraph
              style={{ margin: '0 12px' }}
              copyable={{ text: questionList.map((question) => question.questionId).join(',') }}
            >
              复制所有题目 ID
            </Paragraph>
          )}
        </Flex>
      </Card>

      {Boolean(questionList.length) && (
        <Card className={importQuestionReviewCls}>
          <div className="page-nav" ref={pageNavRef}>
            <div className="pages-container">
              <Flex wrap="nowrap">
                {questionList.map((question, index) => (
                  <Button
                    key={index}
                    variant={question.auditStatus > 1 ? 'solid' : 'outlined'}
                    color={buttonColorMap[question.auditStatus]}
                    onClick={() => setActiveIndex(index)}
                  >
                    <span
                      style={{
                        fontWeight: activeIndex === index ? 'bold' : 'normal',
                      }}
                    >
                      {activeIndex === index ? '✓ ' : ''}
                      {index + 1}
                    </span>
                  </Button>
                ))}
              </Flex>
            </div>

            {canEdit ? (
              <Button
                type="primary"
                onClick={handleSubmit}
                disabled={questionList.some((question) => !question.auditStatus)}
              >
                提交
              </Button>
            ) : (
              <Tag color={buttonColorMap[auditStatus]}>
                {auditStatusListEnumManager?.getLabelByValue(auditStatus)}
              </Tag>
            )}
          </div>

          {activeQuestion && (
            <ImportQuestion
              key={activeQuestion.questionId}
              activeQuestion={activeQuestion}
              setActiveQuestion={(question) => {
                setQuestionList(
                  produce((draft) => {
                    draft[activeIndex] = question;
                  }),
                );
              }}
              canEdit={canEdit}
            />
          )}

          {canEdit && (
            <div className="footer">
              <Button
                disabled={!activeQuestion?.baseTreeNodeIds?.length}
                type="primary"
                onClick={async () => {
                  const param = {
                    auditTaskId: data.auditTaskId,
                    questionId: activeQuestion.questionId,
                    answerWrong: '',
                    explanationWrong: '',
                    questionWrong: '',
                    auditStatus: 2 as API.EnumConstantData['auditStatusList'][number]['value'],
                    baseTreeNodeIds: activeQuestion.baseTreeNodeIds || [],
                  };

                  const [err, ret] = await to(submitImportQuestionAudit(param));

                  if (err) {
                    return;
                  }

                  if (ret.code) {
                    message.error(ret?.message);
                    return;
                  }

                  setQuestionList(
                    produce((draft) => {
                      draft[activeIndex] = {
                        ...draft[activeIndex],
                        ...param,
                      };
                    }),
                  );

                  if (activeIndex < questionList.length - 1) {
                    setActiveIndex(activeIndex + 1);

                    pageNavRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                  }
                }}
              >
                通过
              </Button>
              <Button onClick={() => setRejectModalOpen(true)}>不通过</Button>
            </div>
          )}
        </Card>
      )}

      {canEdit && (
        <RejectModal
          key={activeQuestion?.questionId}
          auditTaskId={data.auditTaskId}
          open={rejectModalOpen}
          activeQuestion={activeQuestion}
          onOk={(reasons) => {
            setQuestionList(
              produce((draft) => {
                draft[activeIndex] = {
                  ...draft[activeIndex],
                  answerWrong: reasons.answerWrong ?? '',
                  explanationWrong: reasons.explanationWrong ?? '',
                  questionWrong: reasons.questionWrong ?? '',
                  auditStatus: 3 as API.EnumConstantData['auditStatusList'][number]['value'],
                };
              }),
            );

            if (activeIndex < questionList.length - 1) {
              setActiveIndex(activeIndex + 1);

              pageNavRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }

            setRejectModalOpen(false);
          }}
          onCancel={() => setRejectModalOpen(false)}
        />
      )}
    </Space>
  );
};

export default ImportQuestionReviewDetail;

const buttonColorMap: Record<number, ButtonColorType> = {
  1: 'default',
  2: 'green',
  3: 'red',
};

const importQuestionReviewCls = css`
  position: relative;

  .submit-btn {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 1;
  }

  .page-nav {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 24px;

    .pages-container {
      flex: 1;
      overflow-x: auto;
      &::-webkit-scrollbar {
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
      &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.05);
      }
      .ant-flex {
        gap: 8px;
        padding: 4px;
        .ant-btn {
          min-width: 48px;
        }
      }
    }
  }
  .section {
    margin-bottom: 24px;
  }
  .title {
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
  }
  .content {
    margin-bottom: 16px;
  }
  .reject-reason {
    margin-bottom: 24px;
    padding: 12px;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 2px;
  }
  .reject-type {
    margin-bottom: 8px;
    color: #ff4d4f;
    font-weight: bold;
  }
  .reject-content {
    color: rgba(0, 0, 0, 0.65);
    & + .reject-type {
      margin-top: 16px;
    }
  }
  .footer {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
`;
