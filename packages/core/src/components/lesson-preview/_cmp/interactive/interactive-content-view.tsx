import footer from "@/public/interactive/interactive-box-footer.png";
import header from "@/public/interactive/interactive-box-header.png";
import InteractiveComponent from "@repo/core/components/interactive-component";
import { FC } from "react";
// import { useInteractiveViewContext } from "./interactive-view-context";

export const InteractiveContentView: FC<{
  url: string;
  type: string;
  onReport: (e: CustomEvent<unknown>) => void;
}> = ({ url, type, onReport }) => {
  // const { next } = useInteractiveViewContext();

  return (
    <div className="py-22 mx-auto h-full w-[82%]">
      <div className="relative h-full w-full p-2.5">
        <div
          className="z-1 absolute -top-[38px] left-0 h-[64px] w-[410px] bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${header.src})` }}
        ></div>
        <div
          className="z-1 absolute -bottom-[29px] right-0 h-[56px] w-[390px] bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${footer.src})` }}
        ></div>
        <div className="absolute left-0 top-0 z-0 h-full w-full rounded-[20px] bg-[#4F4761]"></div>
        <div className="border-3 z-3 relative h-full w-full overflow-auto rounded-[20px] border-[#B7CDFF]">
          <InteractiveComponent
            url={url}
            type={type}
            onReport={onReport}
            className="min-h-full [&>*]:h-full"
          >
            <div>loading...</div>
          </InteractiveComponent>
        </div>
        {/* <NextBtn
          className="z-3 absolute -bottom-11 right-10 w-[112px] active:translate-y-1 active:scale-95"
          onClick={() => {
            next();
          }}
        /> */}
      </div>
    </div>
  );
};
