import { Player, PlayerRef } from "@remotion/player";
import { playbackRateOptions } from "@repo/core/components/player/player-controls";
import { Guide } from "@repo/core/guide/guide";
import { GuideMode, GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { FC, useMemo, useRef } from "react";
import { EmptyTip } from "./_cmp/empty-tip";
import usePlayerPolyfill from "./hooks/usePlayerPolyfill";

type GuidePlayerProps = {
  width?: number;
  height?: number;
  className?: string;
  data: GuideWidgetData | string;
  controls?: boolean;
  style?: React.CSSProperties;
  inputProps?: Partial<React.ComponentProps<typeof Player>["inputProps"]>;
} & Partial<
  Omit<React.ComponentProps<typeof Player>, "inputProps" | "height" | "width">
>;

const GuidePlayer: FC<GuidePlayerProps> = ({
  width = 1000,
  height = 600,
  className,
  data,
  controls = true,
  style,
  inputProps,
  ...props
}) => {
  const guideData = useMemo(() => {
    if (typeof data === "string") {
      return data ? (JSON.parse(data) as GuideWidgetData) : null;
    }
    return data;
  }, [data]);

  const playerRef = useRef<PlayerRef>(null);

  usePlayerPolyfill(playerRef);

  if (!guideData) {
    return <EmptyTip texture="视频未生成" />;
  }

  const { avatar } = guideData;

  return (
    <Player
      ref={playerRef}
      className={className}
      style={{
        transform: "translate3d(0, 0, 0)",
        backfaceVisibility: "hidden",
        ...style,
      }}
      component={Guide}
      inputProps={{
        data: guideData,
        selectable: false,
        guideMode: GuideMode.free,
        ...inputProps,
      }}
      durationInFrames={avatar?.durationInFrames + avatar?.fps} //增加1秒, bugfix: 最后一点语音未播完就结束
      fps={avatar?.fps}
      showPlaybackRateControl={playbackRateOptions}
      playbackRate={1}
      controls={controls} // 这将隐藏整个控制条
      alwaysShowControls
      initiallyMuted={false}
      allowFullscreen={false}
      compositionWidth={Math.max(width, 1)}
      compositionHeight={Math.max(height, 1)}
      acknowledgeRemotionLicense
      errorFallback={(e: { error: { message: string } }) => (
        <span className="text-sm text-red-500">错误: {e.error.message}</span>
      )}
      autoPlay={false}
      {...props}
    />
  );
};

export { GuidePlayer };
