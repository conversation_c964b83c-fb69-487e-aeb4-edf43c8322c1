 # AI课程体验版任务列表

## 项目概述
本项目是一个AI课程体验版的首页实现，基于Figma设计稿进行开发。

## 任务列表

### 1. 全局样式
- 文件: [global-styles.md](./global-styles.md)
- 状态: 待开发
- 优先级: 高

### 2. 页面布局
- 文件: [page-layout.md](./page-layout.md)
- 状态: 待开发
- 优先级: 高

### 3. 导航栏组件
- 文件: [navigation-bar.md](./navigation-bar.md)
- 状态: 待开发
- 优先级: 中

### 4. 知识目录组件
- 文件: [knowledge-directory.md](./knowledge-directory.md)
- 状态: 待开发
- 优先级: 中

### 5. 课程卡片组件
- 文件: [course-card.md](./course-card.md)
- 状态: 待开发
- 优先级: 中

### 6. 底部导航组件
- 文件: [bottom-navigation.md](./bottom-navigation.md)
- 状态: 待开发
- 优先级: 中

## 开发顺序建议
1. 先完成全局样式，为其他组件提供基础
2. 实现页面布局框架
3. 依次实现各个组件
4. 最后进行整体优化和测试

## 依赖关系图
```mermaid
graph TD
    A[全局样式] --> B[导航栏组件]
    A --> C[知识目录组件]
    A --> D[课程卡片组件]
    A --> E[底部导航组件]
    F[页面布局] --> B
    F --> C
    F --> D
    F --> E
```

## 注意事项
1. 所有组件都需要遵循设计规范
2. 注意性能优化
3. 确保响应式设计完善
4. 保持代码质量

