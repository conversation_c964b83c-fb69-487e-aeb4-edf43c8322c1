import { defineConfig } from 'vitest/config';
import path from 'path';

// 检测是否为API测试模式
const isApiTest = process.env.VITEST_API === 'true' || process.argv.includes('--api');

export default defineConfig({
  test: {
    globals: true,
    // API测试使用node环境，其他使用jsdom
    environment: isApiTest ? 'node' : 'jsdom',
    include: isApiTest 
      ? ['app/api/**/*.test.ts'] // API测试只包含API文件
      : [
          '**/__tests__/**/*.{js,ts,tsx}',
          '**/*.test.{js,ts,tsx}'
        ],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/.next/**',
      // 非API测试时排除API测试
      ...(isApiTest ? [] : ['**/app/api/**/*.test.{js,ts}'])
    ],
    // API测试不需要浏览器setup，其他需要
    setupFiles: isApiTest ? [] : ['./vitest.setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: isApiTest ? './coverage/api' : './coverage/general',
      include: isApiTest 
        ? ['app/api/**/*.{ts,js}']
        : ['app/**/*.{ts,tsx}'],
      exclude: [
        '**/*.test.{ts,tsx,js}',
        '**/__tests__/**',
        '**/node_modules/**',
        '**/dist/**',
        '**/.next/**',
        '**/types/**',
        '**/*.d.ts',
        '**/layout.tsx',
        '**/page.tsx',
        '**/loading.tsx',
        '**/error.tsx',
        '**/not-found.tsx',
        '**/vitest.config.ts',
        '**/vitest.setup.ts',
      ],
      thresholds: {
        global: {
          branches: isApiTest ? 75 : 80,
          functions: isApiTest ? 75 : 80,
          lines: isApiTest ? 75 : 80,
          statements: isApiTest ? 75 : 80,
        },
      },
    },
    // API测试单线程避免状态冲突
    pool: isApiTest ? 'threads' : undefined,
    poolOptions: isApiTest ? {
      threads: {
        singleThread: true
      }
    } : undefined,
    testTimeout: isApiTest ? 15000 : 5000,
    hookTimeout: isApiTest ? 10000 : 5000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './app'),
      '@/models': path.resolve(__dirname, './app/models'),
      '@/components': path.resolve(__dirname, './app/components'),
      '@/utils': path.resolve(__dirname, './app/utils'),
      '@/hooks': path.resolve(__dirname, './app/hooks'),
      '@/contexts': path.resolve(__dirname, './app/contexts'),
      '@/viewmodels': path.resolve(__dirname, './app/viewmodels'),
    },
  },
});