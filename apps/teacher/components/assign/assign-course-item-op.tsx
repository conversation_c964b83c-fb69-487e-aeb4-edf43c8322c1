import IcCourseIcon from "@/public/assign/ic-course.svg";
import IcExerciseIcon from "@/public/assign/ic-exercise.svg";
import { AssignCourseResourceType } from "@/types/assign/course";
import { Button } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
import { memo } from "react";

interface AssignCourseItemOpProps {
  style?: React.CSSProperties;
  className?: string;
  type: AssignCourseResourceType;
  estimatedTimeStr: string;
  deployed: string[];
  unDeployed: string[];
  hasJoined: boolean;
  showPreview?: boolean;
  onPreview: (type: AssignCourseResourceType) => void;
  onAdd: (type: AssignCourseResourceType) => void;
  onRemove: (type: AssignCourseResourceType) => void;
}

function AssignCourseItemOpComp({
  style = {},
  className = "",
  type,
  estimatedTimeStr,
  deployed,
  unDeployed,
  hasJoined,
  showPreview = false,
  onPreview,
  onAdd,
  onRemove,
}: AssignCourseItemOpProps) {
  return (
    <div
      className={cn(
        "bg-fill-gray-2 mt-4 flex items-start justify-between gap-1.5 rounded-lg px-3 py-2",
        className
      )}
      style={style}
    >
      {type === "aiCourse" ? (
        <IcCourseIcon className="mt-1 size-4" />
      ) : (
        <IcExerciseIcon className="mt-1 size-4" />
      )}
      <div className="flex flex-1 items-center gap-2.5">
        <div className="flex-1">
          <div className="h-5.25 flex items-center gap-2 text-sm font-medium">
            <span>{type === "aiCourse" ? "课程学习" : "巩固练习"}</span>
            {estimatedTimeStr ? <span>(约{estimatedTimeStr})</span> : null}
          </div>
          {unDeployed.length ? (
            <div className="mt-1 flex text-xs font-normal leading-[1.375rem] text-slate-500">
              <div className="h-5.5 mr-1.5 flex flex-shrink-0 flex-grow-0 items-center justify-center">
                <div className="bg-gray-5 px-1.25 text-xxs text-fill-light h-4 w-10 flex-shrink-0 flex-grow-0 rounded-sm text-center font-medium leading-4">
                  未布置
                </div>
              </div>
              <div className="flex flex-wrap">
                {unDeployed.map((item, index, arr) => (
                  <div key={item + index}>
                    {item}
                    {index !== arr.length - 1 ? "、" : ""}
                  </div>
                ))}
              </div>
            </div>
          ) : null}
          {deployed.length > 0 ? (
            <div className="mt-1 flex text-xs font-normal leading-[1.375rem] text-slate-500">
              <div className="h-5.5 mr-1.5 flex flex-shrink-0 flex-grow-0 items-center justify-center">
                <div className="bg-purple-2 px-1.25 text-xxs text-fill-light h-4 w-10 flex-shrink-0 flex-grow-0 rounded-sm text-center font-medium leading-4">
                  已布置
                </div>
              </div>
              <div className="flex flex-wrap">
                {deployed.map((item, index, arr) => (
                  <div key={item + index}>
                    {item}
                    {index !== arr.length - 1 ? "、" : ""}
                  </div>
                ))}
              </div>
            </div>
          ) : null}
        </div>
        {showPreview ? (
          <Button
            type="default"
            size="md"
            radius="full"
            className="w-22 font-normal"
            onClick={() => onPreview(type)}
          >
            预览{type === "aiCourse" ? "课程" : "练习"}
          </Button>
        ) : null}
        {hasJoined ? (
          <Button
            type="error"
            size="md"
            radius="full"
            className="w-22 font-normal"
            onClick={() => onRemove(type)}
          >
            取消加入
          </Button>
        ) : (
          <Button
            type="outline"
            size="md"
            radius="full"
            className="w-22 font-normal"
            onClick={() => onAdd(type)}
          >
            加入
          </Button>
        )}
      </div>
    </div>
  );
}

export const AssignCourseItemOp = memo(AssignCourseItemOpComp);
