import { AlertDialog } from "@/ui/alertDialog";
import { cn } from "@/utils/utils";
interface AssignCancelAlertProps {
  style?: React.CSSProperties;
  className?: string;
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

export function AssignCancelAlert({
  style = {},
  className = "",
  open = false,
  onCancel = () => {},
  onOk = () => {},
}: AssignCancelAlertProps) {
  return (
    <AlertDialog
      open={open}
      title="确定取消布置吗？"
      description="退出后，当前的任务数据不保存。"
      onCancel={onCancel}
      onOk={onOk}
      variant="warning"
      className={cn(
        "",
        className
      )}
      style={style}
    />
  );
}
