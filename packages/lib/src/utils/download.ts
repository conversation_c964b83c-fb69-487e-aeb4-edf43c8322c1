// 下载文件到本地, 根据 Content-Type 设置文件扩展名
const download = async (url: string, fileName: string): Promise<boolean> => {
  if (!url || !fileName) {
    return false;
  }
  try {
    // 获取文件
    const response = await fetch(url);
    const blob = await response.blob();

    // 获取文件类型
    const contentType = response.headers.get("Content-Type");
    let extension = "";

    // 根据 Content-Type 设置文件扩展名
    if (contentType) {
      // 音频类型
      if (contentType.startsWith("audio/")) {
        switch (contentType) {
          case "audio/mpeg":
            extension = ".mp3";
            break;
          case "audio/wav":
            extension = ".wav";
            break;
          case "audio/ogg":
            extension = ".ogg";
            break;
          case "audio/mp4":
            extension = ".m4a";
            break;
          default:
            extension = ".mp3";
        }
      }
      // 视频类型
      else if (contentType.startsWith("video/")) {
        switch (contentType) {
          case "video/mp4":
            extension = ".mp4";
            break;
          case "video/quicktime":
            extension = ".mov";
            break;
          case "video/x-msvideo":
            extension = ".avi";
            break;
          case "video/x-matroska":
            extension = ".mkv";
            break;
          case "video/webm":
            extension = ".webm";
            break;
          default:
            extension = ".mp4";
        }
      }
      // 图片类型
      else if (contentType.startsWith("image/")) {
        switch (contentType) {
          case "image/jpeg":
          case "image/jpg":
            extension = ".jpg";
            break;
          case "image/png":
            extension = ".png";
            break;
          case "image/gif":
            extension = ".gif";
            break;
          case "image/webp":
            extension = ".webp";
            break;
          case "image/svg+xml":
            extension = ".svg";
            break;
          case "image/bmp":
            extension = ".bmp";
            break;
          default:
            extension = ".jpg";
        }
      }
    }

    // 创建下载链接
    const link = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = link;

    // 使用文本内容作为文件名，如果没有则使用默认名
    a.download = fileName + extension;

    // 触发下载
    document.body.appendChild(a);
    a.click();

    // 清理
    window.URL.revokeObjectURL(link);
    document.body.removeChild(a);
    return true;
  } catch (error) {
    console.error("下载失败:", error);
    return false;
  }
};

export { download };
