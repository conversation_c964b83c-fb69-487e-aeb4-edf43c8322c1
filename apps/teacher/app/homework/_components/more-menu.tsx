"use client";

import { TASK_TYPE } from "@/enums";
import {
  deleteHomeworkAssign,
  updateTaskAssign,
  updateTaskName,
} from "@/services";
import { AlertDialog } from "@/ui/alertDialog";
import { Button } from "@/ui/button";
import { Checkbox } from "@/ui/checkbox";
import { DatePicker, DateValue } from "@/ui/datePicker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { Input } from "@/ui/input";
import { Label } from "@/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/popover";
import { toast } from "@/ui/toast";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { Signal, useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { HelpCircle, MoreVertical } from "lucide-react";
import { useRouter } from "next/navigation";
import { useHomeworkListContext } from "../context";
import { TaskData } from "./task-card";

interface MoreMenuProps {
  taskData: TaskData;
  onSetIsActive?: (isActive: boolean) => void;
}

// 编辑任务对话框子组件
const EditTaskDialog = ({
  taskData,
  onFinish,
  openSignal: parentOpenSignal,
}: {
  taskData: TaskData;
  onFinish: () => void;
  openSignal: Signal<boolean>;
}) => {
  const isOpenSignal = useSignal(false);
  const editTaskNameSignal = useSignal(taskData.title);
  const deadlineSignal = useSignal<Date | undefined>(
    taskData.endTime ? new Date(taskData.endTime * 1000) : undefined
  );

  const isCourseTask = taskData.type === TASK_TYPE.TASK_TYPE_COURSE;

  const handleMonthChange = (month: Date) => {
    console.log("当前显示月份:", month);
  };

  const handleSave = async () => {
    // DONE: 埋点5 => `homework_list_edit_window_submit` 作业列表中在编辑窗口中提交修改
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.EDIT_SUBMIT,
      {}
    );
    try {
      const updates = [];

      // 如果任务名称已更改且不是课程任务，更新任务名称
      if (editTaskNameSignal.value !== taskData.title && !isCourseTask) {
        updates.push(
          updateTaskName({
            taskId: Number(taskData.id),
            taskName: editTaskNameSignal.value,
          })
        );
      }
      // 如果截止时间已更改，更新截止时间
      if (
        deadlineSignal.value &&
        (!taskData.endTime ||
          Math.floor(deadlineSignal.value.getTime() / 1000) !==
            taskData.endTime)
      ) {
        // 获取任务的第一个班级的assignId
        const assignId = taskData.classes[0]?.classData?.assignId || 0;
        if (assignId) {
          updates.push(
            updateTaskAssign({
              assignId,
              deadline: Math.floor(deadlineSignal.value.getTime() / 1000),
            })
          );
        }
      }

      // 如果有更新，等待所有更新完成
      if (updates.length > 0) {
        await Promise.all(updates);
        onFinish(); // Call onFinish after successful save
        toast.success("保存成功");
      } else {
        // toast.info("没有更改内容")
      }

      parentOpenSignal.value = false; // Close dialog on finish
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败");
    }
  };

  // Sync internal state with parent signal
  useSignalEffect(() => {
    isOpenSignal.value = parentOpenSignal.value;
  });

  return (
    <AlertDialog
      className="w-[32.5rem]"
      showTitleIcon={false}
      open={isOpenSignal.value}
      title="修改任务"
      content={
        <div className="h-75 flex w-full flex-col gap-y-4">
          <div className="flex flex-col gap-y-2">
            <div className="flex items-center gap-2">
              <p className="text-danger-1 text-sm">*</p>
              <Label
                htmlFor="taskName"
                className="text-gray-2 font-sans text-sm font-normal leading-[1.375rem]"
              >
                任务名称
              </Label>
              {isCourseTask && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                      <HelpCircle className="text-gray-2 h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60">
                    <p className="text-gray-2 text-sm">
                      课程任务类型不可编辑任务名称
                    </p>
                  </PopoverContent>
                </Popover>
              )}
            </div>
            <Input
              aria-disabled={isCourseTask}
              id="taskName"
              className="text-gray-1 h-8 w-full"
              value={editTaskNameSignal.value}
              onChange={(e) => (editTaskNameSignal.value = e.target.value)}
              disabled={isCourseTask}
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          <div className="flex flex-col gap-y-2">
            <Label
              htmlFor="deadline"
              className="text-gray-2 font-sans text-sm font-normal leading-[1.375rem]"
            >
              要求完成时间
            </Label>
            <DatePicker
              showTime
              className="text-gray-1 h-8 w-full"
              mode="single"
              value={deadlineSignal.value}
              onChange={(date: DateValue) => {
                if (date) {
                  deadlineSignal.value = date;
                }
              }}
              onMonthChange={handleMonthChange}
            />
          </div>
        </div>
      }
      onOk={handleSave}
      onCancel={() => (parentOpenSignal.value = false)}
    />
  );
};

// 删除确认对话框子组件
const DeleteConfirmationDialog = ({
  taskData,
  onFinish,
  openSignal: parentOpenSignal,
}: {
  taskData: TaskData;
  onFinish: () => void;
  openSignal: Signal<boolean>;
}) => {
  const isOpenSignal = useSignal(false);
  const deleteAllSignal = useSignal(false);
  const isDeletingSignal = useSignal(false);

  // Sync internal state with parent signal
  useSignalEffect(() => {
    isOpenSignal.value = parentOpenSignal.value;
  });

  // 删除任务或班级关联
  const handleDelete = async () => {
    try {
      // DONE: 埋点7 => `homework_list_delete_window_submit` 作业列表中在删除窗口中提交删除
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.DELETE_SUBMIT,
        {}
      );
      isDeletingSignal.value = true;

      // 构建请求参数
      const payload = {
        taskId: Number(taskData.id),
        assignIds: deleteAllSignal.value
          ? taskData.homeworkData.reports.map((report) => report.assignId)
          : [taskData.classes[0]?.assignId || 0],
      };
      // 调用删除API
      await deleteHomeworkAssign(payload);

      toast.success(
        deleteAllSignal.value ? "已删除任务及所有关联班级" : "已删除当前班级"
      );
      parentOpenSignal.value = false; // Close dialog on finish
      onFinish();
      return true;
    } catch (error) {
      console.error("删除失败:", error);
      toast.error("删除失败，请稍后重试");
      return false;
    } finally {
      isDeletingSignal.value = false;
    }
  };

  return (
    <AlertDialog
      variant="warning"
      open={isOpenSignal.value}
      title="删除任务"
      description="确认删除任务吗？点击后，任务将从学生端和您的列表中移除。"
      onOk={handleDelete}
      onCancel={() => (parentOpenSignal.value = false)}
      content={
        <div className="mt-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="deleteAllInConfirm"
              checked={deleteAllSignal.value}
              onCheckedChange={(checked) =>
                (deleteAllSignal.value = checked === true)
              }
              onClick={(e) => e.stopPropagation()}
            />
            <Label
              htmlFor="deleteAllInConfirm"
              className="text-sm font-medium"
              onClick={(e) => e.stopPropagation()}
            >
              同时删除任务及该任务关联的所有班级
            </Label>
          </div>
        </div>
      }
    />
  );
};

export function MoreMenu({ taskData, onSetIsActive }: MoreMenuProps) {
  const { fetchTaskList, query } = useHomeworkListContext();
  const router = useRouter();

  const openDropdownSignal = useSignal(false);
  const openEditDialogSignal = useSignal(false);
  const openDeleteDialogSignal = useSignal(false);

  // 问题在于这里：当Dialog关闭时会引起DropdownMenu重新渲染，但如果useSignalEffect中又改变了状态
  // 可能导致无限循环更新。修改为只监听对话框状态变化，而不是每次都更新menuKey
  useSignalEffect(() => {
    // 当对话框关闭时，我们需要确保不会立即触发另一个状态更新
    const isDialogOpen =
      openEditDialogSignal.value || openDeleteDialogSignal.value;

    // 只有当对话框状态从打开变为关闭时，才需要更新menuKey
    if (!isDialogOpen) {
      // 移除setTimeout，避免额外的组件更新
      // 通常对话框关闭后不需要强制更新DropdownMenu
    }
  });

  const handleMoreMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 使用更简洁的打开对话框处理函数，避免中间状态
  const handleOpenEditDialog = (e: React.MouseEvent) => {
    // DONE: 埋点4 => `homework_list_edit_click` 用户点击课程编辑
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.EDIT_CLICK,
      {}
    );
    e.stopPropagation();
    openDropdownSignal.value = false;
    // 延迟打开对话框，避免状态快速变化导致的问题
    setTimeout(() => {
      openEditDialogSignal.value = true;
    }, 100);
  };

  const handleOpenDeleteDialog = (e: React.MouseEvent) => {
    // DONE: 埋点6 => `homework_list_delete_click` 用户点击课程删除
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.DELETE_CLICK,
      {}
    );
    e.stopPropagation();
    openDropdownSignal.value = false;
    // 延迟打开对话框，避免状态快速变化导致的问题
    setTimeout(() => {
      openDeleteDialogSignal.value = true;
    }, 100);
  };

  const handleCopyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // DONE: 埋点3 => `homework_list_copy_click` 用户点击课程复制
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.COPY_CLICK,
      {}
    );

    const subjectId = query.value.subject;
    const taskId = taskData.id;
    const pathMap: Partial<Record<TASK_TYPE, string>> = {
      [TASK_TYPE.TASK_TYPE_COURSE]: `/assign/${subjectId}/course?taskId=${taskId}`,
      [TASK_TYPE.TASK_TYPE_HOMEWORK]: `/assign/${subjectId}/homework?taskId=${taskId}`,
    };

    // 根据任务类型构建不同的路径
    const path = pathMap[taskData.type];
    if (!path) {
      toast.warning("当前任务类型不支持复制");
      return;
    }

    openDropdownSignal.value = false;
    router.push(path);
  };

  return (
    <div
      className="relative z-10 h-5"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <DropdownMenu
        open={openDropdownSignal.value}
        onOpenChange={(open) => {
          // 确保只有在需要时才更新状态
          if (openDropdownSignal.value !== open) {
            openDropdownSignal.value = open;
          }
          if (open) {
            // DONE: 埋点2 => `homework_list_more_click` 用户点击课程更多按钮
            umeng.trackEvent(
              UmengCategory.HOMEWORK,
              UmengHomeworkAction.MORE_CLICK,
              {}
            );
          }
          if (!open) {
            onSetIsActive?.(false);
          }
        }}
      >
        <DropdownMenuTrigger asChild onClick={handleMoreMenuClick}>
          <Button
            variant="ghost"
            size="icon"
            className="h-full w-4 cursor-pointer overflow-hidden p-0 outline-none focus-visible:ring-0"
            onClick={(e) => {
              e.stopPropagation();
              onSetIsActive?.(true);
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              onSetIsActive?.(true);
            }}
            onPointerDown={(e) => {
              e.stopPropagation();
              onSetIsActive?.(true);
            }}
          >
            <MoreVertical className="w-3 text-[#8388ab]" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="max-h-80 w-24 overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]"
        >
          <DropdownMenuItem
            className={`!hover:bg-[#F4F7FE] flex cursor-pointer justify-center gap-2 rounded-[0.625rem] px-4 py-3 text-center text-sm font-normal leading-[150%] text-[#444963]`}
            onClick={handleOpenEditDialog}
          >
            编辑任务
          </DropdownMenuItem>
          {(taskData.type === TASK_TYPE.TASK_TYPE_COURSE ||
            taskData.type === TASK_TYPE.TASK_TYPE_HOMEWORK) && (
            <DropdownMenuItem
              className={`flex cursor-pointer justify-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] text-[#444963] hover:bg-[#F4F7FE]`}
              onClick={handleCopyClick}
            >
              复制任务
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            className={`flex cursor-pointer justify-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm font-normal leading-[150%] !text-[#fb4a3e] hover:bg-[#F4F7FE]`}
            onClick={handleOpenDeleteDialog}
          >
            删除任务
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 渲染子组件，并将taskData和fetchTaskList传递下去 */}
      <EditTaskDialog
        taskData={taskData}
        onFinish={fetchTaskList}
        openSignal={openEditDialogSignal}
      />
      <DeleteConfirmationDialog
        taskData={taskData}
        onFinish={fetchTaskList}
        openSignal={openDeleteDialogSignal}
      />
    </div>
  );
}
