import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { useApp } from "@/hooks/useApp";
import {
  attentionStudents,
  handleStudentBehaviorV2,
  praiseStudents,
  StudentBehaviorType,
} from "@/services";
import { StudentDetailV2 } from "@/types/homeWork";
import { toast } from "@/ui/toast";
import { useRequest } from "ahooks";
import to from "await-to-js";

export const useStudentRemind = () => {
  const { classData, taskId } = useTaskContext();
  const { userInfo } = useApp();

  // 处理表扬学生
  const handleBatchPraise = async (studentIds: number[], message?: string) => {
    if (!userInfo) {
      toast.error("用户信息不完整", { description: "无法执行操作" });
      return;
    }

    if (studentIds.length === 0) {
      toast.warning("没有可表扬的学生", {
        description: "当前没有需要表扬的学生",
      });
      return;
    }

    const currentTaskId = taskId.value;
    const currentAssignId = classData.value.assignId;

    if (!currentTaskId || !currentAssignId) {
      toast.error("任务信息不完整", { description: "无法执行操作" });
      return;
    }

    const [err] = await to(
      praiseStudents(studentIds, currentTaskId, currentAssignId, message)
    );

    if (err) {
      return;
    }

    toast.success("表扬成功", {
      description: `已成功表扬 ${studentIds.length} 名学生`,
    });
  };

  function useBatchPraise(
    students: StudentDetailV2[],
    handleSuccess?: () => void
  ) {
    return useRequest(
      async () => {
        if (!userInfo) {
          toast.error("用户信息不完整", { description: "无法执行操作" });
          return;
        }

        if (students.length === 0) {
          toast.warning("没有需要提醒的学生", {
            description: "当前没有需要关注的学生",
          });
          return;
        }

        const currentTaskId = taskId.value;
        const currentAssignId = classData.value.assignId;

        if (!currentTaskId || !currentAssignId) {
          toast.error("任务信息不完整", { description: "无法执行操作" });
          return;
        }

        const newStudents = students.map((student) => ({
          id: student.studentId ? Number(student.studentId) : 0,
          teacherId: userInfo?.userID || 0,
          behaviorType: "task_praise" as StudentBehaviorType,
          pushDefaultText:
            student.praiseDefaultText || "你今天表现很棒，老师为你鼓励！",
          courseUrl: student.courseUrl,
        }));
        return handleStudentBehaviorV2({
          students: newStudents,
          assignId: currentAssignId,
          taskId: currentTaskId,
        });
      },
      {
        manual: true,
        onSuccess: (data) => {
          console.log("onSuccess", data);
          toast.success("鼓励成功", {
            description: `已成功鼓励 ${students.length} 名学生`,
          });
          handleSuccess?.();
        },
      }
    );
  }

  function useBatchRemind(
    students: StudentDetailV2[],
    handleSuccess?: () => void
  ) {
    return useRequest(
      async () => {
        if (!userInfo) {
          toast.error("用户信息不完整", { description: "无法执行操作" });
          return;
        }

        if (students.length === 0) {
          toast.warning("没有需要提醒的学生", {
            description: "当前没有需要关注的学生",
          });
          return;
        }

        const currentTaskId = taskId.value;
        const currentAssignId = classData.value.assignId;

        if (!currentTaskId || !currentAssignId) {
          toast.error("任务信息不完整", { description: "无法执行操作" });
          return;
        }

        const newStudents = students.map((student) => ({
          id: student.studentId,
          teacherId: userInfo?.userID || 0,
          behaviorType: "task_attention" as StudentBehaviorType,
          pushDefaultText: student.pushDefaultText,
          courseUrl: student.courseUrl,
        }));
        return handleStudentBehaviorV2({
          students: newStudents,
          assignId: currentAssignId,
          taskId: currentTaskId,
        });
      },
      {
        manual: true,
        onSuccess: () => {
          toast.success("提醒成功", {
            description: `已成功提醒 ${students.length} 名学生`,
          });
          handleSuccess?.();
        },
      }
    );
  }

  // 处理提醒学生
  const handleBatchRemind = async (studentIds: number[], message = "") => {
    if (!userInfo) {
      toast.error("用户信息不完整", { description: "无法执行操作" });
      return;
    }

    if (studentIds.length === 0) {
      toast.warning("没有需要提醒的学生", {
        description: "当前没有需要关注的学生",
      });
      return;
    }

    const currentTaskId = taskId.value;
    const currentAssignId = classData.value.assignId;

    if (!currentTaskId || !currentAssignId) {
      toast.error("任务信息不完整", { description: "无法执行操作" });
      return;
    }

    const [err] = await to(
      attentionStudents(studentIds, currentTaskId, currentAssignId, message)
    );

    if (err) {
      return;
    }

    toast.success("提醒成功", {
      description: `已成功提醒 ${studentIds.length} 名学生`,
    });
  };

  return {
    handleBatchPraise,
    handleBatchRemind,
    useBatchPraise,
    useBatchRemind,
  };
};
