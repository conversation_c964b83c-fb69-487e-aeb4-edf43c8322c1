import { useState, useEffect } from "react";
import to from "await-to-js";
import { getSubjectClassList } from "@/services";
import { GradeClass, Subject } from "@/types/subjectClass";

export function useOptions() {
  const [subjectList, setSubjectList] = useState<Subject[]>([]);
  const [classTreeList, setClassTreeList] = useState<GradeClass[]>([]);
  const [classFlatList, setClassFlatList] = useState<{
    gradeId: number;
    gradeName: string;
    classID: number;
    className: string;
  }[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchOptionsData = async () => {
    setLoading(true);
    try {
      const [err, res] = await to(getSubjectClassList());
      if (err) {
        console.error("获取学科班级列表失败", err);
        return;
      }

      // 设置学科列表
      setSubjectList(res.subjects || []);

      // 设置班级树形结构
      setClassTreeList(res.gradeClasses || []);

      // 设置班级扁平列表
      setClassFlatList(
        res.gradeClasses?.flatMap(item =>
          item.class.map(child => ({
            ...child,
            gradeId: item.gradeID,
            gradeName: item.gradeName
          }))
        ) || []
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOptionsData();
  }, []);


  return {
    subjectList,
    classTreeList,
    classFlatList,
    loading,
    refreshOptions: fetchOptionsData
  };
} 