# ViewModel 层

ViewModel 层负责处理业务逻辑和状态管理，为 View 层提供简洁易用的接口。它连接 Model 层和 View 层，封装复杂的业务逻辑。

## 🏗️ 架构原则

- **单一职责** - 每个 ViewModel 专注于特定的业务领域
- **状态管理** - 统一管理组件状态和副作用
- **错误处理** - 提供统一的错误处理机制
- **类型安全** - 使用 TypeScript 确保类型安全
- **可复用性** - 设计可在多个组件中复用的 Hook

## 📁 目录结构

```
viewmodels/
├── chat/           # 聊天相关 ViewModel
├── course/         # 课程相关 ViewModel  
├── exercise/       # 练习相关 ViewModel
├── wrong-question-bank/  # 错题本相关 ViewModel
└── README.md       # 本文档
```

## 🎯 业务模块

### 错题本 (Wrong Question Bank)
- **路径**: `./wrong-question-bank/`
- **核心功能**: 错题切换、状态管理
- **主要 Hook**: `useToggleWrongQuestion`
- **依赖 Model**: `useManualAddWrongQuestion`, `useDeleteWrongQuestions`

### 练习 (Exercise)
- **路径**: `./exercise/`
- **核心功能**: 练习流程管理、答题状态
- **主要 Hook**: 练习相关的业务逻辑 Hook

### 课程 (Course)
- **路径**: `./course/`
- **核心功能**: 课程数据管理、学习进度
- **主要 Hook**: 课程相关的业务逻辑 Hook

### 聊天 (Chat)
- **路径**: `./chat/`
- **核心功能**: 聊天消息管理、实时通信
- **主要 Hook**: 聊天相关的业务逻辑 Hook

## 🚀 使用指南

### 基本使用模式

```typescript
// 1. 导入 ViewModel Hook
import { useToggleWrongQuestion } from '@/viewmodels/wrong-question-bank';

// 2. 在组件中使用
function MyComponent() {
  const { toggle, isLoading, error } = useToggleWrongQuestion();
  
  // 3. 处理业务逻辑
  const handleAction = async () => {
    try {
      await toggle({ /* 参数 */ });
      // 成功处理
    } catch (error) {
      // 错误处理
    }
  };
  
  return (
    <div>
      {/* UI 渲染 */}
    </div>
  );
}
```

### 错误处理模式

```typescript
function ComponentWithErrorHandling() {
  const { toggle, isLoading, error } = useToggleWrongQuestion();
  
  // 统一的错误处理
  useEffect(() => {
    if (error) {
      console.error('操作失败:', error.message);
      // 显示错误提示
    }
  }, [error]);
  
  return (
    <div>
      {error && <ErrorMessage message={error.message} />}
      {/* 其他 UI */}
    </div>
  );
}
```

## 🔧 开发规范

### Hook 命名规范
- 使用 `use` 前缀
- 采用驼峰命名法
- 体现具体功能：`useToggleWrongQuestion`、`useExerciseSession`

### 返回值规范
```typescript
interface ViewModelReturn {
  // 主要操作函数
  action: (...args: any[]) => Promise<void>;
  
  // 状态管理
  isLoading: boolean;
  error: Error | null;
  
  // 数据状态（可选）
  data?: any;
}
```

### 错误处理规范
- 统一使用 `Error` 类型
- 提供 `error` 状态供 UI 展示
- 重新抛出错误供调用方处理
- 在操作开始时重置错误状态

### 类型定义规范
- 导出所有相关类型
- 使用 TypeScript 联合类型处理不同场景
- 提供清晰的接口文档

## ⚠️ 注意事项

1. **依赖管理** - ViewModel 只依赖 Model 层，不直接调用 API
2. **状态重置** - 每次操作前重置相关状态
3. **错误传播** - 既要管理内部错误状态，也要向上传播错误
4. **性能优化** - 使用 `useCallback` 优化函数引用
5. **测试覆盖** - 为每个 ViewModel Hook 编写单元测试

## 🧪 测试策略

- **单元测试** - 测试 Hook 的基本功能和边界情况
- **集成测试** - 测试与 Model 层的集成
- **错误测试** - 测试各种错误场景的处理
- **状态测试** - 测试状态变化的正确性 