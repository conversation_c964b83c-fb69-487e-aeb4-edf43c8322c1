"use client";

import { useSketchCanvasRef } from "@repo/core/guide/context/sketch-canvas-context";
import {
  DrawElement,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Eraser,
  <PERSON>lighter,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Trash,
  Undo,
} from "lucide-react";
import { FC, useEffect, useState } from "react";
import { GuideDrawPlayer } from "./guide-player";

import { toast } from "@/app/components/common/toast";
import { getStorageItem, setStorageItem } from "@repo/lib/utils/local-storage";

// 画笔和橡皮擦粗细配置常量; 画笔和荧光笔共用一套配置
const PENCIL_STROKE_WIDTHS = [1, 2, 4] as const;
const ERASER_STROKE_WIDTHS = [4, 8, 16] as const;
const PENCIL_COLORS = ["#4C55F5", "#F9453F", "#64D769"] as const;

interface GuideDrawWrapperProps {
  videoJson: GuideWidgetData;
  width: number;
  height: number;
  inFrame?: number;
  outFrame?: number;
  onDrawChange?: (paths: DrawElement[] | null) => void;
  backHandler?: () => void;
  handleUndo?: () => void;
  handleRedo?: () => void;
  isUndo: boolean;
  isRedo: boolean;
  partIndex: number;
  totalPartsCount: number;
}

interface PencilConfig {
  strokeWidth: number;
  strokeColor: string;
}

const DRAW_CONFIG_KEY = "draw_page_options_cache";

interface CacheConfig {
  pen?: PencilConfig;
  highlighter?: PencilConfig;
  eraser?: { strokeWidth: number };
}
const setDrawPageOptionsCache = (config: CacheConfig) => {
  const oldConfig = getStorageItem(DRAW_CONFIG_KEY, {});
  setStorageItem(DRAW_CONFIG_KEY, { ...oldConfig, ...config });
};
export const GuideDrawWrapper: FC<GuideDrawWrapperProps> = ({
  videoJson,
  inFrame = 0,
  outFrame,
  onDrawChange,
  backHandler,
  isUndo,
  isRedo,
  handleUndo,
  handleRedo,
  partIndex,
  totalPartsCount,
}) => {
  const {
    canvasRef,
    strokeColor,
    strokeWidth,
    setStrokeColor,
    setStrokeWidth,
  } = useSketchCanvasRef();
  const [eraserMode, setEraserMode] = useState(false);
  const [isHighlighter, setIsHighlighter] = useState(false);
  const cacheConfig: CacheConfig = getStorageItem(DRAW_CONFIG_KEY, {});
  const { pen = {}, highlighter = {}, eraser = {} } = cacheConfig;

  const [penConfig, setPenConfig] = useState<PencilConfig>({
    strokeColor: PENCIL_COLORS[0],
    strokeWidth: PENCIL_STROKE_WIDTHS[1],
    ...pen,
  });
  const [highlighterConfig, setHighlighterConfig] = useState<PencilConfig>({
    strokeColor: PENCIL_COLORS[0],
    strokeWidth: PENCIL_STROKE_WIDTHS[1],
    ...highlighter,
  });
  const [eraserConfig, setEraserConfig] = useState<{ strokeWidth: number }>({
    strokeWidth: ERASER_STROKE_WIDTHS[1],
    ...eraser,
  });

  // 组件初始化时设置默认的画笔粗细
  useEffect(() => {
    const { strokeColor, strokeWidth } = penConfig;
    setStrokeWidth(strokeWidth);
    setStrokeColor(strokeColor);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 只在组件挂载时执行一次

  const handlePenHighlighterToggle = () => {
    let highlighterFlag = isHighlighter;
    if (eraserMode) {
      // 切换回画笔/荧光笔
      canvasRef?.current?.eraseMode(false);
      setEraserMode(false);
    } else {
      highlighterFlag = !isHighlighter;
      setIsHighlighter(highlighterFlag);
    }
    const config = highlighterFlag ? highlighterConfig : penConfig;
    const { strokeWidth, strokeColor } = config;
    setStrokeColor(strokeColor);
    setStrokeWidth(strokeWidth);
  };

  const handleColorClick = (color: string) => {
    if (eraserMode) return;
    if (isHighlighter) {
      setHighlighterConfig((state) => ({ ...state, strokeColor: color }));
    } else {
      setPenConfig((state) => ({ ...state, strokeColor: color }));
    }

    setStrokeColor(color);
  };

  const handleEraserClick = () => {
    if (eraserMode || isHighlighter) return;
    canvasRef?.current?.eraseMode(true);
    setEraserMode(true);
    setStrokeColor("#fff"); // 橡皮擦颜色可自定义
    setStrokeWidth(eraserConfig.strokeWidth); // 使用橡皮擦的粗细设置
  };

  const handleClearClick = () => {
    canvasRef?.current?.clearCanvas();
    onDrawChange?.([]);
    toast.success("已清空本地数据");
  };

  useEffect(() => {
    setDrawPageOptionsCache({ pen: penConfig });
  }, [penConfig]);

  useEffect(() => {
    setDrawPageOptionsCache({ highlighter: highlighterConfig });
  }, [highlighterConfig]);

  useEffect(() => {
    setDrawPageOptionsCache({ eraser: eraserConfig });
  }, [eraserConfig]);

  const renderToolbar = () => (
    <div className="flex h-full w-16 flex-col items-center justify-between bg-white/90">
      <button
        title="返回上一页"
        className="mb-4 flex h-10 w-10 items-center justify-center rounded-full bg-gray-900 text-white shadow-lg transition hover:bg-gray-700"
        onClick={backHandler}
      >
        <ArrowLeft className="size-6" />
      </button>

      <div className="flex w-full flex-col items-center gap-6">
        <div className="mb-2 flex flex-col items-center gap-2">
          <button
            title={isHighlighter ? "荧光笔" : "画笔"}
            className={`mb-1 flex h-8 w-8 items-center justify-center rounded-full border-2 ${
              !eraserMode ? "border-blue-500 bg-blue-50" : "border-gray-200"
            }`}
            onClick={handlePenHighlighterToggle}
            type="button"
          >
            {isHighlighter ? (
              <Highlighter className="size-4 text-yellow-500" />
            ) : (
              <Pencil className="size-4 text-gray-700" />
            )}
          </button>
          {/* <Tooltip>
            <TooltipTrigger asChild>
              <button
                title={isHighlighter ? "荧光笔" : "画笔"}
                className={`mb-1 flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  !eraserMode ? "border-blue-500 bg-blue-50" : "border-gray-200"
                }`}
                onClick={handlePenHighlighterToggle}
                type="button"
              >
                {isHighlighter ? (
                  <Highlighter className="size-4 text-yellow-500" />
                ) : (
                  <Pencil className="size-4 text-gray-700" />
                )}
              </button>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              sideOffset={8}
              align="start"
              className="border border-gray-200 bg-white p-0 shadow-lg"
              hideArrow
            >
              <div className="flex flex-row items-center gap-2 p-2">
                <div className="flex flex-col items-center">
                  {isHighlighter ? (
                    <Highlighter className="mb-1 size-4 text-yellow-500" />
                  ) : (
                    <Pencil className="mb-1 size-4 text-gray-700" />
                  )}
                  {(isHighlighter ? HIGHLIGHTER_COLORS : PEN_COLORS).map(
                    (color) => (
                      <div
                        key={color}
                        className="mb-1 h-4 w-4 rounded-full border border-gray-200 last:mb-0"
                        style={{ background: color }}
                      />
                    )
                  )}
                </div>
              </div>
            </TooltipContent>
          </Tooltip> */}

          <button
            title="橡皮擦"
            className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
              eraserMode ? "border-blue-500 bg-blue-50" : "border-gray-200"
            } ${isHighlighter ? "cursor-not-allowed opacity-50" : ""}`}
            onClick={handleEraserClick}
            type="button"
            disabled={isHighlighter}
          >
            <Eraser className="size-4 text-gray-700" />
          </button>
        </div>
        {/* 颜色区：根据模式切换 */}
        <div className="flex flex-col items-center gap-2">
          <div className="mb-1 flex flex-col items-center gap-2 text-xs font-bold text-gray-700">
            <span>颜色</span>
          </div>
          <div className="flex flex-col items-center gap-2">
            {PENCIL_COLORS.map((color) => (
              <button
                key={color}
                className={`h-6 w-6 rounded-full border-2 ${
                  strokeColor === color && !eraserMode
                    ? "border-blue-500"
                    : "border-white"
                } shadow ${eraserMode ? "cursor-not-allowed opacity-50" : ""}`}
                style={{ background: color }}
                onClick={() => handleColorClick(color)}
                type="button"
                disabled={eraserMode}
              />
            ))}
          </div>
        </div>
        {/* 粗细 */}
        <div className="flex flex-col items-center gap-2">
          <div className="mb-1 text-xs font-bold text-gray-700">粗细</div>
          <div className="flex flex-col items-center gap-2">
            {(eraserMode ? ERASER_STROKE_WIDTHS : PENCIL_STROKE_WIDTHS).map(
              (w) => (
                <button
                  key={w}
                  className={`flex h-8 w-8 items-center justify-center rounded-lg border-2 ${
                    strokeWidth === w
                      ? "border-blue-400 bg-blue-50"
                      : "border-white bg-gray-50"
                  }`}
                  onClick={() => {
                    if (eraserMode) {
                      setEraserConfig((state) => ({
                        ...state,
                        strokeWidth: w,
                      }));
                    } else {
                      if (isHighlighter) {
                        setHighlighterConfig((state) => ({
                          ...state,
                          strokeWidth: w,
                        }));
                      } else {
                        setPenConfig((state) => ({ ...state, strokeWidth: w }));
                      }
                    }
                    setStrokeWidth(w);
                  }}
                  type="button"
                >
                  <div
                    style={{
                      width: 20,
                      height: w,
                      background: "#888",
                      borderRadius: 2,
                    }}
                  />
                </button>
              )
            )}
          </div>
        </div>
        {/* 操作按钮 */}
        <div className="mt-4 flex flex-col items-center gap-4">
          <button onClick={handleClearClick} title="清空" aria-label="清空">
            <Trash className="size-5" />
          </button>
          <button
            title="撤销"
            type="button"
            className="btn btn-sm btn-outline-primary disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleUndo}
            disabled={!isUndo}
          >
            <Undo className="size-5" />
          </button>
          <button
            title="重做"
            type="button"
            className="btn btn-sm btn-outline-primary disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleRedo}
            disabled={!isRedo}
          >
            <Redo className="size-5" />
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative flex h-full w-full flex-row gap-y-64">
      {/* 工具栏 */}
      {renderToolbar()}

      {/* GuidePlayer */}

      <div className="flex aspect-[1000/600] h-full items-center justify-center">
        <GuideDrawPlayer
          className="h-[600px]"
          onDrawChange={onDrawChange}
          data={videoJson}
          width={1000}
          height={600}
          inFrame={inFrame}
          outFrame={outFrame}
          eraserMode={eraserMode}
          highlighter={isHighlighter}
          partIndex={partIndex}
          totalPartsCount={totalPartsCount}
        />
      </div>
    </div>
  );
};
