"use client";

import { GuidePlayer } from "@/app/components/player/guide-player";
import { get } from "@/lib/fetcher";
import { CourseWidgetSummary } from "@repo/core/types/data/course";
import { ComponentProps, FC, useEffect, useMemo, useState } from "react";
import { useCourseViewContext } from "./course-view-context";
// import { GuideWidgetView } from "../guide/guide-view";
import { get as courseCenterFetcherGet } from "@/lib/course-center-fetcher";
import Interactive from "@repo/core/components/interactive-component";
import { StudyType } from "@repo/core/enums";
import { ExerciseView } from "@repo/core/exercise/preview/view/exercise-view";

const WidgetLoader: FC<
  {
    type: "draft" | "official";
    data?: CourseWidgetSummary;
    totalPartsCount: number;
  } & ComponentProps<"div">
> = ({ data, type, totalPartsCount }) => {
  useEffect(() => {
    if (data?.type === "exercise") {
      syncExerciseData();
    } else {
      syncGuideData();
    }
  }, [data?.index]);

  const { lessonId } = useCourseViewContext();
  if (!data) return <div className="h-full w-full bg-red-300">NO DATA</div>;
  const [widgetData, setWidgetData] = useState<any>(null);
  const syncGuideData = async () => {
    let res: any;
    if (type === "draft") {
      res = await get(`/api/v1/lesson_widget/preview/info`, {
        query: { lessonId: String(lessonId), widgetIndex: String(data.index) },
      });
      res.data = JSON.parse(res.data);
    } else {
      res = await courseCenterFetcherGet(`/api/v1/lesson/preview/widget/info`, {
        query: { lessonId: String(lessonId), widgetIndex: String(data.index) },
      });
    }
    setWidgetData(res);
  };

  const syncExerciseData = async () => {
    const res: any = await get(`/api/v1/lesson_widget/question/get`, {
      query: { lessonId: String(lessonId), widgetIndex: String(data.index) },
    });

    let questionList = res.questionList.flat().filter((item: any) => item);
    questionList = questionList.map((item: any) => ({
      questionInfo: {
        ...item,
        // questionContent: item.questionContent.questionStem,
      },
      // questionInfo: transformQuestionData2(item),
      // studentAnswer: item.studentAnswer,
      // answerStats: item.answerStats,
    }));
    // res.data = JSON.parse(res.data);
    setWidgetData({
      questionList,
      widgetType: "exercise",
    });
  };

  if (!widgetData) return null;

  return (
    <>
      {/* TODO: 文稿组件 , 需要从接口获取数据 */}
      {widgetData.widgetType === "guide" && (
        <div className="h-[600px] w-[1000px] overflow-auto">
          <GuidePlayer
            data={widgetData.data}
            width={1000}
            height={600}
            className="h-full w-full"
            partIndex={widgetData.widgetIndex}
            totalPartsCount={totalPartsCount}
          />
        </div>
      )}
      {/* TODO: 接入练习组件 */}
      {widgetData.widgetType === "exercise" && (
        <div className="h-8/10 w-full" style={{ zoom: 0.73 }}>
          {widgetData.questionList.length > 0 ? (
            <ExerciseView
              className="h-full w-full"
              questionList={widgetData.questionList}
              showExplanations={true}
              studyType={StudyType.REINFORCEMENT_EXERCISE}
              onBack={() => window.history.back()}
            />
          ) : (
            <div className="m-60">暂无数据</div>
          )}
        </div>
      )}

      {/* TODO: 接入视频组件 */}
      {widgetData.widgetType === "video" && (
        <div className="w-2/3 py-6">
          <video
            src={widgetData?.data?.url}
            controls
            className="h-full"
          ></video>
        </div>
      )}

      {widgetData.widgetType === "interactive" && (
        <div className="overflow-y-auto p-1">
          {widgetData.data.url ? (
            <Interactive
              key={widgetData.data.url}
              url={widgetData.data.url}
              type={widgetData.data.typeName}
              onReport={(e) => {
                console.log(e);
              }}
            >
              <div>请稍后...</div>
            </Interactive>
          ) : (
            <div className="flex h-full w-full items-center justify-center">
              暂未配置，请前去设置
            </div>
          )}
        </div>
      )}
    </>
  );
};

const CourseWidgetsLoaderView: FC<
  ComponentProps<"div"> & { type: "draft" | "official" }
> = (props) => {
  const { activeIndex, widgetSummarySequence } = useCourseViewContext();
  if (!widgetSummarySequence) return null;

  const activeWidget = useMemo(() => {
    return widgetSummarySequence[activeIndex];
  }, [activeIndex, widgetSummarySequence]);

  return (
    <div className="relative h-full w-full">
      <div className="relative h-full w-full p-2">
        <WidgetLoader
          data={activeWidget}
          totalPartsCount={widgetSummarySequence.length}
          {...props}
          key={activeIndex}
        />
      </div>
    </div>
  );
};

export { CourseWidgetsLoaderView };
