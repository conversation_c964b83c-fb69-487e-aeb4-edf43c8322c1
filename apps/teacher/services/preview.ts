import { r } from "../libs/axios";
import { AiCourseDetail, QuestionSetDetailData } from "@/types/assign";

/**
 * 获取老师的学科列表
 */
export const getQuestionSetDetail = (questionSetId: number) => {
  return r.get<QuestionSetDetailData>("/task/question-set/detail", {
    params: {
      questionSetId,
    },
  });
};

// TODO: 获取AiCourse详情
export const getAiCourseDetail = (bizTreeNodeId: number) => {
  return r.get<AiCourseDetail>("/course/ai-course/preview", {
    params: {
      bizTreeNodeId,
    },
  });
};
