import { FC, SVGProps } from "react";

export default function CourseSectionTitle({
  title,
  icon: Icon,
  children,
}: {
  title: string;
  icon: FC<SVGProps<SVGElement>>;
  children?: React.ReactNode;
}) {
  return (
    <div className="mb-4 flex items-center gap-1">
      <Icon className="h-5 w-5" />

      <h2 className="text-gray-1 text-normal mr-auto font-semibold leading-normal">
        {title}
      </h2>
      {children}
    </div>
  );
}
