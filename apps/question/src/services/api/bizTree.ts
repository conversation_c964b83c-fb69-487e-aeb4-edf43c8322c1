import { request } from '@umijs/max';

export async function fetchBizTreeDetail(
  bizTreeId: number,
  sceneCategory: number = 0,
  showShelfStatus: number = 0,
): Promise<API.BizTreeDetailResponse> {
  // console.log('请求业务树详情：', bizTreeId, sceneCategory, showShelfStatus);
  let payload: API.BizTreeDetailRequestParams = { bizTreeId };
  if (sceneCategory) {
    payload.sceneCategory = sceneCategory;
  }
  if (showShelfStatus) {
    payload.showShelfStatus = showShelfStatus;
  }

  return request('/api/v1/base/data/biz/tree/detail', {
    method: 'GET',
    params: payload,
  });
}

export async function fetchBizTreeList(
  params: API.BizTreeListRequestParams,
): Promise<API.BizTreeListResponse> {
  return request('/api/v1/base/data/biz/tree/list', {
    method: 'POST',
    data: params,
  });
}
