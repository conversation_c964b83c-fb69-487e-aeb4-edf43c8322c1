"use client";

import { useCallback } from 'react';
import { TeacherJobInfo, JobClass } from '@/types/user';
import { signal } from '@preact-signals/safe-react';
import { JOB_TYPE, subjectEnumManager } from '@/enums';

// 定义缺失的类型
export type UserJobs = TeacherJobInfo[];
export type UserJob = TeacherJobInfo;

// 创建全局状态 Signal
const jobsSignal = signal<UserJobs>([]);

// 创建延迟计算的状态容器
const allGradeIdsSignal = signal<number[] | null>(null);
// 学科名称和学科ID的映射
const allSubjectNamesSignal = signal<string[] | null>(null);
// 学科名称和学科ID的映射
const allSubjectsSignal = signal<{ subjectId: number; subjectName: string }[] | null>(null);
// 教学范围
const teachingScopeSignal = signal<Map<string, { subjectId: number; grades: Map<number, { gradeName: string; classes: JobClass[] | null }> }> | null>(null);
// 角色摘要
const roleSummarySignal = signal<string | null>(null);
// 班主任范围
const homeroomScopeSignal = signal<Map<number, { gradeName: string; classes: JobClass[] }> | null>(null);

// 定义 Hook 返回值类型接口
export interface UseUserRolesResult {
    /** 初始化或更新用户职位数据 */
    setJobs: (jobs: UserJobs) => void;
    /** 获取处理后的用户职位数据数组 */
    getJobs: () => UserJobs;
    // /** 获取用户涉及的所有年级 ID 列表 (去重) */
    // getAllGradeIds: () => number[];
    // /** 获取用户涉及的所有有效学科名称列表 (去重, 非空) */
    // getAllSubjectNames: () => string[];
    // /** 获取用户涉及的所有有效学科名称列表 (去重, 非空) */
    // getAllSubjects: () => typeof allSubjectsSignal.value;
    // /** 获取用户的教学范围 */
    // getTeachingScope: () => Map<string, { subjectId: number; grades: Map<number, { gradeName: string; classes: JobClass[] | null }> }>;
    /** 获取用户的班主任范围 */
    // getHomeroomScope: () => Map<number, { gradeName: string; classes: JobClass[] }>;
    /** 获取用户角色信息的文本摘要 */
    getRoleSummary: () => string;
    /** 清除缓存的计算结果 */
    // clearCache: () => void;
    /** 检查用户是否具有指定工作类型 ID 的函数 */
    hasJobType: (jobTypeId: JOB_TYPE) => boolean;
    /** 检查用户是否具有指定工作类型 ID 的函数 */
    hasJobTypes: (jobTypeIds: number[], isAll?: boolean) => boolean;
    /** 计算主要学科 */
    computePrimarySubject: (jobs: UserJobs) => { subjectId: number; subjectName: string } | null;
}

// // 计算所有涉及的年级 ID
// function computeAllGradeIds(): number[] {
//     const gradeIds = new Set<number>();
//     jobsSignal.value.forEach(job => {
//         job.jobInfos?.forEach(info => {
//             if (info.jobGrade) {
//                 gradeIds.add(info.jobGrade);
//             }
//         });
//     });
//     return Array.from(gradeIds);
// }

// // 计算所有有效的学科名称
// function computeAllSubjectNames(): string[] {
//     const subjectNames = new Set<string>();
//     jobsSignal.value.forEach(job => {
//         if (job.jobSubject?.jobSubject !== 0 && job.jobSubject) {
//             subjectNames.add(subjectEnumManager.getLabelByValue(job.jobSubject.jobSubject) || "");
//         }
//     });
//     return Array.from(subjectNames);
// }
// // 计算所有学科
// function computeAllSubjects(): { subjectId: number; subjectName: string }[] {
//     const subjects = new Set<{ subjectId: number; subjectName: string }>();

//     jobsSignal.value.forEach(job => {
//         console.log(`job`, job);

//         if (job.jobSubject?.jobSubject !== 0 && job.jobSubject) {
//             subjects.add({
//                 subjectId: job.jobSubject.jobSubject,
//                 subjectName: subjectEnumManager.getLabelByValue(job.jobSubject.jobSubject) || ""
//             });
//         }
//     });

//     return Array.from(subjects);
// }

// 计算主要学科 - 优先获取学科老师的，如果没有则获取学科主任的
function computePrimarySubject(jobs: UserJobs): { subjectId: number; subjectName: string } | null {
    // 先尝试获取学科老师的学科（jobType = 4）
    const subjectTeacher = jobs.find(job => job.jobType?.jobType === 4 && job.jobSubject && job.jobSubject.jobSubject !== 0);

    if (subjectTeacher?.jobSubject) {
        const subjectId = subjectTeacher.jobSubject.jobSubject;
        return {
            subjectId,
            subjectName: subjectEnumManager.getLabelByValue(subjectId) || ""
        };
    }

    // 如果没有学科老师，尝试获取学科主任的学科（jobType = 3）
    const subjectDirector = jobs.find(job => job.jobType?.jobType === 3 && job.jobSubject && job.jobSubject.jobSubject !== 0);

    if (subjectDirector?.jobSubject) {
        const subjectId = subjectDirector.jobSubject.jobSubject;
        return {
            subjectId,
            subjectName: subjectEnumManager.getLabelByValue(subjectId) || ""
        };
    }

    return null;
}

// // 计算教学范围
// function computeTeachingScope(): Map<string, { subjectId: number; grades: Map<number, { gradeName: string; classes: JobClass[] | null }> }> {
//     const scope = new Map<string, { subjectId: number; grades: Map<number, { gradeName: string; classes: JobClass[] | null }> }>();

//     // 筛选学科教师和学科组长
//     jobsSignal.value.filter(job => (job.jobType?.jobType === 4 || job.jobType?.jobType === 3) && job.jobSubject)
//         .forEach(job => {
//             const subjectName = job.jobSubject?.name;
//             const subjectId = job.jobSubject?.jobSubject || 0;

//             if (subjectId !== 0 && subjectName) {
//                 if (!scope.has(subjectName)) {
//                     scope.set(subjectName, { subjectId: subjectId, grades: new Map() });
//                 }

//                 const subjectEntry = scope.get(subjectName)!;

//                 job.jobInfos?.forEach(info => {
//                     const jobGrade = info.jobGrade || 0;
//                     const gradeName = info.name || '';

//                     if (!subjectEntry.grades.has(jobGrade)) {
//                         subjectEntry.grades.set(jobGrade, { gradeName: gradeName, classes: info.jobClass || null });
//                     } else {
//                         const existingGrade = subjectEntry.grades.get(jobGrade)!;

//                         if (info.jobClass && info.jobClass.length > 0) {
//                             const existingClasses = existingGrade.classes || [];
//                             const newClasses = info.jobClass.filter(newClass =>
//                                 !existingClasses.some(existingClass =>
//                                     existingClass.jobClass === newClass.jobClass
//                                 )
//                             );
//                             existingGrade.classes = [...existingClasses, ...newClasses];
//                         }
//                     }
//                 });
//             }
//         });

//     return scope;
// }

// // 获取某职位类型的工作
// function getJobByType(jobTypeId: number): UserJob | undefined {
//     return jobsSignal.value.find(job => job.jobType?.jobType === jobTypeId);
// }

// // 计算班主任范围
// function computeHomeroomScope(): Map<number, { gradeName: string; classes: JobClass[] }> {
//     const scope = new Map<number, { gradeName: string; classes: JobClass[] }>();
//     const homeroomJob = getJobByType(5); // 班主任 jobType = 5

//     homeroomJob?.jobInfos?.forEach(info => {
//         const jobGrade = info.jobGrade || 0;
//         const gradeName = info.name || '';

//         if (info.jobClass && info.jobClass.length > 0) {
//             if (!scope.has(jobGrade)) {
//                 scope.set(jobGrade, { gradeName: gradeName, classes: [] });
//             }

//             const gradeEntry = scope.get(jobGrade)!;
//             const classMap = new Map(gradeEntry.classes.map(cls => [cls.jobClass, cls]));

//             info.jobClass.forEach(cls => {
//                 if (!classMap.has(cls.jobClass)) {
//                     classMap.set(cls.jobClass, cls);
//                 }
//             });

//             gradeEntry.classes = Array.from(classMap.values());
//         }
//     });

//     return scope;
// }

// 计算角色摘要
function computeRoleSummary(): string {
    return jobsSignal.value.map(job => {
        const jobType = job.jobType;
        if (!jobType) return '';

        const jobTypeName = jobType.name || '';
        let subjectPart = "";
        let scopePart = "";

        if (job.jobSubject?.jobSubject !== 0 && job.jobSubject?.name) {
            subjectPart = job.jobSubject.name;
        }

        const details: string[] = [];

        if (job.jobInfos) {
            job.jobInfos.forEach(info => {
                let gradeDetail = info.name || '';

                if (info.jobClass && info.jobClass.length > 0) {
                    gradeDetail += `(${info.jobClass.map(cls => cls.name).join('、')})`;
                }

                details.push(gradeDetail);
            });

            if (details.length > 0) {
                scopePart = details.join('、');
            }
        }

        let summary = jobTypeName;

        switch (jobType.jobType) {
            case 1: summary = jobTypeName; break;
            case 2: summary = `${scopePart} ${jobTypeName}`; break;
            case 3: summary = `${scopePart} ${subjectPart} ${jobTypeName}`; break;
            case 4: summary = `${scopePart} ${subjectPart} ${jobTypeName}`; break;
            case 5: summary = `${scopePart} ${jobTypeName}`; break;
            default:
                if (scopePart) summary = `${scopePart} ${summary}`;
                if (subjectPart) summary = `${subjectPart} ${summary}`;
                break;
        }

        return summary.replace(/\s+/g, ' ').trim();
    }).filter(summary => summary.length > 0)
        .join('；');
}

/**
 * React 自定义 Hook: 处理和提供核心的用户职位/角色信息
 * @returns {UseUserRolesResult} 包含核心角色信息和辅助函数的对象
 */
export function useUserRoles(): UseUserRolesResult {
    // 设置 jobs 数据
    const setJobs = useCallback((jobs: UserJobs) => {
        // 如果数据变化，清除所有缓存结果
        if (JSON.stringify(jobsSignal.value) !== JSON.stringify(jobs)) {
            clearCache();
            jobsSignal.value = jobs || [];
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 清除缓存的计算结果
    const clearCache = useCallback(() => {
        allGradeIdsSignal.value = null;
        allSubjectNamesSignal.value = null;
        allSubjectsSignal.value = null;
        teachingScopeSignal.value = null;
        roleSummarySignal.value = null;
        homeroomScopeSignal.value = null;
    }, []);

    // // 懒加载获取年级 IDs
    // const getAllGradeIds = useCallback(() => {
    //     if (allGradeIdsSignal.value === null) {
    //         allGradeIdsSignal.value = computeAllGradeIds();
    //     }
    //     return allGradeIdsSignal.value;
    // }, []);

    // // 懒加载获取学科名称
    // const getAllSubjectNames = useCallback(() => {
    //     if (allSubjectNamesSignal.value === null) {
    //         allSubjectNamesSignal.value = computeAllSubjectNames();
    //     }
    //     return allSubjectNamesSignal.value;
    // }, []);

    // // 懒加载获取学科
    // const getAllSubjects = useCallback(() => {
    //     if (!allSubjectsSignal.value?.length) {
    //         allSubjectsSignal.value = computeAllSubjects();
    //     }
    //     return allSubjectsSignal.value;
    // }, []);


    // // 懒加载获取教学范围
    // const getTeachingScope = useCallback(() => {
    //     if (teachingScopeSignal.value === null) {
    //         teachingScopeSignal.value = computeTeachingScope();
    //     }
    //     return teachingScopeSignal.value;
    // }, []);

    // // 懒加载获取班主任范围
    // const getHomeroomScope = useCallback(() => {
    //     if (homeroomScopeSignal.value === null) {
    //         homeroomScopeSignal.value = computeHomeroomScope();
    //     }
    //     return homeroomScopeSignal.value;
    // }, []);

    // 懒加载获取角色摘要
    const getRoleSummary = useCallback(() => {
        if (roleSummarySignal.value === null) {
            roleSummarySignal.value = computeRoleSummary();
        }
        return roleSummarySignal.value;
    }, []);
    // // 获取特定角色在某年级负责的班级
    // const getClassesForJobTypeAndGrade = useCallback((jobTypeId: number, gradeId: number): JobClass[] => {
    //     const job = getJobByType(jobTypeId);
    //     if (!job) return [];

    //     const gradeInfo = job.jobInfos?.find(info => info.jobGrade === gradeId);
    //     return gradeInfo?.jobClass || [];
    // }, []);

    // --- 核心辅助函数 ---

    // 检查是否有某职位类型
    const hasJobType = useCallback((jobTypeId: number): boolean => {
        return jobsSignal.value.some(job => job.jobType?.jobType === jobTypeId);
    }, []);

    // 检查是否具有所有职位类型
    const hasJobTypes = useCallback((jobTypeIds: number[], isAll: boolean = false): boolean => {
        if (isAll) {
            return jobTypeIds.every(jobTypeId => hasJobType(jobTypeId));
        } else {
            return jobTypeIds.some(jobTypeId => hasJobType(jobTypeId));
        }
    }, [hasJobType]);



    // 返回使用 getter 方法的接口
    return {
        getRoleSummary,
        hasJobType,
        hasJobTypes,
        computePrimarySubject,
        setJobs,
        getJobs: () => jobsSignal.value,
        // getAllGradeIds,
        // getAllSubjectNames,
        // getAllSubjects,
        // getTeachingScope,
        // getHomeroomScope,
        // getRoleSummary,
        // clearCache,
        // getClassesForJobTypeAndGrade,
    };
}

// 提供初始化全局状态的函数
export function initUserRoles(jobs: UserJobs): void {
    // 设置数据
    jobsSignal.value = jobs || [];

    // 清除所有缓存的计算结果
    allGradeIdsSignal.value = null;
    allSubjectNamesSignal.value = null;
    allSubjectsSignal.value = null;
    teachingScopeSignal.value = null;
    roleSummarySignal.value = null;
    homeroomScopeSignal.value = null;
}