import React, { createContext, useContext, RefObject } from "react";
import { ReactSketchCanvasRef } from "@repo/react-sketch-canvas";

interface SketchCanvasRefProviderProps {
  canvasRef: RefObject<ReactSketchCanvasRef | null> | null;
  strokeColor: string;
  strokeWidth: number;
  setStrokeColor: (color: string) => void;
  setStrokeWidth: (width: number) => void;
  children: React.ReactNode;
}

export const SketchCanvasRefContext =
  createContext<SketchCanvasRefProviderProps>({
    canvasRef: null,
    strokeColor: "#000000",
    strokeWidth: 4,
    setStrokeColor: () => {},
    setStrokeWidth: () => {},
    children: null,
  });

export const useSketchCanvasRef = () => useContext(SketchCanvasRefContext);

export const SketchCanvasRefProvider: React.FC<
  SketchCanvasRefProviderProps
> = ({
  canvasRef,
  strokeColor,
  strokeWidth,
  setStrokeColor,
  setStrokeWidth,
  children,
}) => {
  return (
    <SketchCanvasRefContext.Provider
      value={{
        canvasRef,
        strokeColor,
        strokeWidth,
        setStrokeColor,
        setStrokeWidth,
        children,
      }}
    >
      {children}
    </SketchCanvasRefContext.Provider>
  );
};
