import { Skeleton } from "@/ui/skeleton";
export default function ClassStats({
  averageAccuracy,
  classProgress,
  loading,
}: {
  averageAccuracy: string;
  classProgress: string;
  loading: boolean;
}) {
  return (
    <div className="border-px border-line-1 relative flex w-60 shrink-0 flex-col items-start justify-start gap-3 self-stretch rounded-xl border bg-white pb-5 pl-5 pr-5 pt-3.5">
      <div
        className="text-gray-1 relative flex items-center justify-start overflow-hidden text-left text-base font-semibold leading-[150%]"
        style={{ textOverflow: "ellipsis", whiteSpace: "nowrap" }}
      >
        班级数据
      </div>
      <div className="relative flex flex-1 flex-row items-center justify-start gap-2 self-stretch">
        {loading ? (
          <div className="flex items-center justify-between h-full gap-2 w-full">
          <Skeleton className="h-24 w-1/2" />
          <Skeleton className="h-24 w-1/2" />
        </div>
        ) : (<><div className="min-w-1/2 relative flex flex-1 flex-col items-center justify-center gap-2 self-stretch rounded-xl border-r border-solid border-[transparent] bg-[rgba(233,244,255,0.50)] p-3">
          <div className="text-blue-1 relative flex shrink-0 flex-row items-center justify-center gap-0.5 self-stretch font-[800]">
            <div className="text-blue-1 relative flex h-8 items-center justify-start text-left text-3xl font-[800] leading-normal">
              {classProgress}
            </div>
            <div className="text-blue-1 font-en-h4-14-semibold-font-family text-en-h4-14-semibold-font-size leading-en-h4-14-semibold-line-height font-en-h4-14-semibold-font-weight relative flex h-full items-end justify-start text-left font-[800]">
              %
            </div>
          </div>
          <div className="text-gray-3 relative w-full text-center text-[0.625rem] font-medium leading-[150%] opacity-80">
            完成率
          </div>
        </div>
        <div className="min-w-1/2 relative flex flex-1 flex-col items-center justify-center gap-2 self-stretch rounded-xl border-r border-solid border-[transparent] bg-[rgba(233,251,228,0.50)] p-3">
          <div className="text-green-0 relative flex shrink-0 flex-row items-center justify-center gap-0.5 self-stretch font-[800]">
            <div className="text-green-0 relative flex h-8 items-center justify-start text-left text-3xl font-[800] leading-normal">
              {averageAccuracy === "100.0" ? "100" : averageAccuracy}
            </div>
            <div className="text-green-0 font-en-h4-14-semibold-font-family text-en-h4-14-semibold-font-size leading-en-h4-14-semibold-line-height font-en-h4-14-semibold-font-weight relative flex h-full items-end justify-start text-left font-[800]">
              %
            </div>
          </div>
          <div className="text-gray-3 relative w-full text-center text-[0.625rem] font-medium leading-[150%] opacity-80">
            正确率
          </div>
        </div></>)}
      </div>
    </div>
  );
}
