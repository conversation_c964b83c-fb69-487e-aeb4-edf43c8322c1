"use client";
import AssignSkeletonComp from "./AssignSkeletonComp";

export default function AssignSkeleton() {
  return (
    <div className="h-full bg-[#F5FAFF] p-6 pt-2.5">
      <AssignSkeletonComp className="h-8.25 mb-5 w-11" />

      <div className="mb-8 grid grid-cols-4 gap-4">
        <AssignSkeletonComp className="col-span-1 h-24 w-auto" />
        <AssignSkeletonComp className="col-span-1 h-24 w-auto" />
        <AssignSkeletonComp className="col-span-1 h-24 w-auto" />
        <AssignSkeletonComp className="col-span-1 h-24 w-auto" />
      </div>

      <AssignSkeletonComp className="h-8.25 w-21.75 mb-5" />

      <div className="mb-5 grid grid-cols-4 gap-4">
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
      </div>
      <div className="grid grid-cols-4 gap-4">
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
        <div className="">
          <AssignSkeletonComp className="mb-1.5 h-6 w-full" />
          <AssignSkeletonComp className="h-3.75 mb-4.5 w-19.5" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
          <AssignSkeletonComp className="h-26 mb-2 w-full" />
        </div>
      </div>
    </div>
  );
}
