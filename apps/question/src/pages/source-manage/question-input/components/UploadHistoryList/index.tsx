'use client';

import { showSubmitModal } from '@/components/SubmitModal';
import CreateQuestion from '@/pages/source-manage/question-create/CreateQuestionModal';
import {
  exerciseRevocationAudit,
  failedPaperRecordDelete,
  fetchUploadHistoryList,
} from '@/services/api';
import { getFormatTimeStr } from '@/utils';
import { DeleteOutlined, EyeOutlined, UndoOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { App } from 'antd';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

const UploadHistoryList: React.FC<{
  onView: (record: API.UploadWordListItem) => void;
  reloadTime: number;
}> = ({ onView, reloadTime }) => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType | null>(null);
  const [reviewItem, setReviewItem] = useState<API.UploadWordListItem | null>(null);
  const columns: ProColumns<API.UploadWordListItem>[] = [
    {
      title: '已上传Word文件',
      order: 1,
      dataIndex: 'paperName',
      ellipsis: true,
    },
    {
      title: '上传时间',
      dataIndex: 'updateTime',
      search: false,
      render: (_, record) => getFormatTimeStr(record.createTime),
    },
    {
      title: '当前状态',
      order: 2,
      dataIndex: 'paperStatus',
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择审核状态',
      },
      valueEnum: {
        1: { text: '上传 word 文件', status: 'processing' },
        2: { text: '上传 word 文件失败', status: 'error' },
        3: { text: '后台自动识别中', status: 'processing' },
        4: { text: '后台识别失败', status: 'error' },
        5: { text: '审核中', status: 'warning' },
        6: { text: '审核不通过', status: 'error' },
        7: { text: '数据已入库', status: 'success' },
        8: { text: '待确认识别结果', status: 'warning' },
      },
      search: false,
    },

    {
      title: '操作',
      width: 100,
      valueType: 'option',
      search: false,
      render: (_, record) => {
        if (record.paperStatus === 6) {
          return (
            <a onClick={() => onViewReason(record)} className="text-blue-500">
              <EyeOutlined /> 查看
            </a>
          );
        }
        if (record.paperStatus === 5) {
          return (
            <a onClick={() => onRevoke(record)} className="text-red-500">
              <UndoOutlined /> 撤销
            </a>
          );
        }
        if (record.paperStatus === 8) {
          return (
            <a onClick={() => setReviewItem(record)} className="text-blue-500">
              <EyeOutlined /> 查看
            </a>
          );
        }
        if (record.paperStatus === 4) {
          return (
            <a onClick={() => onRemove(record)} className="text-red-500">
              <DeleteOutlined /> 删除
            </a>
          );
        }
        return null;
      },
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [reloadTime]);

  const onViewReason = (record: API.UploadWordListItem) => {
    onView(record);
  };
  const revokeAuditTask = async (record: API.UploadWordListItem) => {
    const res = await exerciseRevocationAudit(record.auditTaskId);
    const { code } = res;
    if (code === 0) {
      message.success('撤回审核成功');
      actionRef.current?.reload();
    } else {
      message.error('撤回审核不通过');
    }
  };
  const onRevoke = async (record: API.UploadWordListItem) => {
    showSubmitModal({
      title: '确认撤销审核？',
      content: '',
      onOk: () => revokeAuditTask(record),
    });
  };

  const onRemove = async (record: API.UploadWordListItem) => {
    modal.confirm({
      title: '确认删除吗？',
      content: '',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await failedPaperRecordDelete({
          paperId: record.paperId,
        });
        const { code } = res;
        if (code === 0) {
          message.success('删除成功');
          if (actionRef.current) {
            console.log('actionRef.current', actionRef.current);
            actionRef.current.reloadAndRest?.();
          }
          return;
        }
        message.error('删除失败');
      },
    });
  };

  return (
    <div>
      <ProTable<API.UploadWordListItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params) => {
          const { data } = await fetchUploadHistoryList({
            page: params.current,
            pageSize: params.pageSize,
          });
          const { list, total } = data;
          return {
            data: list ?? [],
            success: true,
            total: total,
          };
        }}
        rowKey="paperId"
        search={false}
        options={{
          reload: true,
          density: true,
          setting: true,
        }}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        headerTitle="上传历史"
      />
      <CreateQuestion
        onSubmit={() => {
          actionRef.current?.reload();
          setReviewItem(null);
        }}
        data={reviewItem as any}
        open={!!reviewItem}
        onBack={() => setReviewItem(null)}
        way="upload"
      />
    </div>
  );
};

export default UploadHistoryList;
